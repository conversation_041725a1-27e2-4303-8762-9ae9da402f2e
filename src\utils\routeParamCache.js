/**
 * 通用路由参数缓存 & 恢复工具
 * 用途：解决在多标签、面包屑跳转或 keep-alive 切换导致的 query 丢失问题。
 * 支持：
 *  - 缓存关键 query 参数（可配置 key 列表或提取函数）
 *  - 自动写入时间戳与 TTL 过期控制
 *  - 恢复当前路由缺失的参数（只补缺，不覆盖现有）
 *  - 命名空间隔离，避免不同页面冲突
 *
 * 使用示例（在组件中）：
 *  import { persistRouteParams, restoreMissingRouteQuery } from '@/utils/routeParamCache';
 *
 *  created() {
 *    restoreMissingRouteQuery(this.$router, this.$route, { ns: 'AddKnowledge', keys: ['id','curriculumId','title','type'] });
 *    // ...加载数据后
 *    persistRouteParams(this.$route, { ns: 'AddKnowledge', keys: ['id','curriculumId','title','type'] });
 *  }
 */

const DEFAULT_TTL = 1000 * 60 * 60 * 3; // 3小时

function buildStorageKey(ns) {
  return `RouteParamCache:${ns || 'default'}`;
}

/**
 * 过滤出需要缓存的字段
 * @param {Object} query 当前路由 query
 * @param {string[]} keys 要保存的键
 * @returns {Object}
 */
function pickKeys(query, keys) {
  const out = {};
  keys.forEach((k) => {
    if (query[k] !== undefined) out[k] = query[k];
  });
  return out;
}

/**
 * 写入缓存
 * @param {Route} route vue-router 的 route 对象
 * @param {Object} options
 * @param {string} options.ns 命名空间
 * @param {string[]} options.keys 要保存的键
 * @param {number} [options.ttl] 过期毫秒
 */
export function persistRouteParams(route, { ns = 'default', keys = [], ttl = DEFAULT_TTL } = {}) {
  try {
    if (!route || !route.query) return;
    const data = pickKeys(route.query, keys);
    if (!Object.keys(data).length) return;
    const payload = { data, ts: Date.now(), ttl };
    sessionStorage.setItem(buildStorageKey(ns), JSON.stringify(payload));
  } catch (e) {
    // 静默失败
    console.warn('[routeParamCache] persist failed', e);
  }
}

/**
 * 读取缓存（不做恢复操作）
 * @param {string} ns
 * @returns {Object|null}
 */
export function getCachedRouteParams(ns = 'default') {
  try {
    const raw = sessionStorage.getItem(buildStorageKey(ns));
    if (!raw) return null;
    const parsed = JSON.parse(raw);
    if (!parsed || !parsed.data) return null;
    const { ts, ttl = DEFAULT_TTL } = parsed;
    if (ttl && Date.now() - ts > ttl) {
      // 过期清理
      sessionStorage.removeItem(buildStorageKey(ns));
      return null;
    }
    return parsed.data;
  } catch (e) {
    console.warn('[routeParamCache] get cache failed', e);
    return null;
  }
}

/**
 * 若当前路由缺少指定 keys 中的任意一项，则从缓存中补齐并无刷新替换
 * @param {VueRouter} router
 * @param {Route} route
 * @param {Object} options
 * @param {string} options.ns 命名空间
 * @param {string[]} options.keys 需要恢复的键
 * @param {boolean} [options.force=false] 是否强制覆盖已有（默认只补缺）
 * @returns {boolean} 是否执行了恢复
 */
export function restoreMissingRouteQuery(router, route, { ns = 'default', keys = [], force = false } = {}) {
  try {
    if (!router || !route) return false;
    const cache = getCachedRouteParams(ns);
    if (!cache) return false;

    let need = false;
    const nextQuery = { ...route.query };
    keys.forEach((k) => {
      if (force) {
        if (cache[k] !== undefined) {
          nextQuery[k] = cache[k];
        }
      } else {
        if (nextQuery[k] === undefined && cache[k] !== undefined) {
          nextQuery[k] = cache[k];
          need = true;
        }
      }
    });

    if (force) {
      // 若强制覆盖，判断是否有差异
      need = keys.some((k) => cache[k] !== undefined && cache[k] !== route.query[k]);
    }

    if (!need) return false;

    router.replace({ path: route.path, query: nextQuery });
    return true;
  } catch (e) {
    console.warn('[routeParamCache] restore failed', e);
    return false;
  }
}

/**
 * 主动清理缓存（例如页面完成或退出时）
 * @param {string} ns
 */
export function clearRouteParamCache(ns = 'default') {
  try {
    sessionStorage.removeItem(buildStorageKey(ns));
  } catch (e) {
    console.warn('[routeParamCache] clear failed', e);
  }
}

export default {
  persistRouteParams,
  getCachedRouteParams,
  restoreMissingRouteQuery,
  clearRouteParamCache
};
