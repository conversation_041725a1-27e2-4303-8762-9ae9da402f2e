import request from "@/utils/request";
// 题库管理接口

// 是否关联知识点
export function getKnowledgePointAssociation(data) {
  return request({
    url: "/dyw/web/chinese/chineseQuestionBank/linkKnowledgeEnum",
    method: "GET",
    params: data
  });
}

// 获取所有题目类型
export function getAllQuestionTypeList(data) {
  return request({
    url: "/dyw/web/chinese/chineseQuestionBank/questionType",
    method: "GET",
    params: data
  });
}

// 获取课程大类
export function getAllCourseTypeList(data) {
  return request({
    url: "znyy/curriculum/chinese",
    method: "GET",
    params: data
  });
}

// 获取题干材料ID列表
export function getQuestionMaterialIdList(data) {
  return request({
    url: "/dyw/web/chinese/questionMaterial/titleList",
    method: "GET",
    params: data
  });
}

// 获取学科列表
export function getAllSubjectList(data) {
  return request({
    url: "/dyw/web/chinese/coursePeriodConfig/selectTree",
    method: "GET",
    params: data
  });
}

// 题库列表分页查询
export function getQuestionBankList(data) {
  return request({
    url: "/dyw/web/chinese/chineseQuestionBank/question/page",
    method: "GET",
    params: data
  });
}

// 删除题库
export function deleteQuestionBank(data) {
  return request({
    url: "/dyw/web/chinese/chineseQuestionBank/delete",
    method: "GET",
    params: data
  });
}
// 获取所有题干材料列表
export function getAllQuestionMaterialList(data) {
  return request({
    url: "/dyw/web/chinese/questionMaterial/page",
    method: "GET",
    params: data
  });
}

// 删除题干材料
export function deleteQuestionMaterial(data) {
  return request({
    url: "/dyw/web/chinese/questionMaterial/delete",
    method: "GET",
    params: data
  })
}

// 添加题干材料
export function addQuestionMaterial(data) {
  return request({
    url: "/dyw/web/chinese/questionMaterial/save",
    method: "POST",
    data
  })
}

// 根据id 获取题干材料详情
export function getQuestionMaterialById(data) {
  return request({
    url: "/dyw/web/chinese/questionMaterial/detail",
    method: "GET",
    params: data
  })
}

// 编辑题干材料
export function editQuestionMaterial(data) {
  return request({
    url: "/dyw/web/chinese/questionMaterial/update",
    method: "POST",
    data
  })
}

// 绑定题目预览列表
export function getQuestionMaterialRelationList(data) {
  return request({
    url: "/dyw/web/chinese/questionMaterial/linkQuestionList",
    method: "GET",
    params: data
  })
}

// 获取试题难度
export function getQuestionDifficulty(data) {
  return request({
    url: "/dyw/web/chinese/chineseQuestionBank/questionDifficulty",
    method: "GET",
    params: data
  })
}

// 获取试题类型
export function getQuestionType(data) {
  return request({
    url: "/dyw/web/chinese/chineseQuestionBank/questionType",
    method: "GET",
    params: data
  })
}

// 查询知识点列表
export function getKnowledgeList(data) {
  return request({
    url: "/dyw/web/chinese/courseKnowledge/list",
    method: "GET",
    params: data
  })
}

// 知识点获取学段接口
export function getKnowledgePeriod(data) {
  return request({
    url: "/dyw/web/chinese/courseKnowledge/phase",
    method: "GET",
    params: data
  })
}

// 课程分类树
export function getCourseClassificationTree(data) {
  return request({
    url: "/dyw/web/chinese/coursePeriodConfig/selectTree",
    method: "GET",
    params: data
  })
}

// 新增题目
export function addQuestion(data) {
  return request({
    url: "/dyw/web/chinese/chineseQuestionBank/insertOrUpdate",
    method: "POST",
    data
  })
}

// 获取题目详情
export function getQuestionDetail(data) {
  return request({
    url: "/dyw/web/chinese/chineseQuestionBank/info",
    method: "GET",
    params: data
  })
}

// 获取所有题目列表
export function getQuestionList(data) {
  return request({
    url: "/dyw/web/chinese/chineseQuestionBank/questionList",
    method: "GET",
    params: data
  })
}




