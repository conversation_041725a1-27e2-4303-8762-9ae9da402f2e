<template>
  <div class="dx-table-tooltip-test">
    <h1>DxTable showOverflowTooltip 默认行为测试</h1>
    
    <div class="test-section">
      <h2>默认行为测试（showOverflowTooltip 应该默认为 true）</h2>
      <p>以下表格列没有显式设置 showOverflowTooltip 属性，应该默认显示 tooltip</p>
      <dx-table :data="longTextData" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="120"></dx-table-column>
        <dx-table-column prop="description" label="描述（默认tooltip）" width="200"></dx-table-column>
        <dx-table-column prop="address" label="地址（默认tooltip）" width="250"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>显式设置 showOverflowTooltip 为 false</h2>
      <p>以下表格列显式设置了 showOverflowTooltip="false"，不应该显示 tooltip</p>
      <dx-table :data="longTextData" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="120"></dx-table-column>
        <dx-table-column prop="description" label="描述（无tooltip）" width="200" :show-overflow-tooltip="false"></dx-table-column>
        <dx-table-column prop="address" label="地址（无tooltip）" width="250" :show-overflow-tooltip="false"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>显式设置 showOverflowTooltip 为 true</h2>
      <p>以下表格列显式设置了 showOverflowTooltip="true"，应该显示 tooltip</p>
      <dx-table :data="longTextData" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="120"></dx-table-column>
        <dx-table-column prop="description" label="描述（显式tooltip）" width="200" :show-overflow-tooltip="true"></dx-table-column>
        <dx-table-column prop="address" label="地址（显式tooltip）" width="250" :show-overflow-tooltip="true"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>混合设置测试</h2>
      <p>混合使用默认、true、false 设置</p>
      <dx-table :data="longTextData" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名（默认）" width="150"></dx-table-column>
        <dx-table-column prop="description" label="描述（false）" width="200" :show-overflow-tooltip="false"></dx-table-column>
        <dx-table-column prop="address" label="地址（true）" width="200" :show-overflow-tooltip="true"></dx-table-column>
      </dx-table>
    </div>

    <div class="instructions">
      <h3>测试说明：</h3>
      <ul>
        <li>将鼠标悬停在表格单元格上，观察是否显示 tooltip</li>
        <li>第一个表格：所有列都应该显示 tooltip（因为默认值为 true）</li>
        <li>第二个表格：描述和地址列不应该显示 tooltip</li>
        <li>第三个表格：描述和地址列应该显示 tooltip</li>
        <li>第四个表格：姓名和地址列应该显示 tooltip，描述列不显示</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DxTableTooltipTest',
  data() {
    return {
      longTextData: [
        {
          id: 1,
          name: '张三',
          description: '这是一段很长很长的描述文字，用来测试表格单元格内容溢出时是否会显示tooltip提示信息，这段文字应该会超出单元格的宽度限制',
          address: '北京市朝阳区建国门外大街1号国贸大厦A座1001室，这是一个很长的地址信息用来测试tooltip功能'
        },
        {
          id: 2,
          name: '李四',
          description: '另一段超长的描述内容，包含了大量的文字信息，目的是为了验证当文字内容超出表格列宽度时的显示效果和tooltip行为',
          address: '上海市浦东新区陆家嘴金融贸易区世纪大道100号上海环球金融中心88层，这也是一个超长的地址'
        },
        {
          id: 3,
          name: '王五',
          description: '第三条测试数据的描述信息，同样是一段很长的文字内容，用于测试表格组件在处理长文本时的表现和用户体验',
          address: '广州市天河区珠江新城花城大道5号南天广场2801室，广东省广州市的一个详细地址信息'
        },
        {
          id: 4,
          name: '赵六',
          description: '最后一条测试记录的描述，这里放置了足够长的文本内容来确保能够触发表格单元格的溢出显示机制',
          address: '深圳市南山区科技园南区高新南一道6号TCL大厦B座15楼，深圳市的一个具体办公地址'
        }
      ]
    };
  }
};
</script>

<style scoped>
.dx-table-tooltip-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
}

.test-section h2 {
  margin-bottom: 10px;
  color: #303133;
  font-size: 18px;
}

.test-section p {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.instructions {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-top: 30px;
}

.instructions h3 {
  margin-bottom: 10px;
  color: #303133;
}

.instructions ul {
  margin: 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 5px;
  color: #606266;
}
</style>
