import request from '@/utils/request'

export default {
  //分页
  list(data) {
    return request({
      url: '/activiti/dblink/list',
      method: 'GET',
      params: data
    })
  },
  listDblinkTables(data){
    return request({
      url: '/activiti/dblink/listDblinkTables',
      method: 'GET',
      params: data
    })
  },
  listDblinkTableColumns(data){
    return request({
      url: '/activiti/dblink/listDblinkTableColumns',
      method: 'GET',
      params: data
    })
  }
}
