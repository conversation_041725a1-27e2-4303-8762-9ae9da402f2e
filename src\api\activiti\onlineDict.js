import request from '@/utils/request'

export default {
  //分页
  list(data) {
    return request({
      url: '/activiti/dict/list',
      method: 'GET',
      params: data
    })
  },
  delete(data) {
    return request({
      url: '/activiti/dict/delete',
      method: 'DELETE',
      params: data
    })
  },
  view(data) {
    return request({
      url: '/activiti/dict/view',
      method: 'GET',
      params: data
    })
  },
  add(data){
    return request({
      url: '/activiti/dict/saveNew',
      method: 'POST',
      data
    })
  },
  update(data){
    return request({
      url: '/activiti/dict/update',
      method: 'POST',
      data
    })
  }

}
