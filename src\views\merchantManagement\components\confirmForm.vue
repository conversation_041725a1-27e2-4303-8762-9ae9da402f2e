<template>
  <div>
    <CustomDialog v-if="dialogVisible" title="开通确认单" :center="true" :value.sync="dialogVisible" width="500px" @close="handleOuterClose">
      <el-form ref="confirmFormRef" :model="form" label-width="120px">
        <el-form-item label="非标" prop="isStandardRegistration">
          <template #label>
            <div>
              非标
              <el-tooltip effect="dark" placement="bottom-start">
                <i class="el-icon-question" />
                <div slot="content" style="line-height: 20px">
                  有非标标签的渠道商，总
                  <br />
                  部不用向城市服务中心结
                  <br />
                  算服务费
                </div>
              </el-tooltip>
            </div>
          </template>
          <el-radio-group v-model="form.isStandardRegistration">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="门店编号" prop="merchantCode">
          <el-input disabled v-model="form.merchantCode"></el-input>
        </el-form-item>
        <el-form-item label="门店名称" prop="merchantName">
          <el-input disabled v-model="form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="realName">
          <el-input disabled type="person" v-model="form.realName"></el-input>
        </el-form-item>
        <el-form-item class="dialog-footer">
          <template>
            <el-button :loading="loading" type="primary" @click="handleConfirm">确认开通</el-button>
            <el-button @click="handleOuterClose">取 消</el-button>
          </template>
        </el-form-item>
      </el-form>
    </CustomDialog>
  </div>
</template>

<script>
  import schoolList from '@/api/schoolList';
  import CustomDialog from '@/components/customDialog/index.vue';
  export default {
    name: 'confirmForm',
    props: {
      isShowForm: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          isStandardRegistration: 1,
          merchantCode: '',
          merchantName: '',
          realName: ''
        },
        loading: false
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.isShowForm;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },
    components: {
      CustomDialog
    },
    methods: {
      handleOuterClose() {
        this.reset();
        this.$emit('closeConfirmDialog');
      },
      async handleConfirm() {
        if (this.loading) return;
        if (![0, 1].includes(this.form.isStandardRegistration)) {
          this.$message.error('请选择是否为非标');
          return;
        }
        this.loading = true;
        try {
          schoolList
            .openSchoolApi(this.form)
            .then(() => {
              this.$message.success('操作成功');
              this.loading = false;
              this.reset();
              this.$emit('closeConfirmDialog', true);
            })
            .catch((error) => {
              this.loading = false;
              this.$message.error(error.message);
            });
        } catch (error) {
          console.log('🚀 ~ handleConfirm ~ error:', error);
          this.$message.error('操作失败');
          this.loading = false;
        }
      },

      setData(data) {
        console.log('🚀 ~ setData ~ data:', data);
        this.form.merchantCode = data.merchantCode;
        this.form.realName = data.realName;
        this.form.merchantName = data.merchantName;
      },

      reset() {
        this.form = {
          isStandardRegistration: 1,
          merchantCode: '',
          merchantName: '',
          realName: ''
        };
        this.$refs.confirmFormRef.resetFields();
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px 0;
    border-top: 1px solid #e8e8e8;
  }
  ::v-deep .el-dialog {
    padding-top: 0;
  }
</style>
