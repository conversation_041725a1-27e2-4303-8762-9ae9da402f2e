import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/abacusMentalCalc',
  component: Layout,
  redirect: '/abacusMentalCalc',
  meta: {
    perm: 'm:abacusMentalCalc',
    title: '珠心算超人',
    icon: 'abacus'
  },
  children: [
    {
      path: 'courseIndex',
      component: () => import('@/views/abacusMentalCalc/course_index'),
      name: 'courseIndex',
      meta: {
        perm: 'm:abacusMentalCalc:courseIndex',
        title: '课程管理'
      }
    },
    {
      path: 'questionIndex',
      component: () => import('@/views/abacusMentalCalc/question_index'),
      name: 'questionIndex',
      meta: {
        perm: 'm:abacusMentalCalc:questionIndex',
        title: '题型管理'
      }
    },
    {
      path: 'categoryIndex',
      component: () => import('@/views/abacusMentalCalc/category_index'),
      name: 'categoryIndex',
      meta: {
        perm: 'm:abacusMentalCalc:categoryIndex',
        title: '维度管理'
      }
    },
    {
      path: 'reportIndex',
      component: () => import('@/views/abacusMentalCalc/report_index'),
      name: 'reportIndex',
      meta: {
        perm: 'm:abacusMentalCalc:reportIndex',
        title: '报告管理'
      }
    },
    {
      path: 'question',
      component: () => import('@/views/abacusMentalCalc/question'),
      name: 'question',
      hidden: true,
      meta: {
        perm: 'm:abacusMentalCalc:question',
        title: '课程题目',
        icon: 'post'
      }
    },
    {
      path: 'subQuestion',
      component: () => import('@/views/abacusMentalCalc/subQuestion'),
      name: 'subQuestion',
      hidden: true,
      meta: {
        perm: 'm:abacusMentalCalc:subQuestion',
        title: '附加题型',
        icon: 'post'
      }
    },
    {
      path: 'report',
      component: () => import('@/views/abacusMentalCalc/report'),
      name: 'report',
      hidden: true,
      meta: {
        perm: 'm:abacusMentalCalc:report',
        title: '报告维度编辑',
        icon: 'post'
      }
    },
    {
      path: 'onlyQuestion',
      component: () => import('@/views/abacusMentalCalc/question/only_question.vue'),
      name: 'onlyQuestion',
      hidden: true,
      meta: { perm: 'm:abacusMentalCalc:onlyQuestion', title: '算式题', icon: 'post' }
    },
    {
      path: 'audioAnswer',
      component: () => import('@/views/abacusMentalCalc/question/audioAnswer.vue'),
      name: 'audioAnswer',
      hidden: true,
      meta: { perm: 'm:abacusMentalCalc:audioAnswer', title: '音频答案题', icon: 'post' }
    },
    {
      path: 'onlyScore',
      component: () => import('@/views/abacusMentalCalc/question/only_score.vue'),
      name: 'onlyScore',
      hidden: true,
      meta: { perm: 'm:abacusMentalCalc:onlyScore', title: '听写题', icon: 'post' }
    },
    {
      path: 'videoAudio',
      component: () => import('@/views/abacusMentalCalc/question/video_audio.vue'),
      name: 'videoAudio',
      hidden: true,
      meta: { perm: 'm:abacusMentalCalc:videoAudio', title: '音视频题', icon: 'post' }
    },
    {
      path: 'questionAnswer',
      component: () => import('@/views/abacusMentalCalc/question/question_answer.vue'),
      name: 'questionAnswer',
      hidden: true,
      meta: { perm: 'm:abacusMentalCalc:questionAnswer', title: '算式答案题', icon: 'post' }
    },
    {
      path: 'countdown',
      component: () => import('@/views/abacusMentalCalc/question/countdown.vue'),
      name: 'countdown',
      hidden: true,
      meta: { perm: 'm:abacusMentalCalc:countdown', title: '倒计时题', icon: 'post' }
    },
    {
      path: 'duiBuShuNum',
      component: () => import('@/views/abacusMentalCalc/question/duibushu_num.vue'),
      name: 'duiBuShuNum',
      hidden: true,
      meta: { perm: 'm:abacusMentalCalc:duiBuShuNum', title: '对补数数字', icon: 'post' }
    },
    {
      path: 'duiBuShuImg',
      component: () => import('@/views/abacusMentalCalc/question/duibushu_img.vue'),
      name: 'duiBuShuImg',
      hidden: true,
      meta: { perm: 'm:abacusMentalCalc:duiBuShuImg', title: '对补数图片', icon: 'post' }
    }
  ]
};
