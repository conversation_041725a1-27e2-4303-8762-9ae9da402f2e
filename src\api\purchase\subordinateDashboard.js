// 采购管理-下级采购看板
import request from '@/utils/request';

export default {
  // 列表分页查询
  queryList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/purchase/board/queryBrandPurchasePage',
      method: 'GET',
      params: {
        pageNum,
        pageSize,
        ...data
      }
    });
  },
  // 查询采购明细
  queryPurchaseDetail(pageNum, pageSize, merchantCode) {
    return request({
      url: '/znyy/purchase/board/detail',
      method: 'GET',
      params: {
        pageNum,
        pageSize,
        merchantCode
      }
    });
  },
  // 分配
  distribution(params) {
    return request({
      url: '/znyy/purchase/board/distribution',
      method: 'GET',
      params
    });
  },
  // 划分
  transfer(params) {
    return request({
      url: '/znyy/purchase/board/transfer',
      method: 'post',
      data: params
    });
  },
  // 查询剩余学习系统数量
  queryStock() {
    return request({
      url: '/znyy/purchase/board/queryRestLearningSysCount',
      method: 'GET'
    });
  },
  depositLearningSysType(params) {
    return request({
      url: '/znyy//purchase/board/depositLearningSysType',
      method: 'GET',
      params
    });
  }
};


