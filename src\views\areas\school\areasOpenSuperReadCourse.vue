<!--开通超级阅读课程-->
<template>
  <div class="app-container">
    <el-card style="margin-bottom: 15px">
      <el-form label-width="100px" ref="dataQuery" :model="dataQuery" label-position="left">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="课程编号:">
              <el-input v-model="dataQuery.courseCode" @keyup.enter.native="fetchData()" style="width: 100%" placeholder="请输入课程编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="课程名称:">
              <el-input v-model="dataQuery.courseName" @keyup.enter.native="fetchData()" style="width: 100%" placeholder="请输入课程名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="题目类型:">
              <el-select v-model="dataQuery.courseTypeDetail" filterable value-key="value" placeholder="请选择" style="width: 100%">
                <el-option v-for="(item, index) in courseTypeDetails" :key="index" :label="item.label" :value="item.idx" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="课程学段:">
              <el-select v-model="dataQuery.courseStage" filterable value-key="value" placeholder="请选择" style="width: 100%">
                <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.idx" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-button type="primary" size="small" class="searchStyle" @click="fetchData01()">搜索</el-button>
            <el-button size="small" class="restStyle" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- 搜索 -->

    <el-card>
      <div class="btn-add">
        <el-button size="small" type="primary" style="background-color: #51cc92 !important; color: #ffffff !important" class="components-exportBtn" @click="openCourse()">
          开通
        </el-button>
      </div>
      <div v-if="tableData.length > 0">
        <el-table
          class="course-table"
          v-loading="tableLoading"
          ref="multipleTable"
          tooltip-effect="dark"
          :row-key="getRowKeys"
          :data="tableData"
          @selection-change="handleSelectionChange"
          stripe
          :default-sort="{ prop: 'date', order: 'descending' }"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
          <el-table-column prop="courseCode" label="课程编号" width="" sortable align="center"></el-table-column>
          <el-table-column prop="courseName" label="课程名称" width="" sortable align="center"></el-table-column>
          <el-table-column prop="courseTypeRemark" label="课程类型" width="" sortable align="center"></el-table-column>
          <el-table-column prop="courseStageName" label="课程学段" width="" sortable align="center"></el-table-column>
          <el-table-column prop="textbookVersion" label="课程版本" width="" sortable align="center"></el-table-column>
          <el-table-column prop="courseTypeDetailRemark" label="题目类型" width="" sortable align="center"></el-table-column>
          <el-table-column prop="coverUrl" label="课程封面" width="" sortable>
            <template slot-scope="scope">
              <div class="demo-image__preview">
                <el-image v-if="scope.row.coverUrl" class="table_list_pic" :src="scope.row.coverUrl" @click="openImg(scope.row)"></el-image>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-row>
          <!-- 分页 -->
          <el-col :span="24" style="overflow-x: auto; text-align: right" :xs="24">
            <el-pagination
              style="margin-top: 20px"
              :current-page="tablePage.currentPage"
              :page-sizes="[10, 20, 30, 40, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tablePage.totalItems"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <NoMore></NoMore>
      </div>
    </el-card>

    <!-- 图片显示 -->
    <CustomDialog :value.sync="dialogOpenimg" width="380px" @close="handleClose" :before-close="handleClose">
      <div class="coverimg">
        <el-image class="table_list_pic" :src="coverImg"></el-image>
      </div>
    </CustomDialog>
  </div>
</template>

<script>
  import superReadCourseApi from '@/api/superReadCourseList';
  import enTypes from '@/api/bstatus';
  import { pageParamNames } from '@/utils/constants';
  import CustomDialog from '@/components/customDialog';
  import NoMore from '@/components/NoMore/index.vue';
  export default {
    data() {
      return {
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        submitOpenCourse: {
          studentCode: '',
          courseIdList: []
        },
        tableLoading: false,
        dataQuery: {
          courseCode: '',
          courseName: '',
          courseTypeDetail: '',
          courseStage: ''
        },
        tableData: [], //表格数据

        courseStageType: [], //课程学段类型
        courseTypeDetails: [], //课程类型

        multipleSelection: [], //多选数据
        studentCode: '',

        //打开大图
        coverImg: '',
        dialogOpenimg: false
      };
    },
    components: { CustomDialog, NoMore },
    created() {
      this.studentCode = this.$route.query.studentCode;
      this.fetchData();
      //获取学段下拉框
      this.getCourseStage();
      //获取课程类型下拉学段
      this.getCourseType();
    },
    methods: {
      //获取学段
      getCourseStage() {
        enTypes.getEnumerationAggregation('CJYDCourseStage').then((res) => {
          this.courseStageType = res.data;
        });
      },
      //获取题目类型
      getCourseType() {
        enTypes.getEnumerationAggregation('CJYDCourseTypeDetail').then((res) => {
          this.courseTypeDetails = res.data;
        });
      },
      getRowKeys(row) {
        return row.id;
      },
      // 多选的值
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },

      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },

      rest() {
        this.dataQuery = {
          courseCode: '',
          courseName: '',
          courseTypeDetail: '',
          courseStage: ''
        };
        this.fetchData01();
      },
      // 查询+搜索课程列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        that.dataQuery.pageNum = that.tablePage.currentPage;
        that.dataQuery.pageSize = that.tablePage.size;
        that.dataQuery.studentCode = that.studentCode;
        superReadCourseApi.getCanOpenCourseList(that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },

      openCourse() {
        this.submitOpenCourse = {
          studentCode: '',
          courseIdList: []
        };
        if (this.multipleSelection.length <= 0) {
          this.$message('请选择课程');
          return false;
        }
        this.submitOpenCourse.studentCode = this.studentCode;
        this.submitOpenCourse.merchantCode = window.localStorage.getItem('loginMerchantCode');
        for (let i = 0; i < this.multipleSelection.length; i++) {
          this.submitOpenCourse.courseIdList.push(this.multipleSelection[i].id);
        }
        this.$confirm('确定操作吗?', '开课', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          superReadCourseApi.openCourse(this.submitOpenCourse).then((res) => {
            this.submitOpenCourse = {
              studentCode: this.studentCode,
              courseIdList: [],
              merchantCode: window.localStorage.getItem('loginMerchantCode')
            };
            this.$refs.multipleTable.clearSelection();
            this.multipleSelection = [];
            this.fetchData01();
            this.$message.success('开课成功');
          });
        });
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },

      openImg(row) {
        this.coverImg = row.coverUrl;
        this.dialogOpenimg = true;
      },
      handleClose() {
        this.dialogOpenimg = false;
      }
    }
  };
</script>

<style>
  .btn-add {
    padding: 5px;
    margin-bottom: 15px;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .coverimg {
    text-align: center !important;
    padding: 50px;
  }
</style>
