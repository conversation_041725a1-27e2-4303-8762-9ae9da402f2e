/* DxTable 样式 - 基于 Element UI 表格样式 */

/* 复制 Element UI 的表格样式并重命名为 dx-table */
.dx-table {
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
  background-color: #fff;
  font-size: 14px;
  color: #606266;
  /* CSS 变量用于控制行高 */
  --dx-table-row-height: 48px;
}

.dx-table__empty-block {
  min-height: 60px;
  text-align: center;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dx-table__empty-text {
  line-height: 60px;
  width: 50%;
  color: #909399;
}

.dx-table__expand-column .cell {
  padding: 0;
  text-align: center;
}

.dx-table__expand-icon {
  position: relative;
  cursor: pointer;
  color: #666;
  font-size: 12px;
  transition: transform 0.2s ease-in-out;
  height: 20px;
}

.dx-table__expand-icon--expanded {
  transform: rotate(90deg);
}

.dx-table__expand-icon > .el-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -5px;
  margin-top: -5px;
}

.dx-table__expanded-cell {
  background-color: #fafafa;
}

.dx-table__expanded-cell[class*='cell'] {
  padding: 20px 50px;
}

.dx-table__expanded-cell:hover {
  background-color: transparent !important;
}

.dx-table__placeholder {
  display: inline-block;
  width: 20px;
}

.dx-table--fit {
  border-right: 0;
  border-bottom: 0;
}

.dx-table--fit .dx-table__cell.gutter {
  border-right-width: 1px;
}

.dx-table--scrollable-x .dx-table__body-wrapper {
  overflow-x: auto;
}

.dx-table--scrollable-y .dx-table__body-wrapper {
  overflow-y: auto;
}

.dx-table thead {
  color: #909399;
  font-weight: 500;
}

.dx-table thead.is-group th {
  background: #f5f7fa;
}

.dx-table th,
.dx-table td {
  min-height: var(--dx-table-row-height);
  padding: 0;
  min-width: 0;
  box-sizing: border-box;
  text-overflow: ellipsis;
  vertical-align: middle;
  position: relative;
  text-align: left;
}

.dx-table {
  table-layout: fixed;
}

.dx-table table {
  width: 100%;
  table-layout: fixed;
}

.dx-table th.is-leaf,
.dx-table td {
  border-bottom: 1px solid #ebeef5;
}

.dx-table th.is-sortable {
  cursor: pointer;
}

.dx-table th {
  overflow: hidden;
  user-select: none;
  background-color: #fafafa;
}

.dx-table th > .cell {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  position: relative;
  padding: 8px 10px;
  width: 100%;
  min-height: calc(var(--dx-table-row-height) - 16px);
  word-break: break-word;
  line-height: 1.4;
}

.dx-table td > .cell {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  position: relative;
  padding: 8px 10px;
  width: 100%;
  min-height: calc(var(--dx-table-row-height) - 16px);
  word-break: break-word;
  line-height: 1.4;
}

.dx-table--border {
  border: 1px solid #ebeef5;
}

.dx-table--border::after,
.dx-table__fixed-right::after,
.dx-table__fixed::after {
  content: '';
  position: absolute;
  background-color: #ebeef5;
  z-index: 1;
}

.dx-table--border::after {
  top: -1px;
  right: -1px;
  width: 1px;
  height: 100%;
}

.dx-table--border .dx-table__cell {
  border-right: 1px solid #ebeef5;
}

.dx-table__body-wrapper,
.dx-table__footer-wrapper,
.dx-table__header-wrapper {
  width: 100%;
}

.dx-table__footer-wrapper {
  margin-top: -1px;
}

.dx-table__footer-wrapper td {
  border-top: 1px solid #ebeef5;
}

.dx-table__body,
.dx-table__footer,
.dx-table__header {
  table-layout: fixed;
  border-collapse: separate;
}

.dx-table__header-wrapper {
  overflow: hidden;
}

.dx-table__header-wrapper th {
  height: 40px;
}

.dx-table__body-wrapper {
  overflow: hidden;
  position: relative;
}

.dx-table__row {
  background-color: #fff;
}

.dx-table__row:hover > td {
  background-color: #f5f7fa;
}

.dx-table__row--striped {
  background: #fafafa;
}

.dx-table__row--striped:hover > td {
  background-color: #f5f7fa;
}

.dx-table__fixed,
.dx-table__fixed-right {
  position: absolute;
  top: 0;
  left: 0;
  overflow-x: hidden;
  overflow-y: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
}

.dx-table__fixed-right {
  top: 0;
  left: auto;
  right: 0;
}

.dx-table__fixed-right::after {
  top: 0;
  left: -1px;
  width: 1px;
  height: 100%;
}

.dx-table__fixed::after {
  top: 0;
  right: -1px;
  width: 1px;
  height: 100%;
}

.dx-table__fixed-body-wrapper,
.dx-table__fixed-footer-wrapper,
.dx-table__fixed-header-wrapper {
  position: absolute;
  left: 0;
  width: 100%;
  overflow: hidden;
}

.dx-table__fixed-header-wrapper {
  top: 0;
  z-index: 3;
}

.dx-table__fixed-body-wrapper {
  top: 40px;
  z-index: 3;
}

.dx-table__fixed-footer-wrapper {
  bottom: 0;
  z-index: 3;
}

.dx-table__fixed-right-patch {
  position: absolute;
  top: -1px;
  right: 0;
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

.dx-table__column-resize-proxy {
  position: absolute;
  left: 200px;
  top: 0;
  bottom: 0;
  width: 0;
  border-left: 1px solid #ebeef5;
  z-index: 10;
}

.dx-table__column-filter-trigger {
  display: inline-block;
  line-height: 34px;
  cursor: pointer;
}

.dx-table__column-filter-trigger i {
  color: #909399;
  font-size: 12px;
  transform: scale(0.75);
}

.dx-table--enable-row-transition .dx-table__body td {
  transition: background-color 0.25s ease;
}

.dx-table--enable-row-hover .dx-table__body tr:hover > td {
  background-color: #f5f7fa;
}

.dx-table--fluid-height .dx-table__fixed,
.dx-table--fluid-height .dx-table__fixed-right {
  bottom: 0;
  overflow: hidden;
}

.dx-table [class*='dx-table__row--level'] .dx-table__expand-icon {
  display: inline-block;
  width: 20px;
  line-height: 20px;
  height: 20px;
  text-align: center;
  margin-right: 3px;
}
