// 采购管理-发货管理相关接口
import request from '@/utils/request';

export const queryPriceInfoList = (data) => {
    return request({
        url: '/znyy/purchase/config/queryPriceInfo',
        method: 'GET',
        params: data
    })
}
export const queryCountInfo = (data) => {
    return request({
        url: '/znyy/purchase/config/queryCountInfo',
        method: 'GET',
        params: data
    })
}
export const updateCount = (data) => {
    return request({
        url: '/znyy/purchase/config/updateCount',
        method: 'GET',
        params: data
    })
}
export const updatePrice = (data) => {
    return request({
        url: '/znyy/purchase/config/updatePrice',
        method: 'GET',
        params: data
    })
}