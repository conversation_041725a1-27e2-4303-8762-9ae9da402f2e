// 课程管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/course',
  component: Layout,
  redirect: '/course/courseCategoryList',
  meta: {
    perm: 'm:course',
    title: '课程管理',
    icon: 'course'
  },
  children: [
    {
      path: 'courseList',
      component: _import('course/courseList'),
      name: 'courseList',
      meta: {
        perm: 'm:course:courseList',
        title: '课程列表',
        icon: 'course'
      }
    }
  ]
};
