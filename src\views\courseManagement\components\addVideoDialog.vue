<template>
  <el-dialog title="添加视频" :visible.sync="videoDialogVisible" width="60%" :close-on-click-modal="false" @close="handleClose" center custom-class="video-dialog">
    <div class="search-container">
      <div class="search-input">
        <el-input v-model.trim="videoName" placeholder="请输入视频名称搜索" size="small"></el-input>
      </div>
      <div class="search-btns">
        <el-button size="small" @click="resetSearch">重置</el-button>
        <el-button size="small" type="primary" @click="handleSearch">搜索</el-button>
      </div>
    </div>
    <el-table ref="table" :data="videoList" @select="handleSelectionChange" @select-all="handleSelectAll" size="small" style="width: 100%" v-loading="tableLoading" row-key="id">
      <el-table-column type="selection" width="60" align="center" :reserve-selection="true"></el-table-column>
      <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
      <el-table-column prop="id" label="视频ID" width="" align="center"></el-table-column>
      <el-table-column prop="videoName" label="视频名称" align="center"></el-table-column>
      <el-table-column prop="courseVersionNodeName" label="版本" align="center"></el-table-column>
      <el-table-column prop="coursePeriodNodeName" label="学段" align="center"></el-table-column>
    </el-table>
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageQuery.pageNum"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageQuery.pageSize"
        layout="prev, pager, next, jumper"
        :total="total"
        small
      ></el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleClose">取消</el-button>
      <el-button size="small" type="primary" @click="confirmSelection">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import courseManagementAPI from '@/api/mathApi/courseManagementAPI';
export default {
  data() {
    return {
      tableLoading: false,
      videoDialogVisible: false,
      videoName: '',
      videoList: [],
      courseNodeData: {},
      total: 0,
      selectedVideos: [],
      isAllSelected: false,
      pageQuery: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  props: {
    selectedVideoList: {
      type: Array,
      default: () => {}
    }
  },
  watch: {
    selectedVideoList: {
      handler(newVal) {
        // 深度监听selectedVideoList的变化
        this.selectedVideos = newVal.map((item) => ({ ...item }));
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.courseNodeData = JSON.parse(sessionStorage.getItem('courseNodeData'));
    // console.log('🚀 ~ created ~ courseNodeData:', courseNodeData);
  },
  methods: {
    open() {
      // 重置所有状态
      this.videoDialogVisible = true;
      this.selectedVideos = [];
      this.$nextTick(() => {
        // 初始化选中状态
        if (this.selectedVideoList && this.selectedVideoList.length > 0) {
          this.selectedVideos = JSON.parse(JSON.stringify(this.selectedVideoList));
        }
        this.handleSearch();
      });
    },
    // 处理关闭
    handleClose() {
      this.selectedVideos = this.selectedVideoList.map((item) => item);
      this.resetSearch();
      this.videoDialogVisible = false;
    },

    // 处理搜索
    handleSearch() {
      this.pageQuery.pageNum = 1;
      this.getVideoList();
    },

    // 重置搜索
    resetSearch() {
      this.videoName = '';
      // 重置清空勾选---目前不清空
      // this.selectedVideos = [];
      this.isAllSelected = false;
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }
      this.handleSearch();
    },

    // 处理全选
    handleSelectAll(selection) {
      this.isAllSelected = selection.length > 0;
      if (selection.length + this.selectedVideos.length - this.getSelectedCountInCurrentPage() > 15) {
        this.$message.warning('选择的视频总数不能超过15个');
        this.$refs.table.clearSelection();
        this.$nextTick(() => {
          this.selectedVideos.forEach((video) => {
            if (this.videoList.some((v) => v.id === video.id)) {
              this.$refs.table.toggleRowSelection(video, true);
            }
          });
        });
        return;
      }

      if (selection.length > 0) {
        selection.forEach((item) => {
          if (!this.selectedVideos.some((v) => v.id === item.id)) {
            this.selectedVideos.push(item);
          }
        });
      } else {
        this.selectedVideos = this.selectedVideos.filter((item) => !this.videoList.some((v) => v.id === item.id));
      }
    },

    // 处理选择变化
    handleSelectionChange(val, row) {
      console.log('🚀 ~ t处理选择变化:', this.selectedVideos.length);

      const status = val.some((v) => v.id === row.id);
      // 选中操作
      if (status) {
        // 先检查是否会超过最大限制
        if (this.selectedVideos.length >= 15) {
          this.$message.warning('最多只能选择15个视频');
          this.$refs.table.toggleRowSelection(row, false);
          return false;
        }

        if (!this.selectedVideos.some((v) => v.id === row.id || v.videoId === row.id)) {
          const newRow = JSON.parse(JSON.stringify(row));
          this.selectedVideos.push(newRow);
        }
      } else {
        // 取消选中操作，同时考虑id和videoId
        this.selectedVideos = this.selectedVideos.filter((v) => {
          return !(v.id === row.id || v.videoId === row.id || v.id === row.videoId);
        });
      }
    },

    // 获取当前页面选中的数量
    getSelectedCountInCurrentPage() {
      return this.selectedVideos.filter((selected) => this.videoList.some((item) => item.id === selected.id)).length;
    },

    // 获取其他页面选中的数量
    getSelectedCountInOtherPages() {
      return this.selectedVideos.filter((selected) => !this.videoList.some((item) => item.id === selected.id)).length;
    },

    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.getVideoList();
    },

    // 处理当前页变化
    handleCurrentChange(val) {
      this.pageQuery.pageNum = val;
      // 保存当前的选择状态
      const currentSelected = JSON.parse(JSON.stringify(this.selectedVideos));
      this.getVideoList();
      // 恢复选择状态
      this.selectedVideos = currentSelected;
      this.$nextTick(() => {
        this.updateTableSelection();
      });
    },

    // 更新表格选择状态
    updateTableSelection() {
      if (!this.$refs.table) return;
      this.$refs.table.clearSelection();
      this.$nextTick(() => {
        this.videoList.forEach((row) => {
          // 检查当前行是否在已选择的列表中
          const isSelected = this.selectedVideos.some((v) => v.id === row.id || v.videoId === row.id || v.id === row.videoId);
          if (isSelected) {
            this.$refs.table.toggleRowSelection(row, true);
          }
        });
      });
    },

    // 确认选择
    confirmSelection() {
      let arr = JSON.parse(JSON.stringify(this.selectedVideos));
      console.log(arr, '0000000000000000000000000');
      if (this.selectedVideos.length === 0) {
        this.$message.warning('请选择视频');
        return;
      }
      if (this.selectedVideos.length > 15) {
        this.$message.warning('最多只能选择15个视频');
        return;
      }
      // 发送选择的数据给父组件
      this.$emit('confirm', arr);
      // return;
      this.videoDialogVisible = false;
    },

    // 获取视频列表
    getVideoList() {
      this.tableLoading = true;
      courseManagementAPI
        .getVideoListAPI({
          pageNum: this.pageQuery.pageNum,
          pageSize: this.pageQuery.pageSize,
          videoName: this.videoName,
          coursePeriodNodeId: this.courseNodeData.coursePeriodNodeId,
          courseVersionNodeId: this.courseNodeData.courseVersionNodeId
        })
        .then((res) => {
          console.log(res);
          this.videoList = res.data.data;
          this.total = +res.data.totalItems;
          this.tableLoading = false;
          this.$nextTick(() => {
            this.updateTableSelection();
          });
        })
        .finally(() => {
          this.tableLoading = false;
        });
    }
  },
  mounted() {
    // 初始化加载视频列表
    this.getVideoList();
  }
};
</script>

<style lang="less" scoped>
.video-dialog {
  :deep(.el-dialog__header) {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    text-align: center;
  }

  :deep(.el-dialog__title) {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  :deep(.el-dialog--center .el-dialog__body) {
    padding: 10px 25px 0 !important;
    border-top: 1px solid #cecece;
  }

  :deep(.el-dialog__footer) {
    padding: 10px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.search-container {
  padding: 0 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
}

.search-input {
  width: 220px;
}

.search-btns {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
  padding: 10px 0;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-pagination) {
  font-size: 13px;
}

:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-button--small) {
  padding: 8px 15px;
}
</style>
