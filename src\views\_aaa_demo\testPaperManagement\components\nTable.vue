<template>
  <div>
    <el-table ref="multipleTable" @selection-change="handleSelectionChange" v-bind="$attrs" v-on="$listeners" style="width: 100%" height="300px" :border="consData.border">
      <el-table-column :show-overflow-tooltip="item.type" :type="item.prop" :prop="item.prop" :label="item.label" v-for="(item, index) in nameList" :key="index">
        <template v-slot="scope" v-if="slotNames.includes(item.prop)">
          <slot :name="item.prop" v-bind="scope"></slot>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="pageSize" style="display: flex; justify-content: end; margin-top: 20px">
      <el-pagination
        :pageSize="pageSize"
        :pager-count="11"
        layout="prev, pager, next"
        :total="total"
        :hide-on-single-page="true"
        @current-change="currentChange"
        @size-change="sizeChange"
      ></el-pagination>

      <!-- @next-click="nextClick"
        @prev-click="prevClick" -->
    </div>
  </div>
</template>

<script>
  //自己封装的table组件，支持接受原组件的所有属性
  export default {
    name: 'nTable',
    props: {
      bindingIds: {},
      total: {
        type: Number,
        default: () => {
          return 0;
        }
      },
      pageSize: {
        type: Number || String,
        default: () => {
          return null;
        }
      },
      consData: {
        type: Object,
        default: () => {
          return {
            border: true
          };
        }
      },
      nameList: {
        type: Array,
        default: () => {
          return [
            { label: '序号', prop: 'index' },
            { label: 'ID', prop: 'id' },
            { label: '题干', prop: 'tigan' },
            { label: '关联知识点', prop: 'zhishdi' },
            { label: '题目分值', prop: 'questionGrade', slot: true },
            { label: '操作', prop: 'scope', slot: true }
          ];
        }
      },
      tableData: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    computed: {
      // 计算所有插槽名称
      slotNames() {
        console.log(Object.keys(this.$scopedSlots).filter((slot) => slot !== 'default'));
        return Object.keys(this.$scopedSlots).filter((slot) => slot !== 'default');
      }
    },
    watch: {
      tableData: {
        handler(newVal) {
          console.log('%cnewVal : ', 'color:#fff;background:#000;font-size: 24px;border: 2px solid purple; padding: 5px', newVal);
          if (newVal.length > 0) {
            this.init();
          }
        },
        immediate: true,
        deep: true
      }
    },
    data() {
      return {
        // 当前页选中的
        currentPageSelection: [],
        // 所有数据（不包括当前页的)
        multipleSelection: []
      };
    },
    methods: {
      // toggleRowSelection(e) {
      //   return this.$refs.multipleTable.toggleRowSelection(e);
      // },
      init() {
        // this.toggleSelection(this.currentPageSelection);
        this.$nextTick(() => {
          // setTimeout(() => {
          this.changeStatusBtn();
          // }, 1000);
        });
      },
      // 切换保持选中状态
      changeStatusBtn() {
        // 回显当前页已选中的项目，并从所有数据中移除
        if (this.tableData.length > 0) {
          console.log(this.multipleSelection, '-----');
          const selectedIds = new Set(this.multipleSelection.map((item) => item.id));
          this.tableData.forEach((data) => {
            const isSelected = selectedIds.has(data.id);
            console.log('%cisSelected : ', 'color:#fff;background:#000;font-size: 24px;border: 2px solid purple; padding: 5px', isSelected);
            this.$refs.multipleTable.toggleRowSelection(data, isSelected);
          });

          // 过滤掉已选中的项目，保留未选中的
          this.multipleSelection = this.multipleSelection.filter((item) => !this.tableData.some((data) => data.id === item.id));
          // console.log('%cmultipleSelection : ', 'color:#fff;background:#000;font-size: 24px;border: 2px solid purple; padding: 5px', this.multipleSelection, this.tableData);
        } else {
          // console.log('%c[] : ', 'color:#fff;background:#000;font-size: 24px;border: 2px solid purple; padding: 5px', []);
        }
        console.log(this.multipleSelection, '-------137');
      },
      handleSelectionChange(val, e) {
        if (val.length > 0) {
          let cuuerJons = JSON.parse(JSON.stringify(this.currentPageSelection));
          this.currentPageSelection = val;
          console.log(cuuerJons.length, this.currentPageSelection.length);
        } else {
          this.currentPageSelection = [];
        }
        this.bindingIds.forEach((i) => {
          this.currentPageSelection.forEach((e) => {
            if (i.id == e.id) {
              e.cpId = i.cpId;
              e.questionGrade = i.questionGrade;
            }
          });
        });
        console.log(this.bindingIds, this.currentPageSelection, '---105');
      },
      // 添加并排除重复数据
      /**
       *
       * @param array 添加到
       * @param dataList 要添加的数据
       */
      addUniqueData(multipleSelection) {
        if (multipleSelection) return (this.multipleSelection = JSON.parse(JSON.stringify(multipleSelection)));
        if (this.currentPageSelection.length <= 0) return this.multipleSelection;
        console.log(this.currentPageSelection, this.multipleSelection, '----------1832');
        if (this.multipleSelection.length) {
          let mData = JSON.parse(JSON.stringify(this.multipleSelection));
          this.currentPageSelection.forEach((data) => {
            if (this.multipleSelection) {
              if (!mData.some((i) => i.id === data.id)) {
                mData.push(data);
              }
            }
          });
          if (mData.length <= 50) {
            this.multipleSelection = mData;
          } else {
            return false;
          }
        } else {
          let mData = [];
          this.currentPageSelection.forEach((data) => {
            mData.push(data);
          });
          if (mData.length <= 50) {
            this.multipleSelection = mData;
          } else {
            return false;
          }
        }
        console.log(this.multipleSelection, '----------1833');

        return this.multipleSelection;
      },
      currentChange(val, n) {
        this.addUniqueData();
        console.log(val, n, 'currentChange');
        this.$emit('changPage', val);
      },
      sizeChange(val, n) {
        this.addUniqueData();
        console.log(val, n, 'sizeChange');
        this.$emit('changPage', val);
      }
    }
  };
</script>
