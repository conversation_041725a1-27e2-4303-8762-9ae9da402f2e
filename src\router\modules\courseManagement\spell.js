// 拼读超人-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/spell',
  component: Layout,
  redirect: '/spell/courseDictationList',
  meta: {
    perm: 'm:spell',
    title: '拼读超人',
    icon: 'spell'
  },
  children: [
    {
      path: 'courseDictationList',
      component: () => import('@/views/course/courseDictation/courseDictationList'),
      name: 'courseDictationList',
      meta: {
        perm: 'm:course:courseDictationList',
        title: '自然拼写课程列表'
      }
    },
    {
      path: 'additionCourseList',
      component: () => import('@/views/course/courseDictation/additionQuestion/additionCourseList'),
      name: 'additionCourseList',
      meta: {
        perm: 'm:course:additionCourseList',
        title: '拼音法课程列表'
      }
    },
    {
      path: 'dictationWordLevel',
      component: () => import('@/views/course/dictationWordLevel'),
      name: 'dictationWordLevel',
      meta: {
        perm: 'm:course:dictationWordLevel',
        title: '听写单词水平列表'
      }
    },
    {
      path: 'dictationWordType',
      component: () => import('@/views/course/dictationWordType'),
      name: 'dictationWordType',
      meta: {
        perm: 'm:course:dictationWordType',
        title: '听写单词类型列表'
      }
    },
    {
      path: 'dictationWordTest',
      component: () => import('@/views/course/dictationWordTest'),
      name: 'dictationWordTest',
      meta: {
        perm: 'm:course:dictationWordTest',
        title: '听写单词测试题库'
      }
    },
    {
      path: 'SpellStrongThesaurus',
      component: () => import('@/views/course/courseDictation/SpellStrongThesaurus'),
      name: 'SpellStrongThesaurus',
      meta: {
        perm: 'm:course:SpellStrongThesaurus',
        title: '拼读强基词库列表'
      }
    },
    {
      path: 'makeCourseDictationList',
      hidden: true,
      component: () => import('@/views/course/courseDictation/makeCourseList/makeCourseDictationList'),
      name: 'makeCourseDictationList',
      meta: {
        // perm: "m:course:courseDictationList:make",
        title: '制作课程',
        icon: 'course_category'
      }
    },
    {
      path: 'makeAdditionCourseList',
      hidden: true,
      component: () => import('@/views/course/courseDictation/makeCourseList/makeAdditionCourseList'),
      name: 'makeAdditionCourseList',
      meta: {
        perm: 'm:course:makeAdditionCourseList',
        title: '制作课程',
        icon: 'course_category'
      }
    }
  ]
};
