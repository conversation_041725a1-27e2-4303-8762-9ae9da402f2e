<template>
  <div>
    <el-card class="process-card">
      <div slot="header" class="card-header">
        <div class="card-title">
          <i class="el-icon-s-grid" style="color: #bbbbbb"></i>
          <span style="margin-left: 6px">学后测试</span>
        </div>
        <el-button type="text" class="delete-button" icon="el-icon-delete" @click="$emit('delete')"></el-button>
      </div>
      <el-form ref="answerQuestionForm1" :model="formData" label-width="100px" :rules="rules" class="process-form-content">
        <p>答题阶段:（助教可在课上知识点范围内选择知识点出题）</p>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="排序:" prop="sortsNum">
              <el-input-number v-model="formData.sortsNum" :min="1" :max="9999999999"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="9">
            <el-form-item label="阶段时长:" prop="stageDuration">
              <el-select v-model="formData.stageDuration" placeholder="请选择阶段时长">
                <el-option v-for="i in 60" :key="i" :label="i" :value="i"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="阶段名称:" prop="stageName">
              <el-input v-model="formData.stageName" placeholder="请输入阶段名称" maxlength="10" show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="阶段提醒:" prop="stageRemind">
              <el-radio-group v-model="formData.stageRemind">
                <el-radio label="0">阶段结束时需要弹框提醒</el-radio>
                <el-radio label="1">阶段结束时不作提醒</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="知识点范围:" prop="knowledgeScope">
              <div class="checkbox-group-horizontal">
                <el-checkbox :value="formData.knowledgeScope.includes('0')" @change="(val) => handleKnowledgeScopeChange(val, '0')" :disabled="true">
                  根据课程内视频关联知识点生成题目
                </el-checkbox>
                <el-checkbox :value="formData.knowledgeScope.includes('1')" @change="(val) => handleKnowledgeScopeChange(val, '1')" :disabled="true">
                  根据课程关联知识点生成题目
                </el-checkbox>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="做题PK:" prop="isPk">
              <el-radio v-model="formData.isPk" label="0">做题过程中不进行PK</el-radio>
              <el-radio v-model="formData.isPk" label="1">做题过程中两两PK</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="出题方式:" prop="questionGroupType">
              <el-radio v-model="formData.questionGroupType" disabled :label="1">按规则出题</el-radio>
              <el-radio v-model="formData.questionGroupType" disabled :label="2">选择固定题目</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="question-section" v-if="formData.questionGroupType == 1">
              <div class="question-type-list">
                <!-- 使用循环渲染各题型 -->
                <div
                  v-for="(type, index) in formData.processQuestionTypeCoList"
                  :key="index"
                  class="question-type-item"
                  :class="{ 'active-item': activeItem == type.questionType }"
                >
                  <div class="question-type-header">
                    <i class="el-icon-s-grid" style="color: #bbbbbb"></i>
                    <span class="question-type-title">{{ getQuestionTypeTitle(type.questionType) }}：</span>
                    <el-button type="text" class="delete-btn question-delete" @click="removeQuestionType(type.questionType)">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </div>
                  <div class="question-config">
                    <div class="question-config-item">
                      <span class="question-label required">排序：</span>
                      <el-input-number
                        v-model="type.sortsNum"
                        :min="1"
                        :max="9999999999"
                        size="small"
                        :controls="false"
                        class="custom-input-number"
                        placeholder="请输入序号"
                      ></el-input-number>
                    </div>
                    <div class="question-config-item">
                      <span class="question-label required">题目数量：</span>
                      <el-input-number
                        v-model="type.questionNum"
                        :min="1"
                        :max="9999999999"
                        size="small"
                        :controls="false"
                        class="custom-input-number"
                        placeholder="请输入题目数量"
                      ></el-input-number>
                    </div>
                    <div class="question-config-item">
                      <span class="question-label required">每题分值：</span>
                      <el-input-number
                        :min="0"
                        :max="99999"
                        :step="0.5"
                        step-strictly
                        v-model="type.questionGrade"
                        size="small"
                        :controls="false"
                        class="custom-input-number"
                        placeholder="请输入每题分值"
                      ></el-input-number>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="formData.questionGroupType == 2">
              <el-form-item>
                <div>*已添加{{ filteredSelection.length }}题，最多添加50题</div>
                <Table :data="filteredSelection" :nameList="nameList">
                  <template v-slot:scope="row">
                    <!-- <el-button size="mini">编辑{{ row.row.id }}</el-button> -->
                    <el-button @click="deleteMultip(row.row)" type="danger" size="mini">删除</el-button>
                  </template>
                  <template v-slot:questionGrade="row">
                    <el-input v-model="row.row.questionGrade" type="number" :max="999" oninput="value=value.replace(/[^\d]/g, '').slice(0,3);" placeholder="请输入内容"></el-input>
                  </template>
                  <template v-slot:questionType="scope">
                    <div>
                      <div class="ellipsis">{{ questType[scope.row.questionType] || '-' }}</div>
                    </div>
                  </template>
                  <template v-slot:questionDifficulty="scope">
                    <div>
                      <div class="ellipsis">{{ questDiff[scope.row.questionDifficulty] || '-' }}</div>
                    </div>
                  </template>
                  <template v-slot:knowledgeNameList="scope">
                    <div>
                      <div class="ellipsis" v-for="i in scope.row.knowledgeNameList" :key="i">
                        {{ i || '-' }}
                      </div>
                    </div>
                  </template>
                  <template v-slot:questionText="row">
                    <div>
                      <mathTest :formulaText="row.row.questionText" />
                      <!-- <div class="ellipsis" :title="row.row.questionText">{{ row.row.questionText || '-' }}</div> -->
                    </div>
                  </template>
                </Table>
              </el-form-item>
            </div>
            <div class="add-question-btn-container">
              <el-button class="add-question-btn" @click="showAddQuestionDialog">添加题型</el-button>
            </div>
          </el-col>
        </el-row>
        <el-row style="margin-top: 20px">
          <p>解答阶段：</p>
          <el-row :gutter="20">
            <el-col :span="9">
              <el-form-item label="阶段时长:" prop="solveStageDuration">
                <el-select v-model="formData.solveStageDuration" placeholder="请选择阶段时长">
                  <el-option v-for="i in 60" :key="i" :label="i" :value="i"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="阶段名称:" prop="solveStageName">
                <el-input v-model="formData.solveStageName" placeholder="请填写阶段名称" maxlength="10" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="阶段提醒:" prop="solveStageRemind">
                <el-radio-group v-model="formData.solveStageRemind">
                  <el-radio label="0">阶段结束时需要弹框提醒</el-radio>
                  <el-radio label="1">阶段结束时不作提醒</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
      </el-form>
    </el-card>

    <!-- 添加题型弹窗 -->
    <el-dialog title="添加题型" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false" :show-close="true" @close="handleDialogClose" append-to-body>
      <el-form ref="addQuestionForm" :model="addQuestionForm" label-width="80px" class="process-form">
        <el-form-item label="题型:" prop="questionType">
          <el-select v-model="questionTypes" multiple placeholder="请选择题型" style="width: 100%">
            <el-option v-for="item in questionTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddQuestion">取消</el-button>
        <el-button type="primary" @click="addQuestionType">保存</el-button>
      </div>
    </el-dialog>
    <NewQuestionAdded ref="newQuestionAdded" @confirm="confirmNewQuestionAdded" :curriculumId="curriculumId" :knowledgeIds="knowledgeIds" />
  </div>
</template>

<script>
  import mathTest from '@/views/_aaa_demo/testPaperManagement/components/mathTest.vue';
  import minx from '@/views/courseManagement/minx.js';
  import NewQuestionAdded from '@/views/_aaa_demo/testPaperManagement/components/newQuestionAdded.vue';
  import Table from '@/views/_aaa_demo/testPaperManagement/components/nTable.vue';
  export default {
    mixins: [minx],
    name: 'afterText',
    components: { Table, NewQuestionAdded, mathTest },
    data() {
      return {
        dialogVisible: false,
        activeItem: '',
        questionTypes: [],
        // 题型对应的显示标题
        displayTitles: {
          0: '单选题',
          1: '填空题',
          2: '计算题',
          3: '解方程',
          4: '证明题',
          5: '几何综合题'
        },
        rules: {
          questionGroupType: [{ required: true, message: '请选择出题方式', trigger: 'change' }],
          stageDuration: [{ required: true, message: '请选择阶段时长', trigger: 'change' }],
          stageName: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],
          stageRemind: [{ required: true, message: '请选择阶段提醒方式', trigger: 'change' }],
          isPk: [{ required: true, message: '请选择做题PK', trigger: 'blur' }],
          knowledgeScope: [{ required: true, message: '请选择知识点范围', trigger: 'change' }],
          solveStageDuration: [{ required: true, message: '请选择阶段时长', trigger: 'change' }],
          solveStageName: [{ required: true, message: '请填写阶段名称', trigger: 'blur' }],
          solveStageRemind: [{ required: true, message: '请选择阶段提醒方式', trigger: 'change' }]
        },
        addQuestionForm: {
          questionType: ''
        },
        questionTypeOptions: [
          { label: '单选题', value: '0' },
          { label: '填空题', value: '1' },
          { label: '计算题', value: '2' },
          { label: '解方程', value: '3' },
          { label: '证明题', value: '4' },
          { label: '几何综合题', value: '5' }
        ]
      };
    },
    props: {
      courseBaseMessageId: {},
      curriculumId: {},
      knowledgeIds: {
        type: Array,
        default: () => {
          return [];
        }
      },
      // index: {
      //   type: Number,
      //   required: true
      // },
      afterSetQuestion: {
        type: Number,
        default: () => {
          return 1;
        }
      },
      formData: {
        type: Object,
        default: () => ({
          questionGroupType: 1,
          processType: 5,
          sortsNum: 1,
          stageDuration: '',
          stageName: '',
          isPk: null,
          knowledgeScope: '',
          questionType: [], // 当前已添加的题型
          solveStageDuration: '',
          solveStageName: '',
          solveStageRemind: '1',
          stageRemind: '1',
          processQuestionTypeCoList: []
        })
      }
    },
    watch: {
      formData: {
        handler(newVal) {
          console.log('formData---', newVal);
          // 当表单数据变化时，通知父组件，只传递必要的数据
          this.$emit('update', newVal);
        },
        deep: true,
        immediate: true
      }
    },
    methods: {
      afterSetQuestionChange(v) {
        this.formData.questionGroupType = v;
      },
      showAddQuestionDialog() {
        if (this.formData.questionGroupType == 1) {
          this.dialogVisible = true;
          //丢失找回部分
          this.addQuestionForm.questionType = '';
          //end
          // 回显已添加的题型
          this.questionTypes = this.formData.processQuestionTypeCoList.map((item) => item.questionType);
          // 延迟重置表单，确保DOM已更新
          this.$nextTick(() => {
            if (this.$refs.addQuestionForm) {
              this.$refs.addQuestionForm.resetFields();
              this.$refs.addQuestionForm.clearValidate();
            }
          });
        } else if (this.formData.questionGroupType == 2) {
          console.log(this.knowledgeIds);
          this.addTopic();
        }
      },
      handleDialogClose() {
        this.addQuestionForm.questionType = '';
        if (this.$refs.addQuestionForm) {
          this.$refs.addQuestionForm.resetFields();
          this.$refs.addQuestionForm.clearValidate();
        }
      },
      cancelAddQuestion() {
        this.dialogVisible = false;
        this.addQuestionForm.questionType = '';
        if (this.$refs.addQuestionForm) {
          this.$refs.addQuestionForm.resetFields();
          this.$refs.addQuestionForm.clearValidate();
        }
      },
      addQuestionType() {
        const valid = this.questionTypes.length > 0;
        if (valid) {
          // 丢失找回部分
          let arr = this.questionTypes.map((i, idx) => {
            const newQuestionType = {
              questionType: i,
              questionNum: 0,
              questionGrade: 0,
              questionStageType: '1',
              sortsNum: idx + 1
            };
            return newQuestionType;
          });
          console.log(arr, '=====================');
          if (arr.length > 0) {
            this.formData.processQuestionTypeCoList = JSON.parse(JSON.stringify(arr));
          } else {
            this.formData.processQuestionTypeCoList = [];
          }
          //end
          const existingTypes = this.formData.processQuestionTypeCoList.map((item) => item.questionType);
          const newTypes = this.questionTypes.filter((type) => !existingTypes.includes(type));

          // 获取当前最大的序号进行排列
          const maxSortNum = this.formData.processQuestionTypeCoList.length > 0 ? Math.max(...this.formData.processQuestionTypeCoList.map((item) => item.sortsNum)) : 0;
          const newQuestionTypes = newTypes.map((type, idx) => ({
            questionType: type,
            questionNum: 0,
            questionGrade: 0,
            questionStageType: '1',
            sortsNum: maxSortNum + idx + 1
          }));
          this.formData.processQuestionTypeCoList = [...this.formData.processQuestionTypeCoList, ...newQuestionTypes];
          this.dialogVisible = false;
        } else {
          this.$message.warning('请选择题型');
        }
      },
      removeQuestionType(type) {
        this.questionTypes = this.questionTypes.filter((item) => item !== type);
        this.formData.processQuestionTypeCoList = this.formData.processQuestionTypeCoList.filter((item) => item.questionType !== type);
      },
      getQuestionTypeTitle(type) {
        return this.displayTitles[type] || '未知题型';
      },
      // 获取传递给父组件的数据
      getData() {
        // 去除多余参数
        const { questionType, questions, ...rest } = this.formData;
        return rest;
      },
      validate() {
        console.log(1230000000000000000000000);
        console.log(this.formData.isPk, '----------------ispk');

        return new Promise((resolve, reject) => {
          if (this.filteredSelection.length > 0) {
            const allQuestionGradesPresent = this.filteredSelection.every((item) => item.questionGrade != undefined && item.questionGrade != null);
            const anyQuestionGradesZero = this.filteredSelection.some((item) => item.questionGrade == 0);
            console.log(allQuestionGradesPresent);
            console.log(anyQuestionGradesZero);
            if (anyQuestionGradesZero) return reject(new Error('添加的题目分数不能为0！！！'));
            if (!allQuestionGradesPresent) return reject(new Error('添加的题目分数不能为空！！！'));
            // const allQuestionGradesPresent = this.multipleSelection.every((item) => {
            //   return item.questionGrade != undefined && item.questionGrade != null && item.questionGrade != 0 && item.questionGrade != '';
            // });
            // console.log(allQuestionGradesPresent, '----------------allQuestionGradesPresent');
          }
          this.$refs.answerQuestionForm1.validate((valid) => {
            if (valid) {
              this.formData.courseProcessManualQuestionCoList = this.multipleSelection.map((item) => {
                let obj = {
                  questionBankId: item.id,
                  stageType: 3,
                  questionGrade: item.questionGrade
                };
                if (item.cpId) {
                  obj['id'] = item.cpId;
                  obj['courseProcessConfigId'] = this.formData.id;
                  obj['courseBaseMessageId'] = this.courseBaseMessageId;
                }
                return obj;
              });
              console.log(this.formData.courseProcessManualQuestionCoList);
              resolve(true);
            } else {
              reject(new Error('表单验证失败'));
            }
          });
        });
      },
      // 处理知识点范围 1 和 2 的切换
      handleKnowledgeScopeChange(checked, value) {
        console.log(checked, value);

        let currentValues = this.formData.knowledgeScope ? this.formData.knowledgeScope.split(',') : [];
        if (checked) {
          if (!currentValues.includes(value)) {
            currentValues.push(value);
          }
        } else {
          currentValues = currentValues.filter((v) => v !== value);
        }
        this.formData.knowledgeScope = currentValues.join(',');
      }
    }
  };
</script>

<style scoped>
  .process-card {
    margin-top: 10px;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .card-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }

  .delete-button {
    padding: 0;
    font-size: 20px;
    color: #2a2a3b;
  }

  .process-form-content {
    padding: 0 20px 0 0;
  }

  ::v-deep .el-card__header {
    padding: 5px 20px;
  }

  .process-card ::v-deep .el-form-item {
    margin-bottom: 12px;
  }

  .process-card ::v-deep .el-select {
    width: 100%;
  }

  .checkbox-group {
    display: flex;
    flex-direction: column;
  }

  .checkbox-group-horizontal {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 30px;
  }

  .checkbox-group .el-checkbox {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .question-section {
    /* padding-left: 100px;
    margin-top: -10px; */
  }

  .section-title {
    font-weight: bold;
    color: #606266;
  }

  .question-type-list {
    margin-bottom: 10px;
  }

  .question-type-item {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 5px 10px;
    margin-bottom: 10px;
    background-color: #fff;
  }

  .active-item {
    border: 1px solid #409eff;
  }

  .question-type-header {
    display: flex;
    align-items: center;
    padding: 5px 0;
  }

  .dots {
    color: #909399;
    margin-right: 5px;
    font-weight: bold;
  }

  .question-type-title {
    flex: 1;
    font-weight: bold;
  }

  .delete-btn {
    color: #606266;
    padding: 0;
  }

  .delete-btn i {
    font-size: 18px;
  }

  .question-config {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    padding: 5px 0;
  }

  .question-config-item {
    display: flex;
    align-items: center;
    width: 45%;
  }

  .question-label {
    width: 130px;
    text-align: right;
    padding-right: 10px;
  }

  .question-label.required::before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }

  .add-question-btn-container {
    display: flex;
    justify-content: center;
    margin-top: 15px;
  }

  .add-question-btn {
    width: 100%;
    background-color: #f0f9ff;
    color: #409eff;
    border: 1px dashed #409eff;
  }

  ::v-deep .el-radio {
    margin-right: 20px;
    margin-bottom: 10px;
  }

  ::v-deep .el-form-item {
    margin-bottom: 15px;
  }

  ::v-deep .el-form-item.is-required .el-form-item__label::before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }

  .process-form {
    padding: 0 20px;
  }

  .dialog-footer {
    text-align: center;
  }

  ::v-deep .el-radio input[aria-hidden='true'] {
    display: none !important;
  }

  ::v-deep .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
    box-shadow: none !important;
  }

  ::v-deep .custom-input-number {
    width: 300px;
  }

  ::v-deep .custom-input-number .el-input__inner {
    text-align: left;
    padding-left: 10px;
  }
</style>
