<template>
  <div class="verification-code-component">
    <el-divider></el-divider>
    <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px">{{ VerifyCodeData.title }}</div>
    <div>
      <el-form label-width="140px" label-position="right" class="transferDropDialog-form">
        <!-- 家长手机号显示 -->
        <el-form-item :label="VerifyCodeData.parentPhoneLabel" class="item">
          <div class="parent-phone">{{ VerifyCodeData.phone }}</div>
        </el-form-item>

        <!-- 验证码输入框 -->
        <el-form-item
          class="item"
          label="验证码"
          :error="errorMessage"
          :rules="[
            { required: true, message: '请输入验证码', trigger: 'blur' },
            { min: 6, max: 6, message: '验证码长度为6位', trigger: 'blur' }
          ]"
        >
          <el-input size="medium" v-model="currentCode" placeholder="请输入验证码" :maxlength="maxlength || 6" @input="handleInput" clearable>
            <el-tooltip popper-class="tips" v-if="!verifyCodeDisabled" slot="append" placement="top">
              <template #content>
                <div style="text-align: center" v-html="VerifyCodeData.tooltip"></div>
              </template>
              <div>发送验证码</div>
            </el-tooltip>
            <el-button
              v-else
              slot="append"
              style="height: 100%"
              :disabled="this.countdown > 0"
              :class="[countdown > 0 ? '' : 'btn-send-code']"
              @click="handleSendCode"
              :loading="isSending"
            >
              {{ countdown > 0 ? `${countdown}s后重发` : '发送验证码' }}
            </el-button>
          </el-input>
        </el-form-item>
      </el-form>

      <!-- 遮罩层 -->
      <div class="overlay" :class="{ active: fackContactStatus != 2 }">
        <!-- 数据未生成时，显示加载中图标 -->
        <div v-if="fackContactStatus == 0">
          <i style="font-size: 80px" class="el-icon-loading"></i>
          <div style="color: black; margin-top: 20px">正在生成合同，请稍候...</div>
        </div>
        <!-- 数据数据生成失败时，显示重试按钮 -->
        <div v-if="fackContactStatus == 1" @click="refreshQrcode">
          <div class="contract-signing-content-qrcode-refresh-icon">
            <i class="el-icon-refresh-right" style="color: #606266; font-size: 80px" />
          </div>
          <div style="color: red; margin-top: 20px">{{ VerifyCodeData.showQrCodeTipsText }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  let timer;
  export default {
    name: 'VerifyCode',
    props: {
      VerifyCodeData: {
        type: Object,
        default: () => ({
          phone: '',
          parentPhoneLabel: '',
          title: '',
          recommend: false,
          tooltip: ''
        })
      },

      // 错误提示信息
      errorMessage: {
        type: String,
        default: ''
      },
      // 最大输入长度
      maxlength: {
        type: Number,
        default: 6
      },
      // 是否禁用
      verifyCodeDisabled: {
        type: Boolean,
        default: false
      },

      // 倒计时秒数
      countdownSeconds: {
        type: Number,
        default: 60
      },

      fackContactStatus: {
        type: Number,
        default: 2
      }
    },
    data() {
      return {
        currentCode: '', // 当前输入的验证码
        countdown: 0, // 倒计时
        isSending: false // 是否正在发送验证码,
      };
    },

    methods: {
      refreshQrcode() {
        this.$emit('handle-refresh');
      },
      clearData() {
        this.currentCode = '';
        clearInterval(timer);
        this.countdown = 0;
      },

      // 输入事件处理
      handleInput(value) {
        // 触发输入事件，将当前值传递给父组件
        this.$emit('input', value);
      },

      // 发送验证码
      async handleSendCode() {
        try {
          this.isSending = true;
          // 触发发送验证码事件，由父组件处理实际的发送逻辑
          await this.$emit('send-code');

          // 发送成功后开始倒计时
          this.countdown = this.countdownSeconds;
          timer = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
              clearInterval(timer);
            }
          }, 1000);
        } catch (error) {
        } finally {
          this.isSending = false;
        }
      }
    },

    beforeDestroy() {
      this.clearData();
    }
  };
</script>

<style scoped>
  .verification-code-component {
    margin: 0 auto;
  }
  .transferDropDialog-form {
    width: 50%;
    margin: 0 auto;
  }

  .parent-phone {
    /* margin-left: -200px; */
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: #606266;
    display: inline-block;
  }
  ::v-deep .item .el-form-item__label {
    margin-left: -10px;
    font-weight: 600;
    color: black;
  }

  .btn-send-code {
    background-color: #1890ff !important;
    color: #fff !important;
  }
</style>
<style>
  .tips {
    background: #4f4f4f !important;
  }
  .container {
    position: relative;
  }
  .overlay {
    position: absolute;
    top: 9.1875rem;
    left: 0;
    width: 100%;
    height: 62%;
    background-color: rgba(250, 250, 250, 0.6);
    backdrop-filter: blur(2px);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
  }

  .overlay.active {
    opacity: 1;
    pointer-events: all;
  }
  .closeIcon {
    position: absolute;
    top: 4%;
    left: 96%;
  }
  .loading-content {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .contract-signing-content-qrcode-refresh-icon {
    font-size: 30px;
    width: 50px;
    margin: 0 auto;
  }
</style>
