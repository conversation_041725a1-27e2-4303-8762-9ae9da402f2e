// 商户管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/merchantManagement',
  component: Layout,
  redirect: '/merchantManagement/marketList',
  meta: {
    perm: 'm:merchant',
    title: '商户管理',
    icon: 'merchantManagement'
  },
  children: [
    {
      path: 'divisionList',
      component: _import('merchantManagement/divisionList'),
      name: 'divisionList',
      meta: {
        perm: 'm:merchant:divisionList',
        title: '事业部列表'
      }
    },

    {
      path: 'divisionAdd',
      hidden: true,
      component: _import('merchantManagement/divisionAdd'),
      name: 'divisionAdd',
      meta: {
        perm: 'm:merchant:divisionAdd',
        title: '事业部增加'
      }
    },
    {
      path: 'branchOfficeList',
      component: _import('merchantManagement/branchOfficeList'),
      name: 'branchOfficeList',
      meta: {
        perm: 'm:merchant:branchOfficeList',
        title: '分公司列表'
      }
    },
    {
      path: 'branchOfficeAdd',
      hidden: true,
      component: _import('merchantManagement/branchOfficeAdd'),
      name: 'branchOfficeAdd',
      meta: {
        perm: 'm:merchant:branchOfficeAdd',
        title: '分公司增加'
      }
    },
    {
      path: 'agentList',
      component: _import('merchantManagement/agentList'),
      name: 'agentList',
      meta: {
        perm: 'm:merchant:agentList',
        title: '市级服务商列表'
      }
    },
    {
      path: 'agentAddList',
      hidden: true,
      component: _import('merchantManagement/agentAddList'),
      name: 'agentAddList',
      meta: {
        perm: 'm:merchant:agentAddList',
        title: '市级服务商新增'
      }
    },
    {
      path: 'areaAgent',
      component: _import('merchantManagement/areaAgent'),
      name: 'areaAgent',
      meta: {
        perm: 'm:merchant:areaAgent',
        title: '区县服务商列表'
      }
    },
    {
      path: 'areaAgentAdd',
      component: _import('merchantManagement/areaAgentAddList'),
      name: 'areaAgentAdd',
      hidden: true,
      meta: {
        perm: 'm:merchant:areaAgent:add',
        title: '区县服务商新增'
      }
    },
    {
      path: 'deliveryCenterList',
      component: _import('merchantManagement/deliveryCenterList'),
      name: 'deliveryCenterList',
      meta: {
        perm: 'm:merchant:deliveryCenter',
        title: '交付中心列表'
      }
    },
    {
      path: 'deliveryCenterAdd',
      hidden: true,
      component: _import('merchantManagement/deliveryCenterAdd'),
      name: 'deliveryCenterAdd',
      meta: {
        perm: 'm:merchant:deliveryCenterAdd',
        title: '交付中心新增'
      }
    },
    // {
    //   path: 'experienceList',
    //   component: _import('merchantManagement/experienceList'),
    //   hidden: true,
    //   name: 'experienceList',
    //   meta: {
    //     perm: 'm:merchant:experienceList',
    //     title: '体验中心列表',
    //   }
    // },
    // {
    //   path: 'experienceAdd',
    //   hidden: true,
    //   component: _import('merchantManagement/experienceAdd'),
    //   name: 'experienceAdd',
    //   meta: {
    //     perm: 'm:merchant:experienceAdd',
    //     title: '添加/修改体验中心'
    //   }
    // },
    {
      path: 'dealerList',
      component: _import('merchantManagement/dealerList'),
      name: 'dealerList',
      meta: {
        perm: 'm:merchant:dealerList',
        title: '托管中心列表'
      }
    },
    {
      path: 'dealerAdd',
      hidden: true,
      component: _import('merchantManagement/dealerAdd'),
      name: 'dealerAdd',
      meta: {
        perm: 'm:merchant:dealerAdd',
        title: '添加/修改托管中心'
      }
    },
    {
      path: 'operationsList',
      component: _import('merchantManagement/operationsList'),
      name: 'operationsList',
      meta: {
        perm: 'm:merchant:operationsList',
        title: '超级俱乐部列表'
      }
    },
    {
      path: 'operationsAdd',
      hidden: true,
      component: _import('merchantManagement/operationsAdd'),
      name: 'operationsAdd',
      meta: {
        perm: 'm:merchant:operationsAdd',
        title: '添加/修改超级俱乐部'
      }
    },
    {
      path: 'operationsPayment',
      hidden: true,
      component: _import('merchantManagement/operationsPayment'),
      name: 'operationsPayment',
      meta: {
        perm: 'm:merchant:operationsPayment',
        title: '超级俱乐部完款'
      }
    },
    {
      path: 'salesDetails',
      // hidden: true,
      component: _import('merchantManagement/salesDetails'),
      name: 'salesDetails',
      meta: {
        perm: 'm:merchant:salesDetails',
        title: '售课列表'
      }
    },
    {
      path: 'schoolList',
      component: () => import('@/views/merchantManagement/schoolList'),
      name: 'schoolList',
      meta: {
        perm: 'm:merchant:schoolList',
        title: '门店列表'
      }
    },
    {
      path: 'schoolCheck',
      hidden: true,
      component: () => import('@/views/merchantManagement/schoolCheck'),
      name: 'schoolCheck',
      meta: {
        perm: 'm:merchant:schoolCheck',
        title: '门店审核'
      }
    },
    {
      path: 'schoolCompletePaymentIs',
      hidden: true,
      component: () => import('@/views/merchantManagement/schoolCompletePaymentIs'),
      name: 'schoolCompletePaymentIs',
      meta: {
        // perm: 'm:merchant:schoolCompletePaymentIs',
        perm: 'm:merchant:schoolEdit',
        title: '门店完款'
      }
    },
    {
      path: 'brand',
      component: () => import('@/views/merchantManagement/brand'),
      name: 'brand',
      meta: {
        perm: 'm:merchant:brand',
        title: '超级品牌列表'
      }
    },
    {
      path: 'addbrandList',
      hidden: true,
      component: () => import('@/views/merchantManagement/components/addbrandList'),
      name: 'addbrandList',
      meta: {
        perm: 'm:merchant:addbrandList',
        title: '添加/修改超级品牌'
      }
    },
    {
      path: 'refundReview',
      component: () => import('@/views/merchantManagement/refundReview'),
      name: 'refundReview',
      meta: {
        perm: 'm:merchant:refundReview',
        title: '门店退款审核'
      }
    }
  ]
};
