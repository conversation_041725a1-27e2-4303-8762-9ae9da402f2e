// 采购管理-采购申请相关接口
import request from '@/utils/request';

export default {
  // 采购申请分页查询
  queryList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/purchase/order/page',
      method: 'GET',
      params: {
        pageNum,
        pageSize,
        ...data
      }
    });
  },
  // 新增采购申请
  addPurchaseApply(params) {
    return request({
      url: '/znyy/purchase/order/add',
      method: 'GET',
      params
    });
  },
  // 采购申请-线上
  addPurchaseApplyOnline(params) {
    return request({
      url: '/znyy/purchase/order/onlinePay',
      method: 'GET',
      params
    });
  },
  // 采购申请-线下
  addPurchaseApplyOffline(params) {
    return request({
      url: '/znyy/purchase/order/offline/pay',
      method: 'GET',
      params
    });
  },
  // 确认收货
  confirmReceipt(params) {
    return request({
      url: `/znyy/purchase/order/recieve`,
      method: 'GET',
      params
    });
  },
  // 查询当前角色采购单价
  queryPurchasePrice(params) {
    return request({
      url: `/znyy/purchase/config/queryPurchasePrice`,
      method: 'GET',
      params
    });
  }
};
