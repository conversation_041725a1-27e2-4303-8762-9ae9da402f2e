<!--语文-题库管理-->
<template>
  <div>
    <el-row class="main-container">
      <el-col :span="24">
        <el-col :span="6" class="left-sidebar">
          <div style="margin: 20px 0 0 20px; line-height: 32px">条件筛选</div>
          <el-form>
            <el-form-item label="题目ID" label-width="90px">
              <el-input placeholder="请输入题目ID" v-model="form.id" style="width: 65%" clearable></el-input>
            </el-form-item>
            <el-form-item label="课程大类" label-width="90px">
              <el-select style="width: 65%" v-model="form.curriculumId" placeholder="请选择课程大类">
                <el-option v-for="item in curriculumList" :key="item.id" :value="item.id" :label="item.enName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="题干材料ID" label-width="90px">
              <el-select style="width: 65%" v-model="form.questionMaterialId" placeholder="请选择题干材料ID" clearable>
                <el-option v-for="item in subjectIdList" :key="item.id" :value="item.id" :label="item.label"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学科学段" label-width="90px">
              <el-cascader style="width: 65%" :options="subjectList" v-model="form.disciplineId" @change="handleChange"
                           clearable
              ></el-cascader>
            </el-form-item>
            <el-form-item label="题目类型" label-width="90px">
              <el-select style="width: 65%" v-model="form.questionType" placeholder="请选择题目类型" clearable>
                <el-option v-for="item in questionTypeList" :key="item.code" :label="item.value" :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="关联状态" label-width="90px">
              <el-select style="width: 65%" v-model="form.linkKnowledge" placeholder="请选择关联状态" clearable>
                <el-option v-for="item in associationList" :key="item.code" :label="item.value" :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div style="margin-top: 20px; text-align: center">
            <el-button type="primary" size="mini" @click="handleSearch()">搜索</el-button>
            <el-button type="primary" size="mini" @click="handleReset()">重置</el-button>
          </div>
        </el-col>
        <div class="right-content">
          <el-col :span="20" style="text-align: right; overflow: auto">
            <div class="filter-add">
              <span>共筛选出{{ tablePage.totalItems }}条数据</span>
              <div>
<!--                <el-button style="margin-right: 20px" type="success" size="mini"-->
<!--                           v-if="checkPermission(['m:chineseReader:addSuperReaderQuestion'])" @click="handleDownloadQuestions"-->
<!--                >下载导入模板-->
<!--                </el-button>-->
<!--                <el-button style="margin-right: 20px" type="success" size="mini"-->
<!--                           v-if="checkPermission(['m:chineseReader:addSuperReaderQuestion'])" @click="handleImportQuestions"-->
<!--                >导入题目-->
<!--                </el-button>-->
                <el-button style="margin-right: 20px" type="primary" size="mini"
                           v-if="checkPermission(['m:chineseReader:addSuperReaderQuestion'])" @click="handleAdd"
                >新增题目
                </el-button>
              </div>

            </div>
          </el-col>
          <el-col :span="20">
            <el-container>
              <el-col :span="24" v-if="topicList.length > 0">
                <el-card style="margin-bottom: 20px" v-for="(item, index) in topicList" :key="index">
                  <el-header style="font-size: 12px; margin-bottom: 20px" class="flex-around">
                    <span>题目ID：{{ item.id }}</span>
                    <span>学段：{{ item.gradeName }}</span>
                    <span>推荐答题时间：{{ item.answerTime }}分钟</span>
                    <span>试题难度： {{
                        item.questionDifficulty === 0 ? '低' : item.questionDifficulty === 1 ? '中' : item.questionDifficulty === 2 ? '高' : '未知'
                      }}</span>
                    <span>最近编辑时间：{{ item.updateTime }}</span>
                  </el-header>
                  <el-main>
                    <div style="margin-bottom: 10px">
                      <div class="question-material-text">{{ item.questionText }}</div>
                    </div>
                    <div style="margin-bottom: 20px">
                      <template v-if="item.questionType === 1">
                        <div v-for="(group, locationIndex) in groupByQuestionLocation(item.mathQuestionOptionVos)"
                             :key="locationIndex" style="margin-bottom: 10px"
                        >
                          <div>填空{{ parseInt(locationIndex) + 1 }}</div>
                          <div style="margin-left: 20px">
                            <p v-for="(option, optionIndex) in group" :key="optionIndex" style="margin-right: 20px">
                              {{ option.choiceOption }}:{{ option.content }}</p>
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        <p v-for="(option, index) in item.mathQuestionOptionVos" :key="index"
                           style="margin-right: 20px"
                        >{{ option.choiceOption }}:{{ option.content }}</p>
                      </template>
                    </div>
                    <div class="flex-around" style="margin-bottom: 20px">
                      <el-image v-for="(url, indexUrl) in item.questionImg" :key="indexUrl"
                                :preview-src-list="item.questionImg" style="height: 100px" :src="url"
                      ></el-image>
                    </div>
                    <div class="flex-around">
                      <el-row>
                        <el-col :span="12" style="text-align:center">
                          <el-button type="primary" size="mini" @click="handleReview(item.id)">查看解析</el-button>
                        </el-col>
                        <el-col :span="12" class="flex-around">
                          <el-button type="primary" size="mini"
                                     v-if="checkPermission(['b:aaademo:topicManagementEdit'])"
                                     @click="handleEdit(item.id)"
                          >编辑
                          </el-button>
                          <el-button type="primary" size="mini" v-if="checkPermission(['b:aaademo:topicManagementdel'])"
                                     @click="handleDelect(item.id)"
                          >删除
                          </el-button>
                        </el-col>
                      </el-row>
                    </div>
                  </el-main>
                  <div v-if="item.buildType == 1" style="display: flex; justify-content: flex-end">
                    <img style="width: 100px" src="../../assets/AIPic.png"/>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="20" v-else style="height: 650px">
                <p style="text-align: center; margin-top: 300px">暂无数据</p>
              </el-col>
            </el-container>
          </el-col>
          <el-col :span="20" style="display: flex; justify-content: center">
            <el-pagination
              :page-size="tablePage.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tablePage.totalItems"
              :page-sizes="[10, 20, 30, 40, 50]"
              @size-change="handleSizeChange"
              :current-page.sync="tablePage.currentPage"
              @current-change="handleCurrentChange"
            />
          </el-col>
        </div>
      </el-col>
    </el-row>

    <!-- 查看解析 -->
    <el-dialog center title="查看解析" :visible.sync="dialogVisible" width="60%">
      <chineseViewAnalysisDialog v-if="dialogVisible" :viewId="viewId"></chineseViewAnalysisDialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
<!--    题目导入记录弹窗-->
<!--    <el-dialog title="题目导入记录" :visible.sync="importOpen" width="70%">-->
<!--      <el-button type="primary" @click="handleOpenFileDialog">导入</el-button>-->
<!--      <el-dialog title="文件导入" :visible.sync="importFileOpen" width="660px" :close-on-click-modal="false" @close="close">-->
<!--        <el-form ref="excelFrom" :model="excelFrom" label-position="left" label-width="120px" style="width: 100%" v-loading="importLoading">-->
<!--          <el-form-item label="文件导入">-->
<!--            <excel-upload :limit="1" :showTip="false" :fileList="fileList" @handleSuccess="handleSuccess" @handleRemove="handleRemove"></excel-upload>-->
<!--          </el-form-item>-->
<!--        </el-form>-->
<!--        <div slot="footer" class="dialog-footer">-->
<!--          <el-button size="mini" type="primary" @click="importSubmit()" :loading="importLoading">确定</el-button>-->
<!--          <el-button size="mini" @click="close">关闭</el-button>-->
<!--        </div>-->
<!--      </el-dialog>-->
<!--      <el-table v-loading="importLoading" :data="importData" border style="width: 100%">-->
<!--        <el-table-column-->
<!--          prop="date"-->
<!--          label="ID"-->
<!--          width="180">-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--          prop="date"-->
<!--          label="文件名称"-->
<!--          width="180">-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--          prop="date"-->
<!--          label="上传人"-->
<!--          width="180">-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--          prop="date"-->
<!--          label="上传时间"-->
<!--          width="180">-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--          prop="date"-->
<!--          label="模板类型"-->
<!--          width="180">-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--          prop="date"-->
<!--          label="状态"-->
<!--          width="180">-->
<!--        </el-table-column>-->
<!--      </el-table>-->
<!--    </el-dialog>-->
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import { pageParamNames } from '@/utils/constants'
import chineseViewAnalysisDialog from './components/chineseViewAnalysisDialog.vue'
import {
  deleteQuestionBank,
  getAllCourseTypeList,
  getAllQuestionTypeList, getAllSubjectList,
  getKnowledgePointAssociation, getQuestionBankList, getQuestionMaterialIdList
} from '@/api/superReaderAPI/testBaseManagement'
import ExcelUpload from '@/views/_chinese_reader/superReaderKnowledgeManagement/upload.vue';
import { importKnowledgeFile } from '@/api/superReaderAPI/knowledgePointManagement';

export default {
  name: 'topicManagement',
  components: {
    ExcelUpload,
    chineseViewAnalysisDialog
  },
  data() {
    return {
      viewId: '', // 查看解析id
      importOpen: false,
      importFileOpen: false,
      fileList: [],
      excelFrom: {
        file: null
      },
      tempFile: null,
      importLoading: false,
      // 筛选数据
      form: {
        curriculumId: '',
        id: '',
        questionType: '',
        questionText: '',
        buildType: '',
        linkKnowledge: '',
        disciplineId: [],
        questionMaterialId: ''
      },
      subjectIdList: [],
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      associationList: [],
      questionTypeList: [],
      dialogVisible: false,
      url: '',
      srcList: [],

      topicList: [],  // 题库列表
      curriculumList: [], // 课程大类列表
      subjectList: [], // 学科列表
      subjectValue: [], // 学科列表选中值
      buildTypeList: [] // 生成类型列表
    }
  },
  created() {
    this.initData()
    this.getType()
    this.getRelation()
  },
  watch: {
    // 监听课程大类
    'form.curriculumId': function(newVal) {
      if (newVal) {
        this.getSubjectList()
      }
    },
    '$route.query.refresh': function(newVal) {
      if (newVal) {
        this.initData() // 调用分页数据刷新方法
        this.getType()
        this.getRelation()
      }
    }
  },

  methods: {
    checkPermission,
    async initData() {
      await this.getKcdlList()
      // this.form.curriculumId = '1223293140236390400';
      await this.getSubjectList() // 获取学科学段

      await this.getQuestionMaterialIdList() // 获取题干材料ID列表

      await this.fetchData() // 请求分页数据
    },
    groupByQuestionLocation(options) {
      if (!options) return {}
      return options.reduce((groups, option) => {
        const location = option.questionLocation || 0
        if (!groups[location]) {
          groups[location] = []
        }
        groups[location].push(option)
        return groups
      }, {})
    },
    handleSuccess(res) {
      this.fileList.push(res);
      this.tempFile = res;
    },
    handleRemove(index) {
      if (index != -1) {
        this.fileList.splice(index, 1);
        this.tempFile = null;
      }
    },
    close() {
      this.importFileOpen = false;
      this.excelFrom = {
        file: null
      };
      this.fileList = [];
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    // 获取所有的课程大类
    getKcdlList() {
      return new Promise((resolve) => {
        getAllCourseTypeList().then((res) => {
          if (res.success) {
            this.curriculumList = res.data
            this.form.curriculumId = res.data[0].id
          }
          resolve()
        })
      })
    },
    // 获取题目类型列表
    async getType() {
      const res = await getAllQuestionTypeList()
      if (res.success) {
        this.questionTypeList = res.data
      }
    },
    // 是否关联知识点
    async getRelation() {
      const res = await getKnowledgePointAssociation()
      if (res.success) {
        this.associationList = res.data
      }
    },
    // 获取页面数据
    fetchData() {
      getQuestionBankList({
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.size,
        curriculumId: this.form.curriculumId,
        disciplineId: this.form.disciplineId?.length ? this.form.disciplineId[0] : '',
        gradeId: this.form.disciplineId?.length ? this.form.disciplineId[1] : '',
        questionType: this.form.questionType,
        questionText: this.form.questionText,
        id: this.form.id,
        buildType: this.form.buildType,
        linkKnowledge: this.form.linkKnowledge,
        questionMaterialId: this.form.questionMaterialId
      })
        .then((res) => {
          if (res.success) {
            this.$set(this, 'topicList', res.data.data)
            pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name] || 0)))
          }
        })
        .catch((err) => {
          this.$set(this, 'topicList', [])
        })
    },
    // 转换数据为 el-cascader 支持的格式
    transformToCascaderData(data, level = 1, maxLevel = 2) {
      if (!data) return [] // 添加对 null 或 undefined 数据的处理
      return data
        .map((item) => {
          const node = {
            label: item.nodeName, // 使用 nodeName 作为显示的名称
            value: item.id // 使用 id 作为值
          }
          // 只在当前层级小于最大层级且存在子列表时才递归添加 children
          if (level < maxLevel && item.childList && item.childList.length > 0) {
            const children = this.transformToCascaderData(item.childList, level + 1, maxLevel)
            if (children.length > 0) {
              node.children = children // 只有当 children 有数据时才保留
            }
          }
          return node
        })
        .filter((node) => node.children || level === maxLevel) // 过滤掉没有 children 的节点，除非是最大层级
    },
    // 获取学科列表
    async getSubjectList() {
      const res = await getAllSubjectList({
        curriculumId: this.form.curriculumId,
        nodeLevel: 3 // 确保 API 请求获取到第三级数据
      })
      if (res.success && res.data) {
        // 直接使用返回的数据，因为数据已经具有正确的嵌套结构
        this.subjectList = this.transformToCascaderData(res.data, 1, 2)

        // 设置默认选中第一项
        if (this.subjectList.length > 0) {
          const firstSubject = this.subjectList[0]
          const firstGrade = firstSubject.children?.[0]?.value || null

          this.form.disciplineId = [firstSubject.value, firstGrade] // 默认选中第一项
        } else {
          this.form.disciplineId = [] // 如果没有数据，清空选中值
        }
      } else {
        this.subjectList = [] // 处理API调用失败或无数据的情况
        this.form.disciplineId = [] // 清空选中值
      }
    },

    // 获取题干材料ID列表
    async getQuestionMaterialIdList() {
      const res = await getQuestionMaterialIdList({
        questionMaterialTitle: ''
      })
      if (res.success) {
        this.subjectIdList = res.data.map(item => {
          return {
            label: item.questionMaterialTitle + ' - ' + item.id,
            id: item.id
          }
        })
      }
    },

    handleChange(value) {
      console.log('🚀 ~ handleChange ~ value:', value)
      this.subjectValue = value //
      this.form.disciplineId = value
    },
    // 新增题目
    handleAdd() {
      this.$router.push({
        path: './addSuperReaderQuestion',
        query: {
          editId: ''
        }
      })
      console.log('新增题目')
    },
    handleDownloadQuestions() {
      const url = 'https://document.dxznjy.com/dyw/语文阅读超人知识点导入模板.xlsx';
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', '语文阅读题干材料导入模板.xlsx');
      document.body.appendChild(link);
      try {
        link.click();
        this.$message.success('模板下载请求已发送，请查看浏览器下载列表');
      } catch (error) {
        this.$message.error('模板下载失败，请重试');
        console.error('文件下载失败:', error);
      }
      // 移除链接并释放内存
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    },

    handleImportQuestions() {
      this.importOpen = true
    },
    handleOpenFileDialog() {
      this.importFileOpen = true
    },
    importSubmit() {
      // 验证是否上传了文件
      if (!this.tempFile) {
        this.$message.error('请先上传文件');
        return;
      }
      // 导入
      this.importLoading = true;
      const formData = new FormData();
      formData.append('file', this.tempFile.raw);
      // formData.append('curriculumId', this.selectOperate.curriculumId); // 额外参数
      importKnowledgeFile(formData)
        .then((res) => {
          if (res.data && res.data.length) {
            this.fileList = [];
            this.tempFile = null;
            this.importLoading = false;
            this.errorData = res.data;
            this.errorDiaOpen = true;
          } else {
            this.$message.success('导入成功');
            this.fileList = [];
            this.tempFile = null;
            this.importLoading = false;
            this.importOpen = false;
            this.getCategoryTree();
            this.currentNodeData.nodeLevel = 1;
            this.currentNodeData.id = this.form.categoryCode;
            this.search();
          }
        })
        .catch((err) => {
          this.importLoading = false;
          this.importOpen = false;
        });
    },
    // 搜索
    handleSearch() {
      this.tablePage.currentPage = 1
      this.fetchData()
    },
    // 重置
    handleReset() {
      this.form.id = ''
      this.form.questionType = ''
      this.form.questionText = ''
      this.form.buildType = ''
      this.form.linkKnowledge = ''
      this.form.disciplineId = []
      this.form.questionMaterialId = ''

      this.subjectValue = []
      console.log('重置', this.form)
      this.tablePage.currentPage = 1
      this.fetchData()
    },
    // 查看解析
    handleReview(id) {
      this.dialogVisible = true
      this.viewId = id
      console.log('查看解析', this.viewId)
    },
    // 编辑
    handleEdit(id) {
      console.log('编辑')
      this.$router.push({
        path: './addSuperReaderQuestion',
        query: {
          editId: id
        }
      })
    },
    // 删除
    handleDelect(id) {
      console.log('删除', id)
      this.$confirm(`您确定删除这条数据吗？`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteQuestionBank({ id: id }).then(() => {
            this.$message.success(`删除题目成功`)
            this.fetchData()
          })
        })
        .catch(() => {
          this.$message.info(`已取消删除`)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-add {
  display: flex;
  justify-content: space-between;
  line-height: 32px;
  margin: 20px 0;
}

.el-header {
  background-color: #b3c0d1;
  color: #333;
  line-height: 60px;
}

.flex-around {
  display: flex;
  justify-content: space-around;
}

.option {
  padding: 5px 0;
  margin: 5px 0;
}

.main-container {
  height: 100vh; /* 撑满整个视口高度 */
  display: flex;
  overflow: hidden;
}

.left-sidebar {
  height: 100%;
  overflow: auto; /* 如果你希望左边也可以滚动就保留 */
  // background: #f9f9f9;
  // border-right: 1px solid #eee;
}

.right-content {
  flex: 1;
  height: 100%;
  overflow-y: auto; /* 只让右边滚动 */
  padding: 16px;
}

.question-material-text {
  white-space: pre-line;
  line-height: 50px;
}
</style>
