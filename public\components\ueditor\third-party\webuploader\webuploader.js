!function(n){function o(e,t){var n,i,r;if("string"==typeof e)return a(e);for(n=[],i=e.length,r=0;r<i;r++)n.push(a(e[r]));return t.apply(null,n)}var e,_,t,i,s={},r=function(e,t,n){var i,r={exports:t};"function"==typeof t&&(n.length||(n=[o,r.exports,r]),void 0!==(i=t.apply(null,n))&&(r.exports=i)),s[e]=r.exports},a=function(e){var t=s[e]||n[e];if(!t)throw new Error("`"+e+"` is undefined");return t},u=(_=n,i=o,(t=function(e,t,n){2===arguments.length&&(n=t,t=null),o(t||[],function(){r(e,n,arguments)})})("dollar-third",[],function(){return _.jQuery||_.<PERSON>}),t("dollar",["dollar-third"],function(e){return e}),t("promise-third",["dollar"],function(e){return{Deferred:e.Deferred,when:e.when,isPromise:function(e){return e&&"function"==typeof e.then}}}),t("promise",["promise-third"],function(e){return e}),t("base",["dollar","promise"],function(a,e){function t(){}var i,n,r,o,s,u,c,l,f,d,h,p,g,m,v=Function.call;function b(e,t){return function(){return e.apply(t,arguments)}}return{version:"0.1.2",$:a,Deferred:e.Deferred,isPromise:e.isPromise,when:e.when,browser:(c=navigator.userAgent,l={},f=c.match(/WebKit\/([\d.]+)/),d=c.match(/Chrome\/([\d.]+)/)||c.match(/CriOS\/([\d.]+)/),h=c.match(/MSIE\s([\d\.]+)/)||c.match(/(?:trident)(?:.*rv:([\w.]+))?/i),p=c.match(/Firefox\/([\d.]+)/),g=c.match(/Safari\/([\d.]+)/),m=c.match(/OPR\/([\d.]+)/),f&&(l.webkit=parseFloat(f[1])),d&&(l.chrome=parseFloat(d[1])),h&&(l.ie=parseFloat(h[1])),p&&(l.firefox=parseFloat(p[1])),g&&(l.safari=parseFloat(g[1])),m&&(l.opera=parseFloat(m[1])),l),os:(r=navigator.userAgent,o={},s=r.match(/(?:Android);?[\s\/]+([\d.]+)?/),u=r.match(/(?:iPad|iPod|iPhone).*OS\s([\d_]+)/),s&&(o.android=parseFloat(s[1])),u&&(o.ios=parseFloat(u[1].replace(/_/g,"."))),o),inherits:function(e,t,n){var i,r,o;return"function"==typeof t?(i=t,t=null):i=t&&t.hasOwnProperty("constructor")?t.constructor:function(){return e.apply(this,arguments)},a.extend(!0,i,e,n||{}),i.__super__=e.prototype,i.prototype=(r=e.prototype,Object.create?Object.create(r):((o=function(){}).prototype=r,new o)),t&&a.extend(!0,i.prototype,t),i},noop:t,bindFn:b,log:_.console?b(console.log,console):t,nextTick:function(e){setTimeout(e,1)},slice:(n=[].slice,function(){return v.apply(n,arguments)}),guid:(i=0,function(e){for(var t=(+new Date).toString(32),n=0;n<5;n++)t+=Math.floor(65535*Math.random()).toString(32);return(e||"wu_")+t+(i++).toString(32)}),formatSize:function(e,t,n){var i;for(n=n||["B","K","M","G","TB"];(i=n.shift())&&1024<e;)e/=1024;return("B"===i?e:e.toFixed(t||2))+i}}}),t("mediator",["base"],function(e){var t,r=e.$,o=[].slice,a=/\s+/;function s(e,t,n,i){return r.grep(e,function(e){return e&&(!t||e.e===t)&&(!n||e.cb===n||e.cb._cb===n)&&(!i||e.ctx===i)})}function u(e,n,i){r.each((e||"").split(a),function(e,t){i(t,n)})}function c(e,t){for(var n,i=!1,r=-1,o=e.length;++r<o;)if(!1===(n=e[r]).cb.apply(n.ctx2,t)){i=!0;break}return!i}return t={on:function(e,t,i){var r,o=this;return t&&(r=this._events||(this._events=[]),u(e,t,function(e,t){var n={e:e};n.cb=t,n.ctx=i,n.ctx2=i||o,n.id=r.length,r.push(n)})),this},once:function(e,t,i){var r=this;return t&&u(e,t,function(e,t){var n=function(){return r.off(e,n),t.apply(i||r,arguments)};n._cb=t,r.on(e,n,i)}),r},off:function(e,t,n){var i=this._events;return i&&(e||t||n?u(e,t,function(e,t){r.each(s(i,e,t,n),function(){delete i[this.id]})}):this._events=[]),this},trigger:function(e){var t,n,i;return this._events&&e?(t=o.call(arguments,1),n=s(this._events,e),i=s(this._events,"all"),c(n,t)&&c(i,arguments)):this}},r.extend({installTo:function(e){return r.extend(e,t)}},t)}),t("uploader",["base","mediator"],function(e,r){var o=e.$;function n(e){this.options=o.extend(!0,{},n.options,e),this._init(this.options)}return n.options={},r.installTo(n.prototype),o.each({upload:"start-upload",stop:"stop-upload",getFile:"get-file",getFiles:"get-files",addFile:"add-file",addFiles:"add-file",sort:"sort-files",removeFile:"remove-file",skipFile:"skip-file",retry:"retry",isInProgress:"is-in-progress",makeThumb:"make-thumb",getDimension:"get-dimension",addButton:"add-btn",getRuntimeType:"get-runtime-type",refresh:"refresh",disable:"disable",enable:"enable",reset:"reset"},function(e,t){n.prototype[e]=function(){return this.request(t,arguments)}}),o.extend(n.prototype,{state:"pending",_init:function(e){var t=this;t.request("init",e,function(){t.state="ready",t.trigger("ready")})},option:function(e,t){var n=this.options;if(!(1<arguments.length))return e?n[e]:n;o.isPlainObject(t)&&o.isPlainObject(n[e])?o.extend(n[e],t):n[e]=t},getStats:function(){var e=this.request("get-stats");return{successNum:e.numOfSuccess,cancelNum:e.numOfCancel,invalidNum:e.numOfInvalid,uploadFailNum:e.numOfUploadFailed,queueNum:e.numOfQueue}},trigger:function(e){var t=[].slice.call(arguments,1),n=this.options,i="on"+e.substring(0,1).toUpperCase()+e.substring(1);return!(!1===r.trigger.apply(this,arguments)||o.isFunction(n[i])&&!1===n[i].apply(this,t)||o.isFunction(this[i])&&!1===this[i].apply(this,t)||!1===r.trigger.apply(r,[this,e].concat(t)))},request:e.noop}),e.create=n.create=function(e){return new n(e)},e.Uploader=n}),t("runtime/runtime",["base","mediator"],function(t,e){function i(e){for(var t in e)if(e.hasOwnProperty(t))return t;return null}var r=t.$,o={};function a(e){this.options=r.extend({container:document.body},e),this.uid=t.guid("rt_")}return r.extend(a.prototype,{getContainer:function(){var e,t,n=this.options;return this._container?this._container:(e=r(n.container||document.body),(t=r(document.createElement("div"))).attr("id","rt_"+this.uid),t.css({position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.append(t),e.addClass("webuploader-container"),this._container=t)},init:t.noop,exec:t.noop,destroy:function(){this._container&&this._container.parentNode.removeChild(this.__container),this.off()}}),a.orders="html5,flash",a.addRuntime=function(e,t){o[e]=t},a.hasRuntime=function(e){return!!(e?o[e]:i(o))},a.create=function(e,t){var n;if(t=t||a.orders,r.each(t.split(/\s*,\s*/g),function(){if(o[this])return n=this,!1}),!(n=n||i(o)))throw new Error("Runtime Error");return new o[n](e)},e.installTo(a.prototype),a}),t("runtime/client",["base","mediator","runtime/runtime"],function(o,e,a){var s,i;function t(t,n){var i,e,r=o.Deferred();this.uid=o.guid("client_"),this.runtimeReady=function(e){return r.done(e)},this.connectRuntime=function(e,t){if(i)throw new Error("already connected!");return r.done(t),"string"==typeof e&&s.get(e)&&(i=s.get(e)),(i=i||s.get(null,n))?(o.$.extend(i.options,e),i.__promise.then(r.resolve),i.__client++):((i=a.create(e,e.runtimeOrder)).__promise=r.promise(),i.once("ready",r.resolve),i.init(),s.add(i),i.__client=1),n&&(i.__standalone=n),i},this.getRuntime=function(){return i},this.disconnectRuntime=function(){i&&(i.__client--,i.__client<=0&&(s.remove(i),delete i.__promise,i.destroy()),i=null)},this.exec=function(){if(i){var e=o.slice(arguments);return t&&e.unshift(t),i.exec.apply(this,e)}},this.getRuid=function(){return i&&i.uid},this.destroy=(e=this.destroy,function(){e&&e.apply(this,arguments),this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()})}return i={},s={add:function(e){i[e.uid]=e},get:function(e,t){var n;if(e)return i[e];for(n in i)if(!t||!i[n].__standalone)return i[n];return null},remove:function(e){delete i[e.uid]}},e.installTo(t.prototype),t}),t("lib/dnd",["base","mediator","runtime/client"],function(e,t,n){var i=e.$;function r(e){(e=this.options=i.extend({},r.options,e)).container=i(e.container),e.container.length&&n.call(this,"DragAndDrop")}return r.options={accept:null,disableGlobalDnd:!1},e.inherits(n,{constructor:r,init:function(){var e=this;e.connectRuntime(e.options,function(){e.exec("init"),e.trigger("ready")})},destroy:function(){this.disconnectRuntime()}}),t.installTo(r.prototype),r}),t("widgets/widget",["base","uploader"],function(f,e){var d=f.$,t=e.prototype._init,h={},r=[];function o(e){this.owner=e,this.options=e.options}return d.extend(o.prototype,{init:f.noop,invoke:function(e,t){var n=this.responseMap;return n&&e in n&&n[e]in this&&d.isFunction(this[n[e]])?this[n[e]].apply(this,t):h},request:function(){return this.owner.request.apply(this.owner,arguments)}}),d.extend(e.prototype,{_init:function(){var n=this,i=n._widgets=[];return d.each(r,function(e,t){i.push(new t(n))}),t.apply(n,arguments)},request:function(e,t,n){var i,r,o,a=0,s=this._widgets,u=s.length,c=[],l=[];for(t=function(e){if(e){var t=e.length,n=d.type(e);return 1===e.nodeType&&t||"array"===n||"function"!==n&&"string"!==n&&(0===t||"number"==typeof t&&0<t&&t-1 in e)}}(t)?t:[t];a<u;a++)(i=s[a].invoke(e,t))!==h&&(f.isPromise(i)?l.push(i):c.push(i));return n||l.length?(r=f.when.apply(f,l))[o=r.pipe?"pipe":"then"](function(){var e=f.Deferred(),t=arguments;return setTimeout(function(){e.resolve.apply(e,t)},1),e.promise()})[o](n||f.noop):c[0]}}),e.register=o.register=function(e,t){var n,i={init:"init"};return 1===arguments.length?(t=e).responseMap=i:t.responseMap=d.extend(i,e),n=f.inherits(o,t),r.push(n),n},o}),t("widgets/filednd",["base","uploader","lib/dnd","widgets/widget"],function(o,e,a){var s=o.$;return e.options.dnd="",e.register({init:function(e){if(e.dnd&&"html5"===this.request("predict-runtime-type")){var t,n=this,i=o.Deferred(),r=s.extend({},{disableGlobalDnd:e.disableGlobalDnd,container:e.dnd,accept:e.accept});return(t=new a(r)).once("ready",i.resolve),t.on("drop",function(e){n.request("add-file",[e])}),t.on("accept",function(e){return n.owner.trigger("dndAccept",e)}),t.init(),i.promise()}}})}),t("lib/filepaste",["base","mediator","runtime/client"],function(e,t,n){var i=e.$;function r(e){(e=this.options=i.extend({},e)).container=i(e.container||document.body),n.call(this,"FilePaste")}return e.inherits(n,{constructor:r,init:function(){var e=this;e.connectRuntime(e.options,function(){e.exec("init"),e.trigger("ready")})},destroy:function(){this.exec("destroy"),this.disconnectRuntime(),this.off()}}),t.installTo(r.prototype),r}),t("widgets/filepaste",["base","uploader","lib/filepaste","widgets/widget"],function(o,e,a){var s=o.$;return e.register({init:function(e){if(e.paste&&"html5"===this.request("predict-runtime-type")){var t,n=this,i=o.Deferred(),r=s.extend({},{container:e.paste,accept:e.accept});return(t=new a(r)).once("ready",i.resolve),t.on("paste",function(e){n.owner.request("add-file",[e])}),t.init(),i.promise()}}})}),t("lib/blob",["base","runtime/client"],function(e,n){function t(e,t){this.source=t,this.ruid=e,n.call(this,"Blob"),this.uid=t.uid||this.uid,this.type=t.type||"",this.size=t.size||0,e&&this.connectRuntime(e)}return e.inherits(n,{constructor:t,slice:function(e,t){return this.exec("slice",e,t)},getSource:function(){return this.source}}),t}),t("lib/file",["base","lib/blob"],function(e,i){var r=1,o=/\.([^.]+)$/;return e.inherits(i,function(e,t){var n;i.apply(this,arguments),this.name=t.name||"untitled"+r++,!(n=o.exec(t.name)?RegExp.$1.toLowerCase():"")&&this.type&&(n=/\/(jpg|jpeg|png|gif|bmp)$/i.exec(this.type)?RegExp.$1.toLowerCase():"",this.name+="."+n),!this.type&&~"jpg,jpeg,png,gif,bmp".indexOf(n)&&(this.type="image/"+("jpg"===n?"jpeg":n)),this.ext=n,this.lastModifiedDate=t.lastModifiedDate||(new Date).toLocaleString()})}),t("lib/filepicker",["base","runtime/client","lib/file"],function(e,t,o){var a=e.$;function n(e){if((e=this.options=a.extend({},n.options,e)).container=a(e.id),!e.container.length)throw new Error("按钮指定错误");e.innerHTML=e.innerHTML||e.label||e.container.html()||"",e.button=a(e.button||document.createElement("div")),e.button.html(e.innerHTML),e.container.html(e.button),t.call(this,"FilePicker",!0)}return n.options={button:null,container:null,label:null,innerHTML:null,multiple:!0,accept:null,name:"file"},e.inherits(t,{constructor:n,init:function(){var n=this,i=n.options,r=i.button;r.addClass("webuploader-pick"),n.on("all",function(e){var t;switch(e){case"mouseenter":r.addClass("webuploader-pick-hover");break;case"mouseleave":r.removeClass("webuploader-pick-hover");break;case"change":t=n.exec("getFiles"),n.trigger("select",a.map(t,function(e){return(e=new o(n.getRuid(),e))._refer=i.container,e}),i.container)}}),n.connectRuntime(i,function(){n.refresh(),n.exec("init",i),n.trigger("ready")}),a(_).on("resize",function(){n.refresh()})},refresh:function(){var e=this.getRuntime().getContainer(),t=this.options.button,n=t.outerWidth?t.outerWidth():t.width(),i=t.outerHeight?t.outerHeight():t.height(),r=t.offset();n&&i&&e.css({bottom:"auto",right:"auto",width:n+"px",height:i+"px"}).offset(r)},enable:function(){this.options.button.removeClass("webuploader-pick-disable"),this.refresh()},disable:function(){var e=this.options.button;this.getRuntime().getContainer().css({top:"-99999px"}),e.addClass("webuploader-pick-disable")},destroy:function(){this.runtime&&(this.exec("destroy"),this.disconnectRuntime())}}),n}),t("widgets/filepicker",["base","uploader","lib/filepicker","widgets/widget"],function(s,e,u){var c=s.$;return c.extend(e.options,{pick:null,accept:null}),e.register({"add-btn":"addButton",refresh:"refresh",disable:"disable",enable:"enable"},{init:function(e){return this.pickers=[],e.pick&&this.addButton(e.pick)},refresh:function(){c.each(this.pickers,function(){this.refresh()})},addButton:function(e){var t,n,i,r=this,o=r.options,a=o.accept;if(e)return i=s.Deferred(),c.isPlainObject(e)||(e={id:e}),t=c.extend({},e,{accept:c.isPlainObject(a)?[a]:a,swf:o.swf,runtimeOrder:o.runtimeOrder}),(n=new u(t)).once("ready",i.resolve),n.on("select",function(e){r.owner.request("add-file",[e])}),n.init(),this.pickers.push(n),i.promise()},disable:function(){c.each(this.pickers,function(){this.disable()})},enable:function(){c.each(this.pickers,function(){this.enable()})}})}),t("lib/image",["base","runtime/client","lib/blob"],function(t,n,i){var r=t.$;function o(e){this.options=r.extend({},o.options,e),n.call(this,"Image"),this.on("load",function(){this._info=this.exec("info"),this._meta=this.exec("meta")})}return o.options={quality:90,crop:!1,preserveHeaders:!0,allowMagnify:!0},t.inherits(n,{constructor:o,info:function(e){return e?(this._info=e,this):this._info},meta:function(e){return e?(this._meta=e,this):this._meta},loadFromBlob:function(e){var t=this,n=e.getRuid();this.connectRuntime(n,function(){t.exec("init",t.options),t.exec("loadFromBlob",e)})},resize:function(){var e=t.slice(arguments);return this.exec.apply(this,["resize"].concat(e))},getAsDataUrl:function(e){return this.exec("getAsDataUrl",e)},getAsBlob:function(e){var t=this.exec("getAsBlob",e);return new i(this.getRuid(),t)}}),o}),t("widgets/image",["base","uploader","lib/image","widgets/widget"],function(t,e,a){var s,i,r,u=t.$;function o(){for(var e;r.length&&i<5242880;)e=r.shift(),i+=e[0],e[1]()}return i=0,r=[],s=function(e,t,n){r.push([t,n]),e.once("destroy",function(){i-=t,setTimeout(o,1)}),setTimeout(o,1)},u.extend(e.options,{thumb:{width:110,height:110,quality:70,allowMagnify:!0,crop:!0,preserveHeaders:!1,type:"image/jpeg"},compress:{width:1600,height:1600,quality:90,allowMagnify:!1,crop:!1,preserveHeaders:!0}}),e.register({"make-thumb":"makeThumb","before-send-file":"compressImage"},{makeThumb:function(e,t,n,i){var r,o;(e=this.request("get-file",e)).type.match(/^image/)?(r=u.extend({},this.options.thumb),u.isPlainObject(n)&&(r=u.extend(r,n),n=null),n=n||r.width,i=i||r.height,(o=new a(r)).once("load",function(){e._info=e._info||o.info(),e._meta=e._meta||o.meta(),o.resize(n,i)}),o.once("complete",function(){t(!1,o.getAsDataUrl(r.type)),o.destroy()}),o.once("error",function(){t(!0),o.destroy()}),s(o,e.source.size,function(){e._info&&o.info(e._info),e._meta&&o.meta(e._meta),o.loadFromBlob(e.source)})):t(!0)},compressImage:function(n){var i,r,o=this.options.compress||this.options.resize,e=o&&o.compressSize||307200;if(n=this.request("get-file",n),o&&~"image/jpeg,image/jpg".indexOf(n.type)&&!(n.size<e)&&!n._compressed)return o=u.extend({},o),r=t.Deferred(),i=new a(o),r.always(function(){i.destroy(),i=null}),i.once("error",r.reject),i.once("load",function(){n._info=n._info||i.info(),n._meta=n._meta||i.meta(),i.resize(o.width,o.height)}),i.once("complete",function(){var e,t;try{e=i.getAsBlob(o.type),t=n.size,e.size<t&&(n.source=e,n.size=e.size,n.trigger("resize",e.size,t)),n._compressed=!0,r.resolve()}catch(e){r.resolve()}}),n._info&&i.info(n._info),n._meta&&i.meta(n._meta),i.loadFromBlob(n.source),r.promise()}})}),t("file",["base","mediator"],function(e,t){var n=e.$,i="WU_FILE_",r=0,o=/\.([^.]+)$/,a={};function s(e){this.name=e.name||"Untitled",this.size=e.size||0,this.type=e.type||"application",this.lastModifiedDate=e.lastModifiedDate||+new Date,this.id=i+r++,this.ext=o.exec(this.name)?RegExp.$1:"",this.statusText="",a[this.id]=s.Status.INITED,this.source=e,this.loaded=0,this.on("error",function(e){this.setStatus(s.Status.ERROR,e)})}return n.extend(s.prototype,{setStatus:function(e,t){var n=a[this.id];void 0!==t&&(this.statusText=t),e!==n&&(a[this.id]=e,this.trigger("statuschange",e,n))},getStatus:function(){return a[this.id]},getSource:function(){return this.source},destory:function(){delete a[this.id]}}),t.installTo(s.prototype),s.Status={INITED:"inited",QUEUED:"queued",PROGRESS:"progress",ERROR:"error",COMPLETE:"complete",CANCELLED:"cancelled",INTERRUPT:"interrupt",INVALID:"invalid"},s}),t("queue",["base","mediator","file"],function(e,t,n){var o=e.$,r=n.Status;function i(){this.stats={numOfQueue:0,numOfSuccess:0,numOfCancel:0,numOfProgress:0,numOfUploadFailed:0,numOfInvalid:0},this._queue=[],this._map={}}return o.extend(i.prototype,{append:function(e){return this._queue.push(e),this._fileAdded(e),this},prepend:function(e){return this._queue.unshift(e),this._fileAdded(e),this},getFile:function(e){return"string"!=typeof e?e:this._map[e]},fetch:function(e){var t,n,i=this._queue.length;for(e=e||r.QUEUED,t=0;t<i;t++)if(e===(n=this._queue[t]).getStatus())return n;return null},sort:function(e){"function"==typeof e&&this._queue.sort(e)},getFiles:function(){for(var e,t=[].slice.call(arguments,0),n=[],i=0,r=this._queue.length;i<r;i++)e=this._queue[i],t.length&&!~o.inArray(e.getStatus(),t)||n.push(e);return n},_fileAdded:function(e){var n=this;this._map[e.id]||(this._map[e.id]=e).on("statuschange",function(e,t){n._onFileStatusChange(e,t)}),e.setStatus(r.QUEUED)},_onFileStatusChange:function(e,t){var n=this.stats;switch(t){case r.PROGRESS:n.numOfProgress--;break;case r.QUEUED:n.numOfQueue--;break;case r.ERROR:n.numOfUploadFailed--;break;case r.INVALID:n.numOfInvalid--}switch(e){case r.QUEUED:n.numOfQueue++;break;case r.PROGRESS:n.numOfProgress++;break;case r.ERROR:n.numOfUploadFailed++;break;case r.COMPLETE:n.numOfSuccess++;break;case r.CANCELLED:n.numOfCancel++;break;case r.INVALID:n.numOfInvalid++}}}),t.installTo(i.prototype),i}),t("widgets/queue",["base","uploader","queue","file","lib/file","runtime/client","widgets/widget"],function(c,e,l,t,n,f){var d=c.$,i=/\.\w+$/,o=t.Status;return e.register({"sort-files":"sortFiles","add-file":"addFiles","get-file":"getFile","fetch-file":"fetchFile","get-stats":"getStats","get-files":"getFiles","remove-file":"removeFile",retry:"retry",reset:"reset","accept-file":"acceptFile"},{init:function(e){var t,n,i,r,o,a,s,u=this;if(d.isPlainObject(e.accept)&&(e.accept=[e.accept]),e.accept){for(o=[],i=0,n=e.accept.length;i<n;i++)(r=e.accept[i].extensions)&&o.push(r);o.length&&(a="\\."+o.join(",").replace(/,/g,"$|\\.").replace(/\*/g,".*")+"$"),u.accept=new RegExp(a,"i")}if(u.queue=new l,u.stats=u.queue.stats,"html5"===this.request("predict-runtime-type"))return t=c.Deferred(),(s=new f("Placeholder")).connectRuntime({runtimeOrder:"html5"},function(){u._ruid=s.getRuid(),t.resolve()}),t.promise()},_wrapFile:function(e){if(!(e instanceof t)){if(!(e instanceof n)){if(!this._ruid)throw new Error("Can't add external files.");e=new n(this._ruid,e)}e=new t(e)}return e},acceptFile:function(e){return!(!e||e.size<6||this.accept&&i.exec(e.name)&&!this.accept.test(e.name))},_addFile:function(e){var t=this;if(e=t._wrapFile(e),t.owner.trigger("beforeFileQueued",e)){if(t.acceptFile(e))return t.queue.append(e),t.owner.trigger("fileQueued",e),e;t.owner.trigger("error","Q_TYPE_DENIED",e)}},getFile:function(e){return this.queue.getFile(e)},addFiles:function(e){var t=this;e.length||(e=[e]),e=d.map(e,function(e){return t._addFile(e)}),t.owner.trigger("filesQueued",e),t.options.auto&&t.request("start-upload")},getStats:function(){return this.stats},removeFile:function(e){(e=e.id?e:this.queue.getFile(e)).setStatus(o.CANCELLED),this.owner.trigger("fileDequeued",e)},getFiles:function(){return this.queue.getFiles.apply(this.queue,arguments)},fetchFile:function(){return this.queue.fetch.apply(this.queue,arguments)},retry:function(e,t){var n,i,r;if(e)return(e=e.id?e:this.queue.getFile(e)).setStatus(o.QUEUED),void(t||this.request("start-upload"));for(i=0,r=(n=this.queue.getFiles(o.ERROR)).length;i<r;i++)(e=n[i]).setStatus(o.QUEUED);this.request("start-upload")},sortFiles:function(){return this.queue.sort.apply(this.queue,arguments)},reset:function(){this.queue=new l,this.stats=this.queue.stats}})}),t("widgets/runtime",["uploader","runtime/runtime","widgets/widget"],function(e,r){return e.support=function(){return r.hasRuntime.apply(r,arguments)},e.register({"predict-runtime-type":"predictRuntmeType"},{init:function(){if(!this.predictRuntmeType())throw Error("Runtime Error")},predictRuntmeType:function(){var e,t,n=this.options.runtimeOrder||r.orders,i=this.type;if(!i)for(e=0,t=(n=n.split(/\s*,\s*/g)).length;e<t;e++)if(r.hasRuntime(n[e])){this.type=i=n[e];break}return i}})}),t("lib/transport",["base","runtime/client","mediator"],function(e,n,t){var i=e.$;function r(e){var t=this;e=t.options=i.extend(!0,{},r.options,e||{}),n.call(this,"Transport"),this._blob=null,this._formData=e.formData||{},this._headers=e.headers||{},this.on("progress",this._timeout),this.on("load error",function(){t.trigger("progress",1),clearTimeout(t._timer)})}return r.options={server:"",method:"POST",withCredentials:!1,fileVal:"file",timeout:12e4,formData:{},headers:{},sendAsBinary:!1},i.extend(r.prototype,{appendBlob:function(e,t,n){var i=this,r=i.options;i.getRuid()&&i.disconnectRuntime(),i.connectRuntime(t.ruid,function(){i.exec("init")}),i._blob=t,r.fileVal=e||r.fileVal,r.filename=n||r.filename},append:function(e,t){"object"==typeof e?i.extend(this._formData,e):this._formData[e]=t},setRequestHeader:function(e,t){"object"==typeof e?i.extend(this._headers,e):this._headers[e]=t},send:function(e){this.exec("send",e),this._timeout()},abort:function(){return clearTimeout(this._timer),this.exec("abort")},destroy:function(){this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()},getResponse:function(){return this.exec("getResponse")},getResponseAsJson:function(){return this.exec("getResponseAsJson")},getStatus:function(){return this.exec("getStatus")},_timeout:function(){var e=this,t=e.options.timeout;t&&(clearTimeout(e._timer),e._timer=setTimeout(function(){e.abort(),e.trigger("error","timeout")},t))}}),t.installTo(r.prototype),r}),t("widgets/upload",["base","uploader","file","lib/transport","widgets/widget"],function(l,e,t,f){var d=l.$,o=l.isPromise,h=t.Status;d.extend(e.options,{prepareNextFile:!1,chunked:!1,chunkSize:5242880,chunkRetry:2,threads:3,formData:null}),e.register({"start-upload":"start","stop-upload":"stop","skip-file":"skipFile","is-in-progress":"isInProgress"},{init:function(){var e=this.owner;this.runing=!1,this.pool=[],this.pending=[],this.remaning=0,this.__tick=l.bindFn(this._tick,this),e.on("uploadComplete",function(e){e.blocks&&d.each(e.blocks,function(e,t){t.transport&&(t.transport.abort(),t.transport.destroy()),delete t.transport}),delete e.blocks,delete e.remaning})},start:function(){var i=this;d.each(i.request("get-files",h.INVALID),function(){i.request("remove-file",this)}),i.runing||(i.runing=!0,d.each(i.pool,function(e,t){var n=t.file;n.getStatus()===h.INTERRUPT&&(n.setStatus(h.PROGRESS),i._trigged=!1,t.transport&&t.transport.send())}),i._trigged=!1,i.owner.trigger("startUpload"),l.nextTick(i.__tick))},stop:function(e){!1!==this.runing&&(this.runing=!1,e&&d.each(this.pool,function(e,t){t.transport&&t.transport.abort(),t.file.setStatus(h.INTERRUPT)}),this.owner.trigger("stopUpload"))},isInProgress:function(){return!!this.runing},getStats:function(){return this.request("get-stats")},skipFile:function(e,t){(e=this.request("get-file",e)).setStatus(t||h.COMPLETE),e.skipped=!0,e.blocks&&d.each(e.blocks,function(e,t){var n=t.transport;n&&(n.abort(),n.destroy(),delete t.transport)}),this.owner.trigger("uploadSkip",e)},_tick:function(){var e,t,n=this,i=n.options;if(n._promise)return n._promise.always(n.__tick);n.pool.length<i.threads&&(t=n._nextBlock())?(n._trigged=!1,e=function(e){n._promise=null,e&&e.file&&n._startSend(e),l.nextTick(n.__tick)},n._promise=o(t)?t.always(e):e(t)):n.remaning||n.getStats().numOfQueue||(n.runing=!1,n._trigged||l.nextTick(function(){n.owner.trigger("uploadFinished")}),n._trigged=!0)},_nextBlock:function(){var e,t,n=this,i=n._act,r=n.options;return i&&i.has()&&i.file.getStatus()===h.PROGRESS?(r.prepareNextFile&&!n.pending.length&&n._prepareNextFile(),i.fetch()):n.runing?(!n.pending.length&&n.getStats().numOfQueue&&n._prepareNextFile(),e=n.pending.shift(),t=function(e){return e?(i=function(e,t){for(var n,i=[],r=e.source.size,o=t?Math.ceil(r/t):1,a=0,s=0;s<o;)n=Math.min(t,r-a),i.push({file:e,start:a,end:t?a+n:r,total:r,chunks:o,chunk:s++}),a+=n;return e.blocks=i.concat(),e.remaning=i.length,{file:e,has:function(){return!!i.length},fetch:function(){return i.shift()}}}(e,r.chunked?r.chunkSize:0),(n._act=i).fetch()):null},o(e)?e[e.pipe?"pipe":"then"](t):t(e)):void 0},_prepareNextFile:function(){var t,n=this,i=n.request("fetch-file"),r=n.pending;i&&((t=n.request("before-send-file",i,function(){return i.getStatus()===h.QUEUED?(n.owner.trigger("uploadStart",i),i.setStatus(h.PROGRESS),i):n._finishFile(i)})).done(function(){var e=d.inArray(t,r);~e&&r.splice(e,1,i)}),t.fail(function(e){i.setStatus(h.ERROR,e),n.owner.trigger("uploadError",i,e),n.owner.trigger("uploadComplete",i)}),r.push(t))},_popBlock:function(e){var t=d.inArray(e,this.pool);this.pool.splice(t,1),e.file.remaning--,this.remaning--},_startSend:function(e){var t=this,n=e.file;t.pool.push(e),t.remaning++,e.blob=1===e.chunks?n.source:n.source.slice(e.start,e.end),t.request("before-send",e,function(){n.getStatus()===h.PROGRESS?t._doSend(e):(t._popBlock(e),l.nextTick(t.__tick))}).fail(function(){1===n.remaning?t._finishFile(n).always(function(){e.percentage=1,t._popBlock(e),t.owner.trigger("uploadComplete",n),l.nextTick(t.__tick)}):(e.percentage=1,t._popBlock(e),l.nextTick(t.__tick))})},_doSend:function(i){var n,r,t=this,o=t.owner,a=t.options,s=i.file,u=new f(a),e=d.extend({},a.formData),c=d.extend({},a.headers);(i.transport=u).on("destroy",function(){delete i.transport,t._popBlock(i),l.nextTick(t.__tick)}),u.on("progress",function(e){var t=0,n=0;t=i.percentage=e,1<i.chunks&&(d.each(s.blocks,function(e,t){n+=(t.percentage||0)*(t.end-t.start)}),t=n/s.size),o.trigger("uploadProgress",s,t||0)}),n=function(t){var e;return(r=u.getResponseAsJson()||{})._raw=u.getResponse(),e=function(e){t=e},o.trigger("uploadAccept",i,r,e)||(t=t||"server"),t},u.on("error",function(e,t){i.retried=i.retried||0,1<i.chunks&&~"http,abort".indexOf(e)&&i.retried<a.chunkRetry?(i.retried++,u.send()):(t||"server"!==e||(e=n(e)),s.setStatus(h.ERROR,e),o.trigger("uploadError",s,e),o.trigger("uploadComplete",s))}),u.on("load",function(){var e;(e=n())?u.trigger("error",e,!0):1===s.remaning?t._finishFile(s,r):u.destroy()}),e=d.extend(e,{id:s.id,name:s.name,type:s.type,lastModifiedDate:s.lastModifiedDate,size:s.size}),1<i.chunks&&d.extend(e,{chunks:i.chunks,chunk:i.chunk}),o.trigger("uploadBeforeSend",i,e,c),u.appendBlob(a.fileVal,i.blob,s.name),u.append(e),u.setRequestHeader(c),u.send()},_finishFile:function(t,e,n){var i=this.owner;return i.request("after-send-file",arguments,function(){t.setStatus(h.COMPLETE),i.trigger("uploadSuccess",t,e,n)}).fail(function(e){t.getStatus()===h.PROGRESS&&t.setStatus(h.ERROR,e),i.trigger("uploadError",t,e)}).always(function(){i.trigger("uploadComplete",t)})}})}),t("widgets/validator",["base","uploader","file","widgets/widget"],function(e,t,n){var i,r=e.$,o={};return i={addValidator:function(e,t){o[e]=t},removeValidator:function(e){delete o[e]}},t.register({init:function(){var e=this;r.each(o,function(){this.call(e.owner)})}}),i.addValidator("fileNumLimit",function(){var e=this,t=e.options,n=0,i=t.fileNumLimit>>0,r=!0;i&&(e.on("beforeFileQueued",function(e){return i<=n&&r&&(r=!1,this.trigger("error","Q_EXCEED_NUM_LIMIT",i,e),setTimeout(function(){r=!0},1)),!(i<=n)}),e.on("fileQueued",function(){n++}),e.on("fileDequeued",function(){n--}),e.on("uploadFinished",function(){n=0}))}),i.addValidator("fileSizeLimit",function(){var e=this,t=e.options,n=0,i=t.fileSizeLimit>>0,r=!0;i&&(e.on("beforeFileQueued",function(e){var t=n+e.size>i;return t&&r&&(r=!1,this.trigger("error","Q_EXCEED_SIZE_LIMIT",i,e),setTimeout(function(){r=!0},1)),!t}),e.on("fileQueued",function(e){n+=e.size}),e.on("fileDequeued",function(e){n-=e.size}),e.on("uploadFinished",function(){n=0}))}),i.addValidator("fileSingleSizeLimit",function(){var t=this.options.fileSingleSizeLimit;t&&this.on("beforeFileQueued",function(e){if(e.size>t)return e.setStatus(n.Status.INVALID,"exceed_size"),this.trigger("error","F_EXCEED_SIZE",e),!1})}),i.addValidator("duplicate",function(){var e=this.options,n={};e.duplicate||(this.on("beforeFileQueued",function(e){var t=e.__hash||(e.__hash=function(e){for(var t=0,n=0,i=e.length;n<i;n++)t=e.charCodeAt(n)+(t<<6)+(t<<16)-t;return t}(e.name+e.size+e.lastModifiedDate));if(n[t])return this.trigger("error","F_DUPLICATE",e),!1}),this.on("fileQueued",function(e){var t=e.__hash;t&&(n[t]=!0)}),this.on("fileDequeued",function(e){var t=e.__hash;t&&delete n[t]}))}),i}),t("runtime/compbase",[],function(){return function(e,t){this.owner=e,this.options=e.options,this.getRuntime=function(){return t},this.getRuid=function(){return t.uid},this.trigger=function(){return e.trigger.apply(e,arguments)}}}),t("runtime/html5/runtime",["base","runtime/runtime","runtime/compbase"],function(s,t,n){var u={};function e(){var o={},a=this,e=this.destory;t.apply(a,arguments),a.type="html5",a.exec=function(e,t){var n,i=this.uid,r=s.slice(arguments,2);if(u[e]&&(n=o[i]=o[i]||new u[e](this,a))[t])return n[t].apply(n,r)},a.destory=function(){return e&&e.apply(this,arguments)}}return s.inherits(t,{constructor:e,init:function(){var e=this;setTimeout(function(){e.trigger("ready")},1)}}),e.register=function(e,t){return u[e]=s.inherits(n,t)},_.Blob&&_.FileReader&&_.DataView&&t.addRuntime("html5",e),e}),t("runtime/html5/blob",["runtime/html5/runtime","lib/blob"],function(e,i){return e.register("Blob",{slice:function(e,t){var n=this.owner.source;return n=(n.slice||n.webkitSlice||n.mozSlice).call(n,e,t),new i(this.getRuid(),n)}})}),t("runtime/html5/dnd",["base","runtime/html5/runtime","lib/file"],function(d,e,r){var o=d.$,a="webuploader-dnd-";return e.register("DragAndDrop",{init:function(){var e=this.elem=this.options.container;this.dragEnterHandler=d.bindFn(this._dragEnterHandler,this),this.dragOverHandler=d.bindFn(this._dragOverHandler,this),this.dragLeaveHandler=d.bindFn(this._dragLeaveHandler,this),this.dropHandler=d.bindFn(this._dropHandler,this),this.dndOver=!1,e.on("dragenter",this.dragEnterHandler),e.on("dragover",this.dragOverHandler),e.on("dragleave",this.dragLeaveHandler),e.on("drop",this.dropHandler),this.options.disableGlobalDnd&&(o(document).on("dragover",this.dragOverHandler),o(document).on("drop",this.dropHandler))},_dragEnterHandler:function(e){var t,n=this,i=n._denied||!1;return e=e.originalEvent||e,n.dndOver||(n.dndOver=!0,(t=e.dataTransfer.items)&&t.length&&(n._denied=i=!n.trigger("accept",t)),n.elem.addClass(a+"over"),n.elem[i?"addClass":"removeClass"](a+"denied")),e.dataTransfer.dropEffect=i?"none":"copy",!1},_dragOverHandler:function(e){var t=this.elem.parent().get(0);return t&&!o.contains(t,e.currentTarget)||(clearTimeout(this._leaveTimer),this._dragEnterHandler.call(this,e)),!1},_dragLeaveHandler:function(){var e,t=this;return e=function(){t.dndOver=!1,t.elem.removeClass(a+"over "+a+"denied")},clearTimeout(t._leaveTimer),t._leaveTimer=setTimeout(e,100),!1},_dropHandler:function(e){var t=this,n=t.getRuid(),i=t.elem.parent().get(0);return i&&!o.contains(i,e.currentTarget)||(t._getTansferFiles(e,function(e){t.trigger("drop",o.map(e,function(e){return new r(n,e)}))}),t.dndOver=!1,t.elem.removeClass(a+"over")),!1},_getTansferFiles:function(e,t){var n,i,r,o,a,s,u,c,l=[],f=[];for(n=(r=(e=e.originalEvent||e).dataTransfer).items,i=r.files,c=!(!n||!n[0].webkitGetAsEntry),s=0,u=i.length;s<u;s++)o=i[s],a=n&&n[s],c&&a.webkitGetAsEntry().isDirectory?f.push(this._traverseDirectoryTree(a.webkitGetAsEntry(),l)):l.push(o);d.when.apply(d,f).done(function(){l.length&&t(l)})},_traverseDirectoryTree:function(e,o){var a=d.Deferred(),s=this;return e.isFile?e.file(function(e){o.push(e),a.resolve()}):e.isDirectory&&e.createReader().readEntries(function(e){var t,n=e.length,i=[],r=[];for(t=0;t<n;t++)i.push(s._traverseDirectoryTree(e[t],r));d.when.apply(d,i).then(function(){o.push.apply(o,r),a.resolve()},a.reject)}),a.promise()},destroy:function(){var e=this.elem;e.off("dragenter",this.dragEnterHandler),e.off("dragover",this.dragEnterHandler),e.off("dragleave",this.dragLeaveHandler),e.off("drop",this.dropHandler),this.options.disableGlobalDnd&&(o(document).off("dragover",this.dragOverHandler),o(document).off("drop",this.dropHandler))}})}),t("runtime/html5/filepaste",["base","runtime/html5/runtime","lib/file"],function(s,e,u){return e.register("FilePaste",{init:function(){var e,t,n,i,r=this.options,o=this.elem=r.container,a=".*";if(r.accept){for(e=[],t=0,n=r.accept.length;t<n;t++)(i=r.accept[t].mimeTypes)&&e.push(i);e.length&&(a=(a=e.join(",")).replace(/,/g,"|").replace(/\*/g,".*"))}this.accept=a=new RegExp(a,"i"),this.hander=s.bindFn(this._pasteHander,this),o.on("paste",this.hander)},_pasteHander:function(e){var t,n,i,r,o,a=[],s=this.getRuid();for(r=0,o=(t=(e=e.originalEvent||e).clipboardData.items).length;r<o;r++)"file"===(n=t[r]).kind&&(i=n.getAsFile())&&a.push(new u(s,i));a.length&&(e.preventDefault(),e.stopPropagation(),this.trigger("paste",a))},destroy:function(){this.elem.off("paste",this.hander)}})}),t("runtime/html5/filepicker",["base","runtime/html5/runtime"],function(e,t){var l=e.$;return t.register("FilePicker",{init:function(){var e,t,n,i,r=this.getRuntime().getContainer(),o=this,a=o.owner,s=o.options,u=l(document.createElement("label")),c=l(document.createElement("input"));if(c.attr("type","file"),c.attr("name",s.name),c.addClass("webuploader-element-invisible"),u.on("click",function(){c.trigger("click")}),u.css({opacity:0,width:"100%",height:"100%",display:"block",cursor:"pointer",background:"#ffffff"}),s.multiple&&c.attr("multiple","multiple"),s.accept&&0<s.accept.length){for(e=[],t=0,n=s.accept.length;t<n;t++)e.push(s.accept[t].mimeTypes);c.attr("accept",e.join(","))}r.append(c),r.append(u),i=function(e){a.trigger(e.type)},c.on("change",function(e){var t,n=arguments.callee;o.files=e.target.files,t=this.cloneNode(!0),this.parentNode.replaceChild(t,this),c.off(),c=l(t).on("change",n).on("mouseenter mouseleave",i),a.trigger("change")}),u.on("mouseenter mouseleave",i)},getFiles:function(){return this.files},destroy:function(){}})}),t("runtime/html5/util",["base"],function(e){var t=_.createObjectURL&&_||_.URL&&URL.revokeObjectURL&&URL||_.webkitURL,n=e.noop,i=n;return t&&(n=function(){return t.createObjectURL.apply(t,arguments)},i=function(){return t.revokeObjectURL.apply(t,arguments)}),{createObjectURL:n,revokeObjectURL:i,dataURL2Blob:function(e){var t,n,i,r,o,a;for(t=(~(a=e.split(","))[0].indexOf("base64")?atob:decodeURIComponent)(a[1]),i=new ArrayBuffer(t.length),n=new Uint8Array(i),r=0;r<t.length;r++)n[r]=t.charCodeAt(r);return o=a[0].split(":")[1].split(";")[0],this.arrayBufferToBlob(i,o)},dataURL2ArrayBuffer:function(e){var t,n,i,r;for(t=(~(r=e.split(","))[0].indexOf("base64")?atob:decodeURIComponent)(r[1]),n=new Uint8Array(t.length),i=0;i<t.length;i++)n[i]=t.charCodeAt(i);return n.buffer},arrayBufferToBlob:function(e,t){var n,i=_.BlobBuilder||_.WebKitBlobBuilder;return i?((n=new i).append(e),n.getBlob(t)):new Blob([e],t?{type:t}:{})},canvasToDataUrl:function(e,t,n){return e.toDataURL(t,n/100)},parseMeta:function(e,t){t(!1,{})},updateImageHead:function(e){return e}}}),t("runtime/html5/imagemeta",["runtime/html5/util"],function(e){var f;return f={parsers:{65505:[]},maxMetaDataSize:262144,parse:function(e,t){var n=this,i=new FileReader;i.onload=function(){t(!1,n._parse(this.result)),i=i.onload=i.onerror=null},i.onerror=function(e){t(e.message),i=i.onload=i.onerror=null},e=e.slice(0,n.maxMetaDataSize),i.readAsArrayBuffer(e.getSource())},_parse:function(e,t){if(!(e.byteLength<6)){var n,i,r,o,a=new DataView(e),s=2,u=a.byteLength-4,c=s,l={};if(65496===a.getUint16(0)){for(;s<u&&(65504<=(n=a.getUint16(s))&&n<=65519||65534===n)&&!(s+(i=a.getUint16(s+2)+2)>a.byteLength);){if(r=f.parsers[n],!t&&r)for(o=0;o<r.length;o+=1)r[o].call(f,a,s,i,l);c=s+=i}6<c&&(e.slice?l.imageHead=e.slice(2,c):l.imageHead=new Uint8Array(e).subarray(2,c))}return l}},updateImageHead:function(e,t){var n,i,r,o=this._parse(e,!0);return r=2,o.imageHead&&(r=2+o.imageHead.byteLength),i=e.slice?e.slice(r):new Uint8Array(e).subarray(r),(n=new Uint8Array(t.byteLength+2+i.byteLength))[0]=255,n[1]=216,n.set(new Uint8Array(t),2),n.set(new Uint8Array(i),t.byteLength+2),n.buffer}},e.parseMeta=function(){return f.parse.apply(f,arguments)},e.updateImageHead=function(){return f.updateImageHead.apply(f,arguments)},f}),t("runtime/html5/imagemeta/exif",["base","runtime/html5/imagemeta"],function(h,e){var p={ExifMap:function(){return this}};return p.ExifMap.prototype.map={Orientation:274},p.ExifMap.prototype.get=function(e){return this[e]||this[this.map[e]]},p.exifTagTypes={1:{getValue:function(e,t){return e.getUint8(t)},size:1},2:{getValue:function(e,t){return String.fromCharCode(e.getUint8(t))},size:1,ascii:!0},3:{getValue:function(e,t,n){return e.getUint16(t,n)},size:2},4:{getValue:function(e,t,n){return e.getUint32(t,n)},size:4},5:{getValue:function(e,t,n){return e.getUint32(t,n)/e.getUint32(t+4,n)},size:8},9:{getValue:function(e,t,n){return e.getInt32(t,n)},size:4},10:{getValue:function(e,t,n){return e.getInt32(t,n)/e.getInt32(t+4,n)},size:8}},p.exifTagTypes[7]=p.exifTagTypes[1],p.getExifValue=function(e,t,n,i,r,o){var a,s,u,c,l,f,d=p.exifTagTypes[i];if(d){if(!((s=4<(a=d.size*r)?t+e.getUint32(n+8,o):n+8)+a>e.byteLength)){if(1===r)return d.getValue(e,s,o);for(u=[],c=0;c<r;c+=1)u[c]=d.getValue(e,s+c*d.size,o);if(d.ascii){for(l="",c=0;c<u.length&&"\0"!==(f=u[c]);c+=1)l+=f;return l}return u}h.log("Invalid Exif data: Invalid data offset.")}else h.log("Invalid Exif data: Invalid tag type.")},p.parseExifTag=function(e,t,n,i,r){var o=e.getUint16(n,i);r.exif[o]=p.getExifValue(e,t,n,e.getUint16(n+2,i),e.getUint32(n+4,i),i)},p.parseExifTags=function(e,t,n,i,r){var o,a,s;if(n+6>e.byteLength)h.log("Invalid Exif data: Invalid directory offset.");else{if(!((a=n+2+12*(o=e.getUint16(n,i)))+4>e.byteLength)){for(s=0;s<o;s+=1)this.parseExifTag(e,t,n+2+12*s,i,r);return e.getUint32(a,i)}h.log("Invalid Exif data: Invalid directory size.")}},p.parseExifData=function(e,t,n,i){var r,o,a=t+10;if(1165519206===e.getUint32(t+4))if(a+8>e.byteLength)h.log("Invalid Exif data: Invalid segment size.");else if(0===e.getUint16(t+8)){switch(e.getUint16(a)){case 18761:r=!0;break;case 19789:r=!1;break;default:return void h.log("Invalid Exif data: Invalid byte alignment marker.")}42===e.getUint16(a+2,r)?(o=e.getUint32(a+4,r),i.exif=new p.ExifMap,o=p.parseExifTags(e,a,a+o,r,i)):h.log("Invalid Exif data: Missing TIFF marker.")}else h.log("Invalid Exif data: Missing byte alignment offset.")},e.parsers[65505].push(p.parseExifData),p}),t("runtime/html5/jpegencoder",[],function(e,t,n){function i(e){Math.round;var R,E,k,F,t,f=Math.floor,T=new Array(64),D=new Array(64),S=new Array(64),A=new Array(64),v=new Array(65535),b=new Array(65535),K=new Array(64),_=new Array(64),O=[],U=0,I=7,C=new Array(64),q=new Array(64),L=new Array(64),n=new Array(256),H=new Array(2048),y=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],M=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],j=[0,1,2,3,4,5,6,7,8,9,10,11],B=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],P=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],z=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],$=[0,1,2,3,4,5,6,7,8,9,10,11],N=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],V=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function i(e,t){for(var n=0,i=0,r=new Array,o=1;o<=16;o++){for(var a=1;a<=e[o];a++)r[t[i]]=[],r[t[i]][0]=n,r[t[i]][1]=o,i++,n++;n*=2}return r}function Q(e){for(var t=e[0],n=e[1]-1;0<=n;)t&1<<n&&(U|=1<<I),n--,--I<0&&(255==U?(G(255),G(0)):G(U),I=7,U=0)}function G(e){O.push(n[e])}function J(e){G(e>>8&255),G(255&e)}function W(e,t,n,i,r){for(var o,a=r[0],s=r[240],u=function(e,t){var n,i,r,o,a,s,u,c,l,f,d=0;for(l=0;l<8;++l){n=e[d],i=e[d+1],r=e[d+2],o=e[d+3],a=e[d+4],s=e[d+5],u=e[d+6];var h=n+(c=e[d+7]),p=n-c,g=i+u,m=i-u,v=r+s,b=r-s,_=o+a,y=o-a,w=h+_,x=h-_,R=g+v,E=g-v;e[d]=w+R,e[d+4]=w-R;var k=.707106781*(E+x);e[d+2]=x+k,e[d+6]=x-k;var F=.382683433*((w=y+b)-(E=m+p)),T=.5411961*w+F,D=1.306562965*E+F,S=.707106781*(R=b+m),A=p+S,O=p-S;e[d+5]=O+T,e[d+3]=O-T,e[d+1]=A+D,e[d+7]=A-D,d+=8}for(l=d=0;l<8;++l){n=e[d],i=e[d+8],r=e[d+16],o=e[d+24],a=e[d+32],s=e[d+40],u=e[d+48];var U=n+(c=e[d+56]),I=n-c,C=i+u,q=i-u,L=r+s,H=r-s,M=o+a,j=o-a,B=U+M,P=U-M,z=C+L,$=C-L;e[d]=B+z,e[d+32]=B-z;var N=.707106781*($+P);e[d+16]=P+N,e[d+48]=P-N;var V=.382683433*((B=j+H)-($=q+I)),Q=.5411961*B+V,G=1.306562965*$+V,J=.707106781*(z=H+q),W=I+J,X=I-J;e[d+40]=X+Q,e[d+24]=X-Q,e[d+8]=W+G,e[d+56]=W-G,d++}for(l=0;l<64;++l)f=e[l]*t[l],K[l]=0<f?.5+f|0:f-.5|0;return K}(e,t),c=0;c<64;++c)_[y[c]]=u[c];var l=_[0]-n;n=_[0],0==l?Q(i[0]):(Q(i[b[o=32767+l]]),Q(v[o]));for(var f=63;0<f&&0==_[f];f--);if(0==f)return Q(a),n;for(var d,h=1;h<=f;){for(var p=h;0==_[h]&&h<=f;++h);var g=h-p;if(16<=g){d=g>>4;for(var m=1;m<=d;++m)Q(s);g&=15}o=32767+_[h],Q(r[(g<<4)+b[o]]),Q(v[o]),h++}return 63!=f&&Q(a),n}function X(e){e<=0&&(e=1),100<e&&(e=100),t!=e&&(function(e){for(var t=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;n<64;n++){var i=f((t[n]*e+50)/100);i<1?i=1:255<i&&(i=255),T[y[n]]=i}for(var r=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],o=0;o<64;o++){var a=f((r[o]*e+50)/100);a<1?a=1:255<a&&(a=255),D[y[o]]=a}for(var s=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],u=0,c=0;c<8;c++)for(var l=0;l<8;l++)S[u]=1/(T[y[u]]*s[c]*s[l]*8),A[u]=1/(D[y[u]]*s[c]*s[l]*8),u++}(e<50?Math.floor(5e3/e):Math.floor(200-2*e)),t=e)}this.encode=function(e,t){var n,i;t&&X(t),O=new Array,U=0,I=7,J(65496),J(65504),J(16),G(74),G(70),G(73),G(70),G(0),G(1),G(1),G(0),J(1),J(1),G(0),G(0),function(){J(65499),J(132),G(0);for(var e=0;e<64;e++)G(T[e]);G(1);for(var t=0;t<64;t++)G(D[t])}(),n=e.width,i=e.height,J(65472),J(17),G(8),J(i),J(n),G(3),G(1),G(17),G(0),G(2),G(17),G(1),G(3),G(17),G(1),function(){J(65476),J(418),G(0);for(var e=0;e<16;e++)G(M[e+1]);for(var t=0;t<=11;t++)G(j[t]);G(16);for(var n=0;n<16;n++)G(B[n+1]);for(var i=0;i<=161;i++)G(P[i]);G(1);for(var r=0;r<16;r++)G(z[r+1]);for(var o=0;o<=11;o++)G($[o]);G(17);for(var a=0;a<16;a++)G(N[a+1]);for(var s=0;s<=161;s++)G(V[s])}(),J(65498),J(12),G(3),G(1),G(0),G(2),G(17),G(3),G(17),G(0),G(63),G(0);var r=0,o=0,a=0;U=0,I=7,this.encode.displayName="_encode_";for(var s,u,c,l,f,d,h,p,g,m=e.data,v=e.width,b=e.height,_=4*v,y=0;y<b;){for(s=0;s<_;){for(d=f=_*y+s,h=-1,g=p=0;g<64;g++)d=f+(p=g>>3)*_+(h=4*(7&g)),b<=y+p&&(d-=_*(y+1+p-b)),_<=s+h&&(d-=s+h-_+4),u=m[d++],c=m[d++],l=m[d++],C[g]=(H[u]+H[c+256>>0]+H[l+512>>0]>>16)-128,q[g]=(H[u+768>>0]+H[c+1024>>0]+H[l+1280>>0]>>16)-128,L[g]=(H[u+1280>>0]+H[c+1536>>0]+H[l+1792>>0]>>16)-128;r=W(C,S,r,R,k),o=W(q,A,o,E,F),a=W(L,A,a,E,F),s+=32}y+=8}if(0<=I){var w=[];w[1]=I+1,w[0]=(1<<I+1)-1,Q(w)}J(65497);var x="data:image/jpeg;base64,"+btoa(O.join(""));return O=[],x},e=e||50,function(){for(var e=String.fromCharCode,t=0;t<256;t++)n[t]=e(t)}(),R=i(M,j),E=i(z,$),k=i(B,P),F=i(N,V),function(){for(var e=1,t=2,n=1;n<=15;n++){for(var i=e;i<t;i++)b[32767+i]=n,v[32767+i]=[],v[32767+i][1]=n,v[32767+i][0]=i;for(var r=-(t-1);r<=-e;r++)b[32767+r]=n,v[32767+r]=[],v[32767+r][1]=n,v[32767+r][0]=t-1+r;e<<=1,t<<=1}}(),function(){for(var e=0;e<256;e++)H[e]=19595*e,H[e+256>>0]=38470*e,H[e+512>>0]=7471*e+32768,H[e+768>>0]=-11059*e,H[e+1024>>0]=-21709*e,H[e+1280>>0]=32768*e+8421375,H[e+1536>>0]=-27439*e,H[e+1792>>0]=-5329*e}(),X(e)}return i.encode=function(e,t){return new i(t).encode(e)},i}),t("runtime/html5/androidpatch",["runtime/html5/util","runtime/html5/jpegencoder","base"],function(e,u,c){var l,f=e.canvasToDataUrl;e.canvasToDataUrl=function(e,t,n){var i,r,o,a,s;return c.os.android?("image/jpeg"===t&&void 0===l&&(a=(a=(~(s=(a=f.apply(null,arguments)).split(","))[0].indexOf("base64")?atob:decodeURIComponent)(s[1])).substring(0,2),l=255===a.charCodeAt(0)&&216===a.charCodeAt(1)),"image/jpeg"!==t||l?f.apply(null,arguments):(r=e.width,o=e.height,i=e.getContext("2d"),u.encode(i.getImageData(0,0,r,o),n))):f.apply(null,arguments)}}),t("runtime/html5/image",["base","runtime/html5/runtime","runtime/html5/util"],function(e,t,r){return t.register("Image",{modified:!1,init:function(){var n=this,e=new Image;e.onload=function(){n._info={type:n.type,width:this.width,height:this.height},n._metas||"image/jpeg"!==n.type?n.owner.trigger("load"):r.parseMeta(n._blob,function(e,t){n._metas=t,n.owner.trigger("load")})},e.onerror=function(){n.owner.trigger("error")},n._img=e},loadFromBlob:function(e){var t=this._img;this._blob=e,this.type=e.type,t.src=r.createObjectURL(e.getSource()),this.owner.once("load",function(){r.revokeObjectURL(t.src)})},resize:function(e,t){var n=this._canvas||(this._canvas=document.createElement("canvas"));this._resize(this._img,n,e,t),this._blob=null,this.modified=!0,this.owner.trigger("complete")},getAsBlob:function(e){var t,n=this._blob,i=this.options;if(e=e||this.type,this.modified||this.type!==e){if(t=this._canvas,"image/jpeg"===e){if(n=r.canvasToDataUrl(t,"image/jpeg",i.quality),i.preserveHeaders&&this._metas&&this._metas.imageHead)return n=r.dataURL2ArrayBuffer(n),n=r.updateImageHead(n,this._metas.imageHead),n=r.arrayBufferToBlob(n,e)}else n=r.canvasToDataUrl(t,e);n=r.dataURL2Blob(n)}return n},getAsDataUrl:function(e){var t=this.options;return"image/jpeg"===(e=e||this.type)?r.canvasToDataUrl(this._canvas,e,t.quality):this._canvas.toDataURL(e)},getOrientation:function(){return this._metas&&this._metas.exif&&this._metas.exif.get("Orientation")||1},info:function(e){return e?(this._info=e,this):this._info},meta:function(e){return e?(this._meta=e,this):this._meta},destroy:function(){var e=this._canvas;this._img.onload=null,e&&(e.getContext("2d").clearRect(0,0,e.width,e.height),e.width=e.height=0,this._canvas=null),this._img.src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs%3D",this._img=this._blob=null},_resize:function(e,t,n,i){var r,o,a,s,u,c=this.options,l=e.width,f=e.height,d=this.getOrientation();~[5,6,7,8].indexOf(d)&&(n^=i,n^=i^=n),r=Math[c.crop?"max":"min"](n/l,i/f),c.allowMagnify||(r=Math.min(1,r)),o=l*r,a=f*r,c.crop?(t.width=n,t.height=i):(t.width=o,t.height=a),s=(t.width-o)/2,u=(t.height-a)/2,c.preserveHeaders||this._rotate2Orientaion(t,d),this._renderImageToCanvas(t,e,s,u,o,a)},_rotate2Orientaion:function(e,t){var n=e.width,i=e.height,r=e.getContext("2d");switch(t){case 5:case 6:case 7:case 8:e.width=i,e.height=n}switch(t){case 2:r.translate(n,0),r.scale(-1,1);break;case 3:r.translate(n,i),r.rotate(Math.PI);break;case 4:r.translate(0,i),r.scale(1,-1);break;case 5:r.rotate(.5*Math.PI),r.scale(1,-1);break;case 6:r.rotate(.5*Math.PI),r.translate(0,-i);break;case 7:r.rotate(.5*Math.PI),r.translate(n,-i),r.scale(-1,1);break;case 8:r.rotate(-.5*Math.PI),r.translate(-n,0)}},_renderImageToCanvas:function(){if(!e.os.ios)return function(e,t,n,i,r,o){e.getContext("2d").drawImage(t,n,i,r,o)};function k(e,t,n){var i,r,o=document.createElement("canvas"),a=o.getContext("2d"),s=0,u=n,c=n;for(o.width=1,o.height=n,a.drawImage(e,0,0),i=a.getImageData(0,0,1,n).data;s<c;)0===i[4*(c-1)+3]?u=c:s=c,c=u+s>>1;return 0==(r=c/n)?1:r}return 7<=e.os.ios?function(e,t,n,i,r,o){var a=t.naturalWidth,s=t.naturalHeight,u=k(t,0,s);return e.getContext("2d").drawImage(t,0,0,a*u,s*u,n,i,r,o)}:function(e,t,n,i,r,o){var a,s,u,c,l,f,d,h,p,g,m,v=t.naturalWidth,b=t.naturalHeight,_=e.getContext("2d"),y=1048576<(m=(h=t).naturalWidth)*h.naturalHeight&&((p=document.createElement("canvas")).width=p.height=1,(g=p.getContext("2d")).drawImage(h,1-m,0),0===g.getImageData(0,0,1,1).data[3]),w="image/jpeg"===this.type,x=1024,R=0,E=0;for(y&&(v/=2,b/=2),_.save(),(a=document.createElement("canvas")).width=a.height=x,s=a.getContext("2d"),u=w?k(t,0,b):1,c=Math.ceil(x*r/v),l=Math.ceil(x*o/b/u);R<b;){for(d=f=0;f<v;)s.clearRect(0,0,x,x),s.drawImage(t,-f,-R),_.drawImage(a,0,0,x,x,n+d,i+E,c,l),f+=x,d+=c;R+=x,E+=l}_.restore(),a=s=null}}()})}),t("runtime/html5/transport",["base","runtime/html5/runtime"],function(u,e){var t=u.noop,c=u.$;return e.register("Transport",{init:function(){this._status=0,this._response=null},send:function(){var n,e,t,i=this.owner,r=this.options,o=this._initAjax(),a=i._blob,s=r.server;r.sendAsBinary?(s+=(/\?/.test(s)?"&":"?")+c.param(i._formData),e=a.getSource()):(n=new FormData,c.each(i._formData,function(e,t){n.append(e,t)}),n.append(r.fileVal,a.getSource(),r.filename||i._formData.name||"")),r.withCredentials&&"withCredentials"in o?(o.open(r.method,s,!0),o.withCredentials=!0):o.open(r.method,s),this._setRequestHeader(o,r.headers),e?(o.overrideMimeType("application/octet-stream"),u.os.android?((t=new FileReader).onload=function(){o.send(this.result),t=t.onload=null},t.readAsArrayBuffer(e)):o.send(e)):o.send(n)},getResponse:function(){return this._response},getResponseAsJson:function(){return this._parseJson(this._response)},getStatus:function(){return this._status},abort:function(){var e=this._xhr;e&&(e.upload.onprogress=t,e.onreadystatechange=t,e.abort(),this._xhr=e=null)},destroy:function(){this.abort()},_initAjax:function(){var n=this,e=new XMLHttpRequest;return!this.options.withCredentials||"withCredentials"in e||"undefined"==typeof XDomainRequest||(e=new XDomainRequest),e.upload.onprogress=function(e){var t=0;return e.lengthComputable&&(t=e.loaded/e.total),n.trigger("progress",t)},e.onreadystatechange=function(){if(4===e.readyState)return e.upload.onprogress=t,e.onreadystatechange=t,n._xhr=null,n._status=e.status,200<=e.status&&e.status<300?(n._response=e.responseText,n.trigger("load")):500<=e.status&&e.status<600?(n._response=e.responseText,n.trigger("error","server")):n.trigger("error",n._status?"http":"abort")},n._xhr=e},_setRequestHeader:function(n,e){c.each(e,function(e,t){n.setRequestHeader(e,t)})},_parseJson:function(e){var t;try{t=JSON.parse(e)}catch(e){t={}}return t}})}),t("runtime/flash/runtime",["base","runtime/runtime","runtime/compbase"],function(u,n,i){var r=u.$,c={};function e(){var o={},a={},e=this.destory,s=this,t=u.guid("webuploader_");n.apply(s,arguments),s.type="flash",s.exec=function(e,t){var n,i=this.uid,r=u.slice(arguments,2);return a[i]=this,c[e]&&(o[i]||(o[i]=new c[e](this,s)),(n=o[i])[t])?n[t].apply(n,r):s.flashExec.apply(this,arguments)},_[t]=function(){var e=arguments;setTimeout(function(){(function(e,t){var n,i,r=e.type||e;i=(n=r.split("::"))[0],"Ready"===(r=n[1])&&i===s.uid?s.trigger("ready"):a[i]&&a[i].trigger(r.toLowerCase(),e,t)}).apply(null,e)},1)},this.jsreciver=t,this.destory=function(){return e&&e.apply(this,arguments)},this.flashExec=function(e,t){var n=s.getFlash(),i=u.slice(arguments,2);return n.exec(this.uid,e,t,i)}}return u.inherits(n,{constructor:e,init:function(){var e,t=this.getContainer(),n=this.options;t.css({position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),e='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+n.swf+'" ',u.browser.ie&&(e+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),e+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+n.swf+'" /><param name="flashvars" value="uid='+this.uid+"&jsreciver="+this.jsreciver+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>',t.html(e)},getFlash:function(){return this._flash||(this._flash=r("#"+this.uid).get(0)),this._flash}}),e.register=function(e,t){return t=c[e]=u.inherits(i,r.extend({flashExec:function(){var e=this.owner;return this.getRuntime().flashExec.apply(e,arguments)}},t))},11.4<=function(){var t;try{t=(t=navigator.plugins["Shockwave Flash"]).description}catch(e){try{t=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(e){t="0.0"}}return t=t.match(/\d+/g),parseFloat(t[0]+"."+t[1],10)}()&&n.addRuntime("flash",e),e}),t("runtime/flash/filepicker",["base","runtime/flash/runtime"],function(e,t){var r=e.$;return t.register("FilePicker",{init:function(e){var t,n,i=r.extend({},e);for(t=i.accept&&i.accept.length,n=0;n<t;n++)i.accept[n].title||(i.accept[n].title="Files");delete i.button,delete i.container,this.flashExec("FilePicker","init",i)},destroy:function(){}})}),t("runtime/flash/image",["runtime/flash/runtime"],function(e){return e.register("Image",{loadFromBlob:function(e){var t=this.owner;t.info()&&this.flashExec("Image","info",t.info()),t.meta()&&this.flashExec("Image","meta",t.meta()),this.flashExec("Image","loadFromBlob",e.uid)}})}),t("runtime/flash/transport",["base","runtime/flash/runtime","runtime/client"],function(e,t,r){var a=e.$;return t.register("Transport",{init:function(){this._status=0,this._response=null,this._responseJson=null},send:function(){var e,t=this.owner,n=this.options,i=this._initAjax(),r=t._blob,o=n.server;i.connectRuntime(r.ruid),n.sendAsBinary?(o+=(/\?/.test(o)?"&":"?")+a.param(t._formData),e=r.uid):(a.each(t._formData,function(e,t){i.exec("append",e,t)}),i.exec("appendBlob",n.fileVal,r.uid,n.filename||t._formData.name||"")),this._setRequestHeader(i,n.headers),i.exec("send",{method:n.method,url:o},e)},getStatus:function(){return this._status},getResponse:function(){return this._response},getResponseAsJson:function(){return this._responseJson},abort:function(){var e=this._xhr;e&&(e.exec("abort"),e.destroy(),this._xhr=e=null)},destroy:function(){this.abort()},_initAjax:function(){var n=this,i=new r("XMLHttpRequest");return i.on("uploadprogress progress",function(e){return n.trigger("progress",e.loaded/e.total)}),i.on("load",function(){var e=i.exec("getStatus"),t="";return i.off(),n._xhr=null,200<=e&&e<300?(n._response=i.exec("getResponse"),n._responseJson=i.exec("getResponseAsJson")):t=500<=e&&e<600?(n._response=i.exec("getResponse"),n._responseJson=i.exec("getResponseAsJson"),"server"):"http",i.destroy(),i=null,t?n.trigger("error",t):n.trigger("load")}),i.on("error",function(){i.off(),n._xhr=null,n.trigger("error","http")}),n._xhr=i},_setRequestHeader:function(n,e){a.each(e,function(e,t){n.exec("setRequestHeader",e,t)})}})}),t("preset/all",["base","widgets/filednd","widgets/filepaste","widgets/filepicker","widgets/image","widgets/queue","widgets/runtime","widgets/upload","widgets/validator","runtime/html5/blob","runtime/html5/dnd","runtime/html5/filepaste","runtime/html5/filepicker","runtime/html5/imagemeta/exif","runtime/html5/androidpatch","runtime/html5/image","runtime/html5/transport","runtime/flash/filepicker","runtime/flash/image","runtime/flash/transport"],function(e){return e}),t("webuploader",["preset/all"],function(e){return e}),i("webuploader"));!function(e){var t,n,i,r,o,a;for(t in a=function(e){return e&&e.charAt(0).toUpperCase()+e.substr(1)},s)if(n=e,s.hasOwnProperty(t)){for(o=a((i=t.split("/")).pop());r=a(i.shift());)n[r]=n[r]||{},n=n[r];n[o]=s[t]}}(u),"object"==typeof module&&"object"==typeof module.exports?module.exports=u:"function"==typeof define&&define.amd?define([],u):(e=n.WebUploader,n.WebUploader=u,n.WebUploader.noConflict=function(){n.WebUploader=e})}(this);