<template>
  <div v-show="true" ref="dialogEl" v-lock-style="dialogStyle" class="persistent-dialog">
    <!-- 新俱乐部合同 -->
    <el-dialog
      title="签署合同"
      class="sign-code"
      :visible="signCodeVisible === null ? false : !signCodeVisible"
      width="50%"
      :show-close="isValid ? true : false"
      :modal="false"
      :before-close="fakeClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-divider></el-divider>
      <el-row v-if="isValid" type="flex" justify="center" class="red">
        我们将在&nbsp;&nbsp;{{ daysDiff }}&nbsp;&nbsp;天后关闭您的学习管理系统使用权限，请尽快完成以下全部合同签署，以免影响后续使用
      </el-row>
      <!-- 步骤条 -->
      <el-row class="mt-4" v-if="titleList.length > 1">
        <el-col :span="20" :offset="2" style="text-align: left">
          <el-steps :active="isAllSignAll ? QRcodeIndex + 2 : QRcodeIndex + 1" process-status="wait" :finish-status="isAllSignAll ? 'success' : 'finish'">
            <el-step v-for="(item, index) in titleList" :key="index" :icon="QRcodeIndex > index && !isAllSignAll ? 'el-icon-circle-check' : ''">
              <template slot="description">{{ QRcodeIndex > index ? '已完成' : item.tips }}</template>
              <template slot="icon" style="background-color: #fff">
                <i :class="QRcodeIndex > index ? 'el-icon-circle-check' : ''">{{ QRcodeIndex <= index ? index + 1 : '' }}</i>
                {{ QRcodeIndex > index ? '已签署' : '请签署' }}
              </template>
            </el-step>
            <!-- <el-step :icon="QRcodeIndex + 1 > 2 ? 'el-icon-circle-check' : ''">
            <template slot="description">请完成《学习管理系统合同》签署</template>
          </el-step>
          <el-step :icon="QRcodeIndex + 1 > 3 ? 'el-icon-circle-check' : ''">
            <template slot="description">请完成《课程推广合作协议》签署</template>
          </el-step> -->
          </el-steps>
        </el-col>
      </el-row>
      <!-- 合同 -->
      <el-row type="flex" justify="center">
        <el-col :span="10" style="text-align: center" class="mt-4" v-for="item in QRcodeList[!isAllSignAll ? QRcodeIndex : QRcodeList.length - 1]">
          <p style="line-height: 30px">
            {{ item.tips }}
            <br />
            {{ item.title }}
          </p>
          <div class="QR_code" v-show="!codeLoading">
            <!-- <div class="QR_code" v-loading="codeLoading"> -->
            <div :class="['QR_code_success', { deactive: !item.isSign }]">
              <i class="el-icon-success"></i>
              <p>签署成功</p>
            </div>
            <el-tooltip class="item" effect="dark" content="点击复制签署链接">
              <!-- <img
                @error="retry($event)"
                src="https://document.dxznjy.com/course/bf188f4751c84e94b0678b463f7054a4.png"
                @click="fallbackCopyText(item.signUrl ? item.signUrl : item.img)"
              /> -->
              <img @error="retry($event)" :src="item.img" @click="fallbackCopyText(item.signUrl ? item.signUrl : item.img)" />
            </el-tooltip>
          </div>
        </el-col>
      </el-row>
      <!-- 操作 -->
      <el-row type="flex" justify="center" class="mt-2">
        <el-button v-if="!isAllSignAll" type="primary" @click="getEsignStatus(true)">
          {{ QRcodeIndex < QRcodeList.length - 1 ? '下一步' : '签署完成' }}
        </el-button>
        <el-button v-if="isAllSignAll" type="primary" @click="fakeClose">返回</el-button>
      </el-row>
      <!-- 提示 -->
      <el-row class="mt-2 info" v-if="!isAllSignAll">全部合同签署成功后，如未跳转，请点击下一步按钮</el-row>
      <el-row class="mt-2 info" v-else-if="isAllSignAll">{{ surplustime }}秒后自动关闭</el-row>
    </el-dialog>
    <!-- 签署合同代码 -->
    <!-- 老俱乐部老门店 -->
    <el-dialog
      title="签署合同"
      class="sign-code"
      :visible.sync="signCodeVisible === null ? false : signCodeVisible"
      width="25%"
      :show-close="isValid"
      :modal="false"
      :before-close="fakeClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-divider></el-divider>
      <el-row type="flex" justify="center" v-if="isValid">
        我们将在&nbsp;&nbsp;{{ daysDiff == 0 ? '今' : daysDiff + 1 }}&nbsp;&nbsp;天后关闭您的学习管理系统使用权限，请尽快完成
        <br />
        《鼎校智能学习管理系统经销商销售合同》签署，以免影响后续使用
      </el-row>
      <el-row type="flex" justify="center" v-if="!isValid">请完成《鼎校智能学习管理系统经销商销售合同》签署</el-row>
      <!-- 合同 -->
      <el-row type="flex" justify="center">
        <el-col :span="20" style="text-align: center" class="mt-2" v-for="item in QRcodeList[QRcodeIndex]">
          <div class="QR_code" v-loading="codeLoading">
            <!-- <div class="QR_code" v-loading="codeLoading"> -->
            <div :class="['QR_code_success', { deactive: !item.isSign }]">
              <i class="el-icon-success"></i>
              <p>签署成功</p>
            </div>
            <!-- 公测环境 -->
            <img @error="retry($event)" :src="item.img" />
            <!-- <img @error="retry($event)" @click="changeSign()" src="https://document.dxznjy.com/course/bf188f4751c84e94b0678b463f7054a4.png" /> -->
          </div>
        </el-col>
      </el-row>
      <!-- 操作及提示 -->
      <el-row type="flex" justify="center" class="mt-2">
        <el-button type="primary" @click="getSignStatus2(true)">签署完成</el-button>
      </el-row>
      <el-row class="mt-2 info">签署完成后，请点击该按钮</el-row>
    </el-dialog>
  </div>
</template>

<script>
  import checkPermission from '@/utils/permission';
  import orderApi from '@/api/dialog/index';
  import dayjs from 'dayjs';
  import ls from '@/api/sessionStorage';
  import dealerPaylist from '@/api/operationsPayment';
  let observer = null;

  export default {
    data() {
      // 数据劫持防止状态修改
      const data = {};
      Object.defineProperty(data, 'isVisible', {
        get: () => true,
        set: () => {}
      });

      return {
        flowId: '',
        templateType: '',
        fullscreenLoading: null, // 全屏loading
        retryCount: 3, // 图片加载失败重试次数
        signCodeVisible: null, // 签署合同代码弹窗
        codeLoading: false, // 加载中
        signLoading: false, // 签署合同加载
        surplustime: 0, // 剩余时间
        st: null, // 剩余时间定时器
        success: false, // 成功
        steps: 1, // 当前步骤
        daysDiff: null, // 剩余天数
        isValid: true, // 是否有效
        QRcodeIndex: 0, // 当前签署第几页合同 ，大于等于 合同列表-QRcodeList 的长度表示已经全部签署完成
        QRcodeTitleList: {}, // 学习管理系统合同信息
        // 合同列表
        titleList: [],
        // 合同列表
        QRcodeList: [],
        isVisible: true,
        // 样式 别动
        dialogStyle: {
          width: '100vw',
          height: '100vh',
          display: 'block !important',
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          padding: '20px',
          background: 'rgba(0,0,0,0.5)',
          boxShadow: '0 0 10px rgba(0,0,0,0.5)',
          'z-index': '2000'
        },
        ...data
      };
    },
    computed: {
      // 当前页 是否全部签署完成
      isAllSign() {
        if (this.QRcodeList.length === 0) {
          return false;
        }
        // 页码大于等于合同列表长度，表示已经全部签署完成
        if (this.QRcodeIndex >= this.QRcodeList.length) {
          return true;
        }
        // 本页没有合同，表示本页签署完成
        if (this.QRcodeList[this.QRcodeIndex].length === 0) {
          console.log('本页没有合同');
          return true;
        }
        return this.QRcodeList[this.QRcodeIndex].every((item) => item.isSign);
      },
      // 所有页 是否全部签署完成
      isAllSignAll() {
        // 当前页全部签署完成，并且当前页数大于等于合同列表长度
        return this.isAllSign && this.QRcodeIndex + 1 >= this.QRcodeList.length;
      }
    },
    watch: {
      // 监听当前页是否全部签署完成
      isAllSign() {
        if (this.isAllSign) {
          // this.$message.success('全部合同签署成功');
          if (this.st0) this.clearEsignInterval(this.st0); // 清空定时器
          if (this.signCodeVisible) return this.nextStep2(); // 新俱乐部 下一步
          if (this.signCodeVisible === null) return this.nextStep2(); // 没有打开弹窗 下一步
          this.nextStep(); // 其他下一步
        }
      }
    },
    mounted() {
      // 获取手机号
      this.mobile = window.localStorage.getItem('ZNYYName');
      // 初始化
      this.init();
      // 防止 DOM 被移除
      this.initMutationObserver();
      // 阻止组件销毁
      this.$once('hook:beforeDestroy', () => {
        this.$mount(document.body.appendChild(document.createElement('div')));
      });
    },

    methods: {
      async changeSign() {
        let data = {
          firstSignStatus: 2,
          secondSignStatus: 2,
          templateType: this.templateType,
          signSource: 1,
          flowId: this.flowId
        };
        const res = await dealerPaylist.compensationSignStatus(data);
        if (res.sccess) {
          this.$message.success('签署成功');
        }
      },
      // 初始化
      init() {
        const that = this;
        // 获取全部合同签署状态  -true.已签署 -其他.未签署
        console.log('获取全部合同签署状态');
        // TODO: 6.13隐藏需求更改，默认全部签署完成
        // start
        localStorage.setItem('AllSignatures', JSON.stringify(true));
        // end
        const status = JSON.parse(localStorage.getItem('AllSignatures') || 'false');
        console.log('status', status);

        if (status) {
          // 关闭弹窗
          return this.fakeClose();
        }
        let isOperations = checkPermission(['Operations']);
        let isSchool = checkPermission(['School']);
        // console.log(isOperations, isSchool, '71748585')
        // 其他角色关闭弹窗 保存签署状态
        if (!isOperations && !isSchool) {
          localStorage.setItem('AllSignatures', JSON.stringify(true)); // 保存签署状态
          return this.fakeClose(); // 关闭弹窗
        }
        this.fullscreenLoading = this.$loading({
          lock: true,
          text: '获取合同签署状态中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0)'
        });
        console.log('isOperations', isOperations, 'isSchool', isSchool);

        // 俱乐部(Operations)还是门店(Shool)
        // 判断新老俱乐部/门店
        orderApi
          .getClubType()
          .then((res) => {
            /**
             * isOld: 1老门店 0新门店
             * regTime: 注册时间
             */

            console.log('🚀 ~ orderApi.getClubType ~ res.data:', res.data);
            if (isOperations) {
              if (!res.data.isOld) {
                this.QRcodeList = [
                  [
                    {
                      isFirstParty: 1,
                      img: '',
                      tips: '您将作为乙方与所归属的品牌签署',
                      title: '《鼎校智能学习管理系统经销商销售合同》',
                      isSign: false
                    }
                  ]
                ];

                // 查询 学习系统合同 信息
                this.getContractStatus();
                // 判断是否在有效期内 / 弹窗是否可以关闭;
                if (that.daysDiff === null) that.initDaysDiff(res.data.enableTime);
              } else {
                // return this.nextStep2(); // 公测临时关闭
                this.signCodeVisible = false;
                this.fullscreenLoading.close(); // 关闭loading
                this.titleList = [
                  { contractType: 2, tips: '请完成《渠道合作伙伴协议》签署' },
                  { contractType: 1, tips: '请完成《鼎校智能合同》签署' },
                  { contractType: 3, tips: '请完成《课程推广合作协议》签署' }
                ];
                this.QRcodeList = [
                  [
                    {
                      isFirstParty: 0,
                      img: '',
                      tips: '您将作为甲方与渠道合作伙伴签署',
                      title: '《渠道合作伙伴协议》',
                      isSign: false
                    },
                    {
                      isFirstParty: 1,
                      img: '',
                      tips: '您将作为乙方与渠道合作伙伴签署',
                      title: '《渠道合作伙伴协议》',
                      isSign: false
                    }
                  ],
                  [
                    {
                      isFirstParty: 0,
                      img: '',
                      tips: '您将作为甲方与下级门店签署',
                      title: '《鼎校智能学习管理系统分销、零售合同》',
                      isSign: false
                    },
                    {
                      isFirstParty: 1,
                      img: '',
                      tips: '您将作为乙方与所归属的品牌签署',
                      title: '《鼎校智能学习管理系统经销商销售合同》',
                      isSign: false
                    }
                  ],
                  [
                    {
                      isFirstParty: 0,
                      img: '',
                      tips: '为保证学生管理费及推荐费用结算正常，请尽快签署',
                      title: '《课程推广合作协议》',
                      isSign: false
                    }
                  ]
                ];
                // 获取合同的模板类型及新渠道上线时间
                this.getContractTemplate();
              }
            } else if (isSchool) {
              if (!res.data.isOld) {
                localStorage.setItem('AllSignatures', JSON.stringify(true)); // 保存签署状态
                this.fakeClose();
                return;
              }
              // return this.nextStep2(); // 公测临时关闭
              this.signCodeVisible = false;
              this.fullscreenLoading.close(); // 关闭loading
              //Shool
              console.log('老门店');

              this.titleList = [
                { contractType: 3, tips: '请完成《课程推广合作协议》签署' },
                { contractType: 1, tips: '请完成《鼎校智能学习管理系统分销、零售合同》签署' }
              ];
              this.QRcodeList = [
                [
                  {
                    isFirstParty: 0,
                    img: '',
                    tips: '您作为课程推广大使，需签署',
                    title: '《课程推广合作协议》',
                    isSign: false
                  },
                  {
                    isFirstParty: 1,
                    img: '',
                    tips: '您将作为甲方与您的课程推广大使签署',
                    title: '《课程推广合作协议》',
                    isSign: false
                  }
                ],
                [
                  {
                    isFirstParty: 1,
                    img: '',
                    tips: '您将作为乙方与所归属的俱乐部签署',
                    title: '《鼎校智能学习管理系统分销、零售合同》',
                    isSign: false
                  }
                ]
              ];
              // 获取合同的模板类型及新渠道上线时间
              this.getContractTemplate();
            }
          })
          .catch((err) => {
            console.log('ppppppp');

            this.fakeClose(); // 关闭弹窗
          });
      },
      // 新俱乐部 -- 下一步
      nextStep2() {
        if (this.st0) this.clearEsignInterval(this.st0); // 删除已存在的定时器
        localStorage.setItem('AllSignatures', JSON.stringify(true)); // 保存签署状态
        return this.fakeClose(); // 关闭弹窗
      },
      // 新俱乐部 -- 查询 学习系统合同 信息
      getContractStatus() {
        let that = this;
        let merchantCode = window.localStorage.getItem('loginMerchantCode');
        // 查询 学习系统合同 信息
        orderApi
          .getContractStatus({ merchantCode })
          .then((res) => {
            console.log(res.data, 'res.data');
            // (总）合同表签署状态：1：待签署，2：签署成功，3：签署失败
            if (res.data.esignReconciliationStatus == 2) {
              this.nextStep2(); // 关闭弹窗
              return;
            }
            this.signCodeVisible = true;
            this.fullscreenLoading.close(); // 关闭loading
            // flowId: item.signFlowId, templateType: item.templateType, signSource: item.signSource
            // 保存 学习系统合同 信息
            this.QRcodeTitleList = {
              signFlowId: res.data.signFlowId,
              templateType: res.data.templateType,
              signSource: res.data.signSource
            };
            this.flowId = res.data.signFlowId;
            this.templateType = res.data.templateType;
            // 获取签署链接
            this.esignInterval2();
          })
          .catch((err) => {
            this.fakeClose(); // 关闭弹窗
          });
      },
      // 新俱乐部 -- 轮询 -获取签署链接 及 签署状态
      esignInterval2() {
        let that = this;
        // 删除已存在的定时器
        if (this.st0) this.clearEsignInterval(this.st0);
        this.codeLoading = true; // 加载中
        // 轮询(获取签署状态)
        that.getSignStatus2(false, 1);

        that.st0 = setInterval(() => {
          // 获取签署状态 及 获取签署链接
          that.getSignStatus2();
        }, 15000);

        // 10分钟后清除定时器
        setTimeout(() => {
          that.clearEsignInterval(that.st0);
        }, 10 * 60 * 1000);
      },
      // 新俱乐部 -- 获取签署链接 及 签署状态
      getSignStatus2(tip = false, isQrLink = 0) {
        // console.log('🚀 ~ getEsignStatus ~ tip:', this.codeLoading);
        let that = this;
        if (this.codeLoading && tip) {
          that.$message.warning('请先等待合同码加载完成');
          return;
        }
        let item = this.QRcodeTitleList;
        orderApi.fetchContractQrLink({ flowId: item.signFlowId, templateType: item.templateType, signSource: item.signSource, isQrLink }).then((res) => {
          res.data.forEach((result) => {
            this.codeLoading = false; // 加载完成
            /**
             * mobile 手机号
             * isFirstParty 是否甲方：0-否 1-是
             * signStatus 签署状态1-待签署 2-签署成功 3-签署失败
             * qrUrl 二维码地址
             * signUrl 原始地址
             * isContract 是否存在合同: 0-否 1-是
             */
            this.QRcodeList[this.QRcodeIndex].forEach((item, i) => {
              // 如果类型相同
              if (item.isFirstParty == result.isFirstParty) {
                // item.img = result.value.qrUrl;
                // 图片有值就赋值
                if (result.qrUrl) {
                  this.retryCount = 5; // 重试次数
                  item.img = result.qrUrl;
                }
                // 签署链接
                item.signUrl != result.signUrl && result.signUrl != '' ? (item.signUrl = result.signUrl) : '';

                // 签署成功修改状态
                if (result.signStatus == 2) {
                  item.isSign = true;
                } else if (tip) {
                  that.$message.warning('合同还未签署，请先签署');
                  tip = false;
                  return;
                }
              }
            });
          });
        });
      },
      // 获取合同的模板类型 及 签署链接，并开始轮询状态
      getContractTemplate() {
        let that = this;
        that.codeLoading = true;
        console.log('oooooooo');

        // 获取合同的模板类型及新渠道上线时间
        let roleTag = window.localStorage.getItem('roleTag');
        orderApi
          .getContractTemplate({ roleTag, contractType: this.titleList[this.QRcodeIndex].contractType })
          .then((res) => {
            // 判断是否在有效期内 / 弹窗是否可以关闭;
            if (that.daysDiff === null) that.initDaysDiff(res.data.onlineTime);
            /**
             * res.data.onlineTime // 新渠道上线时间;
             * res.data.templateName // 合同模板名称;
             * res.data.templateType // 合同模板类型;
             * res.data.isSecondPartyContract // 是否存在作为乙方的合同:0-否 1-是;
             * res.data.isFirstPartyContract // 是否存在作为甲方的合同:0-否 1-是;
             */
            // 判断甲乙双方合同 没有则删除 丢弃
            // if (res.data.isSecondPartyContract == 0) {
            //   this.$set(
            //     this.QRcodeList,
            //     this.QRcodeIndex,
            //     this.QRcodeList[this.QRcodeIndex].filter((item) => item.isFirstParty != 0)
            //   );
            // }
            // if (res.data.isFirstPartyContract == 0) {
            //   this.$set(
            //     this.QRcodeList,
            //     this.QRcodeIndex,
            //     this.QRcodeList[this.QRcodeIndex].filter((item) => item.isFirstParty != 1)
            //   );
            // }
            // if (this.QRcodeList[this.QRcodeIndex].length == 0) {
            //   this.QRcodeList[this.QRcodeIndex] = [...this.QRcodeList[this.QRcodeIndex]];
            //   return;
            // }
            // console.log(this.QRcodeList[this.QRcodeIndex], '[]');
            // 保存合同模板类型
            // this.templateType = res.data.templateType;
            // this.signSource = this.templateType == 'EPLMSContract' ? 0 : 1;
            // 轮询 -获取签署链接 及 签署状态

            this.esignInterval();
            // this.getEsignStatus();
          })
          .catch((err) => {
            // this.fakeClose(); // 关闭弹窗
            // this.isValid = false;
            if (that.daysDiff === null) that.initDaysDiff('2025-04-08 00:00:00');
            // 轮询 -获取签署链接 及 签署状态
            this.esignInterval();
          });
      },
      // 轮询 -获取签署链接 及 签署状态
      esignInterval() {
        let that = this;
        that.codeLoading = true;
        // 删除已存在的定时器
        if (this.st0) this.clearEsignInterval(this.st0);

        that.getEsignStatus(false, 1);
        // 轮询(获取签署状态)
        that.st0 = setInterval(() => {
          // 获取签署状态 及 获取签署链接
          that.getEsignStatus();
        }, 15000);

        // 10分钟后清除定时器
        setTimeout(() => {
          that.clearEsignInterval(that.st0);
        }, 10 * 60 * 1000);
      },
      // 获取签署链接 及 签署状态
      /**
       *
       * @param tip 是否要提示
       * @param isQrLink 是否要图片链接
       */
      getEsignStatus(tip = false, isQrLink = 0) {
        // console.log('🚀 ~ getEsignStatus ~ tip:', this.codeLoading);
        let that = this;
        if (this.signLoading && tip) {
          that.$message.warning('请勿重复点击，正在加载中');
          return;
        }
        that.signLoading = true;
        if (this.codeLoading && tip) {
          that.$message.warning('请先等待合同码加载完成');
          return;
        }
        orderApi
          .getEsignBatchContractUrl({ mobile: this.mobile, contractType: this.titleList[this.QRcodeIndex].contractType, isQrLink })
          .then((res) => {
            // 如果弹窗没有打开
            if (this.signCodeVisible === null) {
              let isAllSign = res.data.every((result) => {
                return result.isContract == 0 || result.signStatus == 2; // 存在合同且签署成功
              });
              // 不是全部签署完成，弹窗显示
              if (!isAllSign) {
                this.signCodeVisible = false;
                this.fullscreenLoading.close(); // 关闭loading
              }
            }
            // some符合条件退出
            res.data.some((result) => {
              /**
               * mobile 手机号
               * isFirstParty 是否甲方：0-否 1-是
               * signStatus 签署状态1-待签署 2-签署成功 3-签署失败
               * qrUrl 二维码地址
               * signUrl 原始地址
               * isContract 是否存在合同: 0-否 1-是
               */
              // 反向遍历 -删除某项可以避免索引错位
              for (let i = that.QRcodeList[that.QRcodeIndex].length - 1; i >= 0; i--) {
                let item = that.QRcodeList[that.QRcodeIndex][i];
                // 如果类型相同
                if (item.isFirstParty == result.isFirstParty) {
                  // item.img = result.value.qrUrl;
                  // 如果不存在合同就删除
                  if (result.isContract == 0) {
                    that.QRcodeList[that.QRcodeIndex].splice(i, 1);
                    continue; // 终止本次循环
                  }
                  // 图片有值就赋值
                  if (result.qrUrl) {
                    this.retryCount = 5; // 重试次数
                    item.img = result.qrUrl;
                  }
                  // 签署链接
                  item.signUrl != result.signUrl && result.signUrl != '' ? (item.signUrl = result.signUrl) : '';

                  // 签署成功修改状态
                  if (result.signStatus == 2) {
                    item.isSign = true;
                  } else if (tip) {
                    // 如果是手动下一步，只要有一个签署失败就提示 并退出some遍历
                    that.$message.warning('请完成本页的合同签署再进行下一步');
                    tip = false;
                    return true;
                  }
                }
              }
              return false; // some防止中断
            });
            that.codeLoading = false;
            that.signLoading = false;
            // 防止数据没更新
            this.$set(this.QRcodeList, this.QRcodeIndex, this.QRcodeList[this.QRcodeIndex]);
          })
          .catch((err) => {
            that.codeLoading = false; // 加载中
            that.signLoading = false; // 加载中
          });
        // // 获取签署链接 及 签署状态 丢弃
        // // 请求列表
        // const PromiseList = [];
        // // 根据剩余项拿到请求列表
        // this.QRcodeList[this.QRcodeIndex].forEach((item) => {
        //   PromiseList.push(orderApi.getEsignBatchContractUrl({ mobile: this.mobile, templateType: this.templateType, isFirstParty: item.isFirstParty }));
        // });
        /**
         * mobile 手机号
         * isFirstParty 是否甲方：1-否 0-是
         * signStatus 签署状态1-待签署 2-签署成功 3-签署失败
         * qrUrl 签署状态1-待签署 2-签署成功 3-签署失败
         * signUrl 原始地址
         */
        // Promise.allSettled(PromiseList).then((results) => {
        //   that.codeLoading = false;
        //   results.forEach((result, index) => {
        //     /**
        //      * result.value 为返回值
        //      * result.reason 为失败原因
        //      * result.status 为状态
        //      */

        //     if (result.status === 'fulfilled') {
        //       if (result.value.data && tip) {
        //         that.$message.warning('请完成本页的合同签署再进行下一步');
        //         tip = false;
        //         return;
        //       }
        //       console.log(index, '成功:', result.value);
        //       // 获取签署链接
        //       that.QRcodeList[that.QRcodeIndex].forEach((item) => {
        //         // console.log(item, 'item', result.value.data.isFirstParty);
        //         if (item.isFirstParty == result.value.data.isFirstParty) {
        //           // item.img = result.value.qrUrl;
        //           !item.img ? (item.img = result.value.qrUrl) : ''; // 没有图片就赋值
        //           // 签署状态：1-没签 2-签署成功
        //           if(result.signStatus == 2){
        //             item.isSign = true;
        //           }
        //         }
        //       });
        //     } else {
        //       console.log(index, '失败:', result.reason);
        //     }
        //   });
        // });
      },
      // 判断是否在有效期内/弹窗是否可以关闭
      initDaysDiff(date1) {
        // 判断是否在有效期内/弹窗是否可以关闭
        // const date1 = '2025-03-20';
        const startTime = dayjs(date1);
        const endTime = dayjs(date1).add(30, 'day');
        // console.log('🚀 ~ initDaysDiff ~ date1:', endTime);
        const currentTime = dayjs();

        this.isValid = currentTime.isBefore(endTime);
        console.log(this.isValid, 'this.isValid'); // 根据当前时间返回 true/false
        if (this.isValid) {
          this.daysDiff = endTime.diff(currentTime, 'day'); // 计算日期差
        } else {
          this.daysDiff = 0; // 如果不在有效期内，日期差为 0
        }
      },
      open(item) {
        item.isSign = !item.isSign;
      },
      //清除轮询定时器
      clearEsignInterval(st) {
        clearInterval(st);
        st = null;
      },
      // 下一步
      nextStep() {
        let that = this;
        if (this.isAllSign) {
          // 判断是否全部签署完成
          if (this.isAllSignAll) {
            this.QRcodeIndex++;
            // 保存签署状态
            localStorage.setItem('AllSignatures', JSON.stringify(true));
            // 开始关闭倒计时
            that.surplustime = 10;
            that.st = setInterval(() => {
              that.surplustime--;
              if (that.surplustime == 0) {
                that.fakeClose();
              }
            }, 1000);
            return;
          }
          // 下一步
          this.QRcodeIndex++;
          this.esignInterval(); // 获取签署链接
          //   this.$router.push({ name: 'schoolCompletePaymentIs' });
        } else {
          // this.$message.warning('请先完成所有合同签署');
          this.getEsignStatus(true);
        }
      },
      // 复制
      fallbackCopyText(text) {
        // this.changeSign();
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed'; // 避免滚动到底部
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          const successful = document.execCommand('copy');
          this.$message({
            message: successful ? '复制成功！' : '复制失败',
            type: successful ? 'success' : 'warning',
            duration: 1500
          });
          // console.log(successful ? '复制成功！' : '复制失败');
        } catch (err) {
          console.error('无法复制:', err);
        }
        document.body.removeChild(textArea);
      },
      /**
       * @arguments_1 img元素实例,this
       * @arguments_2 重试次数
       * @return void
       */
      retry(event) {
        const imgElement = event.target;
        let that = this;

        console.log(`图片加载失败,将会重试${this.retryCount}次`);
        if (this.retryCount-- > 0) {
          // 重定向图片路径, 引起重新加载
          imgElement.src = imgElement.src + '?t=' + Date.now(); // 强制刷新
        } else {
          console.log('图片加载失败, 请检查网络或联系管理员');
          if (this.retryTimer) that.clearEsignInterval(this.retryTimer); // 清除旧定时器
          // 次数超过后取消错误监听, 重置重试次数
          this.retryTimer = setTimeout(() => {
            that.retryCount = 5;
          }, 1000);
        }
      },
      // 观察DOM销毁并重新挂载
      initMutationObserver() {
        observer = new MutationObserver((mutations) => {
          if (!document.contains(this.$el) && this.isVisible) {
            document.body.appendChild(this.$el);
            console.log('弹窗已重新挂载');
          }
        });
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      },
      // 返回
      fakeClose() {
        console.log('🚀 ~ fakeClose ~ fullscreenLoading:', this.fullscreenLoading);
        if (this.fullscreenLoading) this.fullscreenLoading.close(); // 关闭loading
        this.isVisible = false; // 解除保护
        this.$nextTick(() => {
          this.$el.remove();
        });
        // 用于测试关闭保护
        // console.log('关闭操作已被拦截');
        // 解除防护
        this.destroyProtection();
        this.$destroy();
      },
      // 解除所有保护机制
      destroyProtection() {
        // 1. 解除数据锁定
        Object.defineProperty(this, 'isVisible', {
          writable: true,
          value: false
        });

        // 2. 停止DOM监听
        if (observer) {
          observer.disconnect();
          observer = null;
        }

        // 3. 解除样式保护
        this.dialogStyle = {
          display: 'none'
        };
      }
    },

    beforeDestroy() {
      if (observer) observer.disconnect(); // 停止DOM监听
      if (this.st) this.clearEsignInterval(this.st); // 清除轮询定时器
      if (this.st0) this.clearEsignInterval(this.st0);
    }
  };
</script>

<style lang="scss" scoped>
  div {
    text-align: center;
  }
  .mt-2 {
    margin-top: 20px;
  }
  .mt-4 {
    margin-top: 40px;
  }
  .mt-6 {
    margin-top: 60px;
  }
  p {
    margin: 0;
    color: #666;
  }
  :deep(.el-step__description) {
    color: #a7a2a2; /* 设置描述文字颜色 */
  }
  :deep(.el-step__description.is-wait) {
    color: #c0c4cc;
  }
  :deep(.el-step__main) {
    margin-top: 10px;
  }
  .button_d {
    width: 100px !important;
  }
  .school_complete_paymentIs {
    width: 220px;
    height: 140px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .QR_code {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 20px auto;
    .QR_code_success {
      z-index: 1;
      width: 200px;
      height: 200px;
      position: absolute;
      top: 0;
      left: 0;
      background-color: rgba(255, 255, 255, 0.85);

      display: grid;
      grid-template-rows: repeat(auto-fit, 50px);
      align-content: center;
      align-items: center;
      justify-items: center;
      i {
        font-size: 35px;
        color: #67c23a;
      }

      &.deactive {
        display: none;
      }
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
  .create-success {
    font-size: 60px;
    color: #20b759;
  }
  .info {
    color: #666;
    text-align: center;
  }
  :deep(.el-step__head.el-step__head) {
    overflow: hidden;
  }
  :deep(.el-step__icon) {
    width: auto;
    border-width: 0;
    padding: 0 5px;
    float: left;

    i {
      display: inline-block;
      width: 26rpx;
      height: 26rpx;
      box-sizing: border-box;
      border-radius: 50%;
      font-size: 26px;
      margin-right: 8px;
    }
  }
  :deep(.el-step__icon.is-icon) {
    width: auto;
    border-width: 0;
  }
  :deep(.el-step__icon.is-text) {
    width: auto;
    border-width: 0;
  }
  .sign-code {
    :deep(.el-dialog__body) {
      padding: 0px 20px 30px;
      text-align: left;
    }
  }
  :deep(.el-divider--horizontal) {
    width: calc(100% + 40px);
    margin: 0 0 24px;

    position: relative;
    left: -20px;
  }
</style>
