import request from "@/utils/request";

// 根据字典查询阶段相应的值 {dictType:'grammar_phase'}
export function queryByTypeAPI(query) {
  return request({
    url: "/dyf/web/v2/dict/queryByType",
    method: "get",
    params: query,
  });
}

// 下拉选项：语法点/知识点
export function optionsAPI(query) {
  return request({
    url: "/dyf/web/v2/knowledge/options",
    method: "get",
    params: query,
  });
}
// 根据知识点 id 查找思维导图的所有三级节点 /dyf/web/v2/mindMap/thirdNode
export function thirdNodeAPI(query) {
  return request({
    url: "/dyf/web/v2/mindMap/thirdNode",
    method: "get",
    params: query,
  });
}

// 获取课前课后题库类型
export function aroundAPI() {
  return request({
    url: "/dyf/web/v2/around/question/around",
    method: "get"
  });
}

//课前课后题库分页查询
export function pageAPI(query) {
  return request({
    url: "/dyf/web/v2/around/question/page",
    method: "get",
    params: query,
  });
}

// 课前课后题库详情查询
export function oneAPI(query) {
  return request({
    url: "/dyf/web/v2/around/question/one",
    method: "get",
    params: query
  });
}

// 课前课后题库新增/编辑
export function addOrUpdate(data) {
  return request({
    url: "/dyf/web/v2/around/question/addOrUpdate",
    method: "post",
    data
  });
}

// 课前课后题库删除
export function deleteAPI(data) {
  return request({
    url: "/dyf/web/v2/around/question/delete",
    method: "post",
    params: data,
  });
}

