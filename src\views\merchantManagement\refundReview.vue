<template>
  <div>
    <div element-loading-spinner="el-icon-loading" element-loading-text="加载中..." v-loading="loading">
      <el-card shadow="never" class="card">
        <el-form ref="form" :model="searchForm" :inline="true">
          <!-- <el-form-item label="月份">
                        <el-date-picker v-model="searchForm.date" type="month" value-format="yyyy-MM" placeholder="选择月">
                        </el-date-picker>
                    </el-form-item> -->
          <el-form-item label="门店编号">
            <el-input v-model="searchForm.merchantCode" placeholder="请输入俱乐部编号" size="small"></el-input>
          </el-form-item>
          <el-form-item label="门店名称">
            <el-input v-model="searchForm.merchantName" placeholder="请输入俱乐部名称" size="small"></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.value" clearable placeholder="请选择">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit" size="small">查询</el-button>
            <!-- <el-button size="small" type="primary " @click="agree">批量同意</el-button>
                        <el-button size="small" type="danger" @click="refuse">批量拒绝</el-button> -->
            <el-button size="small" @click="onreset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <div id="charts_one" style="min-height: 80vh">
        <!-- @selection-change="handleSelectionChange" -->
        <el-table :header-cell-style="{ background: '#eef1f6', color: '#606266' }" ref="multipleTable" border :data="list">
          <!-- <el-table-column type="selection" align="center" width="100" :selectable="checkSelectable">
                    </el-table-column> -->
          <el-table-column align="center" property="merchantCode" label="门店编号" width="200"></el-table-column>
          <el-table-column align="center" property="merchantName" label="门店名称" width="200"></el-table-column>
          <el-table-column align="center" property="headUser" label="负责人" width="200"></el-table-column>
          <el-table-column align="center" property="headMerchantName" label="所属俱乐部" width="220"></el-table-column>
          <!-- <el-table-column align="center" property="sellHour" label="已售学时（节）" width="200"></el-table-column> -->
          <el-table-column align="center" property="innerAmount" label="账期内余额（元）" width="220"></el-table-column>
          <el-table-column align="center" property="outsideAmount" label="账期外余额（元）" width="220"></el-table-column>
          <el-table-column align="center" property="withdrawalAmount" label="可提金额（元）" width="220"></el-table-column>
          <el-table-column align="center" property="addTime" label="添加时间" width="220"></el-table-column>
          <el-table-column align="center" property="expireTime" label="到期时间" width="220"></el-table-column>
          <el-table-column align="center" property="createTime" label="退费申请时间" width="220"></el-table-column>
          <el-table-column align="center" property="state" label="状态" width="220">
            <template slot-scope="scope">
              <div v-if="scope.row.state == 0">未审核</div>
              <div style="color: green" v-if="scope.row.state == 1">已通过</div>
              <div style="color: red" v-if="scope.row.state == 2">未通过</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" align="center" width="400">
            <template slot-scope="scope">
              <div v-if="scope.row.state == 0">
                <el-button size="mini" type="primary" @click="openAgree(scope.row)">同意退费</el-button>
                <el-button size="mini" type="danger" @click="openReject(scope.row)">拒绝退费</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-col :span="20">
          <Page :total="page_data.total" :page.sync="page_data.page" :limit.sync="page_data.pageSize" @pagination="index" />
        </el-col>
      </div>
      <!-- 同意 -->
      <el-dialog :visible.sync="agreeDialogVisible" width="20%">
        <div class="center">
          <div>请确认是否同意该门店退费申请?</div>
          <div>同意后，该门店将无法再使用当前系统</div>
        </div>
        <div class="f-c">
          <el-button @click="closeProp1">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 拒绝 -->
      <el-dialog :visible.sync="rejucetDialogVisible" width="20%">
        <div class="center">
          <div>请确认是否拒绝该门店的退费申请?</div>
        </div>
        <div class="f-c">
          <el-button @click="refule()">取 消</el-button>
          <el-button type="primary" @click="confirm2">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
  import { getRefundQueryPage, getRefundQueryDeduction } from '@/api/refundReview';
  import Page from '@/components/Pages/pages.vue';
  export default {
    components: {
      Page
    },
    data() {
      return {
        sysUserInfo: localStorage.getItem('sysUserInfo'),
        loading: false,
        agreeDialogVisible: false,
        rejucetDialogVisible: false,
        ids: [],
        // searchForm: {
        //     value: null
        // },
        searchForm: {
          value: null,
          merchantCode: '',
          merchantName: ''
        },
        options: [
          {
            value: 0,
            label: '未审核'
          },
          {
            value: 1,
            label: '已通过'
          },
          {
            value: 2,
            label: '未通过'
          }
        ],

        page_data: {
          pageSize: 10,
          page: 1,
          total: 0
        },
        list: [],
        checkArry: [],
        changeObj: {}
      };
    },
    // created() {
    //     this.merchantCode = this.$route.query.clubId;
    //     console.log(this.merchantCode);

    // },
    mounted() {
      // this.getNowFormatDate()
      this.index();
    },
    methods: {
      refule() {
        this.rejucetDialogVisible = false;
        this.index();
      },
      checkSelectable(row) {
        return row.state == 0; // 状态为1的行不可选
      },
      handleSelectionChange(selection) {
        this.checkArry = selection;
      },
      index() {
        let info = JSON.parse(this.sysUserInfo);
        let param = { pageSize: this.page_data.pageSize, pageNum: this.page_data.page };
        let data = {
          brandMerchantCode: info.merchantCode,
          state: this.searchForm.value
        };
        Object.assign(param, data, this.searchForm);
        getRefundQueryPage(param).then((res) => {
          this.list = res.data.data;
          this.page_data.total = res.data.data ? res.data.totalItems * 1 : 0;
        });
      },
      onSubmit() {
        this.page_data.page = 1;
        this.index();
      },
      // onreset() {
      //     (this.searchForm = {
      //         value: ''
      //     }),
      //         (this.page_data = {
      //             pageSize: 10,
      //             page: 1,
      //             total: 0
      //         });
      //     this.index();

      // },
      onreset() {
        (this.searchForm = {
          years: '',
          merchantCode: '',
          merchantName: ''
        }),
          (this.page_data = {
            pageSize: 10,
            page: 1,
            total: 0
          });
        this.index();
        // this.getNowFormatDate()
      },
      back() {
        (this.searchForm = {
          value: ''
        }),
          (this.page_data = {
            pageSize: 10,
            page: 1,
            total: 0
          });
        this.$router.back();
      },
      openAgree(value) {
        let that = this;
        that.checkArry.push(value);
        that.openAgreeDialog();
      },
      openReject(value) {
        let that = this;
        that.checkArry.push(value);
        that.openRejectDialog();
      },
      agree() {
        let that = this;
        if (that.checkArry.length > 0) {
          that.openAgreeDialog();
        } else {
          this.$message.warning('请勾选俱乐部');
        }
      },
      refuse() {
        let that = this;
        if (that.checkArry.length <= 0) return this.$message.warning('请勾选俱乐部');
        that.openRejectDialog();
      },
      comeReview(id) {
        // this.loading = true;
        this.checkArry.forEach((item) => {
          if (item.hasOwnProperty('state')) {
            item.state = id;
          }
        });
        getRefundQueryDeduction(this.checkArry).then((res) => {
          if (res.success) {
            this.index();
            //   this.loading = false;
          } else {
            this.index();
          }
        });
      },

      openAgreeDialog() {
        let that = this;
        that.agreeDialogVisible = true;
      },
      openRejectDialog() {
        let that = this;
        that.rejucetDialogVisible = true;
      },
      closeProp1() {
        (this.checkArry = []), (this.agreeDialogVisible = false);
      },
      closeProp2() {
        let that = this;
        this.checkArry = [];
        that.rejucetDialogVisible = false;
      },
      confirm() {
        let that = this;
        that.comeReview(1);
        that.closeProp1();
      },
      confirm2() {
        let that = this;
        that.comeReview(2);
        that.closeProp2();
      },
      getNowFormatDate() {
        let date = new Date(),
          year = date.getFullYear(), //获取完整的年份(4位)
          month = date.getMonth() + 1, //获取当前月份(0-11,0代表1月)
          strDate = date.getDate(); // 获取当前日(1-31)
        if (month < 10) month = `0${month}`; // 如果月份是个位数，在前面补0
        this.searchForm.date = `${year}-${month}`;
        this.index();
      }
    }
  };
</script>
<style lang="scss">
  .center {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .f-c {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
  }
</style>
