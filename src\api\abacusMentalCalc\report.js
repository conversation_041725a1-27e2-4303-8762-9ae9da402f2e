
import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/calculation/web/report/list',
      method: 'GET',
      params: data
    })
  },
  //添加
  saveOrUpdate(data) {
    return request({
      url: '/calculation/web/report/save',
      method: 'POST',
      data
    })
  },
  detail(id){
    return request({
      url: '/calculation/web/report/detail',
      method: 'GET',
      params: {
        id:id
      }
    })
  },
  delete(id){
    return request({
      url: '/calculation/web/report',
      method: 'DELETE',
      params: {
        id:id
      }
    })
  },
}
