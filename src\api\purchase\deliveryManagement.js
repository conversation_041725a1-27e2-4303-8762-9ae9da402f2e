// 采购管理-发货管理相关接口
import request from '@/utils/request';

export default {
  // 列表分页查询
  queryList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/purchase/send/page',
      method: 'GET',
      params: {
        pageNum,
        pageSize,
        ...data
      }
    });
  },
  // 改价
  changePrice(params) {
    return request({
      url: '/znyy/purchase/send/changePrice',
      method: 'GET',
      params
    });
  },
  // 发货
  send(params) {
    return request({
      url: '/znyy/purchase/send/send',
      method: 'GET',
      params
    });
  },
  // 查询剩余学习系统数量
  queryStock() {
    return request({
      url: '/znyy/purchase/board/queryRestLearningSysCount',
      method: 'GET'
    });
  }
};
