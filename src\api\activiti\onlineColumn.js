import request from '@/utils/request'

export default {
  //分页
  list(data) {
    return request({
      url: '/activiti/onlineColumn/list',
      method: 'GET',
      params: data
    })
  },
  add(data) {
    return request({
      url: '/activiti/onlineColumn/add',
      method: 'POST',
      data
    })
  },
  update(data) {
    return request({
      url: '/activiti/onlineColumn/update',
      method: 'POST',
      data
    })
  },
  view(data) {
    return request({
      url: '/activiti/onlineColumn/view',
      method: 'GET',
      params: data
    })
  },
  delete(data){
    return request({
      url: '/activiti/onlineColumn/delete',
      method: 'DELETE',
      params: data
    })
  },
  listOnlineColumnRule(data){
    return request({
      url: '/activiti/onlineColumn/listOnlineColumnRule',
      method: 'POST',
      params: data
    })
  },
  listNotInOnlineColumnRule(data){
    return request({
      url: '/activiti/onlineColumn/listNotInOnlineColumnRule',
      method: 'POST',
      params: data
    })
  },
  updateOnlineColumnRule(data){
    return request({
      url: '/activiti/onlineColumn/updateOnlineColumnRule',
      method: 'POST',
      data
    })
  },
  addOnlineColumnRule(data){
    return request({
      url: '/activiti/onlineColumn/addOnlineColumnRule',
      method: 'POST',
      data
    })
  },
  deleteOnlineColumnRule(data){
    return request({
      url: '/activiti/onlineColumn/deleteOnlineColumnRule',
      method: 'POST',
      params: data
    })
  },
  refreshColumn(data){
    return request({
      url: '/activiti/onlineColumn/refresh',
      method: 'POST',
      data
    })
  }
}
