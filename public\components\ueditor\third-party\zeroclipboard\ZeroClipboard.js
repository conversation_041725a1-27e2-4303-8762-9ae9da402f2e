!function(p){"use strict";var c,t,f={bridge:null,version:"0.0.0",pluginType:"unknown",disabled:null,outdated:null,unavailable:null,deactivated:null,overdue:null,ready:null},d={},u=null,r=0,h={},o=0,v={},e=function(){var e,t,a,r,n="ZeroClipboard.swf";if(!document.currentScript||!(r=document.currentScript.src)){var i=document.getElementsByTagName("script");if("readyState"in i[0])for(e=i.length;e--&&("interactive"!==i[e].readyState||!(r=i[e].src)););else if("loading"===document.readyState)r=i[i.length-1].src;else{for(e=i.length;e--;){if(!(a=i[e].src)){t=null;break}if(a=(a=a.split("#")[0].split("?")[0]).slice(0,a.lastIndexOf("/")+1),null==t)t=a;else if(t!==a){t=null;break}}null!==t&&(r=t)}}return r&&(n=(r=r.split("#")[0].split("?")[0]).slice(0,r.lastIndexOf("/")+1)+n),n}(),y=(t=/\-([a-z])/g,function(e){return e.replace(t,a)});function a(e,t){return t.toUpperCase()}function g(e){var t;e=e||p.event,this!==p?t=this:e.target?t=e.target:e.srcElement&&(t=e.srcElement),N.activate(t)}function m(e,t){if(!e||1!==e.nodeType)return e;if(e.classList)return e.classList.contains(t)||e.classList.add(t),e;if(t&&"string"==typeof t){var a=(t||"").split(/\s+/);if(1===e.nodeType)if(e.className){for(var r=" "+e.className+" ",n=e.className,i=0,o=a.length;i<o;i++)r.indexOf(" "+a[i]+" ")<0&&(n+=" "+a[i]);e.className=n.replace(/^\s+|\s+$/g,"")}else e.className=t}return e}function b(e,t){if(!e||1!==e.nodeType)return e;if(e.classList)return e.classList.contains(t)&&e.classList.remove(t),e;if(t&&"string"==typeof t||void 0===t){var a=(t||"").split(/\s+/);if(1===e.nodeType&&e.className)if(t){for(var r=(" "+e.className+" ").replace(/[\n\t]/g," "),n=0,i=a.length;n<i;n++)r=r.replace(" "+a[n]+" "," ");e.className=r.replace(/^\s+|\s+$/g,"")}else e.className=""}return e}function n(e,t){var a,r,n,i,o={left:0,top:0,width:0,height:0,zIndex:O(t)-1};if(e.getBoundingClientRect){var l,s,c,d=e.getBoundingClientRect();s="pageXOffset"in p&&"pageYOffset"in p?(l=p.pageXOffset,p.pageYOffset):(i=1,"function"==typeof document.body.getBoundingClientRect&&(r=(a=document.body.getBoundingClientRect()).right-a.left,n=document.body.offsetWidth,i=Math.round(r/n*100)/100),c=i,l=Math.round(document.documentElement.scrollLeft/c),Math.round(document.documentElement.scrollTop/c));var u=document.documentElement.clientLeft||0,f=document.documentElement.clientTop||0;o.left=d.left+l-u,o.top=d.top+s-f,o.width="width"in d?d.width:d.right-d.left,o.height="height"in d?d.height:d.bottom-d.top}return o}function w(e,t,a){if("function"==typeof t.indexOf)return t.indexOf(e,a);var r,n=t.length;for(void 0===a?a=0:a<0&&(a=n+a),r=a;r<n;r++)if(t.hasOwnProperty(r)&&t[r]===e)return r;return-1}function x(e){if("string"==typeof e)throw new TypeError("ZeroClipboard doesn't accept query strings.");return"number"!=typeof e.length?[e]:e}function l(e,t,a,r){r?p.setTimeout(function(){e.apply(t,a)},0):e.apply(t,a)}function C(){var e,t,a,r,n,i=arguments[0]||{};for(e=1,t=arguments.length;e<t;e++)if(null!=(a=arguments[e]))for(r in a)if(a.hasOwnProperty(r)){if(i[r],i===(n=a[r]))continue;void 0!==n&&(i[r]=n)}return i}var O=function(e){var t,a;return e&&("number"==typeof e&&0<e?t=e:"string"==typeof e&&(a=parseInt(e,10))&&!isNaN(a)&&0<a&&(t=a)),t||("number"==typeof E.zIndex&&0<E.zIndex?t=E.zIndex:"string"==typeof E.zIndex&&(a=parseInt(E.zIndex,10))&&!isNaN(a)&&0<a&&(t=a)),t||0},T=function(e){if(null==e||""===e)return null;if(""===(e=e.replace(/^\s+|\s+$/g,"")))return null;var t=e.indexOf("//"),a=(e=-1===t?e:e.slice(t+2)).indexOf("/");return(!(e=-1===a?e:-1===t||0===a?null:e.slice(0,a))||".swf"!==e.slice(-4).toLowerCase())&&e||null},D=function(e,t){var a=T(t.swfPath);null===a&&(a=e);var r=[];i(t.trustedOrigins,r),i(t.trustedDomains,r);var n=r.length;if(0<n){if(1===n&&"*"===r[0])return"always";if(-1!==w(e,r))return 1===n&&e===a?"sameDomain":"always"}return"never"};function i(e,t){var a,r,n;if(null!=e&&"*"!==t[0]&&("string"==typeof e&&(e=[e]),"object"==typeof e&&"number"==typeof e.length))for(a=0,r=e.length;a<r;a++)if(e.hasOwnProperty(a)&&(n=T(e[a]))){if("*"===n){t.length=0,t.push("*");break}-1===w(n,t)&&t.push(n)}}function z(e){if(null==e)return[];if(Object.keys)return Object.keys(e);var t=[];for(var a in e)e.hasOwnProperty(a)&&t.push(a);return t}var s,k=(s=p.Array.prototype.slice,function(e){return s.call(e,0)});!function(){var t,e,a=!1,r=!1,n=!1,i="";function o(e){var t=e.match(/[\d]+/g);return t.length=3,t.join(".")}function l(e){var t;e&&(a=!0,e.version&&(i=o(e.version)),!i&&e.description&&(i=o(e.description)),e.filename&&(t=e.filename,n=!!t&&(t=t.toLowerCase())&&(/^(pepflashplayer\.dll|libpepflashplayer\.so|pepperflashplayer\.plugin)$/.test(t)||"chrome.plugin"===t.slice(-13))))}if(navigator.plugins&&navigator.plugins.length)l(navigator.plugins["Shockwave Flash"]),navigator.plugins["Shockwave Flash 2.0"]&&(a=!0,i="********");else if(navigator.mimeTypes&&navigator.mimeTypes.length)l((e=navigator.mimeTypes["application/x-shockwave-flash"])&&e.enabledPlugin);else if("undefined"!=typeof ActiveXObject){r=!0;try{t=new ActiveXObject("ShockwaveFlash.ShockwaveFlash.7"),a=!0,i=o(t.GetVariable("$version"))}catch(e){try{t=new ActiveXObject("ShockwaveFlash.ShockwaveFlash.6"),a=!0,i="6.0.21"}catch(e){try{t=new ActiveXObject("ShockwaveFlash.ShockwaveFlash"),a=!0,i=o(t.GetVariable("$version"))}catch(e){r=!1}}}}f.disabled=!0!==a,f.outdated=i&&parseFloat(i)<11,f.version=i||"0.0.0",f.pluginType=n?"pepper":r?"activex":a?"netscape":"unknown"}();var N=function(e){if(!(this instanceof N))return new N(e);if(this.id=""+r++,h[this.id]={instance:this,elements:[],handlers:{}},e&&this.clip(e),"boolean"!=typeof f.ready&&(f.ready=!1),!N.isFlashUnusable()&&null===f.bridge){var t=this,a=E.flashLoadTimeout;"number"==typeof a&&0<=a&&setTimeout(function(){"boolean"!=typeof f.deactivated&&(f.deactivated=!0),!0===f.deactivated&&N.emit({type:"error",name:"flash-deactivated",client:t})},a),f.overdue=!1,I()}};N.prototype.setText=function(e){return N.setData("text/plain",e),this},N.prototype.setHtml=function(e){return N.setData("text/html",e),this},N.prototype.setRichText=function(e){return N.setData("application/rtf",e),this},N.prototype.setData=function(){return N.setData.apply(N,k(arguments)),this},N.prototype.clearData=function(){return N.clearData.apply(N,k(arguments)),this},N.prototype.setSize=function(e,t){return S(e,t),this};N.prototype.destroy=function(){this.unclip(),this.off(),delete h[this.id]};N.version="2.0.0-beta.5";var E={swfPath:e,trustedDomains:p.location.host?[p.location.host]:[],cacheBust:!0,forceHandCursor:!1,forceEnhancedClipboard:!1,zIndex:999999999,debug:!1,title:null,autoActivate:!0,flashLoadTimeout:3e4};N.isFlashUnusable=function(){return!!(f.disabled||f.outdated||f.unavailable||f.deactivated)},N.config=function(e){if("object"==typeof e&&null!==e&&C(E,e),"string"==typeof e&&e)return E.hasOwnProperty(e)?E[e]:void 0;var t={};for(var a in E)E.hasOwnProperty(a)&&("object"==typeof E[a]&&null!==E[a]?"length"in E[a]?t[a]=E[a].slice(0):t[a]=C({},E[a]):t[a]=E[a]);return t},N.destroy=function(){for(var e in N.deactivate(),h)if(h.hasOwnProperty(e)&&h[e]){var t=h[e].instance;t&&"function"==typeof t.destroy&&t.destroy()}var a=f.bridge;if(a){var r=j(a);r&&("activex"===f.pluginType&&"readyState"in a?(a.style.display="none",function e(){if(4===a.readyState){for(var t in a)"function"==typeof a[t]&&(a[t]=null);a.parentNode.removeChild(a),r.parentNode&&r.parentNode.removeChild(r)}else setTimeout(e,10)}()):(a.parentNode.removeChild(a),r.parentNode&&r.parentNode.removeChild(r))),f.ready=null,f.bridge=null,f.deactivated=null}N.clearData()},N.activate=function(e){c&&(b(c,E.hoverClass),b(c,E.activeClass)),m(c=e,E.hoverClass),L();var t=E.title||e.getAttribute("title");if(t){var a=j(f.bridge);a&&a.setAttribute("title",t)}var r,n,i,o,l,s=!0===E.forceHandCursor||"pointer"===(r=e,n="cursor",i=p.getComputedStyle?p.getComputedStyle(r,null).getPropertyValue(n):(o=y(n),r.currentStyle?r.currentStyle[o]:r.style[o]),"cursor"!==n||i&&"auto"!==i||"a"!==r.tagName.toLowerCase()?i:"pointer");l=s,!0===f.ready&&f.bridge&&"function"==typeof f.bridge.setHandCursor?f.bridge.setHandCursor(l):f.ready=!1},N.deactivate=function(){var e=j(f.bridge);e&&(e.removeAttribute("title"),e.style.left="0px",e.style.top="-9999px",S(1,1)),c&&(b(c,E.hoverClass),b(c,E.activeClass),c=null)},N.state=function(){return{browser:function(e,t){for(var a={},r=0,n=t.length;r<n;r++)t[r]in e&&(a[t[r]]=e[t[r]]);return a}(p.navigator,["userAgent","platform","appName"]),flash:function(e,t){var a={};for(var r in e)-1===w(r,t)&&(a[r]=e[r]);return a}(f,["bridge"]),zeroclipboard:{version:N.version,config:N.config()}}},N.setData=function(e,t){var a;if("object"==typeof e&&e&&void 0===t)a=e,N.clearData();else{if("string"!=typeof e||!e)return;(a={})[e]=t}for(var r in a)r&&a.hasOwnProperty(r)&&"string"==typeof a[r]&&a[r]&&(d[r]=a[r])},N.clearData=function(e){void 0===e?(function(e){if(e)for(var t in e)e.hasOwnProperty(t)&&delete e[t]}(d),u=null):"string"==typeof e&&d.hasOwnProperty(e)&&delete d[e]};var I=function(){var e,t,a,r,n=document.getElementById("global-zeroclipboard-html-bridge");if(!n){var i=D(p.location.host,E),o="never"===i?"none":"all",l=function(e){var t,a,r,n,i="",o=[];if(e.trustedDomains&&("string"==typeof e.trustedDomains?n=[e.trustedDomains]:"object"==typeof e.trustedDomains&&"length"in e.trustedDomains&&(n=e.trustedDomains)),n&&n.length)for(t=0,a=n.length;t<a;t++)if(n.hasOwnProperty(t)&&n[t]&&"string"==typeof n[t]){if(!(r=T(n[t])))continue;if("*"===r){o=[r];break}o.push.apply(o,[r,"//"+r,p.location.protocol+"//"+r])}return o.length&&(i+="trustedOrigins="+encodeURIComponent(o.join(","))),!0===e.forceEnhancedClipboard&&(i+=(i?"&":"")+"forceEnhancedClipboard=true"),i}(E),s=E.swfPath+(a=E.swfPath,null==(r=E)||r&&!0===r.cacheBust?(-1===a.indexOf("?")?"?":"&")+"noCache="+(new Date).getTime():"");n=P();var c=document.createElement("div");n.appendChild(c),document.body.appendChild(n);var d=document.createElement("div"),u="activex"===f.pluginType;d.innerHTML='<object id="global-zeroclipboard-flash-bridge" name="global-zeroclipboard-flash-bridge" width="100%" height="100%" '+(u?'classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"':'type="application/x-shockwave-flash" data="'+s+'"')+">"+(u?'<param name="movie" value="'+s+'"/>':"")+'<param name="allowScriptAccess" value="'+i+'"/><param name="allowNetworking" value="'+o+'"/><param name="menu" value="false"/><param name="wmode" value="transparent"/><param name="flashvars" value="'+l+'"/></object>',e=d.firstChild,d=null,e.ZeroClipboard=N,n.replaceChild(e,c)}e||((e=document["global-zeroclipboard-flash-bridge"])&&(t=e.length)&&(e=e[t-1]),e=e||n.firstChild),f.bridge=e||null},P=function(){var e=document.createElement("div");return e.id="global-zeroclipboard-html-bridge",e.className="global-zeroclipboard-container",e.style.position="absolute",e.style.left="0px",e.style.top="-9999px",e.style.width="1px",e.style.height="1px",e.style.zIndex=""+O(E.zIndex),e},j=function(e){for(var t=e&&e.parentNode;t&&"OBJECT"===t.nodeName&&t.parentNode;)t=t.parentNode;return t||null},L=function(){if(c){var e=n(c,E.zIndex),t=j(f.bridge);t&&(t.style.top=e.top+"px",t.style.left=e.left+"px",t.style.width=e.width+"px",t.style.height=e.height+"px",t.style.zIndex=e.zIndex+1),S(e.width,e.height)}},S=function(e,t){var a=j(f.bridge);a&&(a.style.width=e+"px",a.style.height=t+"px")};N.emit=function(e){var t,a,r,n,i,o,l,s,c;if("string"==typeof e&&e&&(t=e),"object"==typeof e&&e&&"string"==typeof e.type&&e.type&&(t=e.type,a=e),t){if(e=B(t,a),M(e),"ready"===e.type&&!0===f.overdue)return N.emit({type:"error",name:"flash-overdue"});if(r=!/^(before)?copy$/.test(e.type),e.client)A.call(e.client,e,r);else for(i=0,o=(n=e.target&&e.target!==p&&!0===E.autoActivate?$(e.target):function(){var e,t,a,r=[],n=z(h);for(e=0,t=n.length;e<t;e++)(a=h[n[e]].instance)&&a instanceof N&&r.push(a);return r}()).length;i<o;i++)l=C({},e,{client:n[i]}),A.call(n[i],l,r);return"copy"===e.type&&(s=(c=function(e){var t={},a={};if("object"==typeof e&&e){for(var r in e)if(r&&e.hasOwnProperty(r)&&"string"==typeof e[r]&&e[r])switch(r.toLowerCase()){case"text/plain":case"text":case"air:text":case"flash:text":t.text=e[r],a.text=r;break;case"text/html":case"html":case"air:html":case"flash:html":t.html=e[r],a.html=r;break;case"application/rtf":case"text/rtf":case"rtf":case"richtext":case"air:rtf":case"flash:rtf":t.rtf=e[r],a.rtf=r}return{data:t,formatMap:a}}}(d)).data,u=c.formatMap),s}};var A=function(e,t){var a=h[this.id]&&h[this.id].handlers[e.type];if(a&&a.length){var r,n,i,o;for(r=0,n=a.length;r<n;r++)o=this,"string"==typeof(i=a[r])&&"function"==typeof p[i]&&(i=p[i]),"object"==typeof i&&i&&"function"==typeof i.handleEvent&&(i=(o=i).handleEvent),"function"==typeof i&&l(i,o,[e],t)}return this},F={ready:"Flash communication is established",error:{"flash-disabled":"Flash is disabled or not installed","flash-outdated":"Flash is too outdated to support ZeroClipboard","flash-unavailable":"Flash is unable to communicate bidirectionally with JavaScript","flash-deactivated":"Flash is too outdated for your browser and/or is configured as click-to-activate","flash-overdue":"Flash communication was established but NOT within the acceptable time limit"}},B=function(e,t){if(e||t&&t.type){t=t||{},e=(e||t.type).toLowerCase(),C(t,{type:e,target:t.target||c||null,relatedTarget:t.relatedTarget||null,currentTarget:f&&f.bridge||null});var a=F[t.type];return"error"===t.type&&t.name&&a&&(a=a[t.name]),a&&(t.message=a),"ready"===t.type&&C(t,{target:null,version:f.version}),"error"===t.type&&(t.target=null,/^flash-(outdated|unavailable|deactivated|overdue)$/.test(t.name)&&C(t,{version:f.version,minimumVersion:"11.0.0"})),"copy"===t.type&&(t.clipboardData={setData:N.setData,clearData:N.clearData}),"aftercopy"===t.type&&(t=function(e,t){if("object"!=typeof e||!e||"object"!=typeof t||!t)return e;var a={};for(var r in e)if(e.hasOwnProperty(r)){if("success"!==r&&"data"!==r){a[r]=e[r];continue}a[r]={};var n=e[r];for(var i in n)i&&n.hasOwnProperty(i)&&t.hasOwnProperty(i)&&(a[r][t[i]]=n[i])}return a}(t,u)),t.target&&!t.relatedTarget&&(t.relatedTarget=H(t.target)),t}},H=function(e){var t=e&&e.getAttribute&&e.getAttribute("data-clipboard-target");return t?document.getElementById(t):null},M=function(e){var t=e.target||c;switch(e.type){case"error":w(e.name,["flash-disabled","flash-outdated","flash-deactivated","flash-overdue"])&&C(f,{disabled:"flash-disabled"===e.name,outdated:"flash-outdated"===e.name,unavailable:"flash-unavailable"===e.name,deactivated:"flash-deactivated"===e.name,overdue:"flash-overdue"===e.name,ready:!1});break;case"ready":var a=!0===f.deactivated;C(f,{disabled:!1,outdated:!1,unavailable:!1,deactivated:!1,overdue:a,ready:!a});break;case"copy":var r,n,i=e.relatedTarget;!d["text/html"]&&!d["text/plain"]&&i&&(n=i.value||i.outerHTML||i.innerHTML)&&(r=i.value||i.textContent||i.innerText)?(e.clipboardData.clearData(),e.clipboardData.setData("text/plain",r),n!==r&&e.clipboardData.setData("text/html",n)):!d["text/plain"]&&e.target&&(r=e.target.getAttribute("data-clipboard-text"))&&(e.clipboardData.clearData(),e.clipboardData.setData("text/plain",r));break;case"aftercopy":N.clearData(),t&&t!==function(){try{return document.activeElement}catch(e){}return null}()&&t.focus&&t.focus();break;case"mouseover":m(t,E.hoverClass);break;case"mouseout":!0===E.autoActivate&&N.deactivate();break;case"mousedown":m(t,E.activeClass);break;case"mouseup":b(t,E.activeClass)}};N.prototype.on=function(e,t){var a,r,n,i={},o=h[this.id]&&h[this.id].handlers;if("string"==typeof e&&e)n=e.toLowerCase().split(/\s+/);else if("object"==typeof e&&e&&void 0===t)for(a in e)e.hasOwnProperty(a)&&"string"==typeof a&&a&&"function"==typeof e[a]&&this.on(a,e[a]);if(n&&n.length){for(a=0,r=n.length;a<r;a++)i[e=n[a].replace(/^on/,"")]=!0,o[e]||(o[e]=[]),o[e].push(t);if(i.ready&&f.ready&&N.emit({type:"ready",client:this}),i.error){var l=["disabled","outdated","unavailable","deactivated","overdue"];for(a=0,r=l.length;a<r;a++)if(f[l[a]]){N.emit({type:"error",name:"flash-"+l[a],client:this});break}}}return this},N.prototype.off=function(e,t){var a,r,n,i,o,l=h[this.id]&&h[this.id].handlers;if(0===arguments.length)i=z(l);else if("string"==typeof e&&e)i=e.split(/\s+/);else if("object"==typeof e&&e&&void 0===t)for(a in e)e.hasOwnProperty(a)&&"string"==typeof a&&a&&"function"==typeof e[a]&&this.off(a,e[a]);if(i&&i.length)for(a=0,r=i.length;a<r;a++)if((o=l[e=i[a].toLowerCase().replace(/^on/,"")])&&o.length)if(t)for(n=w(t,o);-1!==n;)o.splice(n,1),n=w(t,o,n);else l[e].length=0;return this},N.prototype.handlers=function(e){var t,a=null,r=h[this.id]&&h[this.id].handlers;if(r){if("string"==typeof e&&e)return r[e]?r[e].slice(0):null;for(t in a={},r)r.hasOwnProperty(t)&&r[t]&&(a[t]=r[t].slice(0))}return a},N.prototype.clip=function(e){e=x(e);for(var t=0;t<e.length;t++)if(e.hasOwnProperty(t)&&e[t]&&1===e[t].nodeType){e[t].zcClippingId?-1===w(this.id,v[e[t].zcClippingId])&&v[e[t].zcClippingId].push(this.id):(e[t].zcClippingId="zcClippingId_"+o++,v[e[t].zcClippingId]=[this.id],!0===E.autoActivate&&(r=e[t],n="mouseover",i=g,r&&1===r.nodeType&&(r.addEventListener?r.addEventListener(n,i,!1):r.attachEvent&&r.attachEvent("on"+n,i))));var a=h[this.id].elements;-1===w(e[t],a)&&a.push(e[t])}var r,n,i;return this},N.prototype.unclip=function(e){var t=h[this.id];if(!t)return this;for(var a,r,n,i,o=t.elements,l=(e=void 0===e?o.slice(0):x(e)).length;l--;)if(e.hasOwnProperty(l)&&e[l]&&1===e[l].nodeType){for(a=0;-1!==(a=w(e[l],o,a));)o.splice(a,1);var s=v[e[l].zcClippingId];if(s){for(a=0;-1!==(a=w(this.id,s,a));)s.splice(a,1);0===s.length&&(!0===E.autoActivate&&(r=e[l],n="mouseover",i=g,r&&1===r.nodeType&&(r.removeEventListener?r.removeEventListener(n,i,!1):r.detachEvent&&r.detachEvent("on"+n,i))),delete e[l].zcClippingId)}}return this},N.prototype.elements=function(){var e=h[this.id];return e&&e.elements?e.elements.slice(0):[]};var $=function(e){var t,a,r,n,i,o=[];if(e&&1===e.nodeType&&(t=e.zcClippingId)&&v.hasOwnProperty(t)&&(a=v[t])&&a.length)for(r=0,n=a.length;r<n;r++)(i=h[a[r]].instance)&&i instanceof N&&o.push(i);return o};E.hoverClass="zeroclipboard-is-hover",E.activeClass="zeroclipboard-is-active","function"==typeof define&&define.amd?define(function(){return N}):"object"==typeof module&&module&&"object"==typeof module.exports&&module.exports?module.exports=N:p.ZeroClipboard=N}(function(){return this}());