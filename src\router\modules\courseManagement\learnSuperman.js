// 学能超人-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/learnSuperman',
  component: Layout,
  redirect: '/studyExamPassed',
  meta: {
    perm: 'm:learnSuperman',
    title: '学能超人',
    icon: 'learns'
  },
  children: [
    {
      path: '/paper',
      component: () => import('@/views/routerView/index'),
      redirect: '/paper/index',
      meta: {
        perm: 'm:paper',
        title: '试卷系统'
      },
      children: [
        {
          path: 'index',
          component: () => import('@/views/paper/question/index'),
          name: 'question',
          meta: { perm: 'm:paper:question', title: '评估题目' }
        },
        {
          path: 'singleChoice',
          component: () => import('@/views/paper/question/edit/single-choice'),
          name: 'singleChoicePage',
          meta: {
            title: '单选题编辑',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'multipleChoice',
          component: () => import('@/views/paper/question/edit/multiple-choice'),
          name: 'multipleChoicePage',
          meta: {
            title: '多选题编辑',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'trueFalse',
          component: () => import('@/views/paper/question/edit/true-false'),
          name: 'trueFalsePage',
          meta: {
            title: '判断题编辑',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'gapFilling',
          component: () => import('@/views/paper/question/edit/gap-filling'),
          name: 'gapFillingPage',
          meta: {
            title: '填空题编辑',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'shortAnswer',
          component: () => import('@/views/paper/question/edit/short-answer'),
          name: 'shortAnswerPage',
          meta: {
            title: '简答题编辑',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'combination',
          component: () => import('@/views/paper/question/edit/combination'),
          name: 'combinationrPage',
          meta: {
            title: '组合题编辑',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'strMemory',
          component: () => import('@/views/paper/question/edit/str-memory'),
          name: 'strMemory',
          meta: {
            title: '字符串记忆题',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'wordMemory',
          component: () => import('@/views/paper/question/edit/word-memory'),
          name: 'wordMemory',
          meta: {
            title: '词语记忆题',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'matchMove',
          component: () => import('@/views/paper/question/edit/match-move'),
          name: 'matchMove',
          meta: {
            title: '火柴移动题',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'iconMove',
          component: () => import('@/views/paper/question/edit/icon-move'),
          name: 'iconMove',
          meta: {
            title: '硬币移动题',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'clue',
          component: () => import('@/views/paper/question/edit/clue'),
          name: 'clue',
          meta: {
            title: '线索题',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'grid',
          component: () => import('@/views/paper/question/edit/grid'),
          name: 'grid',
          meta: {
            title: '跳格子题',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'audioFill',
          component: () => import('@/views/paper/question/edit/audio-fill'),
          name: 'audioFill',
          meta: {
            title: '音频填空题',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'textRepeat',
          component: () => import('@/views/paper/question/edit/text-repeat'),
          name: 'textRepeat',
          meta: {
            title: '文字找重题',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          path: 'card',
          component: () => import('@/views/paper/question/edit/card'),
          name: 'card',
          meta: {
            title: '答题卡题',
            noCache: true,
            activeMenu: '/exam/question/list'
          },
          hidden: true
        },
        {
          hidden: true,
          path: 'articleMemory',
          component: () => import('@/views/paper/question/edit/article-memory'),
          name: 'articleMemory',
          meta: { title: '物品记忆题', noCache: true }
        },
        {
          hidden: true,
          path: 'imageAnswer',
          component: () => import('@/views/paper/question/edit/image-answer'),
          name: 'imageAnswer',
          meta: { title: '图片问答题', noCache: true }
        },
        {
          hidden: true,
          path: 'listenAnswer',
          component: () => import('@/views/paper/question/edit/listen-answer'),
          name: 'listenAnswer',
          meta: { title: '听力问答题', noCache: true }
        },
        {
          path: 'schoolSection',
          component: () => import('@/views/paper/grade/index'),
          name: 'schoolSection',
          meta: {
            perm: 'm:paper:schoolSection',
            title: '学段管理'
          }
        },
        {
          path: 'subject',
          component: () => import('@/views/paper/subject/index'),
          name: 'subject',
          meta: { perm: 'm:paper:subject', title: '维度管理' }
        },
        {
          path: 'abilityReport',
          component: () => import('@/views/paper/abilityReport/index'),
          name: 'abilityReport',
          meta: {
            perm: 'm:paper:abilityReport',
            title: '报告管理'
          }
        },
        {
          path: 'abilityReportSave',
          component: () => import('@/views/paper/abilityReport/save'),
          name: 'abilityReportSave',
          meta: {
            perm: 'm:paper:abilityReportSave',
            title: '报告新增/修改',
            icon: 'zhibo'
          },
          hidden: true
        },
        {
          path: 'assessmentReport',
          component: () => import('@/views/paper/assessmentReport/index'),
          name: 'assessmentReport',
          meta: {
            perm: 'm:paper:assessmentReport',
            title: '评估课报告管理'
          }
        },
        {
          path: 'assessmentReportSave',
          component: () => import('@/views/paper/assessmentReport/assessmentSave'),
          name: 'assessmentReportSave',
          meta: {
            perm: 'm:paper:assessmentReportSave',
            title: '评估报告新增/修改',
            icon: 'zhibo'
          },
          hidden: true
        },
        {
          path: 'knowledge',
          component: () => import('@/views/paper/knowledge/index'),
          name: 'knowledge',
          meta: { perm: 'm:paper:knowledge', title: '知识点管理' }
        },
        {
          path: 'course',
          component: () => import('@/views/paper/course/index'),
          name: 'course',
          meta: { perm: 'm:paper:course', title: '课程管理' }
        },
        {
          path: 'courseQuestion',
          component: () => import('@/views/paper/course/question'),
          name: 'courseQuestion',
          meta: {
            perm: 'm:paper:courseQuestion',
            title: '问题列表',
            icon: 'zhibo'
          },
          hidden: true
        }
      ]
    },
    {
      path: '/train',
      component: () => import('@/views/routerView/index'),
      redirect: '/train/question',
      meta: {
        perm: 'm:train',
        title: '训练管理'
      },
      children: [
        {
          path: 'category',
          component: () => import('@/views/paper/train/category/index'),
          name: 'category',
          meta: { perm: 'm:train:category', title: '维度管理' }
        },
        {
          path: 'guide',
          component: () => import('@/views/paper/train/guide/index'),
          name: 'guide',
          meta: { perm: 'm:train:guide', title: '引导管理' }
        },
        {
          path: 'guideData',
          hidden: true,
          component: () => import('@/views/paper/train/guide/data'),
          name: 'guideData',
          meta: { title: '引导问答设置', icon: 'zhibo' }
        },
        {
          path: 'difficulty',
          component: () => import('@/views/paper/train/difficulty/index'),
          name: 'difficulty',
          meta: { perm: 'm:train:difficulty', title: '难度管理' }
        },
        {
          path: 'trainImage',
          component: () => import('@/views/paper/train/image/index'),
          name: 'trainImage',
          meta: { perm: 'm:train:trainImage', title: '题库图片' }
        },
        {
          path: 'auditory',
          component: () => import('@/views/paper/train/question/auditory/index'),
          name: 'auditory',
          meta: { perm: 'm:train:auditory', title: '听力题' }
        },
        {
          path: 'auditoryFollow',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/follow'),
          name: 'auditoryFollow',
          meta: {
            perm: 'm:train:auditoryFollow',
            title: '跟读题',
            icon: 'zhibo'
          }
        },
        {
          path: 'auditoryHandle',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/handle'),
          name: 'auditoryHandle',
          meta: {
            perm: 'm:train:auditoryHandle',
            title: '听力动手题',
            icon: 'zhibo'
          }
        },
        {
          path: 'auditoryFilling',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/filling'),
          name: 'auditoryFilling',
          meta: {
            perm: 'm:train:auditoryFilling',
            title: '听力填空题',
            icon: 'zhibo'
          }
        },
        {
          path: 'auditoryWordFilling',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/audio_filling'),
          name: 'auditoryWordFilling',
          meta: {
            perm: 'm:train:auditoryWordFilling',
            title: '音频文本填空题',
            icon: 'zhibo'
          }
        },
        {
          path: 'auditoryWordFillingNew',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/audio_filling_new'),
          name: 'auditoryWordFillingNew',
          meta: {
            perm: 'm:train:auditoryWordFillingNew',
            title: '新音频文本填空题',
            icon: 'zhibo'
          }
        },
        {
          path: 'auditoryDistinguish',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/distinguish'),
          name: 'auditoryDistinguish',
          meta: {
            perm: 'm:train:auditoryDistinguish',
            title: '听力辨别题',
            icon: 'zhibo'
          }
        },
        {
          path: 'auditoryLeak',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/leak'),
          name: 'auditoryLeak',
          meta: {
            perm: 'm:train:auditoryLeak',
            title: '听力漏读题',
            icon: 'zhibo'
          }
        },
        {
          path: 'auditoryAnswer',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/answer'),
          name: 'auditoryAnswer',
          meta: {
            perm: 'm:train:auditoryAnswer',
            title: '听力回答题',
            icon: 'zhibo'
          }
        },
        {
          path: 'auditoryAnswerNew',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/answer_new'),
          name: 'auditoryAnswerNew',
          meta: {
            perm: 'm:train:auditoryAnswerNew',
            title: '新听力回答题',
            icon: 'zhibo'
          }
        },
        {
          path: 'auditoryRepeat',
          hidden: true,
          component: () => import('@/views/paper/train/question/auditory/repeat'),
          name: 'visualRepeat',
          meta: { perm: 'm:train:visualRepeat', title: '找重题', icon: 'zhibo' }
        },
        {
          path: 'visual',
          component: () => import('@/views/paper/train/question/visual/index'),
          name: 'visual',
          meta: { perm: 'm:train:visual', title: '视觉题' }
        },
        {
          path: 'visualDifferent',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/different'),
          name: 'visualDifferent',
          meta: {
            perm: 'm:train:visualDifferent',
            title: '数字找不同题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualDifferentNew',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/different_new'),
          name: 'visualDifferentNew',
          meta: {
            perm: 'm:train:visualDifferentNew',
            title: '新数字找不同题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualPattern',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/pattern'),
          name: 'visualPattern',
          meta: {
            perm: 'm:train:visualLeak',
            title: '找图形个数题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualDistinguish',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/distinguish'),
          name: 'visualDistinguish',
          meta: {
            perm: 'm:train:visualDistinguish',
            title: '视觉分辨题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualLeak',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/leak'),
          name: 'visualLeak',
          meta: {
            perm: 'm:train:visualLeak',
            title: '方格正序题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualLeakNew',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/leak_new'),
          name: 'visualLeakNew',
          meta: {
            perm: 'm:train:visualLeakNew',
            title: '新方格正序题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualChoice',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/choice'),
          name: 'visualChoice',
          meta: {
            perm: 'm:train:visualChoice',
            title: '图片找不同题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualFillLeak',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/fillLeak'),
          name: 'visualFillLeak',
          meta: {
            perm: 'm:train:visualFillLeak',
            title: '填漏题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualRead',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/read'),
          name: 'visualRead',
          meta: { perm: 'm:train:visualRead', title: '朗读题', icon: 'zhibo' }
        },
        {
          path: 'visualReadNew',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/read_new'),
          name: 'visualReadNew',
          meta: { perm: 'm:train:visualReadNew', title: '新朗读题', icon: 'zhibo' }
        },
        {
          path: 'visualPicMemory',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/picMemory'),
          name: 'visualPicMemory',
          meta: {
            perm: 'm:train:visualPicMemory',
            title: '图片记忆题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualWordMemory',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/wordMemory'),
          name: 'visualWordMemory',
          meta: {
            perm: 'm:train:visualWordMemory',
            title: '单词记忆题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualNumMemory',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/numMemory'),
          name: 'visualNumMemory',
          meta: {
            perm: 'm:train:visualNumMemory',
            title: '数字记忆题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualFindWord',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/findWord'),
          name: 'visualFindWord',
          meta: {
            perm: 'm:train:visualFindWord',
            title: '圈数字题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualFindWordNew',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/findWord_new'),
          name: 'visualFindWordNew',
          meta: {
            perm: 'm:train:visualFindWordNew',
            title: '新圈数字题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualAntiInterference',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/anti_interference'),
          name: 'visualAntiInterference',
          meta: {
            perm: 'm:train:visualAntiInterference',
            title: '视觉转移题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualTextRepeat',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/textRepeat'),
          name: 'visualTextRepeat',
          meta: {
            perm: 'm:train:visualTextRepeat',
            title: '文字找重题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualTextRepeatNew',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/textRepeat_new'),
          name: 'visualTexvisualTextRepeatNewtRepeat',
          meta: {
            perm: 'm:train:visualTextRepeatNew',
            title: '新文字找重题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualPictureGrid',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/pictureGrid'),
          name: 'visualPictureGrid',
          meta: {
            perm: 'm:train:visualPictureGrid',
            title: '图片方格题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualFeatureGraphics',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/featureGraphics'),
          name: 'visualFeatureGraphics',
          meta: {
            perm: 'm:train:visualFeatureGraphics',
            title: '特征找图形题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualFeatureGraphicsNew',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/featureGraphics_new'),
          name: 'visualFeatureGraphicsNew',
          meta: {
            perm: 'm:train:visualFeatureGraphicsNew',
            title: '新特征找图形题',
            icon: 'zhibo'
          }
        },
        {
          path: 'visualGraphicMatching',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/graphicMatching'),
          name: 'visualGraphicMatching',
          meta: {
            perm: 'm:train:visualGraphicMatching',
            title: '图形匹配题',
            icon: 'zhibo'
          }
        },
        {
          path: 'upload',
          component: () => import('@/views/paper/train/question/upload/index'),
          name: 'upload',
          meta: { perm: 'm:train:upload', title: '拍照上传题' }
        },
        {
          path: 'uploadCopy',
          hidden: true,
          component: () => import('@/views/paper/train/question/upload/copy'),
          name: 'uploadCopy',
          meta: {
            perm: 'm:train:uploadCopy',
            title: '内容抄写题',
            icon: 'zhibo'
          }
        },
        {
          path: 'uploadDrawing',
          hidden: true,
          component: () => import('@/views/paper/train/question/upload/drawing'),
          name: 'uploadDrawing',
          meta: {
            perm: 'm:train:uploadDrawing',
            title: '看图画画题',
            icon: 'zhibo'
          }
        },
        {
          path: 'step',
          component: () => import('@/views/paper/train/question/visual/step'),
          name: 'step',
          meta: { perm: 'm:train:step', title: '题型步骤方法' }
        },
        {
          path: 'lookWordsCreateSentences',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/lookWordsCreateSentences'),
          name: 'lookWordsCreateSentences',
          meta: { perm: 'm:train:lookWordsCreateSentences', title: '看字组句题', icon: 'zhibo' }
        },
        {
          path: 'findIdenticalWordExamples',
          hidden: true,
          component: () => import('@/views/paper/train/question/visual/findIdenticalWordExamples'),
          name: 'findIdenticalWordExamples',
          meta: { perm: 'm:train:findIdenticalWordExamples', title: '找相同字例题', icon: 'zhibo' }
        },
        {
          path: 'stare',
          component: () => import('@/views/paper/train/question/stare/index'),
          name: 'stare',
          meta: { perm: 'm:train:stare', title: '课外练习' }
        },
        {
          path: 'starePoint',
          component: () => import('@/views/paper/train/question/stare/point'),
          name: 'starePoint',
          hidden: true,
          meta: { perm: 'm:train:starePoint', title: '盯点练习', icon: 'zhibo' }
        },
        {
          path: 'stareMiniGame',
          component: () => import('@/views/paper/train/question/stare/miniGame'),
          name: 'stareMiniGame',
          hidden: true,
          meta: { perm: 'm:train:stareMiniGame', title: '课前小游戏', icon: 'zhibo' }
        },
        {
          path: 'stareVideo',
          component: () => import('@/views/paper/train/question/stare/video'),
          name: 'stareVideo',
          hidden: true,
          meta: { perm: 'm:train:stareVideo', title: '课前视频', icon: 'zhibo' }
        },
        {
          path: 'stareFingerCalc',
          component: () => import('@/views/paper/train/question/stare/fingerCalc'),
          name: 'stareFingerCalc',
          hidden: true,
          meta: { perm: 'm:train:stareFingerCalc', title: '手指计算题', icon: 'zhibo' }
        },
        {
          path: 'course',
          component: () => import('@/views/paper/train/course/index'),
          name: 'course',
          meta: { perm: 'm:train:course', title: '课程管理' }
        },
        {
          path: 'pass',
          hidden: true,
          component: () => import('@/views/paper/train/course/pass'),
          name: 'pass',
          meta: { perm: 'm:train:pass', title: '关卡管理', icon: 'zhibo' }
        },
        {
          path: 'question',
          hidden: true,
          component: () => import('@/views/paper/train/course/question'),
          name: 'question',
          meta: { perm: 'm:train:question', title: '关卡问题', icon: 'zhibo' }
        },
        {
          path: 'trainAfterCourse',
          component: () => import('@/views/paper/train-after-class/question-bank.vue'),
          name: 'trainAfterCourse',
          meta: {
            perm: 'm:train:trainAfterCourse',
            title: '一课一练课程'
          }
        },
        {
          path: 'learnmaster',
          component: () => import('@/views/paper/train/learnmaster/index'),
          name: 'learnmaster',
          meta: {
            perm: 'm:learnmaster:list',
            title: '学能师管理'
          }
        },
        {
          path: 'learnmasterStudents',
          component: () => import('@/views/paper/train/learnmaster/students'),
          hidden: true,
          name: 'learnmasterStudents',
          meta: {
            perm: 'm:learnmaster:students',
            title: '学员管理',
            icon: 'zhibo'
          }
        }
      ]
    }
  ]
};
