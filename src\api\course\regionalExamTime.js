import request from '@/utils/request'
export function getRegionalExamTimeList(data) {
  return request({
    url: `znyy/wap/regionalExamTime/list/${data.pageNum}/${data.pageSize}`,
    method: 'GET',
    params: data
  })
}
export function addRegionalExamTime(data) {
  return request({
    url: 'znyy/wap/regionalExamTime/add',
    method: 'PUT',
    data
  })
}
export function editRegionalExamTime(data) {
  return request({
    url: 'znyy/wap/regionalExamTime/edit',
    method: 'POST',
    data
  })
}
export function deleteRegionalExamTime(data) {
  return request({
    url: 'znyy/wap/regionalExamTime/delete',
    method: 'DELETE',
    params: data
  })
}