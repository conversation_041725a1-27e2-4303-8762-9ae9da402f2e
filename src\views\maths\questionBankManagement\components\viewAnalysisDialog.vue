<template>
  <div>
    <!-- 题目区域 -->
    <div class="mb-10" v-loading="loading">
      <div class="mb-10" style="font-size: 16px; font-weight: bold">题干：</div>
      <div class="bgc-color">
        <mathematicalFormula :formulaText="topicList.questionText" />
      </div>
    </div>
    <div class="flex-around bgc-color">
      <el-image v-for="(url, indexUrl) in topicList.questionImg" :key="indexUrl" style="height: 100px" :src="url" fit="scale-down"></el-image>
    </div>
    <div v-for="(group, groupIndex) in groupByQuestionLocation(topicList.mathQuestionOptionVos)" :key="groupIndex">
      <!-- 显示问题 -->
      <div style="margin: 20px 0; font-weight: bold">问题 {{ Number(groupIndex) + 1 }}：{{ group.questionSmallProblem }}</div>
      <!-- 显示选项 -->
      <div v-for="(option, optionIndex) in group.options" :key="optionIndex" class="option">
        <!-- <div >{{ option.choiceOption }}. {{ option.content }}</div> -->
        <mathematicalFormula :formulaText="`${option.choiceOption}. ${option.content}`" />
      </div>
    </div>
    <div style="text-align: left; margin: 20px 0">
      <div style="font-size: 16px; font-weight: bold" class="mb-10">题目解析：</div>
      <div class="mb-10 bgc-color">最终答案为：{{ topicList.correctAnswer }}</div>
      <!-- <div class="bgc-color">
        {{ topicList.analysis }}
      </div> -->
      <div class="bgc-color">
        <mathematicalFormula :formulaText="topicList.analysis" />
      </div>
      <div class="flex-around">
        <!-- <el-image v-for="(url, indexUrl) in topicList.analysisImg" :key="indexUrl" :preview-src-list="item.analysisImg" style="height: 100px" :src="url"></el-image> -->
        <el-image v-for="(urlImage, indexUrl) in topicList?.analysisImg" :key="indexUrl" style="height: 100px" fit="scale-down" :src="urlImage"></el-image>
      </div>
    </div>
  </div>
</template>

<script>
  import { listQuestionOneAPI } from '@/api/mathApi/topicManagementAPI';
  import mathematicalFormula from './mathematicalFormula.vue';
  export default {
    name: 'ViewAnalysisDialog',
    components: {
      mathematicalFormula
    },
    props: {
      viewId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        topicList: [],
        loading: true
      };
    },
    created() {
      this.getTopicList();
    },
    watch: {
      // 监听 viewId 的变化
      viewId(newVal) {
        this.topicList = []; // 清空之前的数据
        this.getTopicList(); // 当 viewId 变化时重新请求数据
      }
    },
    methods: {
      async getTopicList() {
        this.loading = true; // 开始加载
        try {
          const res = await listQuestionOneAPI({ id: this.viewId });
          if (res.success) {
            this.topicList = res.data;
            console.log('🚀 ~ getTopicList ~ this.topicList:', this.topicList);
          }
        } catch (error) {
          console.error('数据加载失败:', error);
        } finally {
          this.loading = false; // 加载完成
        }
      },
      groupByQuestionLocation(options) {
        return options.reduce((groups, option) => {
          const location = option.questionLocation || 0; // 默认值为 0
          if (!groups[location]) {
            groups[location] = {
              questionSmallProblem: option.questionSmallProblem, // 小问题
              scoreProportion: option.scoreProportion, // 分数占比
              options: [] // 选项列表
            };
          }
          groups[location].options.push(option); // 将选项添加到对应分组
          return groups;
        }, {});
      }
    }
  };
</script>

<style lang="scss" scoped>
  .mb-10 {
    margin-bottom: 10px;
  }
  .bgc-color {
    padding: 10px;
  }
  .flex-around {
    display: flex;
    justify-content: space-around;
  }
</style>
