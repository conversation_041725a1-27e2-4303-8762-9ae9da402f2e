// 数学超人-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/_aaa_demo',
  redirect: '/_aaa_demo/courseManagementList',
  component: Layout,
  meta: {
    perm: 'm:aaademo',
    title: '数学超人',
    icon: 'math'
  },
  children: [
    // 鼎数学--课程管理
    {
      path: 'courseManagementList',
      component: () => import('@/views/courseManagement/courseManagementList.vue'),
      name: 'courseManagementList',
      meta: {
        perm: 'm:aaademo:courseManagementList',
        title: '课程管理'
      }
    },
    {
      path: 'speech',
      hidden: true,
      component: () => import('@/views/courseManagement/speech.vue'),
      name: 'speech',
      meta: {
        perm: 'm:aaademo:speech',
        title: '话术',
        icon: 'course'
      }
    },
    {
      path: 'testPaperManagement',
      hidden: false,
      component: _import('_aaa_demo/testPaperManagement/index'),
      name: 'testPaperManagement',
      meta: {
        perm: 'm:aaademo:testPaperManagement',
        title: '试卷管理'
      }
    },
    {
      path: 'addPaper',
      hidden: true,
      component: _import('_aaa_demo/testPaperManagement/addPaper'),
      name: 'addPaper',
      meta: {
        perm: 'm:aaademo:addPaper',
        title: '新增/编辑试卷',
        icon: 'agent'
      }
    },
    {
      path: 'addCourse',
      hidden: true,
      component: () => import('@/views/courseManagement/addCourse.vue'),
      name: 'addCourse',
      meta: {
        perm: 'm:aaademo:addCourse',
        title: '新增/编辑课程',
        icon: 'course'
      }
    },
    {
      path: 'mathsVedioManage',
      component: () => import('@/views/mathsVedioManage/videoManagment.vue'),
      name: 'vedioManage',
      meta: {
        perm: 'm:aaademo:videoManagment',
        title: '视频管理'
      }
    },
    {
      path: 'editVideoNodes',
      component: () => import('@/views/mathsVedioManage/editVideoNodes.vue'),
      name: 'editVideoNodes',
      hidden: true,
      meta: {
        perm: 'm:aaademo:editVideoNodes',
        title: '编辑视频节点',
        icon: 'course'
      }
    },
    {
      path: 'addVideo',
      hidden: true,
      component: () => import('@/views/mathsVedioManage/addVideo.vue'),
      name: 'addVideo',
      meta: {
        perm: 'm:aaademo:addVideo',
        title: '新增/编辑视频',
        icon: 'course'
      }
    },
    {
      path: 'topicManagement',
      component: () => import('@/views/maths/questionBankManagement/topicManagement'),
      name: 'topicManagement',
      meta: {
        perm: 'm:aaademo:topicManagement',
        title: '题库管理',
        noCache: true
      }
    },
    {
      path: 'addQuestion',
      hidden: true,
      component: () => import('@/views/maths/questionBankManagement/addQuestion'),
      name: 'addQuestion',
      meta: {
        perm: 'm:aaademo:addQuestion',
        title: '新增/编辑题目'
      }
    },
    {
      path: 'knowledgeManagement',
      hidden: false,
      component: _import('_aaa_demo/knowledgeManagement/index'),
      name: 'knowledgeManagement',
      meta: {
        perm: 'm:aaademo:knowledgeManagement',
        title: '知识点管理'
      }
    },
    {
      path: 'studentManagement',
      hidden: false,
      component: _import('_aaa_demo/studentManagement/index'),
      name: 'studentManagement',
      meta: {
        perm: 'm:aaademo:studentManagement',
        title: '学生管理'
      }
    },
    {
      path: 'courseTypeConfig',
      hidden: false,
      component: _import('_aaa_demo/courseTypeConfig/index'),
      name: 'courseTypeConfig',
      meta: {
        perm: 'm:aaademo:courseTypeConfig',
        title: '课程分类配置'
      }
    },
    {
      path: 'topicOfClassConfig',
      hidden: false,
      component: _import('_aaa_demo/topicOfClassConfig/index'),
      name: 'topicOfClassConfig',
      meta: {
        perm: 'm:aaademo:topicOfClassConfig',
        title: '班型题目配置'
      }
    }
  ]
};
