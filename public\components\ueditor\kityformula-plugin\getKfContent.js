UE.Editor.prototype.getKfContent=function(t){var s=this,e=s.getActionUrl(s.getOpt("scrawlActionName")),r=UE.utils.serializeParam(s.queryCommandValue("serverparam"))||"",a=UE.utils.formatUrl(e+(-1==e.indexOf("?")?"?":"&")+r),i=0,n=s.body.getElementsByTagName("img"),o=[];function c(){i>=o.length&&(s.sync(),t(s.getContent()))}UE.utils.each(n,function(t){"data:image/png"===t.getAttribute("src").match(/^[^;]+/)[0]&&o.push(t)}),0==o.length?c():UE.utils.each(o,function(n){var t={};t[s.getOpt("scrawlFieldName")]=n.getAttribute("src").replace(/^[^,]+,/,""),t.onsuccess=function(t){var e=UE.utils.str2json(t.responseText),r=s.options.scrawlUrlPrefix+e.url;n.setAttribute("src",r),n.setAttribute("_src",r),i++,c()},t.onerror=function(t){console.error(t),i++,c()},UE.ajax.request(a,t)})};