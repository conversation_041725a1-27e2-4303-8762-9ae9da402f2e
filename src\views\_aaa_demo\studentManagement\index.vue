<!-- 学生管理 -->
<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="课程大类：">
        <el-select v-model="dataQuery.curriculumId" placeholder="请选择" @change="handleCurriculumChange">
          <el-option v-for="item in kcdlList" :key="item.id" :value="item.id" :label="item.enName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户信息：">
        <el-input v-model.trim="dataQuery.yhxx" placeholder="请输入学生姓名/学生编号搜索" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-refresh" style="margin-left: 5px" @click="reset">重置</el-button>
        <el-button type="primary" style="margin-right: 5px" @click="submitForm">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="container">
      <el-table class="common-table" v-loading="listLoading" :data="tableData" row-key="id" :default-sort="{ prop: 'sort', order: 'descending' }" height="62vh">
        <el-table-column type="index" width="50" label="序号"></el-table-column>
        <el-table-column prop="curriculumName" label="课程大类" />
        <el-table-column prop="userName" label="用户昵称" />
        <el-table-column prop="studentCode" label="学生编号" />
        <el-table-column label="操作" align="center" width="220px">
          <template slot-scope="{ row }">
            <el-button size="mini" type="text" @click="viewQuestion(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination">
        <el-col :span="24" style="margin-bottom: 20px; margin-top: 20px; padding-right: 20px">
          <el-pagination
            :current-page="tablePage.currentPage"
            :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tablePage.totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-col>
      </div>
    </div>
    <!-- 查看 -->
    <el-dialog title="查看" :visible.sync="viewDiaOpen" width="50%" :close-on-click-modal="false">
      <el-form ref="importFrom" :model="importFrom" label-position="top" label-width="120px" style="width: 100%" v-loading="formLoading">
        <h3>基础信息</h3>
        <el-row :gutter="20">
          <el-col :span="24" style="margin-bottom: 20px">
            <el-image style="width: 100px; height: 100px" :src="importFrom.avatarUrl" :preview-src-list="srcList">
              <div slot="error" class="image-slot">
                <i style="font-size: 100px" class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <!-- <el-image style="width: 100px; height: 100px" v-else :lazy="true">
             
            </el-image> -->
          </el-col>
          <el-col :span="9">学生姓名：{{ importFrom.userName }}</el-col>
          <el-col :span="15">学生编号：{{ importFrom.studentCode }}</el-col>
        </el-row>
        &nbsp;&nbsp;
        <h3>课程信息</h3>
        <div class="listBox">
          <div v-for="(item, index) in importFrom.courseStudentDetailVoList" :key="index">
            <el-row :gutter="10" style="margin-bottom: 20px">
              <el-col :span="7">课程大类：{{ item.curriculumName }}</el-col>
              <el-col :span="3">版本：{{ item.courseVersionName }}</el-col>
              <el-col :span="3">学科：{{ item.disciplineName }}</el-col>
              <el-col :span="4">学段：{{ item.educationalStageName }}</el-col>
              <el-col :span="5">学前测结果：{{ item.scoreLevel + '(' + item.score + '分' + ')' }}</el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="viewDiaOpen = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import '/public/components/ueditor/themes/iframe.css';
  import forStudent from '@/api/testPaper/management';

  export default {
    name: 'studentManagement',
    data() {
      return {
        srcList: [],
        kcdlList: [], // 课程大类列表
        viewDiaOpen: false,
        importFrom: {
          avatarUrl: '', // 头像
          userName: '', // 用户名
          studentCode: '', // 学生编号
          courseStudentDetailVoList: [] // 课程信息
        },
        dataQuery: {
          curriculumId: null, // 课程大类
          curriculumName: '',
          yhxx: null
        },
        listLoading: false,
        formLoading: false,
        tableData: [],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
      };
    },
    created() {
      this.getKcdlList();
    },
    watch: {
      $route(to, from) {
        console.log(to, from, 'studentManagement路由变化了，重新加载数据');
        if (to.path == '/_aaa_demo/studentManagement') {
          this.reset();
        }
      }
    },
    methods: {
      handleCurriculumChange() {
        this.getPageList();
      },
      reset() {
        this.dataQuery.yhxx = null;
        this.tablePage.currentPage = 1;
        this.tablePage.size = 10;
        this.dataQuery.curriculumId = this.kcdlList[0].id;
        this.getPageList();
      },
      submitForm() {
        this.tablePage.currentPage = 1;
        this.tablePage.size = 10;
        this.getPageList();
      },
      getPageList() {
        this.listLoading = true;
        let data = {
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.size,
          curriculumId: this.dataQuery.curriculumId,
          keyWord: this.dataQuery.yhxx
        };

        forStudent
          .searchForStudent(data)
          .then((res) => {
            if (res.success) {
              this.tableData = res.data.data;
              this.tablePage.totalItems = Number(res.data.totalItems);
              this.listLoading = false;
            }
          })
          .catch((err) => {
            this.listLoading = false;
          });
      },
      viewQuestion(row) {
        this.viewDiaOpen = true;
        this.formLoading = true;
        let data = {
          courseStudentId: row.id
        };
        forStudent
          .searchForDetail(data)
          .then((res) => {
            if (res.success) {
              this.importFrom = res.data;
              this.srcList = [res.data.avatarUrl];
              this.formLoading = false;
            }
          })
          .catch((err) => {
            this.formLoading = false;
          });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.getPageList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getPageList();
      },
      getKcdlList() {
        forStudent.getKcdlForKnowledge().then((res) => {
          if (res.success) {
            this.kcdlList = res.data;
            this.dataQuery.curriculumId = res.data[0].id;
            this.dataQuery.curriculumName = res.data[0].enName;
            this.submitForm();
          }
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  /deep/.el-dialog__header {
    border-bottom: 1px solid #dfdfdf;
  }
  .container {
    width: 100%;
    // height: 100%;
    height: 78vh;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    padding: 18px;
    box-sizing: border-box;
    border-radius: 6px;
    position: relative;
    .pagination {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
  .listBox {
    max-height: 120px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  /* 表格区域样式 */
  .add-course-btn {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  ::v-deep .common-table .el-table__header th {
    background-color: #f0f0f0;
  }
  ::v-deep .el-pagination {
    text-align: right;
  }
  .disabled {
    color: #aaa;
  }
</style>
