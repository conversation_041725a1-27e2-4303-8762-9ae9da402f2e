<template>
  <div v-loading="loading">
    <!-- 题干模块 -->
    <div class="section">
      <div class="section-title">题干：</div>
      <div class="section-content">
        <div>{{ topicList.questionText }}</div>
        <!-- 题干图片 -->
        <div v-if="topicList.questionImg && topicList.questionImg.length > 0" class="image-container">
          <el-image
            v-for="(url, index) in topicList.questionImg"
            :key="index"
            style="height: 100px; margin-right: 10px"
            :src="url"
            fit="scale-down"
            :preview-src-list="topicList.questionImg"
          ></el-image>
        </div>
      </div>
    </div>

    <!-- 问题模块 -->
    <div class="section" v-if="topicList.questionType !== 1 && topicList.questionType !==4">
      <!-- 单选和多选题 -->
      <div class="section-title">选项：</div>
      <div class="section-content">
        <div v-for="(option, index) in topicList.mathQuestionOptionVos" :key="index" class="option-item">
          {{ option.choiceOption }}. {{ option.content }}
        </div>
      </div>
    </div>

    <div v-else>
      <!-- 填空题 -->
      <div
        v-for="(group, locationIndex) in groupedOptions"
        :key="locationIndex"
        class="section">
        <div class="section-title">填空{{ parseInt(locationIndex) + 1 }}：</div>
        <div class="section-content">
          <div v-for="(option, optionIndex) in group" :key="optionIndex" class="option-item">
            {{ option.choiceOption }}. {{ option.content }}
          </div>
        </div>
      </div>
    </div>

    <!-- 解析模块 -->
    <div class="section">
      <div class="section-title">题目解析：</div>
      <div class="section-content">
        <div class="mb-10">最终答案为：{{ topicList.correctAnswer }}</div>
        <div>{{ topicList.analysis }}</div>
        <!-- 解析图片 -->
        <div v-if="topicList.analysisImg && topicList.analysisImg.length > 0" class="image-container">
          <el-image
            v-for="(url, index) in topicList.analysisImg"
            :key="index"
            style="height: 100px; margin-right: 10px"
            :src="url"
            fit="scale-down"
            :preview-src-list="topicList.analysisImg"
          ></el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listQuestionOneAPI } from '@/api/mathApi/topicManagementAPI';
import { getQuestionDetail } from '@/api/superReaderAPI/testBaseManagement';

export default {
  name: 'ViewAnalysisDialog',
  props: {
    viewId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      topicList: {},
      loading: true
    };
  },
  computed: {
    groupedOptions() {
      if (!this.topicList.mathQuestionOptionVos) return {};
      return this.topicList.mathQuestionOptionVos.reduce((groups, option) => {
        const location = option.questionLocation || 0;
        if (!groups[location]) {
          groups[location] = [];
        }
        groups[location].push(option);
        return groups;
      }, {});
    }
  },
  created() {
    this.getTopicList();
  },
  watch: {
    viewId(newVal) {
      this.topicList = {};
      this.getTopicList();
    }
  },
  methods: {
    async getTopicList() {
      this.loading = true;
      try {
        const res = await getQuestionDetail({ id: this.viewId });
        if (res.success) {
          this.topicList = res.data;
        }
      } catch (error) {
        console.error('获取题目详情失败:', error);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.section {
  margin-bottom: 20px;
  text-align: left;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.section-content {
  padding: 10px;
  background-color: #f5f5f5;
}

.option-item {
  margin-bottom: 5px;
}

.image-container {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
}

.mb-10 {
  margin-bottom: 10px;
}
</style>

