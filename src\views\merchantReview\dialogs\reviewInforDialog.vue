<template>
  <div>
    <CustomDialog
      :title="title"
      :value.sync="dialogVisible"
      width="980px"
      top="5%"
      :close-on-click-modal="title == '查看' ? true : false"
      titleColor="#ffffff"
      titleBackgroundColor="#60766d"
      borderRadius="8px"
      @close="handleClose"
      class="review-dialog"
    >
      <div v-loading="reviewLoading">
        <div class="dialog-tabs">
          <div class="tab-header" style="position: relative">
            <div class="tab-item" :class="{ active: activeTab === 'reviewInfo' }" @click="switchTab('reviewInfo')">审核信息</div>
            <div class="tab-item" :class="{ active: activeTab === 'reviewProcess' }" @click="switchTab('reviewProcess')">审核流程</div>
            <div class="tab-underline" :class="activeTab"></div>
          </div>

          <div class="tab-content">
            <!-- 审核信息tab -->
            <div v-show="activeTab === 'reviewInfo'" class="review-info">
              <div class="info-section">
                <div class="info-row">
                  <div class="info-item">
                    <span class="label">{{ storeClubType == '俱乐部' ? '俱乐部' : '门店' }}名称：</span>
                    <span class="value">{{ reviewDetailedData.merchantName }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">负责人姓名：</span>
                    <span class="value">{{ reviewDetailedData.realName }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">个人职业：</span>
                    <span class="value">{{ reviewDetailedData.occupation }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">登录账号：</span>
                    <span class="value">{{ reviewDetailedData.name }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">身份证号码：</span>
                    <span class="value">{{ reviewDetailedData.idCard }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">身份证照片：</span>
                    <div class="id-card-photos">
                      <div v-for="(item, index) in reviewDetailedData.idCardPhoto || []" :key="index">
                        <el-image fit="cover" class="photo-placeholder" :src="item" :preview-src-list="[item]"></el-image>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">{{ storeClubType == '俱乐部' ? '所属品牌：' : '所属俱乐部：' }}</span>
                    <span class="value">{{ storeClubType == '俱乐部' ? reviewDetailedData.brandName : reviewDetailedData.operationsName }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">{{ storeClubType == '俱乐部' ? '渠道合作伙伴：' : '课程推广大使：' }}</span>
                    <span class="value">{{ reviewDetailedData.refereeName }}</span>
                  </div>
                </div>

                <div class="info-row" v-if="storeClubType == '俱乐部'">
                  <div class="info-item">
                    <span class="label">企业名称</span>
                    <span class="value">{{ reviewDetailedData.businessName }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">业务开展地：</span>
                    <span class="value">{{ reviewDetailedData.businessArea }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label businessLocation">
                      业务开展地是
                      <br />
                      否一致：
                    </span>
                    <span class="value" style="line-height: 17px">
                      <br />
                      {{ reviewDetailedData.isBusinessArea === 1 ? '是' : '否' }}
                    </span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">经营地址：</span>
                    <span class="value">{{ reviewDetailedData.businessAddress }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">孩子的年龄：</span>
                    <span class="value">{{ reviewDetailedData.childAge }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">所在的行业：</span>
                    <span class="value">{{ reviewDetailedData.industry }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">特长或能力：</span>
                    <span class="value">{{ reviewDetailedData.ability }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">从事行业：</span>
                    <span class="value">{{ reviewDetailedData.engagedIndustry }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">行业资源：</span>
                    <span class="value">{{ reviewDetailedData.industryResources }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">年收入：</span>
                    <span class="value">{{ reviewDetailedData.annualIncome }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">个人资源：</span>
                    <span class="value">{{ reviewDetailedData.personalResources }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">个人成就：</span>
                    <span class="value">{{ reviewDetailedData.personalAchievements }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">
                      通过何种方式
                      <br />
                      了解鼎校：
                    </span>
                    <span class="value" style="line-height: 17px">
                      <br />
                      {{ reviewDetailedData.knowChannel }}
                    </span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">
                      是否参加过春
                      <br />
                      笋会议：
                    </span>
                    <span class="value" style="line-height: 17px">
                      <br />
                      {{ reviewDetailedData.attendedSpringMeeting == '0' ? '否' : '是' }}
                    </span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">补充材料：</span>
                    <div style="display: flex; flex-direction: column">
                      <span class="value">{{ reviewDetailedData.supplementType == 1 ? '居住证' : reviewDetailedData.supplementType == 2 ? '营业执照' : '' }}</span>
                      <div v-if="reviewDetailedData.supplementPhoto && reviewDetailedData.supplementPhoto.length > 0">
                        <div v-for="(item, index) in reviewDetailedData.supplementPhoto" :key="index">
                          <el-image fit="cover" :src="item" :preview-src-list="[item]" class="photo-placeholder" style="margin-top: 8px" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 审核流程tab -->
            <div v-show="activeTab === 'reviewProcess'" class="review-process">
              <div class="process-timeline">
                <template v-for="(item, index) in auditProcessData">
                  <div
                    :key="`step-${index}`"
                    class="process-step"
                    :class="{
                      completed: item.auditStatus == 2,
                      rejected: item.auditStatus == 3,
                      current: item.auditStatus == 1
                    }"
                  >
                    <img src="../../../assets/images/agreeIcon.png" v-if="item.auditStatus == 2" alt="" class="radiusStyle" />
                    <div
                      class="radiusStyle"
                      style="border-radius: 50%"
                      :style="{ background: item.auditStatus == 1 ? '#51CC92' : item.auditStatus == 3 ? '#e55656' : '' }"
                      v-if="item.auditStatus == 1 || item.auditStatus == 3"
                    ></div>
                    <div class="step-content">
                      <div class="step-title">
                        <span class="approvalPeople" :style="{ color: item.auditStatus == 1 || item.auditStatus == 2 ? '#51CC92' : '#E55656' }">
                          审批人 · {{ item.auditStatus == 1 ? '审核中' : item.auditStatus == 2 ? '已通过' : '已驳回' }}
                        </span>
                        <span class="approvalTime" style="margin-right: 16px">{{ item.approveTime }}</span>
                      </div>
                      <div class="approvalTime">{{ item.approveName }}</div>
                      <div v-if="item.rejectReason" class="refuse">{{ item.rejectReason }}</div>
                    </div>
                  </div>
                  <div v-if="index < auditProcessData.length - 1" :key="`line-${index}`" class="process-line"></div>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer" v-if="activeTab === 'reviewInfo' && title == '审批'">
          <el-button @click="handleReject(0)" type="danger" class="reject-btn">驳回</el-button>
          <el-button @click="handleApprove(1)" type="primary" class="approve-btn">通过</el-button>
        </div>
      </div>
    </CustomDialog>

    <!-- 驳回原因弹框 -->
    <el-dialog title="驳回原因" :visible.sync="rejectDialogVisible" width="400px" :close-on-click-modal="false" class="reject-dialog">
      <div class="reject-content">
        <el-input v-model="rejectReason" type="textarea" :rows="6" placeholder="请输入驳回原因" maxlength="100" show-word-limit class="reject-textarea"></el-input>
      </div>
      <div slot="footer" class="reject-footer">
        <el-button @click="cancelReject" class="cancel-btn">取消</el-button>
        <el-button @click="confirmReject" type="primary" class="submit-btn">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import registrationCodeApi from '@/api/registrationCodeApi/registrationCodeApi';
  import checkPermission from '@/utils/permission';
  import CustomDialog from '@/components/customDialog';
  import { color } from 'echarts';
  export default {
    name: 'ReviewInforDialog',
    data() {
      return {
        activeTab: 'reviewInfo',
        dialogVisible: false,
        rejectDialogVisible: false,
        rejectReason: '',
        isAdmin: false,
        reviewDetailedData: {
          idCardPhoto: [],
          supplementPhoto: []
        },
        // 审核流程
        auditProcessData: [],
        reviewLoading: false,
        title: '',
        processInstanceId: '',
        auditResultType: 0,
        // 门店/俱乐部
        storeClubType: ''
      };
    },
    components: {
      CustomDialog
    },
    mounted() {
      this.isAdmin = checkPermission(['admin']);
    },
    methods: {
      open(id, title, storeClubType) {
        this.dialogVisible = true;
        this.title = title;
        this.storeClubType = storeClubType;
        this.processInstanceId = id;
        this.activeTab = 'reviewInfo';
        this.getDetail(id);
        // 重置滚动条到顶部
        this.$nextTick(() => {
          const tabContent = this.$el.querySelector('.tab-content');
          if (tabContent) {
            tabContent.scrollTop = 0;
          }
        });
      },

      handleClose() {
        this.dialogVisible = false;
        this.processInstanceId = '';
        this.auditResultType = 0;
        this.rejectReason = '';
      },

      switchTab(tabName) {
        this.activeTab = tabName;
        // 切换tab时重置滚动条到顶部
        this.$nextTick(() => {
          const tabContent = this.$el.querySelector('.tab-content');
          if (tabContent) {
            tabContent.scrollTop = 0;
          }
        });
      },
      getDetail(id) {
        this.reviewLoading = true;
        registrationCodeApi[this.storeClubType == '门店' ? 'StoreListDataDetail' : 'ClubReviewDataDetail']({ id })
          .then((res) => {
            if (res.success) {
              this.reviewDetailedData = res.data;
              this.auditProcessData = res.data.auditDetailInfoList;
              this.reviewLoading = false;
            } else {
              this.reviewDetailedData = {
                idCardPhoto: [],
                supplementPhoto: []
              };
              this.auditProcessData = [];
              this.reviewLoading = false;
            }
          })
          .catch(() => {
            this.reviewLoading = false;
          });
      },
      handleReject(type) {
        this.rejectDialogVisible = true;
        this.auditResultType = type;
      },
      cancelReject() {
        this.rejectDialogVisible = false;
        this.rejectReason = '';
      },
      // 确认驳回
      confirmReject() {
        if (!this.rejectReason.trim()) {
          this.$message.warning('请输入驳回原因');
          return;
        }
        let params = {
          id: this.processInstanceId,
          auditResult: this.auditResultType,
          rejectReason: this.rejectReason.trim()
        };
        const loading = this.$loading({
          lock: true,
          spinner: 'el-icon-loading'
        });
        registrationCodeApi[this.storeClubType == '门店' ? 'StoreListDataRejectOrPass' : 'ClubReviewRejectOrPass'](params)
          .then((res) => {
            if (res.success) {
              this.$message.success(res.message);
              this.handleClose();
              this.cancelReject();
              this.refreshParentList();
            }
          })
          .catch(() => {})
          .finally(() => {
            loading.close();
          });
      },
      handleApprove(type) {
        this.auditResultType = type;
        let params = {
          id: this.processInstanceId,
          auditResult: this.auditResultType
        };
        this.$confirm('请确认是否信息已核对准确且审核通过？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success'
        }).then(() => {
          const loading = this.$loading({
            lock: true,
            spinner: 'el-icon-loading'
          });
          registrationCodeApi[this.storeClubType == '门店' ? 'StoreListDataRejectOrPass' : 'ClubReviewRejectOrPass'](params)
            .then((res) => {
              if (res.success) {
                this.$message.success(res.message);
                this.handleClose();
                this.cancelReject();
                this.refreshParentList();
              }
            })
            .catch(() => {})
            .finally(() => {
              loading.close();
            });
        });
      },
      refreshParentList() {
        this.$emit('refresh-list');
      }
    }
  };
</script>

<style scoped lang="less">
  .review-dialog {
    ::v-deep .el-dialog__body {
      padding: 43px 45px 30px;
    }
    ::v-deep .dialog-tabs .tab-header .tab-item {
      height: 48px;
      line-height: 50px;
    }
  }
  .dialog-tabs {
    .tab-header {
      display: flex;
      background: #dcf5e9;
      margin-bottom: 0;
      .tab-item {
        flex: 1;
        text-align: center;
        cursor: pointer;
        color: #000000;
        font-size: 16px;
        transition: all 0.3s;
        position: relative;
        &.active {
          color: #51cc92;
          font-weight: 500;
        }
      }
    }
    .tab-content {
      height: 500px;
      padding: 10px 0 0;
      overflow-y: auto;
      overflow-x: hidden;
      margin-top: 10px;
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }
  .review-info {
    .info-section {
      .info-row {
        margin-bottom: 16px;
        .info-item {
          display: flex;
          align-items: flex-start;
          .label {
            min-width: 120px;
            color: #000000;
            font-size: 14px;
            margin-right: 8px;
          }
          .value {
            color: #606266;
            font-size: 14px;
            flex: 1;
          }
          &.id-card-section {
            flex-direction: column;
            align-items: flex-start;
            .label {
              margin-bottom: 8px;
            }
          }
          .id-card-photos {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            max-width: 450px;
          }
          .photo-placeholder {
            width: 200px;
            height: 100px;
            border-radius: 4px;
            object-fit: cover;
          }
          .businessLocation {
            line-height: 1.2;
            white-space: normal;
          }
        }
      }
    }
  }
  .review-process {
    .process-timeline {
      padding: 12px 50px;
      box-sizing: border-box;
      position: relative;
      .process-step {
        display: flex;
        align-items: flex-start;
        position: relative;
        .step-icon {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          flex-shrink: 0;
          position: relative;
          z-index: 2;
          i {
            color: white;
            font-size: 10px;
          }
        }
        &.completed .step-icon {
          background: transparent;
          border: 1px solid #51cc92;
          i {
            color: #51cc92;
          }
        }
        &.current .step-icon {
          background: #51cc92;
          i {
            display: none;
          }
        }
        &.rejected .step-icon {
          background: #e55656;
          i {
            display: none;
          }
        }
        .step-content {
          flex: 1;
          padding-top: 0;
          height: 90px;
          .step-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .approvalPeople {
            font-size: 20px;
            color: #51cc92;
            margin-bottom: 2px;
            line-height: 1.4;
          }
          .approvalTime {
            font-size: 14px;
            color: #777777;
          }
          .refuse {
            margin-top: 8px;
            background: #fadddd;
            color: #e55656;
            padding: 12px 20px;
            font-size: 18px;
            border-radius: 4px;
          }
        }
      }
      .process-line {
        position: absolute;
        left: 63px;
        top: 42px;
        width: 1px;
        height: calc(100% - 146px);
        min-height: 40px;
        background: #d3f2e3;
        z-index: 1;
      }
    }
  }
  .dialog-footer {
    text-align: center;
    padding: 16px 0 10px;
    .el-button {
      margin: 0 15px;
      padding: 10px 30px;
      font-size: 14px;
      border-radius: 4px;
      &.reject-btn {
        background: #f56c6c;
        border-color: #f56c6c;
        color: white;
      }
      &.approve-btn {
        background: #51cc92;
        border-color: #51cc92;
        color: white;
      }
    }
  }
  ::v-deep .title-close {
    color: #ffffff !important;
  }
  // 驳回原因弹框样式
  .reject-dialog {
    ::v-deep .el-dialog__header {
      text-align: center;
      padding: 20px 20px 10px;
      .el-dialog__title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }
    ::v-deep .el-dialog__body {
      padding: 10px 20px 20px;
    }
  }
  .reject-content {
    .reject-textarea {
      ::v-deep .el-textarea__inner {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 12px;
        font-size: 14px;
        line-height: 1.5;
        resize: none;
        &:focus {
          border-color: #409eff;
        }
      }
    }
  }
  .reject-footer {
    text-align: center;
    padding: 10px 0;
    .el-button {
      margin: 0 10px;
      padding: 8px 20px;
      font-size: 14px;
      border-radius: 4px;
      &.cancel-btn {
        background: #ffffff;
        border: 1px solid #dcdfe6;
        color: #606266;
        &:hover {
          color: #409eff;
          border-color: #c6e2ff;
          background-color: #ecf5ff;
        }
      }
      &.submit-btn {
        background: #409eff;
        border-color: #409eff;
        color: white;
        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
  .tab-underline {
    position: absolute;
    bottom: 0;
    width: 400px;
    height: 6px;
    background: #51cc92;
    border-radius: 3px;
    transition: left 0.2s;
    z-index: 2;
    opacity: 0.7;
  }
  .tab-underline.reviewInfo {
    left: calc(25% - 200px);
  }
  .tab-underline.reviewProcess {
    left: calc(75% - 200px);
  }
  .radiusStyle {
    width: 28px;
    height: 28px;
    margin-right: 16px;
  }
</style>
