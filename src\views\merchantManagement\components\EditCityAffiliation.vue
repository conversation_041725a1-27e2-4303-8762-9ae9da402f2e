<template>
  <div>
    <el-dialog title="编辑所属地" :visible.sync="dialogVisible" width="40%" @close="handleOuterClose">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="俱乐部名称">
          <el-input v-model="formData.clubName" disabled />
        </el-form-item>
        <el-form-item label="原城市所属地">
          <el-input v-model="formData.oldAddress" disabled />
        </el-form-item>
        <el-form-item label="新城市所属地" prop="cityTag">
          <el-radio-group v-model.number="formData.cityTag" @change="changeHandler">
            <el-radio :label="1">中国</el-radio>
            <el-radio :label="2">海外</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.cityTag === 1" prop="selectedOptions">
          <el-cascader v-model="formData.selectedOptions" :options="areaData" placeholder="请选择所属地" clearable style="width: 300px" :props="{ value: 'label' }" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleOuterClose">取 消</el-button>
        <el-button type="primary" @click="handleOpenConfirmDialog">修 改</el-button>
      </span>

      <el-dialog title="请确认是否修改该俱乐部的归属地" :visible.sync="innerVisible" width="30%" append-to-body>
        <el-form label-width="120px" :label-position="labelPosition">
          <el-form-item label="俱乐部名称">
            {{ formData.clubName }}
          </el-form-item>
          <el-form-item label="原城市所属地">
            {{ formData.oldAddress }}
          </el-form-item>
          <el-form-item label="新城市所属地">
            {{ formData.cityTag === 1 ? formData.selectedOptions[0] + '-' + formData.selectedOptions[1] : '海外' }}
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="handleInnerCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
        </span>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
  import areaData from '@/utils/provinceCityTreeNo.json';
  import dealerPaylist from '@/api/operationsPayment';

  export default {
    name: 'EditCityAffiliation',
    props: {
      isShowCityDialog: {
        type: Boolean,
        required: true
      }
    },
    data() {
      return {
        innerVisible: false,
        formData: {
          clubName: '',
          localCity: '',
          cityTag: 3,
          selectedOptions: [],
          oldAddress: '',
          id: ''
        },
        labelPosition: 'left',
        areaData,
        rules: {
          cityTag: [
            { required: true, type: 'number', message: '城市标签不能为空', trigger: 'change' },
            {
              validator: (_rule, value, callback) => {
                if (value === 3) {
                  return callback(new Error('请选择新城市所属地'));
                }
                callback();
              }
            }
          ],
          selectedOptions: [
            {
              trigger: 'change',
              validator: (_rule, value, callback) => {
                if (this.formData.cityTag === 1 && (!value || value.length === 0)) {
                  return callback(new Error('请选择新城市所属地'));
                } else if (this.formData.cityTag === 3) {
                  return callback(new Error('请选择新城市所属地'));
                }
                callback();
              }
            }
          ]
        },
        loading: false
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.isShowCityDialog;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },
    methods: {
      changeHandler() {
        this.formData.selectedOptions = [];
      },
      handleOuterClose() {
        this.dialogVisible = false;
        this.resetData();
        this.$emit('closeCityDialog');
        this.$refs.formRef.resetFields();
        // this.formData.cityTag = 3;
      },
      handleOpenConfirmDialog() {
        this.$refs.formRef.validate((valid) => {
          console.log(valid, 'valid');

          if (!valid) return;
          if (this.formData.cityTag === 3) {
            return this.$message.error('请选择新城市所属地');
          }
          this.innerVisible = true;
        });
      },
      handleInnerCancel() {
        this.innerVisible = false;
      },
      async handleConfirm() {
        console.log(this.loading, 'this.loading');

        if (this.loading) return;
        try {
          this.loading = true;
          const provinceCity = this.formData.cityTag === 1 ? this.formData.selectedOptions.join('-') : '海外';
          const params = {
            id: this.formData.id,
            provinceCity
          };
          await dealerPaylist.updateProvinceCity(params);
          this.loading = false;
          this.$message.success('修改成功');
          this.innerVisible = false;
          this.dialogVisible = false;
          this.$emit('closeCityDialog', 1);
        } catch (e) {
          this.loading = false;
        }
      },
      setData(data) {
        this.formData.clubName = data?.merchantName || '';
        this.formData.localCity = data?.localCity || '';
        this.formData.cityTag = Number(data?.cityTag ?? 3);
        this.formData.id = data?.id;
        this.formData.selectedOptions = data?.selectedOptions?.split('-') || [];

        if (data?.localCity === '海外' || data?.localCity === '-') {
          this.formData.oldAddress = data.localCity;
          return;
        }
        this.formData.oldAddress = [data?.localProvince, data?.localCity].filter(Boolean).join('-');
      },
      resetData() {
        this.formData = {
          clubName: '',
          localCity: '',
          cityTag: 3,
          selectedOptions: [],
          oldAddress: '',
          id: ''
        };
        this.$refs.formRef.resetFields();
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    text-align: right;
  }
</style>
