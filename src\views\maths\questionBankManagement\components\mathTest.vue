<template>
  <div class="subjectTest">
    <!-- 题目标题 -->
    <div class="title">
      <div style="font-size: 16px; text-align: left">
        <div v-for="(segment, index) in parseContent(html)" :key="index" style="display: inline; vertical-align: bottom; line-height: 40px">
          <span v-if="segment.type === 'text'" style="display: inline; vertical-align: bottom; line-height: 40px">{{ segment.content }}</span>
          <span v-else style="display: inline; vertical-align: bottom; line-height: 40px" v-html="renderFormula(segment.content)"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import katex from 'katex';

  export default {
    name: 'mathTest',
    props: {
      formulaText: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        html: ''
      };
    },
    watch: {
      formulaText: {
        immediate: true,
        handler(val) {
          this.html = val;
        }
      }
    },
    methods: {
      parseContent(e) {
        const regex = /\\\((.*?)\\\)/g;
        let lastIndex = 0;
        let match;
        const segments = [];

        while ((match = regex.exec(e)) !== null) {
          if (match.index > lastIndex) {
            segments.push({
              type: 'text',
              content: e.substring(lastIndex, match.index)
            });
          }
          segments.push({ type: 'formula', content: match[1] });
          lastIndex = match.index + match[0].length;
        }

        if (lastIndex < e.length) {
          segments.push({ type: 'text', content: e.substring(lastIndex) });
        }

        return segments;
      },
      renderFormula(text) {
        try {
          // 转义百分号
          // if (!text) return;
          // text = text.replace(/%/g, '\\%');
          // // 处理不完整的 \frac 表达式
          // text = text.replace(/\\frac\{([^}]*)\}(?!\{)/g, (match, p1) => {
          //   return `\\frac{${p1}}{1}`;
          // });
          // // 处理分数
          // text = text.replace(/-(\d+)(\d+)(\d+)(\d+)​/g, (match, p1, p2, p3, p4) => {
          //   return `-\\frac{${p1}${p2}}{${p3}${p4}}`;
          // });
          // text = text.replace(/(\d+)(\d+)(\d+)(\d+)​/g, (match, p1, p2, p3, p4) => {
          //   return `\\frac{${p1}${p2}}{${p3}${p4}}`;
          // });

          // 修复特定的分数格式
          // text = text.replace(/\\frac\{\\mathrm\{\\pi\}\}\{(\d+)\}/g, '\\frac{\\pi}{$1}');
          // text = text.replace(/\\frac\{\\pi\{1\}\}\{(\d+)\}/g, '\\frac{\\pi}{$1}');
          // text = text.replace(/\\frac\{([^}]*)\}\{(\d+)\}/g, '\\frac{$1}{$2}');

          // // 处理不完整的分数
          // text = text.replace(/\\frac{(\d+)}\{(\d+)}/g, '\\frac{$1}{$2}');

          // 处理特殊字符
          // text = text.replace(/\\cdot\\cdot/g, '\\cdots');
          // text = text.replace(/\\ldots/g, '\\dots');
          // text = text.replace(/\\dot{([^}]*)}/g, '\\dot{$1}');

          // // 处理带指数的根号
          // text = text.replace(/\\sqrt\[(\d+)\]\{(\d+)\}/g, (match, p1, p2) => {
          //   return `\\sqrt[${p1}]{${p2}}`;
          // });

          // 处理循环小数
          // text = text.replace(/0\.(\d+)˙(\d+)˙(\d+)˙(\d+)˙/g, (match, p1, p2, p3, p4) => {
          //   return `0.\\dot{${p1}${p2}${p3}${p4}}`;
          // });

          // // 处理希腊字母
          // text = text.replace(/\\mathrm\{\\pi\}/g, '\\pi');
          // text = text.replace(/\\mathrm\{\\Pi\}/g, '\\Pi');
          // text = text.replace(/ππ/g, '\\pi');

          // 处理省略号
          // text = text.replace(/……/g, '\\dots');
          // text = text.replace(/⋅⋅⋅⋅⋅⋅/g, '\\dots');

          // 渲染公式
          const rendered = katex.renderToString(text, {
            throwOnError: false,
            displayMode: false,
            strict: false,
            trust: true,
            // 添加扩展包支持
            macros: {
              '\\wideparen': '\\overgroup{#1}'
            }
          });

          return rendered.includes('ParseError') ? text : rendered;
        } catch (error) {
          console.error('公式渲染失败:', error);
          return text;
        }
      }
    }
  };
</script>

<style scoped>
  @import '~katex/dist/katex.min.css';
  .subjectTest {
    border-radius: 10px;
    background-color: #fff;
    margin-top: 20px;
    /* padding: 30px 20px;  */
  }
  .title {
    display: flex;
    flex-wrap: wrap;
  }
  .imgs img {
    width: 200px;
    height: 200px;
    margin: 10px;
  }
  .questionsItem {
    margin: 20px auto 18px;
    min-height: 90px;
    width: 650px;
    line-height: 90px;
    padding-left: 30px;
    box-sizing: border-box;
    border: 1px solid #dfdfdf;
    border-radius: 10px;
    font-size: 28px;
    display: flex;
    color: #333333;
  }
  .right {
    border: 1px solid #94e6c7;
    background: rgba(148, 230, 199, 0.15);
    color: #31cf93;
  }
  .error {
    border: 1px solid #ffaf85;
    background: rgba(255, 172, 129, 0.1);
    color: #ffaf85;
  }
</style>
