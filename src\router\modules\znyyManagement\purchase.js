// 采购管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/purchase',
  component: Layout,
  meta: {
    perm: 'm:purchase',
    title: '采购管理',
    icon: 'purchase'
  },
  children: [
    {
      path: 'procurementAllocation',
      component: () => import('@/views/purchase/procurementAllocation'),
      name: 'procurementAllocation',
      meta: {
        perm: 'm:purchase:procurementAllocation',
        title: '采购配置'
      }
    },
    {
      path: 'purchaseApply',
      component: () => import('@/views/purchase/purchaseApply'),
      name: 'purchaseApply',
      meta: {
        perm: 'm:purchase:purchaseApply',
        title: '采购申请'
      }
    },
    {
      path: 'deliveryManagement',
      component: () => import('@/views/purchase/deliveryManagement'),
      name: 'deliveryManagement',
      meta: {
        perm: 'm:purchase:deliveryManagement',
        title: '发货管理'
      }
    },
    {
      path: 'subordinateDashboard',
      component: () => import('@/views/purchase/subordinateDashboard'),
      name: 'subordinateDashboard',
      meta: {
        perm: 'm:purchase:subordinateDashboard',
        title: '下级采购看板'
      }
    }
  ]
};
