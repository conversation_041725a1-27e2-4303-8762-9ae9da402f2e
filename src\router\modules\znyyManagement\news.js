// 消息管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  hidden: true,
  path: '/news',
  component: Layout,
  // redirect: '/news/index',
  meta: {
    perm: 'm:news',
    title: '消息管理',
    icon: 'chart'
  },
  children: [
    {
      path: 'list',
      component: _import('news/list'),
      name: 'list',
      meta: {
        perm: 'm:news:list',
        title: '消息通知'
      }
    },

    {
      path: 'sendNews',
      // hidden: true,
      component: _import('news/sendNews'),
      name: 'sendNews',
      meta: {
        perm: 'm:news:send',
        title: '消息管理'
      }
    },

    {
      path: 'newContent',
      // hidden: true,
      component: _import('news/newContent'),
      name: 'newContent',
      meta: {
        perm: 'm:news:content',
        title: '通知内容'
      }
    },

    {
      path: 'newsAdd',
      // hidden: true,
      component: _import('news/newsAdd'),
      name: 'add',
      meta: {
        perm: 'm:news:add',
        title: '新增消息'
      }
    },

    {
      path: 'newsEdit',
      // hidden: true,
      component: _import('news/newsEdit'),
      name: 'edit',
      meta: {
        perm: 'm:news:edit',
        title: '编辑消息'
      }
    }
  ]
};
