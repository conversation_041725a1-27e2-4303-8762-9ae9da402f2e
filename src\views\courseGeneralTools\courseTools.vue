<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" style="padding: 30px 30px 0 30px">
      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item label="课程名称">
            <el-input placeholder="请输入课程名称" v-model="dataQuery.curriculumName"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-button type="primary" icon="el-icon-search" @click="fetchDataSearch()">查询</el-button>
          <el-button type="info" icon="el-icon-refresh" @click="fetchDataReset()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="addCourseHandle()">新增课程类型</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="course-table" :data="tableData" stripe border :default-sort="{ prop: 'addTime', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="curriculumName" label="课程名称"></el-table-column>
        <el-table-column prop="curriculumTypeName" label="课程类型"></el-table-column>
        <el-table-column prop="status" label="是否启用">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="0"
              :inactive-value="1"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="课程权重"></el-table-column>
        <el-table-column prop="curriculumToolName" label="关联工具"></el-table-column>
        <el-table-column label="操作" width="300">
          <template slot-scope="scope">
            <el-button type="text" size="large" @click="editListenHandle(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page.sync="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        :page-size="tablePage.size"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 添加编辑弹窗 -->
    <el-dialog center :close-on-click-modal="false" :close-on-press-escape="false" :title="dialogTitle" :visible.sync="dialogVisible" width="40%" @close="dialogClose('ruleForm')">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="课程名称" prop="courseName">
          <el-input v-model="ruleForm.courseName" maxlength="15" show-word-limit placeholder="请输入课程名称"></el-input>
        </el-form-item>
        <el-form-item label="课程类型" prop="courseType">
          <el-select style="width: 100%" v-model="ruleForm.courseType" @change="changeListType" multiple filterable value-key="value" placeholder="请选择课程类型">
            <el-option v-for="(item, index) in courseNameList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="课程权重" prop="courseSort">
          <el-input v-model="ruleForm.courseSort" type="number" @input="handleInput" placeholder="请输入课程权重"></el-input>
        </el-form-item>
        <el-form-item label="关联工具" prop="relatedTools">
          <el-select
            style="width: 100%"
            @remove-tag="clearHandle"
            @change="handleRelatedToolsChange"
            v-model="ruleForm.relatedTools"
            multiple
            filterable
            value-key="value"
            placeholder="请选择工具"
          >
            <el-option v-for="(item, index) in relatedToolsList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogClose('ruleForm')">取 消</el-button>
        <el-button type="primary" @click="dialogConfirm('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import courseTools from '@/api/courseTools/courseTools';
  import { getCourseCateList2 } from '@/api/courseCate';
  import { pageParamNames } from '@/utils/constants';
  import { log } from 'bpmn-js-token-simulation';
  export default {
    data() {
      return {
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        relatedToolsList: [],
        courseNameList: [], // 课程类型下拉框数据
        dataQuery: {
          curriculumName: '' // 课程名称
        },
        isRouterAlive: true, //局部刷新
        tableData: [], //表格数据
        dialogTitle: '',
        dialogVisible: false,
        ruleForm: {
          courseName: '', // 课程名称
          courseType: '', // 课程内容
          courseSort: '', // 课程权重
          relatedTools: '', // 关联工具类型
          relatedToolsId: '',
          relatedToolsListEdit: []
        },
        rules: {
          courseName: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
          courseType: [{ required: true, message: '请选择课程', trigger: 'blur' }],
          courseSort: [{ required: true, message: '请输入权重', trigger: 'blur' }],
          relatedTools: [{ required: true, message: '请选择工具', trigger: 'blur' }]
        },
        operationType: '', // 新增编辑标识符
        editId: ''
      };
    },
    created() {
      this.fetchData();
      this.fetchRelatedTools();
      this.fetchCourseTools();
    },

    methods: {
      fetchDataSearch() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchDataReset() {
        (this.dataQuery = {
          curriculumName: '' // 课程类型
        }),
          this.fetchDataSearch();
      },
      handleInput() {
        if (Number(this.ruleForm.courseSort) > 100) {
          this.$message.warning('输入排序不能大于 100');
          this.ruleForm.courseSort = '';
        } else if (Number(this.ruleForm.courseSort) < 1 && this.ruleForm.courseSort !== '') {
          this.$message.warning('输入排序不能小于 1');
          this.ruleForm.courseSort = '';
        }
      },
      changeListType() {
        this.ruleForm.listeningType = '';
        this.ruleForm.listeningYear = '';
      },
      // 查询表格列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        let params = {
          curriculumName: that.dataQuery.curriculumName,
          pageSize: that.tablePage.size,
          pageNum: that.tablePage.currentPage
        };
        // 分页
        courseTools.curriculumListByPageAPI(params).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name] || 0)));
        });
      },
      fetchRelatedTools() {
        // 查询关联工具下拉框
        courseTools.relatedListAPI({ pageNum: 1, pageSize: 1000, toolName: '' }).then((res) => {
          this.relatedToolsList = res.data.data.map((item) => ({
            label: item.toolName,
            value: item.id
          }));
        });
      },
      // 查询关联工具下拉框
      fetchCourseTools() {
        getCourseCateList2({ pageNum: 1, pageSize: 1000, enName: '' }).then((res) => {
          this.courseNameList = res.data.data.map((item) => ({
            label: item.enName,
            value: item.id
          }));
        });
      },
      // 新增课程
      addCourseHandle() {
        this.dialogTitle = '添加课程';
        this.ruleForm = {
          courseName: '', // 课程名称
          courseType: [], // 课程类型
          courseSort: '', // 课程权重
          relatedTools: [] // 关联工具类型
        };
        this.operationType = 'add'; // 设置操作类型为新增
        this.dialogVisible = true;
      },
      // 编辑课程
      editListenHandle(row) {
        this.editId = row.id;
        this.dialogTitle = '编辑课程';
        this.operationType = 'edit'; // 设置操作类型为编辑
        row.curriculumTool.forEach((item) => {
          console.log('🚀 ~ editListenHandle ~ item:', item);
          this.relatedToolsList.push({
            value: item.id,
            label: item.toolName
          });
        });
        console.log('🚀 ~ row.curriculumTool.forEach ~ this.relatedToolsList:', this.relatedToolsList);

        this.ruleForm = {
          courseName: row.curriculumName, // 课程名称
          courseType: row.curriculumIdList, // 课程类型
          courseSort: row.sort, // 课程权重
          // relatedToolsListEdit: row.curriculumTool || [], // 关联工具类型
          // relatedTools: (row.curriculumTool || []).map((tool) => String(tool.toolName))
          relatedTools: (row.curriculumTool || []).map((tool) => tool.id) // 关联工具类型
        };
        this.dialogVisible = true;
      },
      handleRelatedToolsChange(selectedValues) {
        console.log('🚀 ~ handleRelatedToolsChange ~ selectedValues:', selectedValues);
        this.ruleForm.relatedToolsId = selectedValues;
      },
      clearHandle(removedName) {
        // const item = this.ruleForm.relatedToolsListEdit.find((tool) => tool.toolName === removedName);
        // if (item) {
        //   const exists = this.relatedToolsList.some((tool) => tool.value === item.id);
        //   if (!exists) {
        //     this.relatedToolsList.push({
        //       label: item.toolName,
        //       value: item.id
        //     });
        //     this.ruleForm.relatedToolsId = this.relatedToolsList.map((item) => item.value);
        //   }
        // }
        const item = this.ruleForm.relatedToolsListEdit.find((tool) => tool.toolName === removedName);
        if (item) {
          // 使用 filter 创建一个不包含当前 item 的新数组
          const filteredList = this.relatedToolsList.filter((tool) => tool.value !== item.id);

          // 如果不存在，则添加新的条目
          if (filteredList.length === this.relatedToolsList.length) {
            filteredList.push({
              label: item.toolName,
              value: item.id
            });
          }

          // 更新 relatedToolsList 和 ruleForm 的相关属性
          this.relatedToolsList = filteredList;
          this.ruleForm.relatedToolsId = filteredList.map((item) => item.value);
        }
      },

      handleStatusChange(row) {
        courseTools.enableAPI({ curriculumTypeId: row.id }).then((res) => {
          if (res.success) {
            this.$message.success('操作成功');
            this.fetchData();
          }
        });
      },
      // 弹窗验证
      dialogConfirm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let textToast = this.operationType === 'add' ? '新增课程' : '编辑课程';
            const loading = this.$loading({
              lock: true,
              text: textToast,
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            let params = {
              curriculumName: this.ruleForm.courseName,
              curriculumIdList: this.ruleForm.courseType, // 课程类型
              sort: this.ruleForm.courseSort, // 课程权重
              toolIdList: this.ruleForm.relatedTools
            };
            if (this.operationType === 'add') {
              courseTools
                .relatedSaveAPI({ ...params })
                .then((res) => {
                  if (res.success) {
                    this.$message.success('添加成功！');
                    loading.close();
                    this.dialogClose(formName);
                    this.fetchData();
                    this.fetchRelatedTools();
                  }
                })
                .catch((err) => {
                  loading.close();
                });
            } else if (this.operationType === 'edit') {
              courseTools
                .updateAPI({ ...params, id: this.editId })
                .then((res) => {
                  if (res.success) {
                    this.$message.success('编辑成功！');
                    loading.close();
                    this.dialogClose(formName);
                    this.fetchData();
                    this.fetchRelatedTools();
                  }
                })
                .catch((err) => {
                  loading.close();
                });
            }
          } else {
            return false;
          }
        });
      },
      // 关闭弹窗 重置表单
      dialogClose(formName) {
        this.dialogVisible = false;
        this.fetchRelatedTools();
        this.$refs[formName].resetFields();
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      //局部刷新
      reload() {
        this.isRouterAlive = false;
        this.$nextTick(function () {
          this.isRouterAlive = true;
        });
      }
    }
  };
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
    margin: 15px 0;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    /* background: url("../../icons/stop.png") no-repeat top center/contain; */
  }

  .mt22 {
    margin-top: 22px;
  }

  .blue {
    margin-right: 50px;
    color: #409eff;
  }

  .clearfix {
    color: red;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
