// 任务管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/taskManager',
  component: Layout,
  redirect: 'taskManager',
  meta: {
    perm: 'm:taskManager',
    title: '任务管理',
    icon: 'taskManager'
  },
  children: [
    {
      path: 'myTask',
      component: () => import('@/views/activiti/workFlow/taskManager/formMyTask'),
      name: 'myTask',
      meta: {
        perm: 'm:taskManager:myTask',
        title: '待办任务'
      }
    },
    {
      path: 'handlerFlowTask',
      hidden: true,
      component: () => import('@/views/activiti/workFlow/handlerFlowTask/index'),
      name: 'handlerFlowTask',
      meta: {
        perm: 'm:taskManager:handlerFlowTask',
        title: '任务办理'
      }
    },
    {
      path: 'myHistoryTask',
      component: () => import('@/views/activiti/workFlow/taskManager/formMyHistoryTask'),
      name: 'myHistoryTask',
      meta: {
        perm: 'm:taskManager:myHistoryTask',
        title: '历史任务'
      }
    },
    {
      path: 'myApprovedTask',
      component: () => import('@/views/activiti/workFlow/taskManager/formMyApprovedTask'),
      name: 'myApprovedTask',
      meta: {
        perm: 'm:taskManager:myApprovedTask',
        title: '已办任务'
      }
    }
  ]
};
