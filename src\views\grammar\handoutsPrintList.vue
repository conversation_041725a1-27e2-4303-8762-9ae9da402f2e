<template>
  <div class="app-container">
    <el-card style="margin-bottom: 16px">
      <el-form label-width="90px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6" :xs="24">
            <el-form-item label="学员编码：">
              <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编码" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="语法名：">
              <el-input v-model="dataQuery.title" placeholder="请输入语法名" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="打印状态：">
              <el-select v-model="dataQuery.status" placeholder="全部" style="width: 100%">
                <el-option
                  v-for="(item, index) in [
                    { value: 1, label: '已打印' },
                    { value: 0, label: '未打印' }
                  ]"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="提交时间：">
              <el-date-picker
                style="width: 100%"
                value-format="yyyy-MM-dd hh:mm:ss"
                clearable
                v-model="value1"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" :xs="24">
            <el-form-item label-width="0">
              <el-button type="primary" @click="fetchData01()">搜索</el-button>
              <el-button type="primary" @click="fetchData03()">刷新</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card v-if="tablePage.totalItems > 0">
      <el-table
        class="common-table"
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        stripe
        default-expand-all
        :tree-props="{ list: 'children', hasChildren: 'true' }"
      >
        <el-table-column prop="studentCode" label="学员编码" sortable></el-table-column>
        <el-table-column prop="studentName" label="姓名" sortable></el-table-column>
        <el-table-column prop="withdrawnBonus" label="操作">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="jumpOpenCourse(scope.row.id, scope.row.relationId)">打印</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="语法点" sortable></el-table-column>
        <el-table-column prop="addTime" label="结束时间" sortable></el-table-column>
        <el-table-column prop="status" label="状态" sortable>
          <template slot-scope="scope">
            <span class="blue" v-if="scope.row.status === 1">已打印</span>
            <span class="red" v-else>未打印</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-row type="flex" justify="end" style="height: 28px; margin-top: 36px; line-height: 28px">
        <el-pagination
          :current-page="tablePage.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tablePage.totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
    </el-card>
    <el-card v-else>
      <NoMore></NoMore>
    </el-card>
  </div>
</template>

<script>
  import grammarApi from '@/api/grammar';
  import Tinymce from '@/components/Tinymce';
  import { pageParamNames } from '@/utils/constants';
  import ls from '@/api/sessionStorage';
  import NoMore from '@/components/NoMore/index.vue';
  export default {
    name: 'areasStudentWordPrintList',
    components: { NoMore },
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          studentCode: '',
          title: '',
          status: '',
          startTime: '',
          endTime: '',
          type: 'Handouts'
        },
        studyRank: [],
        value1: '',
        exportLoading: false
      };
    },
    created() {
      this.fetchData();
      // this.getStudyRank();
    },
    methods: {
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchData03() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.dataQuery = {
          studentCode: '',
          status: '',
          startTime: '',
          endTime: '',
          type: 'Handouts'
        };
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        const that = this;
        if (that.value1 !== '') {
          if (that.value1.length > 0) {
            that.dataQuery.startTime = that.value1[0];
            that.dataQuery.endTime = that.value1[1];
          } else {
            that.dataQuery.startTime = '';
            that.dataQuery.endTime = '';
          }
        } else {
          that.dataQuery.startTime = '';
          that.dataQuery.endTime = '';
        }
        that.tableLoading = true;
        grammarApi.handoutsPrintList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 跳转到打印页面
      jumpOpenCourse(id, grammarId) {
        const that = this;
        ls.setItem('handoutsPrintId', id);
        ls.setItem('grammarId', grammarId);
        that.$router.push({
          path: '/grammar/studentHandoutsPrint',
          query: {
            handoutsPrintId: id,
            grammarId: grammarId
          }
        });
      }
    }
  };
</script>

<style>
  .period-table td,
  .period-table th {
    text-align: center;
  }
  .mt20 {
    margin-top: 20px;
  }
  .red {
    color: red;
  }
  .green {
    color: green;
  }
  .blue {
    color: blue;
  }
</style>
