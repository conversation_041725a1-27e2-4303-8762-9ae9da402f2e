
import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/calculation/web/dimension/list',
      method: 'GET',
      params: data
    })
  },
  //添加
  saveOrUpdate(data) {
    return request({
      url: '/calculation/web/dimension/saveOrUpdate',
      method: 'POST',
      data
    })
  },
  detail(id){
    return request({
      url: '/calculation/web/dimension/detail',
      method: 'GET',
      params: {
        id:id
      }
    })
  },
  delete(id){
    return request({
      url: '/calculation/web/dimension/delete',
      method: 'DELETE',
      params: {
        id:id
      }
    })
  },
  //查询所有维度
  getAllDimension(){
    return request({
      url: '/calculation/web/dimension/allDimension',
      method: 'GET',
    })
  },
  //查询所有维度 不去重
  getAllDimensionHaveRepeat(){
    return request({
      url: '/calculation/web/dimension/all',
      method: 'GET',
    })
  },
  //查询所有学段
  getGrade(){
    return request({
      url: '/calculation/web/dimension/allGrade',
      method: 'GET',
    })
  },
  //传学段差维度 传维度查学段
  getDimensionForGrade(grade,dimension){
    return request({
      url: '/calculation/web/dimension/getList',
      method: 'GET',
      params: {
        grade:grade,
        dimension:dimension
      }
    })
  }
}

