import { shallowMount, mount } from '@vue/test-utils';
import InabilityReason from '@/views/merchantManagement/components/InabilityReason.vue';

// 创建mock的Element UI组件
const mockElementComponents = {
  'el-dialog': {
    template: '<div><slot></slot></div>',
    props: ['visible', 'title', 'width', 'center', 'closeOnPressEscape', 'closeOnClickModal'],
    computed: {
      // 添加必要的计算属性
    }
  },
  'el-form': {
    template: '<div><slot></slot></div>',
    props: ['model', 'rules', 'labelWidth']
  },
  'el-form-item': {
    template: '<div><slot></slot></div>'
  },
  'el-button': {
    template: '<button><slot></slot></button>',
    props: ['type', 'loading']
  }
};

describe('InabilityReason.vue', () => {
  // 测试组件是否正确渲染
  it('renders correctly with default props', () => {
    const wrapper = shallowMount(InabilityReason, {
      stubs: mockElementComponents
    });

    // 检查组件是否正确渲染
    expect(wrapper.exists()).toBe(true);

    // 检查默认属性
    expect(wrapper.props().dialogReasonVisible).toBe(false);
    expect(wrapper.props().reasonContent).toEqual([]);
    expect(wrapper.props().workStep).toBe(1);
    expect(wrapper.props().showTitleStatus).toBe(1);
    expect(wrapper.props().isSubmitLoading).toBe(false);
    expect(wrapper.props().reasonType).toBe(1);
  });

  // 测试第一种工作步骤的对话框显示
  it('shows first dialog when workStep is 1', () => {
    const wrapper = mount(InabilityReason, {
      propsData: {
        workStep: 1,
        dialogReasonVisible: true,
        reasonContent: ['原因1', '原因2'],
        showTitleStatus: 1
      },
      stubs: mockElementComponents
    });

    // 检查组件是否正确渲染
    expect(wrapper.exists()).toBe(true);

    // 检查标题是否正确（通过props）
    expect(wrapper.vm.reasonTitle).toBe('当前门店无法变更所属俱乐部');

    // 检查原因列表是否正确渲染
    expect(wrapper.props().reasonContent.length).toBe(2);
  });

  // 测试第四种工作步骤的成功提示对话框
  it('shows success dialog when workStep is 4', () => {
    const wrapper = mount(InabilityReason, {
      propsData: {
        workStep: 4,
        dialogReasonVisible: true,
        reasonType: 1
      },
      stubs: mockElementComponents
    });

    // 检查组件是否正确渲染
    expect(wrapper.exists()).toBe(true);
  });

  // 测试关闭对话框功能
  it('emits handleCloseDialog event when handleConfirm is called', async () => {
    const wrapper = mount(InabilityReason, {
      propsData: {
        workStep: 1,
        dialogReasonVisible: true
      },
      stubs: mockElementComponents
    });

    // 调用handleConfirm方法
    wrapper.vm.handleConfirm();

    // 检查是否发出事件
    expect(wrapper.emitted('handleCloseDialog')).toBeTruthy();
    expect(wrapper.emitted('handleCloseDialog')[0]).toEqual(['closeDialog']);
  });

  // 测试提交功能
  it('emits handleSubmit event and sets loading when handleSubmit is called', async () => {
    const wrapper = mount(InabilityReason, {
      propsData: {
        workStep: 4,
        dialogReasonVisible: true,
        reasonType: 1
      },
      stubs: mockElementComponents
    });

    // 调用handleSubmit方法
    wrapper.vm.handleSubmit();

    // 检查是否发出提交事件
    expect(wrapper.emitted('handleSubmit')).toBeTruthy();

    // 检查加载状态是否设置
    expect(wrapper.vm.submitLoading).toBe(true);
  });

  // 测试防止重复提交
  it('prevents multiple submissions when submitLoading is true', async () => {
    const wrapper = mount(InabilityReason, {
      propsData: {
        workStep: 4,
        dialogReasonVisible: true,
        isSubmitLoading: true,
        reasonType: 1
      },
      stubs: mockElementComponents
    });

    // 设置初始submitLoading为true
    wrapper.setData({ submitLoading: true });

    // 监听 handleSubmit 事件
    const handleSubmitSpy = jest.fn();
    wrapper.vm.$on('handleSubmit', handleSubmitSpy);

    // 调用handleSubmit方法
    wrapper.vm.handleSubmit();

    // 检查事件未被触发
    expect(handleSubmitSpy).not.toHaveBeenCalled();
  });

  // 测试计算属性 reasonTitle
  it('computes reasonTitle correctly', () => {
    const wrapper = shallowMount(InabilityReason, {
      propsData: {
        showTitleStatus: 2,
        workStep: 1 // 确保 workStep 也传入
      },
      stubs: mockElementComponents
    });

    expect(wrapper.vm.reasonTitle).toBe('当前门店无法变更推广大使');
  });
  // 测试 reasonTitle 的不同情况
  it('computes different reasonTitle based on showTitleStatus', () => {
    const wrapper1 = shallowMount(InabilityReason, {
      propsData: { showTitleStatus: 1, workStep: 1 },
      stubs: mockElementComponents
    });

    const wrapper2 = shallowMount(InabilityReason, {
      propsData: { showTitleStatus: 2, workStep: 1 },
      stubs: mockElementComponents
    });

    expect(wrapper1.vm.reasonTitle).toBe('当前门店无法变更所属俱乐部');
    expect(wrapper2.vm.reasonTitle).toBe('当前门店无法变更推广大使');
  });

  // 测试 workStep=4 时的特殊逻辑
  it('handles workStep 4 correctly', () => {
    const wrapper = shallowMount(InabilityReason, {
      propsData: {
        workStep: 4,
        dialogReasonVisible: true,
        reasonType: 1
      },
      stubs: mockElementComponents
    });

    expect(wrapper.exists()).toBe(true);
    // 可以添加更多针对 workStep 4 的断言
  });

  // 测试 watcher 对 isSubmitLoading 的响应
  it('updates submitLoading when isSubmitLoading prop changes', async () => {
    const wrapper = shallowMount(InabilityReason, {
      stubs: mockElementComponents
    });

    // 初始状态
    expect(wrapper.vm.submitLoading).toBe(false);

    // 更新 prop
    await wrapper.setProps({ isSubmitLoading: true });

    // 检查 submitLoading 是否更新
    expect(wrapper.vm.submitLoading).toBe(true);
  });

  // 测试计算属性更全面的情况
  it('computes reasonTitle for all showTitleStatus values', () => {
    const testCases = [
      { status: 1, expected: '当前门店无法变更所属俱乐部' },
      { status: 2, expected: '当前门店无法变更推广大使' },
      { status: 3, expected: '当前俱乐部无法变更所属品牌' },
      { status: 4, expected: '当前俱乐部无法变更渠道合作伙伴' }
    ];

    testCases.forEach(({ status, expected }) => {
      const wrapper = shallowMount(InabilityReason, {
        propsData: { showTitleStatus: status, workStep: 1 },
        stubs: mockElementComponents
      });
      expect(wrapper.vm.reasonTitle).toBe(expected);
      wrapper.destroy();
    });
  });

  // 测试成功提示内容
  it('has correct success tip content', () => {
    const wrapper = shallowMount(InabilityReason, {
      stubs: mockElementComponents
    });

    expect(wrapper.vm.successTip[1]).toContain('确认变更后');
    expect(wrapper.vm.successTip[2]).toContain('确认变更后');
    expect(wrapper.vm.successTip[3]).toContain('确认变更后');
    expect(wrapper.vm.successTip[4]).toContain('确认变更后');
  });
});
