import request from '@/utils/request'

export default {
  //分页
  pageList(data) {
    return request({
      url: '/activiti/onlinePage/list',
      method: 'GET',
      params: data
    })
  },
  add(data){
    return request({
      url: '/activiti/onlinePage',
      method: 'POST',
      data
    })
  },
  update(data){
    return request({
      url: '/activiti/onlinePage/update',
      method: 'POST',
      data
    })
  },
  view(data){
    return request({
      url: '/activiti/onlinePage/view',
      method: 'GET',
      params: data
    })
  },
  delete(data){
    return request({
      url: '/activiti/onlinePage/delete',
      method: 'DELETE',
      params: data
    })
  },
  updatePublished(data){
    return request({
      url: '/activiti/onlinePage/updatePublished',
      method: 'POST',
      params: data
    })
  }
}
