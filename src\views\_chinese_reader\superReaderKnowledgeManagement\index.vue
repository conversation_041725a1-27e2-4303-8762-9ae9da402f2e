<!-- 知识点管理 -->
<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="课程大类：">
        <el-select v-model="dataQuery.curriculumId" placeholder="请选择" @change="exchangeKcdl">
          <el-option v-for="item in kcdlList" :key="item.id" :value="item.id" :label="item.enName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="知识点名称：">
        <el-input v-model.trim="dataQuery.knowledgeName" placeholder="请输入知识点名称搜索" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="margin-left: 5px" @click="resetQuery">重置</el-button>
        <el-button type="primary" style="margin-right: 5px" @click="search">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="contentBox">
      <div style="display: flex; align-items: center; justify-content: space-between">
        <div style="display: flex; align-items: center; gap: 10px">
          <h3 style="margin: 0">知识点</h3>
          <div>
            <span style="font-size: 12px; color: #6c6c6c; margin-top: 8px">
              没有版本、学科、学段，
              <el-button type="text" @click="goConfig">去配置></el-button>
              。
              <!-- 没有权限请联系平台运营 -->
            </span>
          </div>
        </div>
        <div>
          <el-button type="primary" icon="el-icon-download" @click="importTemplate">下载模板</el-button>
          <el-button type="primary" icon="el-icon-document-add" @click="importQuestion">导入数据</el-button>
          <el-button type="primary" icon="el-icon-plus" @click="addKnowledge">新增知识点</el-button>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="courseType">
        <el-tree
          ref="categoryTree"
          v-loading="treeLoading"
          :data="categoryTreeData"
          node-key="id"
          default-expand-all
          highlight-current
          :expand-on-click-node="false"
          :current-node-key="currentNodeKey"
          @node-click="categoryClick"
          :allow-drop="allowDrop"
          @node-drag-end="handleDragEnd"
          @node-drag-over="handleDragOver"
          @node-drop="nodeDrop"
          :props="defaultProps"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <i class="el-icon-document"></i>
            <span class="tree-label">{{ node.label }}</span>
            <span style="display: none" class="btns">
              <span v-if="data.nodeLevel === 2 || data.nodeLevel === 3" class="el-icon-plus" @click="handleAdd(node, data)"></span>
              <span v-if="data.nodeLevel === 3 || data.nodeLevel === 4" class="el-icon-edit" @click="handleUpdate(node, data)"></span>
              <template v-if="(!draggingFlag && data.nodeLevel === 3) || (!draggingFlag && data.nodeLevel === 4)">
                <el-button type="text" class="el-icon-top" :disabled="getActionState(node, data, 'up')" @click.stop="upClass(node, data)"></el-button>
                <el-button type="text" class="el-icon-bottom" :disabled="getActionState(node, data, 'down')" @click.stop="downClass(node, data)"></el-button>
              </template>
              &nbsp;
              <span v-if="data.nodeLevel === 3 || data.nodeLevel === 4" class="el-icon-delete" @click.stop="handleTypeDel(node, data)"></span>
            </span>
          </span>
        </el-tree>
      </div>
      <div class="list-area">
        <el-table class="common-table" :data="tableData" v-loading.body="tableLoading" :default-sort="{ prop: 'sort', order: 'descending' }" height="62vh">
          <el-table-column type="index" label="序号">
            <template slot-scope="scope">
              {{ (tablePage.currentPage - 1) * tablePage.size + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="knowledgeName" label="知识点名称" />
          <el-table-column prop="sortsNum" label="排序号" />
          <el-table-column prop="courseSubjectNodeName" label="学科" />
          <el-table-column prop="coursePeriodNodeName" label="学段" />
          <el-table-column prop="chapterNodeName" label="章节" />
          <el-table-column prop="createTime" label="新增时间" />
          <el-table-column prop="id" label="操作">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="changeItem(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" @click="deleteItem(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-col :span="24" style="margin-bottom: 20px; margin-top: 20px; padding-right: 20px">
          <el-pagination
            :current-page="tablePage.currentPage"
            :page-size="tablePage.size"
            :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tablePage.totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-col>
      </div>
    </div>
    <el-dialog :title="dialogTitle" :visible.sync="viewDiaOpen" width="500px" :close-on-click-modal="false">
      <el-form ref="importFrom" :model="importFrom" :rules="rules" label-position="right" label-width="120px" style="width: 100%">
        <el-row :gutter="20" v-if="dialogState == 0">
          <el-col :span="24">
            <el-form-item label="章节名称" label-width="180px" prop="zjmc">
              <el-input v-model.trim="importFrom.zjmc" style="width: 220px" maxlength="15" show-word-limit type="textarea"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="dialogState == 1">
          <el-col :span="24">
            <el-form-item label="知识点名称" label-width="180px" prop="zsdmc">
              <el-input v-model.trim="importFrom.zsdmc" style="width: 220px" maxlength="20" show-word-limit type="textarea"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="排序号" label-width="180px" prop="pxh">
              <el-input v-model.trim="importFrom.pxh" style="width: 220px" @input="validateMinValue"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="dialogState == 2">
          <el-col :span="24">
            <el-form-item label="知识小节名称" label-width="180px" prop="zsxjmc">
              <el-input v-model.trim="importFrom.zsxjmc" style="width: 220px" maxlength="15" show-word-limit type="textarea"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDiaOpen = false">取消</el-button>
        <el-button type="primary" @click="saveStatus">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="文件导入" :visible.sync="importOpen" width="660px" :close-on-click-modal="false" @close="close">
      <el-form ref="excelFrom" :model="excelFrom" label-position="left" label-width="120px" style="width: 100%" v-loading="importLoading">
        <el-form-item label="文件导入">
          <excel-upload :limit="1" :showTip="false" :fileList="fileList" @handleSuccess="handleSuccess" @handleRemove="handleRemove"></excel-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="importSubmit()" :loading="importLoading">确定</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="错误信息列表" :visible.sync="errorDiaOpen" width="50%" :close-on-click-modal="false">
      <el-table class="common-table" :data="errorData" max-height="300" border style="width: 100%">
        <el-table-column prop="taskNo" label="序号"></el-table-column>
        <el-table-column prop="curriculumName" label="课程大类"></el-table-column>
        <el-table-column prop="subjectName" label="学科"></el-table-column>
        <el-table-column prop="periodName" label="学段"></el-table-column>
        <el-table-column prop="chapterName" label="章节名称"></el-table-column>
        <el-table-column prop="knowledgeSummaryName" label="知识小结"></el-table-column>
        <el-table-column prop="knowledgeName" label="知识点名称"></el-table-column>
        <el-table-column prop="sortsNum" label="排序号"></el-table-column>
        <el-table-column prop="errMsg" label="错误信息" width="auto"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="errorClose">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import forStudent from '@/api/testPaper/management';
  import ExcelUpload from './upload.vue';
  import checkPermission from '@/utils/permission';
  import {
    addEditKnowledge, addEditKnowledgeSection, deleteKnowledge, deleteKnowledgeSection,
    getCourseCategories,
    getCourseTreeDataNoVersion,
    getKnowledgeList, importKnowledgeFile, updateKnowledgeSort
  } from '@/api/superReaderAPI/knowledgePointManagement';
  export default {
    name: 'knowledgeManagement',
    components: {
      ExcelUpload
    },
    data() {
      return {
        errorDiaOpen: false,
        errorData: [],
        importOpen: false,
        fileList: [],
        excelFrom: {
          file: null
        },
        tempFile: null,
        importLoading: false,
        importFrom: {
          zjmc: '', // 章节名称
          zsdmc: '', // 知识点名称
          pxh: '', // 排序号
          zsxjmc: '', // 知识小结名称
          id: null
        },
        rules: {
          zjmc: [{ required: true, message: '必填', trigger: 'blur' }],
          zsdmc: [{ required: true, message: '必填', trigger: 'blur' }],
          pxh: [{ required: true, message: '必填', trigger: 'blur' }],
          zsxjmc: [{ required: true, message: '必填', trigger: 'blur' }]
        },
        dataQuery: {
          curriculumId: null, // 课程大类
          curriculumName: '',
          knowledgeName: ''
        },
        kcdlList: [], // 课程大类列表
        treeLoading: false,
        categoryTreeData: [],
        currentNodeKey: '', // 当前点击节点的id
        currentNodeLevel: '', // 当前点击节点的分类层级
        currentNodeData: {}, // 当前点击节点的数据
        draggingFlag: false, // 用户是否正在拖拽移动
        form: {
          categoryCode: ''
        },
        tableLoading: false,
        tableData: [],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalItems: 0
        },
        viewDiaOpen: false,
        dialogTitle: '新增版本',
        dialogState: 0,
        isAdd: true, // 新增/编辑
        childrenId: null, // 子级id
        parentId: null, // 父级id
        moreParentId: null, // 更多父级id
        mostParentId: null, // 最上级id
        defaultProps: {
          children: 'children',
          label: 'nodeName'
        }
      };
    },
    created() {
      this.getKcdlList();
    },
    watch: {
      $route(to, from) {
        console.log(to, from, 'knowledgeManagement路由变化了，重新加载数据');
        if (to.path == '/_chinese_reader/superReaderKnowledgeManagement') {
          this.dataQuery.knowledgeName = '';
          this.getCategoryTree();
        }
      }
    },
    methods: {
      checkPermission,
      handleSuccess(res) {
        this.fileList.push(res);
        this.tempFile = res;
      },
      handleRemove(index) {
        if (index != -1) {
          this.fileList.splice(index, 1);
          this.tempFile = null;
        }
      },
      importSubmit() {
        // 验证是否上传了文件
        if (!this.tempFile) {
          this.$message.error('请先上传文件');
          return;
        }
        // 导入
        this.importLoading = true;
        const formData = new FormData();
        formData.append('file', this.tempFile.raw);
        // formData.append('curriculumId', this.selectOperate.curriculumId); // 额外参数
        importKnowledgeFile(formData)
          .then((res) => {
            if (res.data && res.data.length) {
              this.fileList = [];
              this.tempFile = null;
              this.importLoading = false;
              this.errorData = res.data;
              this.errorDiaOpen = true;
            } else {
              this.$message.success('导入成功');
              this.fileList = [];
              this.tempFile = null;
              this.importLoading = false;
              this.importOpen = false;
              this.getCategoryTree();
              this.currentNodeData.nodeLevel = 1;
              this.currentNodeData.id = this.form.categoryCode;
              this.search();
            }
          })
          .catch((err) => {
            this.importLoading = false;
            this.importOpen = false;
          });
      },
      errorClose() {
        this.importOpen = false;
        this.errorDiaOpen = false;
      },
      close() {
        this.importOpen = false;
        this.excelFrom = {
          file: null
        };
        this.fileList = [];
      },
      // 导入
      importQuestion() {
        this.importOpen = true;
      },
      goConfig() {
        if (this.checkPermission(['m:chineseReader:superReaderCourseTypeConfig'])) {
          this.$router.push({ path: '/_chinese_reader/superReaderCourseTypeConfig' });
        } else {
          this.$message.error('您没有权限，请联系管理配置！');
        }
      },
      saveStatus() {
        switch (this.dialogState) {
          case 0: // 新增/编辑章节
            this.addChapter();
            break;
          case 1: // 新增/编辑知识点
            this.saveItem();
            break;
          case 2: // 新增知识小结
            this.addChapter();
            break;
          default:
            break;
        }
      },
      // 新增
      handleAdd(node, data) {
        this.isAdd = true;
        if (data.nodeLevel == 2) {
          this.dialogState = 0;
          this.dialogTitle = '新增章节';
          this.importFrom.zjmc = '';
        } else if (data.nodeLevel == 3) {
          this.dialogState = 2;
          this.dialogTitle = '新增知识小节';
          this.importFrom.zsxjmc = '';
        }
        this.viewDiaOpen = true;
      },
      // 编辑
      handleUpdate(node, data) {
        this.isAdd = false;
        if (data.nodeLevel == 3) {
          this.dialogState = 0;
          this.dialogTitle = '编辑章节';
          this.importFrom.zjmc = data.nodeName;
        } else if (data.nodeLevel == 4) {
          this.dialogState = 2;
          this.dialogTitle = '编辑知识小节';
          this.importFrom.zsxjmc = data.nodeName;
        }
        this.viewDiaOpen = true;
      },
      // 删除
      handleTypeDel(node, data) {
        this.$confirm('确认删除课程分类？删除分类前系统会检查分类下是否有子分类和课程，若存在，则不允许删除。', '课程分类删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let id = data.id
            deleteKnowledgeSection(id).then((res) => {
              if (res.success) {
                this.$message.success('删除成功！');
                this.getCategoryTree();
                // 删除当前节点，则将默认回到第一层第一个节点，并作为当前节点
                this.currentNodeData.nodeLevel = 1;
                this.currentNodeData.id = this.form.categoryCode;
                this.search();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      exchangeKcdl() {
        this.getCategoryTree();
      },
      // 获取课程分类树
      getCategoryTree() {
        this.treeLoading = true;
        this.categoryTreeData = [];
        const that = this;
        let data = {
          curriculumId: this.dataQuery.curriculumId,
          nodeLevel: 4
        };
        getCourseTreeDataNoVersion(data)
          .then((res) => {
            that.categoryTreeData = that.deepReplace(res.data);

            if (that.categoryTreeData instanceof Array && that.categoryTreeData.length > 0) {
              that.currentNodeKey = that.categoryTreeData[0].id;
              that.currentNodeLevel = that.categoryTreeData[0].nodeLevel;
              that.$nextTick(function () {
                that.$refs['categoryTree'].setCurrentKey(that.currentNodeKey);
              });
              // 查询第一个一级分类下绑定的课程
              that.form.categoryCode = that.categoryTreeData[0].id;
              this.search();
            }
            that.treeLoading = false;
          })
          .catch((err) => {
            that.treeLoading = false;
          });
      },
      // 点击分类节点
      categoryClick(data, node) {
        if (node.level == 3) {
          this.childrenId = node.childNodes.length ? node.childNodes[0].data.id : null;
          this.parentId = node.parent.data.id;
          this.moreParentId = node.parent.parent.data.id;
        } else if (node.level == 4) {
          this.parentId = node.parent.data.id;
          this.moreParentId = node.parent.parent.data.id;
          this.mostParentId = node.parent.parent.parent.data.id;
        }

        this.currentNodeKey = data.id;
        this.currentNodeLevel = data.nodeLevel;
        this.currentNodeData = data;
        this.search();
      },
      handleDragEnd(draggingNode, dropNode, type, event) {
        const allowFlag1 = draggingNode.level === dropNode.level && type !== 'inner';
        const allowFlag2 = draggingNode.level > dropNode.level && draggingNode.level - dropNode.level == 1 && type === 'inner';
        if (!allowFlag1 && !allowFlag2) {
          this.$message.error('分类排序错误，请重新排序');
        }
      },
      handleDragOver(draggingNode, dropNode, event) {
        // 同级移动时不出现禁止图标
        if (draggingNode.level === dropNode.level) {
          event.dataTransfer.dropEffect = 'move';
        }
        //在此事件内修正offset解决el-tree在有滚动条时拖拽提示线错位的问题
        let offsetTop = this.$refs['categoryTree'].$el.scrollTop || 0;
        let originalTop = document.querySelector('.el-tree__drop-indicator').offsetTop;
        document.querySelector('.el-tree__drop-indicator').style.top = offsetTop + originalTop + 'px';
      },
      nodeDrop(draggingNode, dropNode, dropType, event) {
        if (!this.draggingFlag) {
          this.draggingFlag = !this.draggingFlag;
        }
      },
      getActionState(node, data, action) {
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const index = children.findIndex((el) => el.id == data.id);
        const len = children.length;
        if (index == 0 && action == 'up') {
          return true;
        } else if (index == len - 1 && action == 'down') {
          return true;
        }
        return false;
      },
      deepReplace(array) {
        if (array instanceof Array && array.length >= 1) {
          return array.map((el) => {
            return {
              id: el.id,
              label: el.nodeName,
              children: this.deepReplace(el.childList),
              ...el
            };
          });
        } else {
          return [];
        }
      },
      // 分类拖拽
      allowDrop(draggingNode, dropNode, type) {
        console.log(draggingNode, dropNode);
        if ((draggingNode.level === 3 || draggingNode.level === 2 || draggingNode.level === 4) && draggingNode.data.parentNodeId !== dropNode.data.parentNodeId) {
          return false;
        }

        if (draggingNode.level === dropNode.level) {
          return type === 'prev' || type === 'next';
        } else if (draggingNode.level > dropNode.level && draggingNode.level - dropNode.level == 1) {
          return type === 'inner';
        } else {
          // 不同级进行处理
          return false;
        }
      },
      upClass(node, data) {
        let param = [];
        param.push({
          id: data.id,
          sortsNum: data.sortsNum
        });
        const parentData = Array.isArray(node.parent.childNodes) ? node.parent.childNodes : [node.parent.childNodes];
        let matchedIndex = parentData.findIndex((item) => item.data.id === data.id);
        // 获取前一项
        const prevItem = parentData[matchedIndex - 1];
        param.push({
          id: prevItem.data.id,
          sortsNum: prevItem.data.sortsNum
        });
        updateKnowledgeSort(param).then((res) => {
          if (res.success) {
            this.$message.success('操作成功！');
            this.viewDiaOpen = false;
            this.getCategoryTree();
          }
        });
      },
      downClass(node, data) {
        let param = [];
        param.push({
          id: data.id,
          sortsNum: data.sortsNum
        });
        const parentData = Array.isArray(node.parent.childNodes) ? node.parent.childNodes : [node.parent.childNodes];
        let matchedIndex = parentData.findIndex((item) => item.data.id === data.id);
        // 获取前一项
        const prevItem = parentData[matchedIndex + 1];
        param.push({
          id: prevItem.data.id,
          sortsNum: prevItem.data.sortsNum
        });
        updateKnowledgeSort(param).then((res) => {
          if (res.success) {
            this.$message.success('操作成功！');
            this.viewDiaOpen = false;
            this.getCategoryTree();
          }
        });
      },
      // 编辑
      changeItem(row) {
        this.dialogTitle = '编辑知识点';
        this.dialogState = 1;
        this.importFrom.zsdmc = row.knowledgeName;
        this.importFrom.pxh = row.sortsNum;
        this.importFrom.id = row.id;
        this.viewDiaOpen = true;
      },
      // 新增/编辑章节/知识小点
      addChapter() {
        this.$refs['importFrom'].validate((valid) => {
          if (!valid) return;
          let data = {};
          data.id = this.isAdd == true ? null : this.currentNodeData.id;
          data.nodeName = this.dialogState == 0 ? this.importFrom.zjmc : this.importFrom.zsxjmc;
          data.nodeType = this.isAdd == true ? this.currentNodeData.nodeType + 1 : this.currentNodeData.nodeType;
          data.nodeLevel = this.isAdd == true ? this.currentNodeData.nodeLevel + 1 : this.currentNodeData.nodeLevel;
          data.curriculumId = this.dataQuery.curriculumId;
          data.curriculumName = this.dataQuery.curriculumName;
          data.parentNodeId = this.isAdd == true ? this.currentNodeData.id : this.currentNodeData.parentNodeId;
          data.sortsNum = 1; // 排序号默认为1，后续拖拽排序
          data.isChild = this.currentNodeData.isChild;
          addEditKnowledgeSection(data).then((res) => {
            if (res.success) {
              this.$message.success('操作成功！');
              this.viewDiaOpen = false;
              this.getCategoryTree();
            }
          });
        });
      },
      // 新增/编辑知识点
      saveItem() {
        this.$refs['importFrom'].validate((valid) => {
          if (!valid) return;
          let data = {};
          data.id = this.importFrom.id || null;
          data.curriculumId = this.dataQuery.curriculumId;
          data.knowledgeName = this.importFrom.zsdmc;
          data.sortsNum = this.importFrom.pxh;
          if (this.currentNodeLevel == 1) {
            // 学科节点id
            this.$message.error('请选择章节或知识点节点进行操作！');
            return;
          } else if (this.currentNodeLevel == 2) {
            // 课程学段节点id
            this.$message.error('请选择章节或知识点节点进行操作！');
            return;
          } else if (this.currentNodeLevel == 3) {
            // 章节节点id
            data.courseSubjectNodeId = this.moreParentId;
            data.coursePeriodNodeId = this.parentId;
            data.chapterNodeId = this.currentNodeData.id;
            data.knowledgeSummaryNodeId = this.childrenId;
          } else if (this.currentNodeLevel == 4) {
            // 知识小结节点id
            data.courseSubjectNodeId = this.mostParentId;
            data.coursePeriodNodeId = this.moreParentId;
            data.chapterNodeId = this.parentId;
            data.knowledgeSummaryNodeId = this.currentNodeData.id;
          } else {
            data.courseSubjectNodeId = this.form.categoryCode;
          }
          addEditKnowledge(data).then((res) => {
            if (res.success) {
              this.$message.success('操作成功！');
              this.viewDiaOpen = false;
              this.submitForm();
            }
          });
        });
      },
      // 新增知识点
      addKnowledge() {
        if (this.currentNodeLevel == 4) {
          this.dialogTitle = '新增知识点';
          this.dialogState = 1;
          this.importFrom.zsdmc = '';
          this.importFrom.pxh = '';
          this.importFrom.id = null;
          this.viewDiaOpen = true;
        } else {
          this.$message.error('请选择知识小结节点进行操作！');
        }
      },
      // 表单删除
      deleteItem(row) {
        this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            deleteKnowledge({ id: row.id }).then((res) => {
              if (res.success) {
                this.$message({
                  type: 'success',
                  message: res.message
                });
                this.search();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      // 下载模板
      importTemplate() {
        if (!this.dataQuery.curriculumId) {
          this.$message.error('请先选择课程大类');
          return;
        }
        const url = 'https://document.dxznjy.com/dyw/语文阅读超人知识点导入模板.xlsx';
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', '语文阅读知识点导入模板.xlsx');
        document.body.appendChild(link);
        try {
          link.click();
          this.$message.success('模板下载请求已发送，请查看浏览器下载列表');
        } catch (error) {
          this.$message.error('模板下载失败，请重试');
          console.error('文件下载失败:', error);
        }
        // 移除链接并释放内存
        link.parentNode.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
      search() {
        this.tablePage.currentPage = 1;
        this.tablePage.size = 10;
        this.submitForm();
      },
      // 查询
      submitForm() {
        this.tableLoading = true;
        let data = { ...this.dataQuery };
        data.pageNum = this.tablePage.currentPage;
        data.pageSize = this.tablePage.size;
        if (this.currentNodeData.nodeLevel == 1) {
          data.courseSubjectNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 2) {
          data.coursePeriodNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 3) {
          data.chapterNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 4) {
          data.knowledgeSummaryNodeId = this.currentNodeData.id;
        } else {
          data.courseSubjectNodeId = this.form.categoryCode;
        }
        getKnowledgeList(data)
          .then((res) => {
            if (res.success) {
              this.tableData = res.data.data;
              this.tablePage.totalItems = Number(res.data.totalItems);
              this.tablePage.currentPage = Number(res.data.currentPage);
              this.tableLoading = false;
            }
          })
          .catch((err) => {
            this.tableLoading = false;
          });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.currentPage = 1;
        this.tablePage.size = val;
        this.submitForm();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.submitForm();
      },
      // 重置
      resetQuery() {
        this.tablePage.currentPage = 1;
        this.tablePage.size = 10;
        this.dataQuery.knowledgeName = '';
        this.submitForm();
      },
      getKcdlList() {
        getCourseCategories().then((res) => {
          if (res.success) {
            this.kcdlList = res.data;
            this.dataQuery.curriculumId = res.data[0].id;
            this.dataQuery.curriculumName = res.data[0].enName;
            this.getCategoryTree();
          }
        });
      },
      validateMinValue() {
        const inputValue = this.importFrom.pxh;
        // 若输入为空，直接返回
        if (inputValue === '') return;

        // 检查输入是否为有效数字
        if (isNaN(Number(inputValue))) {
          // 输入非有效数字，清空输入框
          this.importFrom.pxh = '';
          return;
        }
        const value = Number(inputValue);
        // 检查是否小于 1
        if (value < 1) {
          this.importFrom.pxh = '1';
        }
        // 检查是否大于 1000
        else if (value > 1000) {
          // 回退到上一个有效的值
          this.importFrom.pxh = inputValue.slice(0, -1);
        }
      }
    },
    computed: {}
  };
</script>

<style lang="less" scoped>
  /deep/.el-dialog__header {
    border-bottom: 1px solid #dfdfdf;
  }
  .contentBox {
    padding: 20px 0;
  }
  ::v-deep .el-textarea__inner {
    padding-right: 50px !important;
  }
</style>

<style scoped>
  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
  /* 树形控件样式 */
  .el-icon-document {
    display: inline-block;
    margin-right: 6px;
  }
  ::v-deep .el-tree {
    max-height: 75vh;
    overflow-y: auto;
  }
  ::v-deep .el-tree::-webkit-scrollbar {
    width: 4px !important;
  }
  /* 滑块样式 */
  ::v-deep .el-tree::-webkit-scrollbar-thumb {
    background-color: #aaa;
    border-radius: 10px;
  }
  /* 滚动条轨道样式 */
  ::v-deep .el-tree::-webkit-scrollbar-track {
    background-color: #fafafa;
    border-radius: 10px;
  }

  ::v-deep .el-tree .el-tree-node__content:hover {
    color: #1890ff;
  }
  ::v-deep .el-tree .el-tree-node__content:hover .btns {
    display: inline-block !important;
    margin-left: 20px;
  }
  ::v-deep .el-tree .el-tree-node__content:hover .btns span {
    display: inline-block;
    margin-right: 6px;
    font-weight: 500;
  }
  ::v-deep .el-tree .tree-label {
    font-size: 15px;
  }
  .container {
    width: 100%;
    display: flex;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    padding: 18px;
    box-sizing: border-box;
    border-radius: 6px;
  }
  .courseType {
    border: 1px solid #eee;
    flex: 2;
  }
  .tree-title {
    width: 100%;
    background-color: #f0f0f0;
    border-bottom: 1px solid #eee;
    padding: 5px 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }
  ::v-deep .courseType .el-tree {
    padding: 20px;
    box-sizing: border-box;
  }
  .list-area {
    flex: 7;
    margin-left: 10px;
    overflow: hidden;
  }
  .list-area .toast {
    color: #555;
    font-size: 22px;
    margin-top: 20%;
    transform: translateY(-50%);
    text-align: center;
  }
  .search-input {
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 20px;
    box-sizing: border-box;
  }
  .tree-title {
    width: 100%;
    background-color: #f0f0f0;
    border-bottom: 1px solid #eee;
    padding: 5px 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }
  ::v-deep .courseType .el-tree {
    padding: 20px;
    box-sizing: border-box;
  }
  /* 表格区域样式 */
  .add-course-btn {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  ::v-deep .common-table .el-table__header th {
    background-color: #f0f0f0;
  }
  ::v-deep .el-pagination {
    text-align: right;
  }
  .disabled {
    color: #aaa;
  }
</style>
