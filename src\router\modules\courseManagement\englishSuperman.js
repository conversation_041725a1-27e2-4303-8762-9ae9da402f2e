// 英语超人-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/englishSuperman',
  component: Layout,
  redirect: '/course/superReadCourseList',
  meta: {
    perm: 'm:englishSuperman',
    title: '英语超人',
    icon: 'english'
  },
  children: [
    {
      path: '/course',
      redirect: '/course/superReadCourseList',
      component: () => import('@/views/routerView'),
      meta: {
        perm: 'm:course',
        title: '课程管理'
      },
      children: [
        {
          path: 'superReadCourseList',
          component: () => import('@/views/course/superReadCourseList'),
          name: 'superReadCourseList',
          meta: {
            perm: 'm:course:superReadCourseList',
            title: '超级阅读课程列表'
          }
        },
        // 文章列表
        {
          path: 'articleList',
          hidden: true,
          component: () => import('@/views/course/articleList'),
          name: 'articleList',
          meta: {
            perm: 'm:course:articleList',
            title: '文章列表'
          }
        },
        // 试题列表
        {
          path: 'testQuestionList',
          hidden: true,
          component: () => import('@/views/course/testQuestionList'),
          name: 'testQuestionList',
          meta: {
            perm: 'm:course:testQuestionList',
            title: '试题列表'
          }
        },
        // 试题编辑弹窗
        {
          path: 'questionDialog',
          hidden: true,
          component: () => import('@/views/course/questionDialog'),
          name: 'questionDialog',
          meta: {
            perm: 'm:course:questionDialog',
            title: '增加/编辑'
          }
        },
        // 新版全能听力模块
        {
          path: 'newListenCourseList',
          component: () => import('@/views/course/newListenCourseList'),
          name: 'newListenCourseList',
          meta: {
            perm: 'm:course:newListenCourseList',
            title: '新版听力课程列表'
          }
        },
        {
          path: 'newListenList',
          hidden: true,
          component: () => import('@/views/course/newListenList'),
          name: 'newListenList',
          meta: {
            perm: 'm:course:newListenList',
            title: '听力列表'
          }
        },
        // 听力试题列表
        {
          path: 'listenQuestionList',
          hidden: true,
          component: () => import('@/views/course/listenQuestionList'),
          name: 'listenQuestionList',
          meta: {
            perm: 'm:course:listenQuestionList',
            title: '听力题目列表'
          }
        },
        // 试题编辑弹窗
        {
          path: 'listenQuestionDialog',
          hidden: true,
          component: () => import('@/views/course/listenQuestionDialog'),
          name: 'listenQuestionDialog',
          meta: {
            perm: 'm:course:listenQuestionDialog',
            title: '增加/编辑'
          }
        },
        {
          path: 'textbookVersionList',
          component: () => import('@/views/course/textbookVersionList'),
          name: 'textbookVersionList',
          meta: {
            perm: 'm:course:courseEditionList',
            title: '教材版本列表'
          }
        },
        {
          path: 'coursePackageList',
          // hidden: true,
          component: () => import('@/views/course/coursePackageList'),
          name: 'coursePackageList',
          meta: {
            perm: 'm:coursePackage:list',
            title: '课程包'
          }
        },
        {
          path: '/student/areasOpenCourse',
          hidden: true,
          component: () => import('@/views/areas/school/areasOpenCourse'),
          name: 'areasOpenCourse',
          meta: {
            perm: 'm:student:areasOpenCourse',
            title: '开通课程'
          }
        },
        {
          path: 'wordLevelList',
          component: () => import('@/views/course/wordLevelList'),
          name: 'wordLevelList',
          meta: {
            perm: 'm:course:wordLevelList',
            title: '词汇水平列表'
          }
        },
        {
          path: 'wordLevelTestdbList',
          component: () => import('@/views/course/wordLevelTestdbList'),
          name: 'wordLevelTestdbList',
          meta: {
            perm: 'm:course:wordLevelTestdbList',
            title: '单词水平测试题库'
          }
        },
        //课时规划单
        {
          path: 'lessonPlanList',
          component: () => import('@/views/course/lessonPlanList'),
          name: 'lessonPlanList',
          meta: {
            perm: 'm:course:lessonPlanList',
            title: '课程规划单课时列表'
          }
        },
        {
          path: 'courseTools',
          component: () => import('@/views/courseGeneralTools/courseTools'),
          name: 'courseTools',
          meta: {
            perm: 'm:courseGeneralTools:courseTools',
            title: '课程工具关联配置'
          }
        },
        {
          path: 'generalTools',
          component: () => import('@/views/courseGeneralTools/generalTools'),
          name: 'generalTools',
          meta: {
            perm: 'm:courseGeneralTools:generalTools',
            title: '课程工具配置'
          }
        },
        {
          path: 'courseCate',
          component: () => import('@/views/course/courseCate'),
          name: 'courseCate',
          meta: {
            perm: 'm:course:courseCate',
            title: '课程类型'
          }
        },
        {
          path: 'courseCategoryList',
          component: () => import('@/views/course/courseCategoryList'),
          name: 'marketList',
          meta: {
            perm: 'm:course:courseCategoryList',
            title: '课程分类'
          }
        },
        {
          path: 'regionalExamTime',
          component: () => import('@/views/course/regionalExamTime'),
          name: 'regionalExamTime',
          meta: {
            perm: 'm:course:regionalExamTime',
            title: '区域考试时间列表'
          }
        },
        {
          path: 'courseChildrenList',
          hidden: true,
          component: () => import('@/views/course/courseChildrenList'),
          name: 'courseChildrenList',
          meta: {
            perm: 'm:course:courseChildrenList',
            title: '课程子类',
            icon: 'course_category'
          }
        },
        {
          path: 'courseMake',
          hidden: true,
          component: () => import('@/views/course/courseMake'),
          name: 'courseMake',
          meta: {
            perm: 'm:course:courseMake',
            title: '制作课程'
          }
        }
      ]
    },
    {
      path: '/interest',
      redirect: '/interest/levelConfig',
      component: () => import('@/views/routerView'),
      meta: {
        perm: 'm:interest',
        title: '趣味复习'
      },
      children: [
        {
          path: 'levelConfig',
          component: () => import('@/views/interest/levelConfig'),
          name: 'levelConfig',
          meta: {
            perm: 'm:interest:levelConfig',
            title: '关数配置'
          }
        },
        {
          path: 'scoreConfig',
          component: () => import('@/views/interest/scoreConfig'),
          name: 'scoreConfig',
          meta: {
            perm: 'm:interest:scoreConfig',
            title: '等级配置'
          }
        },
        {
          path: 'broadcastCode',
          component: () => import('@/views/broadcast/broadcastCode'),
          name: 'broadcastCode',
          meta: {
            perm: 'm:purchaseCode:broadcastCode',
            title: '趣味复习购买码'
          }
        }
      ]
    },
    {
      path: '/grammar',
      redirect: '/grammar/grammarPoint',
      component: () => import('@/views/routerView'),
      meta: {
        perm: 'm:grammar',
        title: '语法管理'
      },
      children: [
        {
          path: 'grammarPoint',
          component: () => import('@/views/grammar/grammarPoint'),
          name: 'grammarPoint',
          meta: {
            perm: 'm:grammar:grammarPoint',
            title: '语法点管理'
          }
        },
        {
          path: 'knowledgePoint',
          component: () => import('@/views/grammar/knowledgePoint'),
          name: 'knowledgePoint',
          meta: {
            perm: 'm:grammar:knowledgePoint',
            title: '知识点管理'
          }
        },
        {
          path: 'grammarQuestion',
          component: () => import('@/views/grammar/grammarQuestion'),
          name: 'grammarQuestion',
          meta: {
            perm: 'm:grammar:grammarQuestion',
            title: '题库管理'
          }
        },
        {
          path: 'grammarConfig',
          component: () => import('@/views/grammar/grammarConfig'),
          name: 'grammarConfig',
          meta: {
            perm: 'm:grammar:grammarConfig',
            title: '语法点配置'
          }
        },
        {
          path: 'questionConfig',
          component: () => import('@/views/grammar/questionConfig'),
          name: 'questionConfig',
          meta: {
            perm: 'm:grammar:questionConfig',
            title: '题目配置'
          }
        },
        {
          path: 'studyRateConfig',
          component: () => import('@/views/grammar/studyRateConfig'),
          name: 'studyRateConfig',
          meta: {
            perm: 'm:grammar:studyRateConfig',
            title: '语法掌握度配置'
          }
        },
        {
          path: 'grammarPwdConfig',
          component: () => import('@/views/grammar/grammarPwdConfig'),
          name: 'grammarPwdConfig',
          meta: {
            perm: 'm:grammar:grammarPwdConfig',
            title: '教练 授权码配置'
          }
        },
        {
          path: 'grammarConsumeHours',
          component: () => import('@/views/grammar/grammarConsumeHours'),
          name: 'grammarConsumeHours',
          meta: {
            perm: 'm:grammar:grammarConsumeHours',
            title: '语法包消费学时配置'
          }
        }
      ]
    },
    {
      path: '/syntax',
      redirect: '/syntax/grammarPoint',
      component: () => import('@/views/routerView'),
      meta: {
        perm: 'm:syntax',
        title: '新版语法管理'
      },
      children: [
        {
          path: 'grammarPoint',
          component: () => import('@/views/syntax/grammarPoint'),
          name: 'syntaxPoint',
          meta: {
            perm: 'm:syntax:grammarPoint',
            title: '语法点管理'
          }
        },
        {
          path: 'knowledgePoint',
          component: () => import('@/views/syntax/knowledgePoint'),
          name: 'syntaxknowledgePoint',
          meta: {
            perm: 'm:syntax:knowledgePoint',
            title: '知识点管理'
          }
        },
        {
          path: 'prePostTestList',
          component: () => import('@/views/syntax/prePostTestList'),
          name: 'syntaxprePostTestList',
          meta: {
            perm: 'm:syntax:prePostTestList',
            title: '课前课后试题列表'
          }
        },
        {
          path: 'grammarQuestion',
          component: () => import('@/views/syntax/grammarQuestion'),
          name: 'syntaxQuestion',
          meta: {
            perm: 'm:syntax:grammarQuestion',
            title: '知识点题库'
          }
        },
        {
          path: 'grammarQuestionGra',
          component: () => import('@/views/syntax/grammarQuestionGra'),
          name: 'syntaxQuestion',
          meta: {
            perm: 'm:syntax:grammarQuestionGra',
            title: '语法点和结业检测题库'
          }
        },
        {
          path: 'questionConfig',
          component: () => import('@/views/syntax/questionConfig'),
          name: 'syntaxConfig',
          meta: {
            perm: 'm:syntax:questionConfig',
            title: '题目配置'
          }
        },
        {
          path: 'studyRateConfig',
          component: () => import('@/views/syntax/studyRateConfig'),
          name: 'syntaxRateConfig',
          meta: {
            perm: 'm:syntax:studyRateConfig',
            title: '语法掌握度配置'
          }
        }
      ]
    }
  ]
};
