<template>
  <CustomDialog
    v-if="HeaderSettingsStyle"
    :value.sync="HeaderSettingsStyle"
    :width="dialogWidth"
    :show-close="false"
    :close-on-click-modal="false"
    :title="title"
    :closeButtonColor="closeButtonColor"
    :titleColor="titleColor"
    :titleBackgroundColor="titleBackgroundColor"
    :borderRadius="borderRadius"
    @close="dialogBeforeClose"
  >
    <div class="header-settings">
      <!-- 可选列区域 -->
      <div class="settings-section available-columns">
        <div class="checkbox-group-wrapper">
          <el-checkbox-group v-model="checkList" @change="handleCheckChange">
            <div class="checkbox-grid">
              <el-checkbox v-for="item in filteredHeaderSettings" :key="item.prop" :label="item.prop" class="checkbox-item">
                <span class="checkbox-label">{{ item.label }}</span>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </div>

      <!-- 已选列区域 -->
      <div class="settings-section selected-columns">
        <div class="selected-header">
          <div class="section-title">已选{{ selectedItems.length }}列</div>
        </div>
        <draggable v-model="selectedItems" class="drag-list" @end="onDragEnd">
          <div v-for="item in selectedItems" :key="item.prop" class="drag-item">
            <div class="drag-handle">
              <img src="../../assets/icons_images/setting-config.png" alt="拖拽图标" class="drag-icon" />
            </div>
            <span class="item-label">{{ item.label }}</span>
          </div>
        </draggable>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogBeforeClose">取 消</el-button>
      <el-button type="primary" @click="saveSettings">保 存</el-button>
    </div>
  </CustomDialog>
</template>

<script>
  import draggable from 'vuedraggable';
  import CustomDialog from '@/components/customDialog/index.vue';

  export default {
    name: 'HeaderSettingsDialog',
    components: {
      draggable,
      CustomDialog
    },
    props: {
      // 控制弹窗显示
      HeaderSettingsStyle: {
        type: Boolean,
        default: false
      },
      // 学员列表的信息
      rowlist: {
        default: false
      },
      // 表头配置项
      headerSettings: {
        type: Array,
        default: () => []
      },
      closeButtonColor: {
        type: String,
        default: '##fff'
      },
      titleColor: {
        type: String,
        default: '#fff'
      }
    },
    data() {
      return {
        screenWidth: window.innerWidth,
        checkList: [],
        selectedItems: [],
        title: '表头设置',
        titleBackgroundColor: '#60766d',
        borderRadius: '8px'
      };
    },
    computed: {
      // 过滤掉 operate 项的表头配置
      filteredHeaderSettings() {
        return this.headerSettings.filter((item) => item.prop !== 'operate');
      },
      // 根据屏幕宽度计算对话框宽度
      dialogWidth() {
        if (this.screenWidth > 1300) {
          return '60%';
        } else if (this.screenWidth > 768) {
          return '70%';
        } else {
          return '90%';
        }
      }
    },
    watch: {
      // 监听弹窗显示状态变化，初始化数据
      HeaderSettingsStyle: {
        immediate: true,
        handler(newVal) {
          if (newVal) {
            this.initializeSettings();
          }
        }
      },
      // 监听headerSettings变化，重新初始化
      headerSettings: {
        handler() {
          if (this.HeaderSettingsStyle) {
            this.initializeSettings();
          }
        },
        deep: true
      }
    },
    mounted() {
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
      // 移除事件监听
      window.removeEventListener('resize', this.handleResize);
    },
    methods: {
      // 初始化设置
      initializeSettings() {
        try {
          // 如果已有选中项，保持当前选择
          if (this.checkList.length === 0) {
            // 默认选中所有过滤后的项
            this.checkList = this.filteredHeaderSettings.map((item) => item.prop);
            this.selectedItems = [...this.filteredHeaderSettings];
          } else {
            // 根据当前checkList更新selectedItems
            this.handleCheckChange(this.checkList);
          }
        } catch (error) {
          console.error('初始化表头设置失败:', error);
          // 出错时重置为默认状态
          this.checkList = this.filteredHeaderSettings.map((item) => item.prop);
          this.selectedItems = [...this.filteredHeaderSettings];
        }
      },

      // 处理窗口大小变化
      handleResize() {
        this.screenWidth = window.innerWidth;
      },

      // 处理选择变化
      handleCheckChange(value) {
        try {
          // 根据checkList（选中的prop数组）来更新selectedItems（选中的对象数组）
          this.selectedItems = value
            .map((prop) => {
              return this.filteredHeaderSettings.find((item) => item.prop === prop);
            })
            .filter((item) => item != null); // 过滤掉未找到的项
        } catch (error) {
          console.error('处理选择变化失败:', error);
        }
      },

      // 处理拖拽结束
      onDragEnd() {
        try {
          // 拖拽结束后更新checkList以保持同步
          this.checkList = this.selectedItems.map((item) => item.prop);
        } catch (error) {
          console.error('处理拖拽结束失败:', error);
        }
      },

      // 处理弹窗关闭前
      dialogBeforeClose() {
        this.$emit('update:HeaderSettingsStyle', false);
      },

      // 处理保存
      saveSettings() {
        try {
          if (this.selectedItems.length === 0) {
            this.$message.warning('请至少选择一列');
            return;
          }

          // 保存选中的配置项
          this.$emit('saveHeaderSettings', this.selectedItems);
          this.dialogBeforeClose();
        } catch (error) {
          console.error('保存设置失败:', error);
          this.$message.error('保存失败，请重试');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .header-settings {
    display: flex;
    border-top: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    padding: 1.25rem 0;
    height: 28rem;
    font-size: 14px;

    @media (max-width: 768px) {
      flex-direction: column;
      height: auto;
    }
  }

  .settings-section {
    display: flex;
    flex-direction: column;
    padding: 0 1rem;

    &.available-columns {
      flex: 7;
      border-right: 1px solid #e8e8e8;

      @media (max-width: 768px) {
        border-right: none;
        border-bottom: 1px solid #e8e8e8;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
      }
    }

    &.selected-columns {
      flex: 3;
      align-items: flex-start;
    }
  }

  .selected-header {
    width: 100%;
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    text-align: left;
  }

  .checkbox-group-wrapper {
    flex: 1;
    overflow-y: auto;
  }

  .checkbox-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px 16px;

    @media (max-width: 1300px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 10px 12px;
    }
  }

  .checkbox-item {
    display: flex;
    align-items: flex-start;
    min-height: 40px;
    margin: 0 !important;
    width: 8rem;

    ::v-deep .el-checkbox__label {
      font-size: 14px;
      color: #606266;
      white-space: normal;
      word-wrap: break-word;
      line-height: 1.4;
      max-width: 100%;
      text-align: left;
      width: 100%;
      padding-left: 4px;
    }

    ::v-deep &.is-checked .el-checkbox__label {
      color: #606266 !important;
    }

    ::v-deep .el-checkbox {
      display: flex;
      align-items: flex-start;
      width: 100%;
    }

    ::v-deep .el-checkbox__input {
      margin-top: 2px;
    }
  }

  .drag-list {
    width: 100%;
    flex: 1;
    overflow-y: auto;
    min-height: 200px;
  }

  .drag-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px 12px;
    padding-left: 0;
    margin-bottom: 8px;
    background-color: transparent;
    border-radius: 4px;
    cursor: move;
    width: 100%;
    box-sizing: border-box;
  }

  .drag-handle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }

  .drag-icon {
    width: 100%;
    height: 100%;
    opacity: 0.6;
    transition: opacity 0.2s;

    .drag-item:hover & {
      opacity: 1;
    }
  }

  .item-label {
    font-size: 14px;
    text-align: left;
    flex: 1;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 0 20px;
  }

  ::v-deep .el-dialog__body {
    padding: 0;
  }
  ::v-deep .el-dialog__header {
    padding: 0;
  }

  .headerSettings-body {
    @extend .header-settings;
  }

  .choiceArea {
    @extend .available-columns;
  }

  .dragArea {
    @extend .selected-columns;
  }

  .paikeTwo {
    margin-top: 10px;
  }
</style>
