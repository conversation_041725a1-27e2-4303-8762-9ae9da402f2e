<template>
  <div>
    <div class="" style="display: flex">
      <el-upload
        class="upload-demo"
        :class="{ 'has-file': !!fileData }"
        action=""
        ref="uploadRef"
        list-type="picture-card"
        :http-request="uploadFileHttp"
        :before-upload="handleBeforeUpload"
        :show-file-list="false"
        :limit="1"
        accept=".ppt,.pptx,.pdf"
        :disabled="fileData && !uploading"
      >
        <!-- 上传按钮：仅在未上传文件时显示 -->
        <!-- <el-button v-if="!fileData && !uploading" type="primary">选择课件</el-button> -->
        <i v-if="!fileData && !uploading" class="el-icon-plus" style="margin-top: 4px"></i>
        <el-progress v-if="uploading" :percentage="progress" type="circle" class="progress-btn" />
        <!-- 上传成功缩略图 -->
        <div v-if="fileData && !uploading" class="file-card">
          <!-- <div class="thumb-wrap"> -->
          <img :src="fileData.thumbnail" class="thumb" alt="缩略图" />
          <div class="overlay">
            <div class="overlay-inner">
              <el-button class="overlay-btn preview" size="mini" type="text" @click.stop.prevent="handlePictureCardPreview(fileData)">预览</el-button>
              <el-button class="overlay-btn remove" size="mini" type="text" @click.stop.prevent="handleRemove">删除</el-button>
            </div>
          </div>
        </div>
      </el-upload>
      <div v-if="fileData" class="fileName" :title="fileData.name">文件名：{{ fileNameDisplay }}</div>
    </div>

    <div class="redWord">仅支持上传1个课件，支持格式ppt、pptx、pdf，大小限制100M</div>

    <!-- 使用 PptPriview 全屏预览组件 -->
    <PptPriview :visible="previewVisible" :coursewareUrl="previewUrl" @close="previewVisible = false" />
  </div>
</template>

<script>
  import { ossPrClient } from '@/api/alibaba';
  import PptPriview from './PptPriview.vue';
  export default {
    name: 'BaseUploadMSoft',
    components: {
      PptPriview
    },
    data() {
      return {
        previewVisible: false,
        previewUrl: '',
        uploading: false,
        progress: 0,
        fileData: null // 上传成功后的文件信息
      };
    },
    computed: {
      // 显示的文件名：超过20字符用...截断
      fileNameDisplay() {
        const name = this.fileData && this.fileData.name ? this.fileData.name : '';
        if (!name) return '';
        return name.length > 20 ? name.slice(0, 20) + '...' : name;
      }
    },
    created() {
      ossPrClient();
    },

    methods: {
      resetState() {
        this.uploading = false;
        this.progress = 0;
        this.fileData = null;
        this.previewUrl = '';
        try {
          this.$refs.uploadRef && this.$refs.uploadRef.clearFiles && this.$refs.uploadRef.clearFiles();
        } catch (e) {}
      },
      // 对外暴露的彻底清理方法（父组件可调用）
      clearAll() {
        this.resetState();
      },
      // 上传前校验
      handleBeforeUpload(file) {
        const isValid =
          file.type === 'application/pdf' ||
          file.type === 'application/vnd.ms-powerpoint' ||
          file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation';

        if (!isValid) {
          this.$message.error('仅支持上传 ppt、pptx、pdf 文件！');
          return false;
        }
        if (file.size > 100 * 1024 * 1024) {
          this.$message.error('文件大小不能超过 100MB！');
          return false;
        }
        return true;
      },

      // 自定义上传
      uploadFileHttp({ file }) {
        this.uploading = true;
        this.progress = 0;
        this.$emit('uploading-change', true);
        // 模拟上传文件
        const that = this;
        const fileName = `manage/${Date.parse(new Date())}/${file.name}`;
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              console.log('阿里云OSS上传课件成功', file, res, url, name);
              // 根据文件名后缀生成对应的缩略图
              const ext = that.getFileExtFromName(file.name);
              const thumbnail = that.fileTypeJudge(ext) || '';
              that.fileData = {
                name: file.name,
                url: url,
                thumbnail: thumbnail
              };
              that.$emit('upload-success', { url, name });
              that.$emit('uploading-change', false);
              that.uploading = false;
              that.progress = 100;
              const formData = new FormData();
              formData.append('file', file);
            })
            .catch((err) => {
              that.$message.error('上传失败，请重试！');
              that.uploading = false;
              that.progress = 0;
              that.$emit('uploading-change', false);
            });
        });
      },

      // 删除文件
      handleRemove() {
        this.$emit('delete-file');
        this.resetState();
        this.$emit('uploading-change', false);
        this.$message.success('删除成功'); // 添加删除成功提示
      },

      // 从文件名提取后缀（带点），如 '.docx'
      getFileExtFromName(fileName) {
        if (!fileName) return '';
        const idx = fileName.lastIndexOf('.');
        return idx !== -1 ? fileName.slice(idx).toLowerCase() : '';
      },

      // 根据后缀返回缩略图 URL
      fileTypeJudge(fileExt) {
        const fileIconMap = {
          '.doc': 'cc86c7e6-e197-4cc4-90c9-f5bf2cb0fe91',
          '.docx': 'cc86c7e6-e197-4cc4-90c9-f5bf2cb0fe91',
          '.ppt': 'b1c6a54b-fb0d-40c9-ab40-c86a856e01fc',
          '.pptx': 'b1c6a54b-fb0d-40c9-ab40-c86a856e01fc',
          '.xls': '6e78c060-9c84-49b2-9602-31a88c25554a',
          '.xlsx': '6e78c060-9c84-49b2-9602-31a88c25554a',
          '.pdf': '27e2c534-8eb8-4996-b4ee-75f4eeaeee0d',
          '.jpg': '0eb69725-160e-4c48-811d-ffc418fa8a25',
          '.jpeg': '0eb69725-160e-4c48-811d-ffc418fa8a25',
          '.png': '0eb69725-160e-4c48-811d-ffc418fa8a25'
        };

        const baseurl = 'https://document.dxznjy.com/dxSelect/';
        const iconId = fileIconMap[fileExt];

        return iconId ? `${baseurl}${iconId}.png` : '';
      },

      // 预览：打开 PptPriview 组件并传入课件 URL
      handlePictureCardPreview(file) {
        if (!file || !file.url) {
          this.$message.warning('无效的预览文件');
          return;
        }
        this.previewUrl = file.url;
        this.previewVisible = true;
      }
    }
  };
</script>

<style lang="less" scoped>
  .redWord {
    color: #bd3124;
  }
  .file-card {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #fff;
    border: none;
    border-radius: 6px;
    overflow: hidden;
    box-sizing: border-box;
    margin: 0;
  }
  .thumb {
    width: 100%;
    height: 100%;
    object-fit: contain; /* 保持等比例缩放 */
    display: block;
  }
  .progress-btn {
    margin-top: 10px;
  }
  .file-card:hover .overlay {
    opacity: 1;
  }
  .file-card .overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.15); /* 稍微加深，提升对比度 */
    opacity: 0;
    transition: opacity 0.18s ease;
    pointer-events: none; /* 保持与之前逻辑一致 */
  }
  .overlay-inner {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    pointer-events: auto; /* 允许按钮接收点击 */
  }
  .overlay-btn {
    position: relative;
    z-index: 2;
    background: #fff;
    border-radius: 6px;
    width: 96px;
    text-align: center;
    padding: 8px 0;
    font-size: 14px;
    line-height: 1;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(0, 0, 0, 0.06);
  }
  .overlay-btn.preview {
    color: #333;
  }
  .overlay-btn.remove {
    color: #ff7a00; /* 橙色 */
  }
  .file-info {
    margin-left: 10px;
  }
  .file-actions {
    display: flex;
    gap: 10px;
  }
  .fileName {
    color: #6c6c6c;
    margin-top: 122px;
    margin-left: 5px;
  }

  /deep/ .el-button + .el-button {
    margin-left: 0 !important;
  }
  /* 去除图片上传容器的虚线边框 */
  /deep/ .upload-demo.has-file .el-upload--picture-card {
    border: none;
    background: transparent;
  }
  /* 始终希望取消虚线，可直接取消下面注释 */
</style>
