<template>
  <div class="app-container">
    <el-form :model="query" ref="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="门店编号">
        <el-input v-model="query.merchantCode" placeholder="请输入门店编号"></el-input>
      </el-form-item>
      <el-form-item label="门店名称">
        <el-input v-model="query.merchantName" placeholder="请输入门店名称"></el-input>
      </el-form-item>
      <el-form-item label="课程类型">
        <el-select v-model="query.curriculumId" placeholder="请选择" clearable filterable>
          <el-option v-for="item in courseTypeList" :key="item.value" :label="item.enName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="变动类型">
        <el-select v-model="query.flowType" placeholder="请选择" clearable filterable>
          <el-option v-for="item in changeTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="课时类型">
        <el-select v-model="query.classType" placeholder="请选择" clearable filterable>
          <el-option v-for="item in courseTimeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="date"
          @change="dateChange"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="serach">搜索</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <div v-if="showTips" style="color: red; margin-bottom: 16px">温馨提示：当前页面展示的为课程推广大使门店的课时变动明细</div>
    <el-table :data="list" :header-cell-style="{ background: '#eef1f6', color: '#606266' }">
      <el-table-column v-for="col in columns" :prop="col.id" :key="col.id" :label="col.label" align="center">
        <template v-slot="{ row }">
          <div v-if="col.id == 'flowType'">{{ changeTypeList.find((e) => e.value == row.flowType) && changeTypeList.find((e) => e.value == row.flowType).name }}</div>
          <div v-else-if="col.id == 'courseType'">{{ courseTypeList.find((e) => e.id == row.curriculumId) && courseTypeList.find((e) => e.id == row.curriculumId).enName }}</div>
          <div v-else-if="col.id == 'classType'">{{ courseTimeList.find((e) => e.value == row.classType) && courseTimeList.find((e) => e.value == row.classType).name }}</div>
          <div v-else>{{ row[col.id] }}</div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="margin-top: 20px">
      <el-col :span="8" :offset="26">
        <el-pagination
          :current-page="query.pageNum"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="query.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { getCourseCateList } from '@/api/courseCate';
  import salesDetails from '@/api/salesDetails';
  import checkPermission from '@/utils/permission';

  export default {
    data() {
      return {
        query: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },
        courseTypeList: [],
        changeTypeList: [
          { name: '学员退课', value: 'INNER_REFUND' },
          { name: '学员销课', value: 'CourseRelieved' },
          { name: '充值时长', value: 'RechargeCourse' },
          { name: '时长退还', value: 'CourseBack' }
        ],
        courseTimeList: [
          { name: '自有课时', value: 'COURSE' },
          { name: '自行交付', value: 'SELF' },
          { name: '交付课时', value: 'DELIVER' }
        ],

        //仅针对合伙人展示该提示语，其他角色不可见
        showTips: false,

        list: [],
        date: [],
        columns: [
          { id: 'merchantCode', label: '门店编号' },
          { id: 'merchantName', label: '门店名称' },
          { id: 'courseType', label: '课程类型' },
          { id: 'semesterTerm', label: '学段' },
          { id: 'flowType', label: '变动类型' },
          { id: 'classType', label: '课时类型' },
          { id: 'chooseHour', label: '变动学时(节)' },
          { id: 'startTime', label: '创建时间' }
        ]
      };
    },

    created() {
      // 判断登录用户是否是合伙人角色, 是合伙人则显示提示
      this.showTips = checkPermission(['School']);
      this.getCourseTypeList();
      this.init();
    },

    methods: {
      dateChange(e) {
        console.log(e);
        this.query.startDate = e[0];
        this.query.endDate = e[1];
      },
      async init() {
        let { data } = await salesDetails.saleCourseFlowList(this.query);
        this.list = data.data.map((item) => ({
          ...item,
          semesterTerm: item.semesterTerm || '-',
          chooseHour: item.chooseHour * 0.01
        }));
        // this.list = data.data;

        this.query.total = data.totalItems - 0;
      },
      async getCourseTypeList() {
        let res = await getCourseCateList({ pageNum: 1, pageSize: 100 });

        this.courseTypeList = res.data.data;
      },
      serach() {
        this.query.pageNum = 1;
        this.init();
      },
      reset() {
        this.date = [];
        this.query = {
          pageNum: 1,
          pageSize: 10,
          total: 0
        };
        this.init();
      },

      handleSizeChange(val) {
        this.query.pageSize = val;
        this.init();
      },
      handleCurrentChange(val) {
        this.query.pageNum = val;
        this.init();
      }
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .blue {
    color: blue;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
