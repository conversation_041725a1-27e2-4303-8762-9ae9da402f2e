<template>
  <div class="dx-table-row-height-test">
    <h1>DxTable 行高测试</h1>

    <div class="test-section">
      <h2>默认行高 (48px)</h2>
      <dx-table :data="tableData" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="150"></dx-table-column>
        <dx-table-column prop="description" label="描述" width="300"></dx-table-column>
        <dx-table-column prop="date" label="日期" width="150"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>自定义行高 (60px)</h2>
      <dx-table :data="tableData" :row-height="60" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="150"></dx-table-column>
        <dx-table-column prop="description" label="描述" width="300"></dx-table-column>
        <dx-table-column prop="date" label="日期" width="150"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>紧凑行高 (36px)</h2>
      <dx-table :data="tableData" :row-height="36" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="150"></dx-table-column>
        <dx-table-column prop="description" label="描述" width="300"></dx-table-column>
        <dx-table-column prop="date" label="日期" width="150"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>字符串形式行高 (4rem)</h2>
      <dx-table :data="tableData" row-height="4rem" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="150"></dx-table-column>
        <dx-table-column prop="description" label="描述" width="300"></dx-table-column>
        <dx-table-column prop="date" label="日期" width="150"></dx-table-column>
      </dx-table>
    </div>

    <div class="instructions">
      <h3>测试说明：</h3>
      <ul>
        <li>第一个表格使用默认行高 48px</li>
        <li>第二个表格使用自定义行高 60px</li>
        <li>第三个表格使用紧凑行高 36px</li>
        <li>第四个表格使用字符串形式行高 4rem</li>
        <li>所有表格的行高应该是固定的，内容垂直居中</li>
      </ul>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'DxTableRowHeightTest',
    data() {
      return {
        tableData: [
          {
            id: 1,
            name: '张三',
            description: '这是一段简短的描述文字',
            date: '2023-01-01'
          },
          {
            id: 2,
            name: '李四',
            description: '这是另一段描述文字，稍微长一些，用来测试中等长度的文本显示效果',
            date: '2023-01-02'
          },
          {
            id: 3,
            name: '王五',
            description:
              '这是一个非常长的描述文本，用来测试当内容很多的时候表格行高是否能够自动调整。这段文字包含了很多内容，应该会换行显示，而不是被固定的行高截断。我们需要确保文字能够完整显示，同时保持表格的美观性。',
            date: '2023-01-03'
          },
          {
            id: 4,
            name: '赵六',
            description:
              '另一个超长的描述：在现代Web开发中，表格组件是非常重要的UI元素。一个好的表格组件应该能够处理各种不同长度的内容，既要保证短内容时的美观，也要确保长内容时的可读性。通过使用min-height而不是固定height，我们可以实现这种自适应的效果，让表格既有统一的最小高度，又能根据内容自动扩展。',
            date: '2023-01-04'
          },
          {
            id: 5,
            name: '孙七',
            description: '短文本',
            date: '2023-01-05'
          }
        ]
      };
    }
  };
</script>

<style scoped>
  .dx-table-row-height-test {
    padding: 20px;
  }

  .test-section {
    margin-bottom: 40px;
  }

  .test-section h2 {
    margin-bottom: 15px;
    color: #303133;
    font-size: 18px;
  }

  .instructions {
    background-color: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-top: 30px;
  }

  .instructions h3 {
    margin-bottom: 10px;
    color: #303133;
  }

  .instructions ul {
    margin: 0;
    padding-left: 20px;
  }

  .instructions li {
    margin-bottom: 5px;
    color: #606266;
  }
</style>
