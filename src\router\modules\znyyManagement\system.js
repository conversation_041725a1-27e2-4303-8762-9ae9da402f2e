// 系统管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/system',
  component: Layout,
  redirect: '/system/appVersionControl',
  meta: {
    perm: 'm:system',
    title: '系统管理',
    icon: 'system'
  },
  children: [
    {
      path: 'cityCourseConfiguration',
      component: _import('system/cityCourseConfiguration'),
      name: 'cityCourseConfiguration',
      meta: {
        perm: 'm:system:cityCourseConfiguration',
        title: '学时价格配置'
      }
    },
    {
      path: 'link',
      hidden: true,
      component: _import('system/link'),
      name: 'link',
      meta: {
        perm: 'm:link:system/link',
        title: '通联云链接'
      }
    },
    {
      path: 'deliverSetting',
      component: _import('system/deliverSetting'),
      name: 'deliverSetting',
      meta: {
        perm: 'm:system:deliverSetting',
        title: '交付中心配置'
      }
    },
    {
      path: 'updatePssword',
      component: _import('system/updatePssword'),
      name: 'updatePssword',
      meta: {
        perm: 'm:system:updatePssword',
        title: '修改密码'
      }
    },
    {
      path: 'experienceLevelList',
      component: _import('system/experienceLevelList'),
      name: 'experienceLevelList',
      meta: {
        perm: 'm:system:experienceLevelList',
        title: '托管中心级别列表'
      }
    },
    {
      path: 'authorizationCodeExport',
      component: _import('system/authorizationCodeExport'),
      name: 'authorizationCodeExport',
      meta: {
        perm: 'm:system:authorizationCodeExport',
        title: '授权码列表'
      }
    },
    {
      path: 'divisionRankList',
      component: _import('system/divisionRankList'),
      name: 'divisionRankList',
      meta: {
        perm: 'm:system:divisionRankList',
        title: '事业部级别列表'
      }
    },
    {
      path: 'divisionProfitRankList',
      component: _import('system/divisionProfitRankList'),
      name: 'divisionProfitRankList',
      meta: {
        perm: 'm:system:divisionProfitRankList',
        title: '事业部推荐反润比例列表'
      }
    },
    {
      path: 'merProfitConfig',
      component: () => import('@/views/system/merProfitConfig'),
      name: 'merProfitConfig',
      meta: {
        perm: 'm:merProfit:config:list',
        title: '渠道分润比例配置'
      }
    },
    {
      path: 'userOptionLog',
      component: () => import('@/views/system/userOptionLog'),
      name: 'userOptionLog',
      meta: {
        perm: 'm:system:userOptionLog',
        title: '用户操作日志'
      }
    }
  ]
};
