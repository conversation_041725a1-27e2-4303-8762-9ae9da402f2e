import request from '@/utils/request'

export default {
  //分页
  list(data) {
    return request({
      url: '/activiti/flowEntryVariable/list',
      method: 'GET',
      params: data
    })
  },
  add(data) {
    return request({
      url: '/activiti/flowEntryVariable/add',
      method: 'POST',
      data
    })
  },
  update(data) {
    return request({
      url: '/activiti/flowEntryVariable/update',
      method: 'POST',
      data
    })
  },
  view(data) {
    return request({
      url: '/activiti/flowEntryVariable/view',
      method: 'GET',
      params: data
    })
  },
  delete(data) {
    return request({
      url: '/activiti/flowEntryVariable/delete',
      method: 'POST',
      params: data
    })
  },
}
