<!--阅读超人 - 学情报告组件-->
<template>
  <div>
    <el-card class="process-card">
      <div slot="header" class="card-header">
        <div class="card-title">
          <i class="el-icon-s-grid" style="color: #bbbbbb"></i>
          <span style="margin-left: 6px">学情报告</span>
        </div>
        <el-button type="text" class="delete-button" icon="el-icon-delete" @click="$emit('delete')"></el-button>
      </div>
      <el-form ref="answerQuestionForm" :model="formData" label-width="100px" :rules="rules" class="process-form-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="排序:" prop="sortsNum">
              <el-input-number v-model="formData.sortsNum" :min="1" :max="9999999999" :disabled="true"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="9">
            <el-form-item label="阶段时长:" prop="stageDuration">
              <el-select v-model="formData.stageDuration" placeholder="请选择阶段时长">
                <el-option v-for="i in 60" :key="i" :label="i" :value="i"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="阶段名称:" prop="stageName">
              <el-input v-model="formData.stageName" placeholder="请输入阶段名称" maxlength="10" show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>
<script>
export default {
  name: 'superReaderLearningReport',
  props: {
    formData: {
      type: Object,
      required: true,
      default: () => ({
        sortsNum: 9999,
        stageName: '学情报告',
        stageDuration: 1
      })
    }
  },
  data() {
    return {
      rules: {
        sortsNum: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ],
        stageDuration: [
          { required: true, message: '请选择阶段时长', trigger: 'change' }
        ],
        stageName: [
          { required: true, message: '请输入阶段名称', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    formData: {
      handler(newVal) {
        this.$emit('update', newVal);
      },
      deep: true
    }
  },
  methods: {
    // 提供验证方法供父组件调用
    validate() {
      return this.$refs.answerQuestionForm.validate();
    }
  }
}
</script>

<style scoped>
  .process-card {
    margin-top: 10px;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .card-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }

  .delete-button {
    padding: 0;
    font-size: 20px;
    color: #2a2a3b;
  }

  .process-form-content {
    padding: 0 20px 0 0;
  }

</style>
