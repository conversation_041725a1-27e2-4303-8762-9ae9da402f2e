export default {
  inserted(el, binding) {
    const styles = binding.value;

    // 应用强制样式
    Object.entries(styles).forEach(([prop, val]) => {
      el.style.setProperty(prop, val, 'important');
    });

    // 创建样式守卫
    const originalStyle = el.style.cssText;
    const observer = new MutationObserver((mutations) => {
      if (el.style.cssText !== originalStyle) {
        el.style.cssText = originalStyle;
        console.warn('样式修改已被重置');
      }
    });

    observer.observe(el, {
      attributes: true,
      attributeFilter: ['style']
    });
  }
};
