import { shallowMount, mount } from '@vue/test-utils';
import EditOperationsCodeDialog from '@/views/merchantManagement/components/EditOperationsCodeDialog.vue';

// Mock API modules
jest.mock('@/api/schoolList', () => ({
  getMerchantNameApi: jest.fn()
}));

jest.mock('@/api/operationsList', () => ({
  checkNewChanel: jest.fn(),
  confirmChangeChannel: jest.fn()
}));

import schoolApi from '@/api/schoolList';
import dealerListApi from '@/api/operationsList';

// 创建mock的Element UI组件
const mockElementComponents = {
  'el-dialog': {
    template: '<div class="el-dialog"><slot></slot></div>',
    props: ['title', 'visible', 'width', 'center', 'closeOnPressEscape', 'closeOnClickModal'],
    model: {
      prop: 'visible',
      event: 'update:visible'
    }
  },
  'el-form': {
    template: '<div class="el-form"><slot></slot></div>',
    props: ['model', 'rules', 'labelWidth'],
    methods: {
      validate: jest.fn(),
      resetFields: jest.fn()
    }
  },
  'el-form-item': {
    template: '<div class="el-form-item"><slot></slot></div>',
    props: ['label', 'prop']
  },
  'el-input': {
    template: '<input class="el-input" :value="value" @input="$emit(\'input\', $event.target.value)" @blur="$emit(\'blur\')" />',
    props: ['value', 'disabled', 'maxlength', 'placeholder', 'showWordLimit'],
    model: {
      prop: 'value',
      event: 'input'
    }
  },
  'el-button': {
    template: '<button class="el-button" :disabled="loading"><slot></slot></button>',
    props: ['type', 'loading']
  }
};

describe('EditOperationsCodeDialog.vue', () => {
  let wrapper;

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Mock console methods to avoid noise in test output
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
    // Restore console methods
    console.log.mockRestore();
    console.error.mockRestore();
  });

  // 基础渲染测试
  describe('基础渲染', () => {
    it('应该使用默认props正确渲染', () => {
      wrapper = shallowMount(EditOperationsCodeDialog, {
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.el-dialog').exists()).toBe(true);
      expect(wrapper.props().dialogVisible).toBe(false);
    });

    it('当dialogVisible为true时应该显示对话框', () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });

      expect(wrapper.vm.isShowDialog).toBe(true);
    });

    it('应该渲染所有表单字段', () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });

      const formItems = wrapper.findAll('.el-form-item');
      expect(formItems.length).toBeGreaterThan(0);
    });
  });

  // 表单验证测试
  describe('表单验证', () => {
    beforeEach(() => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });
    });

    it('应该有正确的表单验证规则', () => {
      const rules = wrapper.vm.rules;
      expect(rules.newChannelCode).toBeDefined();
      expect(rules.newChannelCode).toHaveLength(2);

      // 测试必填验证
      const requiredRule = rules.newChannelCode[0];
      expect(requiredRule.required).toBe(true);
      expect(requiredRule.message).toBe('新渠道合作伙伴编号未输入');

      // 测试数字格式验证
      const patternRule = rules.newChannelCode[1];
      expect(patternRule.pattern).toEqual(/^\d{1,30}$/);
      expect(patternRule.message).toBe('新渠道合作伙伴编号必须是数字');
    });

    it('应该验证newChannelCode为必填项', () => {
      const rules = wrapper.vm.rules;
      const requiredRule = rules.newChannelCode[0];

      expect(requiredRule.required).toBe(true);
      expect(requiredRule.trigger).toContain('blur');
      expect(requiredRule.trigger).toContain('change');
    });
  });

  // API调用测试
  describe('API调用', () => {
    beforeEach(() => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });

      // Mock $message methods
      wrapper.vm.$message = {
        warning: jest.fn(),
        error: jest.fn(),
        success: jest.fn()
      };
    });

    describe('handleNewRefereeCodeBlur', () => {
      it('当newChannelCode为空时应该清空newRefereeName', async () => {
        wrapper.vm.form.newChannelCode = '';
        wrapper.vm.form.newRefereeName = 'test name';

        await wrapper.vm.handleNewRefereeCodeBlur();

        expect(wrapper.vm.form.newRefereeName).toBe('');
      });

      it('应该成功获取商户名称', async () => {
        const mockResponse = {
          code: 20000,
          data: 'Test Merchant Name'
        };
        schoolApi.getMerchantNameApi.mockResolvedValue(mockResponse);

        wrapper.vm.form.newChannelCode = '12345';

        await wrapper.vm.handleNewRefereeCodeBlur();

        expect(schoolApi.getMerchantNameApi).toHaveBeenCalledWith({
          merchantCode: '12345'
        });
        expect(wrapper.vm.form.newRefereeName).toBe('Test Merchant Name');
      });

      it('应该优雅地处理API错误', async () => {
        const mockError = new Error('API Error');
        schoolApi.getMerchantNameApi.mockRejectedValue(mockError);

        wrapper.vm.form.newChannelCode = '12345';

        await wrapper.vm.handleNewRefereeCodeBlur();

        expect(wrapper.vm.$message.error).toHaveBeenCalledWith('获取渠道合作伙伴名称失败');
        expect(wrapper.vm.form.newRefereeName).toBe('');
      });

      it('应该处理非20000响应码', async () => {
        const mockResponse = {
          code: 40000,
          message: 'Invalid merchant code'
        };
        schoolApi.getMerchantNameApi.mockResolvedValue(mockResponse);

        wrapper.vm.form.newChannelCode = '12345';

        await wrapper.vm.handleNewRefereeCodeBlur();

        expect(wrapper.vm.$message.warning).toHaveBeenCalledWith('Invalid merchant code');
        expect(wrapper.vm.form.newRefereeName).toBe('');
      });
    });

    describe('handleConfirm', () => {
      it('加载时应该防止重复提交', async () => {
        wrapper.vm.loading = true;

        await wrapper.vm.handleConfirm();

        expect(dealerListApi.checkNewChanel).not.toHaveBeenCalled();
      });

      it('应该在API调用前验证表单', async () => {
        const mockValidate = jest.fn().mockRejectedValue(new Error('Validation failed'));
        const mockResetFields = jest.fn();
        wrapper.vm.$refs.channerManagerRef = {
          validate: mockValidate,
          resetFields: mockResetFields
        };

        await wrapper.vm.handleConfirm();

        expect(mockValidate).toHaveBeenCalled();
        expect(dealerListApi.checkNewChanel).not.toHaveBeenCalled();
      });

      it('当canChange为false时应该处理成功的API调用', async () => {
        const mockValidate = jest.fn().mockResolvedValue(true);
        const mockResetFields = jest.fn();
        wrapper.vm.$refs.channerManagerRef = {
          validate: mockValidate,
          resetFields: mockResetFields
        };

        const mockResponse = {
          data: {
            canChange: false,
            reasons: ['Reason 1', 'Reason 2']
          }
        };
        dealerListApi.checkNewChanel.mockResolvedValue(mockResponse);

        wrapper.vm.form.merchantCode = 'MERCHANT123';
        wrapper.vm.form.newChannelCode = '12345';

        await wrapper.vm.handleConfirm();

        expect(dealerListApi.checkNewChanel).toHaveBeenCalledWith({
          merchantCode: 'MERCHANT123',
          newChannelCode: '12345'
        });
        expect(wrapper.vm.dialogReasonVisible).toBe(true);
        expect(wrapper.vm.reasonContent).toEqual(['Reason 1', 'Reason 2']);
        expect(wrapper.vm.workStep).toBe(2);
      });

      it('当canChange为true时应该处理成功的API调用', async () => {
        const mockValidate = jest.fn().mockResolvedValue(true);
        const mockResetFields = jest.fn();
        wrapper.vm.$refs.channerManagerRef = {
          validate: mockValidate,
          resetFields: mockResetFields
        };

        const mockResponse = {
          data: {
            canChange: true
          }
        };
        dealerListApi.checkNewChanel.mockResolvedValue(mockResponse);

        await wrapper.vm.handleConfirm();

        expect(wrapper.vm.dialogReasonVisible).toBe(true);
        expect(wrapper.vm.workStep).toBe(4);
        expect(wrapper.vm.reasonType).toBe(4);
      });
    });

    describe('handleSubmit', () => {
      it('加载时应该防止重复提交', async () => {
        wrapper.vm.loading = true;

        await wrapper.vm.handleSubmit();

        expect(dealerListApi.confirmChangeChannel).not.toHaveBeenCalled();
      });

      it('应该成功提交', async () => {
        const mockValidate = jest.fn().mockResolvedValue(true);
        const mockResetFields = jest.fn();
        wrapper.vm.$refs.channerManagerRef = {
          validate: mockValidate,
          resetFields: mockResetFields
        };

        const mockResponse = {
          code: 20000
        };
        dealerListApi.confirmChangeChannel.mockResolvedValue(mockResponse);

        wrapper.vm.form.merchantCode = 'MERCHANT123';
        wrapper.vm.form.newChannelCode = '12345';

        await wrapper.vm.handleSubmit();

        expect(dealerListApi.confirmChangeChannel).toHaveBeenCalledWith({
          merchantCode: 'MERCHANT123',
          newChannelCode: '12345'
        });
        expect(wrapper.vm.$message.success).toHaveBeenCalledWith('操作成功');
      });

      it('应该处理API错误响应', async () => {
        const mockValidate = jest.fn().mockResolvedValue(true);
        const mockResetFields = jest.fn();
        wrapper.vm.$refs.channerManagerRef = {
          validate: mockValidate,
          resetFields: mockResetFields
        };

        // 设置有效的表单数据，以通过参数验证
        wrapper.vm.form.merchantCode = 'MERCHANT123';
        wrapper.vm.form.newChannelCode = '12345';

        const mockResponse = {
          code: 40000,
          message: 'Submit failed'
        };
        dealerListApi.confirmChangeChannel.mockResolvedValue(mockResponse);

        await wrapper.vm.handleSubmit();

        expect(wrapper.vm.$message.error).toHaveBeenCalledWith('Submit failed');
      });

      it('应该在参数不完整时显示错误提示', async () => {
        const mockValidate = jest.fn().mockResolvedValue(true);
        const mockResetFields = jest.fn();
        wrapper.vm.$refs.channerManagerRef = {
          validate: mockValidate,
          resetFields: mockResetFields
        };

        // 不设置表单数据，保持为空
        wrapper.vm.form.merchantCode = '';
        wrapper.vm.form.newChannelCode = '';

        await wrapper.vm.handleSubmit();

        // 验证参数验证错误提示
        expect(wrapper.vm.$message.error).toHaveBeenCalledWith('缺少必要参数，请重新选择商户和填写渠道编号');

        // 验证API不会被调用
        expect(dealerListApi.confirmChangeChannel).not.toHaveBeenCalled();
      });
    });
  });

  // 状态管理测试
  describe('状态管理', () => {
    beforeEach(() => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });
    });

    it('应该正确重置所有状态', () => {
      // 设置一些初始状态
      wrapper.vm.loading = true;
      wrapper.vm.dialogReasonVisible = true;
      wrapper.vm.reasonContent = ['test reason'];
      wrapper.vm.workStep = 2;
      wrapper.vm.form.merchantName = 'Test Merchant';

      wrapper.vm.resetAllStates();

      expect(wrapper.vm.loading).toBe(false);
      expect(wrapper.vm.dialogReasonVisible).toBe(false);
      expect(wrapper.vm.reasonContent).toEqual([]);
      expect(wrapper.vm.workStep).toBe(1);
      expect(wrapper.vm.form.merchantName).toBe('');
    });

    it('应该正确设置表单数据', () => {
      const testData = {
        merchantName: 'Test Merchant',
        operationsName: 'Test Operations',
        merchantCode: 'TEST123',
        newChannelCode: '12345'
      };

      wrapper.vm.setData(testData);

      expect(wrapper.vm.form.merchantName).toBe('Test Merchant');
      expect(wrapper.vm.form.operationsName).toBe('Test Operations');
      expect(wrapper.vm.form.merchantCode).toBe('TEST123');
      expect(wrapper.vm.form.newChannelCode).toBe('12345');
    });

    it('应该只设置表单中存在的属性', () => {
      const testData = {
        merchantName: 'Test Merchant',
        nonExistentProperty: 'Should not be set'
      };

      wrapper.vm.setData(testData);

      expect(wrapper.vm.form.merchantName).toBe('Test Merchant');
      expect(wrapper.vm.form.nonExistentProperty).toBeUndefined();
    });
  });

  // 事件处理测试
  describe('事件处理', () => {
    beforeEach(() => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });
    });

    it('关闭时应该触发handleCancel事件', () => {
      wrapper.vm.handleOuterClose();

      expect(wrapper.emitted('handleCancel')).toBeTruthy();
    });

    it('应该正确处理关闭对话框', () => {
      wrapper.vm.dialogReasonVisible = true;

      wrapper.vm.handleOuterClose('closeDialog');

      expect(wrapper.vm.dialogReasonVisible).toBe(false);
      expect(wrapper.emitted('handleCancel')).toBeFalsy();
    });

    it('成功提交时应该触发handleCancel事件并传入true', async () => {
      wrapper.vm.resetAllStates = jest.fn();

      wrapper.vm.handleOuterClose(undefined);

      expect(wrapper.vm.resetAllStates).toHaveBeenCalled();
      expect(wrapper.emitted('handleCancel')).toBeTruthy();
    });
  });

  // computed属性测试
  describe('computed属性', () => {
    it('应该将isShowDialog与dialogVisible属性同步', () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });

      expect(wrapper.vm.isShowDialog).toBe(true);
    });

    it('设置isShowDialog时应该触发update事件', () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: false
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });

      wrapper.vm.isShowDialog = true;

      expect(wrapper.emitted('update:isShowCityDialog')).toBeTruthy();
      expect(wrapper.emitted('update:isShowCityDialog')[0]).toEqual([true]);
    });
  });

  // 子组件交互测试
  describe('子组件交互', () => {
    it('当dialogReasonVisible为true时应该渲染InabilityReason组件', async () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: {
            template: '<div class="inability-reason"></div>',
            props: ['dialogReasonVisible', 'workStep', 'showTitleStatus', 'reasonType', 'reasonContent']
          }
        }
      });

      wrapper.vm.dialogReasonVisible = true;
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.inability-reason').exists()).toBe(true);
    });

    it('应该向InabilityReason传递正确的props', () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: {
            template: '<div class="inability-reason"></div>',
            props: ['dialogReasonVisible', 'workStep', 'showTitleStatus', 'reasonType', 'reasonContent']
          }
        }
      });

      wrapper.vm.dialogReasonVisible = true;
      wrapper.vm.workStep = 2;
      wrapper.vm.reasonType = 1;
      wrapper.vm.reasonContent = ['test reason'];

      // 由于组件被模拟，我们只能检查数据是否设置正确
      expect(wrapper.vm.dialogReasonVisible).toBe(true);
      expect(wrapper.vm.workStep).toBe(2);
      expect(wrapper.vm.reasonType).toBe(1);
      expect(wrapper.vm.reasonContent).toEqual(['test reason']);
    });

    it('应该向InabilityReason组件传递showTitleStatus为4', async () => {
      // 使用真实的InabilityReason组件来测试props传递
      const InabilityReasonComponent = require('@/views/merchantManagement/components/InabilityReason.vue').default;

      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents
        },
        components: {
          InabilityReason: InabilityReasonComponent
        }
      });

      wrapper.vm.dialogReasonVisible = true;
      wrapper.vm.workStep = 2;
      await wrapper.vm.$nextTick();

      // 简化测试：验证父组件状态设置正确
      expect(wrapper.vm.dialogReasonVisible).toBe(true);
      expect(wrapper.vm.workStep).toBe(2);

      // 注：在真实的EditOperationsCodeDialog组件中，showTitleStatus传递的是字符串"4"
      // 这对应InabilityReason组件中的reasonTitle: '当前俱乐部无法变更渠道合作伙伴'
    });

    it('当workStep为2时应该正确显示InabilityReason组件', async () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: {
            template: '<div class="inability-reason workstep-2" data-workstep="2"></div>',
            props: ['dialogReasonVisible', 'workStep', 'showTitleStatus', 'reasonType', 'reasonContent']
          }
        }
      });

      // 模拟canChange为false的场景，设置workStep为2
      wrapper.vm.dialogReasonVisible = true;
      wrapper.vm.workStep = 2;
      wrapper.vm.reasonContent = ['无法操作原因1', '无法操作原因2'];
      wrapper.vm.reasonType = 0;

      await wrapper.vm.$nextTick();

      // 验证组件被渲染
      expect(wrapper.find('.inability-reason').exists()).toBe(true);
      // 验证状态设置正确
      expect(wrapper.vm.workStep).toBe(2);
      expect(wrapper.vm.dialogReasonVisible).toBe(true);
      expect(wrapper.vm.reasonContent).toEqual(['无法操作原因1', '无法操作原因2']);
      expect(wrapper.vm.reasonType).toBe(0);
    });

    it('当workStep为4时应该正确显示InabilityReason组件', async () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: {
            template: '<div class="inability-reason workstep-4" data-workstep="4"></div>',
            props: ['dialogReasonVisible', 'workStep', 'showTitleStatus', 'reasonType', 'reasonContent']
          }
        }
      });

      // 模拟canChange为true的场景，设置workStep为4
      wrapper.vm.dialogReasonVisible = true;
      wrapper.vm.workStep = 4;
      wrapper.vm.reasonType = 4;
      wrapper.vm.reasonContent = [];

      await wrapper.vm.$nextTick();

      // 验证组件被渲染
      expect(wrapper.find('.inability-reason').exists()).toBe(true);
      // 验证状态设置正确
      expect(wrapper.vm.workStep).toBe(4);
      expect(wrapper.vm.dialogReasonVisible).toBe(true);
      expect(wrapper.vm.reasonType).toBe(4);
      expect(wrapper.vm.reasonContent).toEqual([]);
    });
  });

  // 表单重置测试
  describe('表单重置', () => {
    beforeEach(() => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });
    });

    it('应该将表单数据重置为初始状态', () => {
      // 设置一些表单数据
      wrapper.vm.form.merchantName = 'Test Merchant';
      wrapper.vm.form.operationsName = 'Test Operations';
      wrapper.vm.form.newChannelCode = '12345';

      wrapper.vm.reset();

      expect(wrapper.vm.form.merchantName).toBe('');
      expect(wrapper.vm.form.operationsName).toBe('');
      expect(wrapper.vm.form.newChannelCode).toBe('');
    });

    it('应该调用表单ref的resetFields方法', async () => {
      const mockResetFields = jest.fn();
      wrapper.vm.$refs.channerManagerRef = { resetFields: mockResetFields };

      wrapper.vm.reset();

      await wrapper.vm.$nextTick();
      expect(mockResetFields).toHaveBeenCalled();

      // 测试setTimeout回调
      await new Promise((resolve) => setTimeout(resolve, 60));
      expect(mockResetFields).toHaveBeenCalledTimes(2);
    });
  });

  // 错误处理和边界情况测试
  describe('错误处理和边界情况', () => {
    beforeEach(() => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });

      wrapper.vm.$message = {
        warning: jest.fn(),
        error: jest.fn(),
        success: jest.fn()
      };
    });

    it('当API返回无数据时应该正确处理', async () => {
      const mockResponse = {
        code: 20000,
        data: null
      };
      schoolApi.getMerchantNameApi.mockResolvedValue(mockResponse);

      wrapper.vm.form.newChannelCode = '12345';
      await wrapper.vm.handleNewRefereeCodeBlur();

      expect(wrapper.vm.form.newRefereeName).toBe('');
    });

    it('当API返回undefined数据时应该正确处理', async () => {
      const mockResponse = {
        code: 20000,
        data: undefined
      };
      schoolApi.getMerchantNameApi.mockResolvedValue(mockResponse);

      wrapper.vm.form.newChannelCode = '12345';
      await wrapper.vm.handleNewRefereeCodeBlur();

      expect(wrapper.vm.form.newRefereeName).toBe('');
    });

    it('handleConfirm中应该处理checkNewChanel API异常', async () => {
      const mockValidate = jest.fn().mockResolvedValue(true);
      const mockResetFields = jest.fn();
      wrapper.vm.$refs.channerManagerRef = {
        validate: mockValidate,
        resetFields: mockResetFields
      };

      const mockError = new Error('网络错误');
      dealerListApi.checkNewChanel.mockRejectedValue(mockError);

      wrapper.vm.form.merchantCode = 'MERCHANT123';
      wrapper.vm.form.newChannelCode = '12345';

      await wrapper.vm.handleConfirm();

      expect(wrapper.vm.loading).toBe(false);
      expect(console.error).toHaveBeenCalledWith('🚀 ~ handleConfirm ~ checkNewChanel API error:', mockError);
    });

    it('handleSubmit中应该处理confirmChangeChannel API异常', async () => {
      const mockValidate = jest.fn().mockResolvedValue(true);
      const mockResetFields = jest.fn();
      wrapper.vm.$refs.channerManagerRef = {
        validate: mockValidate,
        resetFields: mockResetFields
      };

      // 设置有效的表单数据，以通过参数验证
      wrapper.vm.form.merchantCode = 'MERCHANT123';
      wrapper.vm.form.newChannelCode = '12345';

      const mockError = new Error('网络错误');
      dealerListApi.confirmChangeChannel.mockRejectedValue(mockError);

      await wrapper.vm.handleSubmit();

      expect(wrapper.vm.loading).toBe(false);
      expect(wrapper.vm.$message.error).toHaveBeenCalledWith('网络连接失败，请检查网络后重试');
      expect(console.error).toHaveBeenCalledWith('🚀 ~ handleSubmit ~ error:', mockError);
    });

    it('handleSubmit中参数不完整时应该提早返回', async () => {
      const mockValidate = jest.fn().mockResolvedValue(true);
      const mockResetFields = jest.fn();
      wrapper.vm.$refs.channerManagerRef = {
        validate: mockValidate,
        resetFields: mockResetFields
      };

      // 不设置表单数据，保持为空，触发参数验证失败
      wrapper.vm.form.merchantCode = '';
      wrapper.vm.form.newChannelCode = '';

      await wrapper.vm.handleSubmit();

      // 验证提早返回，API没有被调用
      expect(dealerListApi.confirmChangeChannel).not.toHaveBeenCalled();
      expect(wrapper.vm.$message.error).toHaveBeenCalledWith('缺少必要参数，请重新选择商户和填写渠道编号');
    });

    it('setData方法应该忽略不存在的属性', () => {
      const testData = {
        merchantName: 'Test Merchant',
        invalidProperty: 'Should be ignored',
        anotherInvalidProp: 123
      };

      wrapper.vm.setData(testData);

      expect(wrapper.vm.form.merchantName).toBe('Test Merchant');
      expect(wrapper.vm.form.invalidProperty).toBeUndefined();
      expect(wrapper.vm.form.anotherInvalidProp).toBeUndefined();
    });
  });

  // 用户交互和UI测试
  describe('用户交互和UI测试', () => {
    beforeEach(() => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });
    });

    it('应该显示正确的对话框标题', () => {
      const dialog = wrapper.find('.el-dialog');
      expect(dialog.props('title')).toBe('修改渠道合作伙伴');
    });

    it('应该禁用取消和确定按钮外的点击关闭', () => {
      const dialog = wrapper.find('.el-dialog');
      expect(dialog.props('closeOnPressEscape')).toBe(false);
      expect(dialog.props('closeOnClickModal')).toBe(false);
    });

    it('应该显示正确的宽度', () => {
      const dialog = wrapper.find('.el-dialog');
      expect(dialog.props('width')).toBe('700px');
    });
  });

  // 数据流和状态变化测试
  describe('数据流和状态变化测试', () => {
    beforeEach(() => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });
    });

    it('初始化时应该有正确的默认状态', () => {
      expect(wrapper.vm.form).toEqual({
        merchantName: '',
        operationsName: '',
        newChannelCode: '',
        newRefereeName: '',
        merchantCode: ''
      });
      expect(wrapper.vm.loading).toBe(false);
      expect(wrapper.vm.dialogReasonVisible).toBe(false);
      expect(wrapper.vm.reasonContent).toEqual([]);
      expect(wrapper.vm.workStep).toBe(1);
      expect(wrapper.vm.reasonType).toBe(0);
    });

    it('reasonType应该正确更新', () => {
      wrapper.vm.reasonType = 4;
      expect(wrapper.vm.reasonType).toBe(4);
    });

    it('reasonContent应该正确更新为数组', () => {
      const reasons = ['原因1', '原因2', '原因3'];
      wrapper.vm.reasonContent = reasons;
      expect(wrapper.vm.reasonContent).toEqual(reasons);
    });

    it('workStep状态变化应该正确', () => {
      expect(wrapper.vm.workStep).toBe(1);

      wrapper.vm.workStep = 2;
      expect(wrapper.vm.workStep).toBe(2);

      wrapper.vm.workStep = 4;
      expect(wrapper.vm.workStep).toBe(4);
    });
  });

  // 表单验证深度测试
  describe('表单验证深度测试', () => {
    beforeEach(() => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });
    });

    it('newChannelCode数字格式验证应该正确工作', () => {
      const rule = wrapper.vm.rules.newChannelCode[1];

      // 测试有效数字
      expect(rule.pattern.test('12345')).toBe(true);
      expect(rule.pattern.test('1')).toBe(true);
      expect(rule.pattern.test('123456789012345678901234567890')).toBe(true);

      // 测试无效格式
      expect(rule.pattern.test('abc')).toBe(false);
      expect(rule.pattern.test('123abc')).toBe(false);
      expect(rule.pattern.test('')).toBe(false);
      expect(rule.pattern.test('12.34')).toBe(false);
    });

    it('表单字段应该有正确的trigger设置', () => {
      const rules = wrapper.vm.rules.newChannelCode;

      rules.forEach((rule) => {
        expect(rule.trigger).toContain('blur');
        expect(rule.trigger).toContain('change');
      });
    });
  });

  // Props和事件深度测试
  describe('Props和事件深度测试', () => {
    it('dialogVisible prop变化应该触发isShowDialog计算属性', async () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: false
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });

      expect(wrapper.vm.isShowDialog).toBe(false);

      await wrapper.setProps({ dialogVisible: true });
      expect(wrapper.vm.isShowDialog).toBe(true);
    });

    it('handleCancel事件应该正确传递参数', () => {
      wrapper = mount(EditOperationsCodeDialog, {
        propsData: {
          dialogVisible: true
        },
        stubs: {
          ...mockElementComponents,
          InabilityReason: true
        }
      });

      wrapper.vm.$emit('handleCancel', true);

      expect(wrapper.emitted('handleCancel')).toBeTruthy();
      expect(wrapper.emitted('handleCancel')[0]).toEqual([true]);
    });

    it('应该正确处理空的$refs情况', async () => {
      wrapper.vm.$refs.channerManagerRef = null;

      wrapper.vm.reset();

      expect(wrapper.vm.form.merchantName).toBe('');
      expect(wrapper.vm.form.newChannelCode).toBe('');
    });
  });
});
