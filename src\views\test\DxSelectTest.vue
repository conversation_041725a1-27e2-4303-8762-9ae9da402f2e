<template>
  <div class="dx-select-test">
    <h1>DxSelect 组件测试</h1>
    
    <div class="test-section">
      <h2>基础选择器</h2>
      <dx-select v-model="value1" placeholder="请选择">
        <dx-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </dx-option>
      </dx-select>
      <p>选中值：{{ value1 }}</p>
    </div>

    <div class="test-section">
      <h2>有禁用选项</h2>
      <dx-select v-model="value2" placeholder="请选择">
        <dx-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="item.disabled">
        </dx-option>
      </dx-select>
      <p>选中值：{{ value2 }}</p>
    </div>

    <div class="test-section">
      <h2>禁用状态</h2>
      <dx-select v-model="value3" disabled placeholder="请选择">
        <dx-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </dx-option>
      </dx-select>
    </div>

    <div class="test-section">
      <h2>可清空单选</h2>
      <dx-select v-model="value4" clearable placeholder="请选择">
        <dx-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </dx-option>
      </dx-select>
      <p>选中值：{{ value4 }}</p>
    </div>

    <div class="test-section">
      <h2>基础多选</h2>
      <dx-select v-model="value5" multiple placeholder="请选择">
        <dx-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </dx-option>
      </dx-select>
      <p>选中值：{{ value5 }}</p>
    </div>

    <div class="test-section">
      <h2>自定义模板</h2>
      <dx-select v-model="value6" placeholder="请选择">
        <dx-option
          v-for="item in cities"
          :key="item.value"
          :label="item.label"
          :value="item.value">
          <span style="float: left">{{ item.label }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
        </dx-option>
      </dx-select>
      <p>选中值：{{ value6 }}</p>
    </div>

    <div class="test-section">
      <h2>分组</h2>
      <dx-select v-model="value7" placeholder="请选择">
        <dx-option-group
          v-for="group in groupOptions"
          :key="group.label"
          :label="group.label">
          <dx-option
            v-for="item in group.options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </dx-option>
        </dx-option-group>
      </dx-select>
      <p>选中值：{{ value7 }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DxSelectTest',
  data() {
    return {
      value1: '',
      value2: '',
      value3: '',
      value4: '',
      value5: [],
      value6: '',
      value7: '',
      options: [{
        value: '选项1',
        label: '黄金糕'
      }, {
        value: '选项2',
        label: '双皮奶'
      }, {
        value: '选项3',
        label: '蚵仔煎',
        disabled: true
      }, {
        value: '选项4',
        label: '龙须面'
      }, {
        value: '选项5',
        label: '北京烤鸭'
      }],
      cities: [{
        value: 'Beijing',
        label: '北京'
      }, {
        value: 'Shanghai',
        label: '上海'
      }, {
        value: 'Nanjing',
        label: '南京'
      }, {
        value: 'Chengdu',
        label: '成都'
      }, {
        value: 'Shenzhen',
        label: '深圳'
      }, {
        value: 'Guangzhou',
        label: '广州'
      }],
      groupOptions: [{
        label: '热门城市',
        options: [{
          value: 'Shanghai',
          label: '上海'
        }, {
          value: 'Beijing',
          label: '北京'
        }]
      }, {
        label: '城市名',
        options: [{
          value: 'Chengdu',
          label: '成都'
        }, {
          value: 'Shenzhen',
          label: '深圳'
        }, {
          value: 'Guangzhou',
          label: '广州'
        }, {
          value: 'Dalian',
          label: '大连'
        }]
      }]
    }
  }
}
</script>

<style scoped>
.dx-select-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
}

.test-section h2 {
  margin-bottom: 20px;
  color: #303133;
  font-size: 18px;
}

.test-section p {
  margin-top: 10px;
  color: #606266;
}
</style>
