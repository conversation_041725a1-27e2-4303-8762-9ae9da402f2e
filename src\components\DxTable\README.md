# DxTable 自定义表格组件

这是基于 Element UI 的 el-table 和 el-table-column 组件完全复制封装的自定义表格组件。使用方式与原生 Element UI 表格组件完全一致，但可以自由修改和定制。

## 组件说明

- `DxTable`: 自定义表格组件，对应 `el-table`，使用 `<dx-table>` 标签
- `DxTableColumn`: 自定义表格列组件，对应 `el-table-column`，使用 `<dx-table-column>` 标签

## 使用方式

### 基础用法

```vue
<template>
  <div>
    <dx-table :data="tableData" style="width: 100%">
      <dx-table-column prop="date" label="日期" width="180"></dx-table-column>
      <dx-table-column prop="name" label="姓名" width="180"></dx-table-column>
      <dx-table-column prop="address" label="地址"></dx-table-column>
    </dx-table>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        tableData: [
          {
            date: '2016-05-02',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1518 弄'
          },
          {
            date: '2016-05-04',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1517 弄'
          },
          {
            date: '2016-05-01',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1519 弄'
          },
          {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄'
          }
        ]
      };
    }
  };
</script>
```

### 带斑马纹表格

```vue
<template>
  <custom-table :data="tableData" stripe style="width: 100%">
    <custom-table-column prop="date" label="日期" width="180"></custom-table-column>
    <custom-table-column prop="name" label="姓名" width="180"></custom-table-column>
    <custom-table-column prop="address" label="地址"></custom-table-column>
  </custom-table>
</template>
```

### 带边框表格

```vue
<template>
  <custom-table :data="tableData" border style="width: 100%">
    <custom-table-column prop="date" label="日期" width="180"></custom-table-column>
    <custom-table-column prop="name" label="姓名" width="180"></custom-table-column>
    <custom-table-column prop="address" label="地址"></custom-table-column>
  </custom-table>
</template>
```

### 带状态表格

```vue
<template>
  <custom-table :data="tableData" style="width: 100%">
    <custom-table-column prop="date" label="日期" width="180"></custom-table-column>
    <custom-table-column prop="name" label="姓名" width="180"></custom-table-column>
    <custom-table-column prop="address" label="地址"></custom-table-column>
    <custom-table-column label="操作">
      <template slot-scope="scope">
        <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>
        <el-button type="text" size="small">编辑</el-button>
      </template>
    </custom-table-column>
  </custom-table>
</template>
```

### 固定表头

```vue
<template>
  <custom-table :data="tableData" height="250" style="width: 100%">
    <custom-table-column prop="date" label="日期" width="180"></custom-table-column>
    <custom-table-column prop="name" label="姓名" width="180"></custom-table-column>
    <custom-table-column prop="address" label="地址"></custom-table-column>
  </custom-table>
</template>
```

### 固定列

```vue
<template>
  <custom-table :data="tableData" style="width: 100%">
    <custom-table-column fixed prop="date" label="日期" width="150"></custom-table-column>
    <custom-table-column prop="name" label="姓名" width="120"></custom-table-column>
    <custom-table-column prop="province" label="省份" width="120"></custom-table-column>
    <custom-table-column prop="city" label="市区" width="120"></custom-table-column>
    <custom-table-column prop="address" label="地址" width="300"></custom-table-column>
    <custom-table-column prop="zip" label="邮编" width="120"></custom-table-column>
    <custom-table-column fixed="right" label="操作" width="100">
      <template slot-scope="scope">
        <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>
        <el-button type="text" size="small">编辑</el-button>
      </template>
    </custom-table-column>
  </custom-table>
</template>
```

## 属性说明

### CustomTable 属性

所有属性与 `el-table` 完全一致，包括但不限于：

- `data`: 显示的数据
- `height`: Table 的高度
- `max-height`: Table 的最大高度
- `stripe`: 是否为斑马纹 table
- `border`: 是否带有纵向边框
- `size`: Table 的尺寸
- `fit`: 列的宽度是否自撑开
- `show-header`: 是否显示表头
- `highlight-current-row`: 是否要高亮当前行
- `current-row-key`: 当前行的 key
- `row-class-name`: 行的 className 的回调方法
- `row-style`: 行的 style 的回调方法
- `cell-class-name`: 单元格的 className 的回调方法
- `cell-style`: 单元格的 style 的回调方法
- `header-row-class-name`: 表头行的 className 的回调方法
- `header-row-style`: 表头行的 style 的回调方法
- `header-cell-class-name`: 表头单元格的 className 的回调方法
- `header-cell-style`: 表头单元格的 style 的回调方法
- `row-key`: 行数据的 Key
- `empty-text`: 空数据时显示的文本内容
- `default-expand-all`: 是否默认展开所有行
- `expand-row-keys`: 可以通过该属性设置 Table 目前的展开行
- `default-sort`: 默认的排序列的 prop 和顺序
- `tooltip-effect`: tooltip effect 属性
- `show-summary`: 是否在表尾显示合计行
- `sum-text`: 合计行第一列的文本
- `summary-method`: 自定义的合计计算方法
- `span-method`: 合并行或列的计算方法
- `select-on-indeterminate`: 在多选表格中，当仅有部分行被选中时，点击表头的多选框时的行为
- `indent`: 展示树形数据时，树节点的缩进
- `lazy`: 是否懒加载子节点数据
- `load`: 加载子节点数据的函数
- `tree-props`: 渲染嵌套数据的配置选项

### CustomTableColumn 属性

所有属性与 `el-table-column` 完全一致，包括但不限于：

- `type`: 对应列的类型
- `index`: 如果设置了 type=index，可以通过传递 index 属性来自定义索引
- `column-key`: column 的 key
- `label`: 显示的标题
- `prop`: 对应列内容的字段名
- `width`: 对应列的宽度
- `min-width`: 对应列的最小宽度
- `fixed`: 列是否固定在左侧或者右侧
- `render-header`: 列标题 Label 区域渲染使用的 Function
- `sortable`: 对应列是否可以排序
- `sort-method`: 对数据进行排序的时候使用的方法
- `sort-by`: 指定数据按照哪个属性进行排序
- `sort-orders`: 数据在排序时所使用排序策略的轮转顺序
- `resizable`: 对应列是否可以通过拖动改变宽度
- `formatter`: 用来格式化内容
- `show-overflow-tooltip`: 当内容过长被隐藏时显示 tooltip
- `align`: 对齐方式
- `header-align`: 表头对齐方式
- `class-name`: 列的 className
- `label-class-name`: 当前列标题的自定义类名
- `selectable`: 仅对 type=selection 的列有效，类型为 Function
- `reserve-selection`: 仅对 type=selection 的列有效，类型为 Boolean
- `filters`: 数据过滤的选项
- `filter-placement`: 过滤弹出框的定位
- `filter-multiple`: 数据过滤的选项是否多选
- `filter-method`: 数据过滤使用的方法
- `filtered-value`: 选中的数据过滤项

## 事件说明

### CustomTable 事件

所有事件与 `el-table` 完全一致，包括但不限于：

- `select`: 当用户手动勾选数据行的 Checkbox 时触发的事件
- `select-all`: 当用户手动勾选全选 Checkbox 时触发的事件
- `selection-change`: 当选择项发生变化时会触发该事件
- `cell-mouse-enter`: 当单元格 hover 进入时会触发该事件
- `cell-mouse-leave`: 当单元格 hover 退出时会触发该事件
- `cell-click`: 当某个单元格被点击时会触发该事件
- `cell-dblclick`: 当某个单元格被双击击时会触发该事件
- `row-click`: 当某一行被点击时会触发该事件
- `row-contextmenu`: 当某一行被鼠标右键点击时会触发该事件
- `row-dblclick`: 当某一行被双击时会触发该事件
- `header-click`: 当某一列的表头被点击时会触发该事件
- `header-contextmenu`: 当某一列的表头被鼠标右键点击时触发该事件
- `sort-change`: 当表格的排序条件发生变化的时候会触发该事件
- `filter-change`: 当表格的筛选条件发生变化的时候会触发该事件
- `current-change`: 当表格的当前行发生变化的时候会触发该事件
- `header-dragend`: 当拖动表头改变了列的宽度的时候会触发该事件
- `expand-change`: 当用户对某一行展开或者关闭的时候会触发该事件

## 方法说明

### CustomTable 方法

所有方法与 `el-table` 完全一致，包括但不限于：

- `clearSelection()`: 用于多选表格，清空用户的选择
- `toggleRowSelection(row, selected)`: 用于多选表格，切换某一行的选中状态
- `toggleAllSelection()`: 用于多选表格，切换全选和全不选
- `toggleRowExpansion(row, expanded)`: 用于可展开表格与树形表格，切换某一行的展开状态
- `setCurrentRow(row)`: 用于单选表格，设定某一行为选中行
- `clearSort()`: 用于清空排序条件，数据会恢复成未排序的状态
- `clearFilter(columnKey)`: 不传入参数时用于清空所有过滤条件，数据会恢复成未过滤的状态
- `doLayout()`: 对 Table 进行重新布局
- `sort(prop, order)`: 手动对 Table 进行排序

## 注意事项

1. 使用方式与原生 Element UI 表格组件完全一致
2. 所有属性、事件、方法都与原生组件保持一致
3. 可以在 `src/components/CustomTable/src/` 目录下修改源码来定制功能
4. 修改后的代码会影响整个项目中使用该组件的地方
5. 建议在修改前先备份原始代码

## 自定义修改

如果需要修改表格的默认行为或样式，可以直接编辑以下文件：

- `src/components/CustomTable/CustomTable.vue` - 主表格组件
- `src/components/CustomTable/CustomTableColumn.vue` - 表格列组件
- `src/components/CustomTable/src/` - 相关的工具类和辅助组件

修改后，所有使用 `custom-table` 和 `custom-table-column` 的地方都会应用新的修改。
