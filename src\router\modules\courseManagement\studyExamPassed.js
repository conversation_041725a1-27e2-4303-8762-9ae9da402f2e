// 考试超人(小四门)-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/studyExamPassed',
  component: Layout,
  redirect: '/studyExamPassed',
  meta: {
    perm: 'm:studyExamPassed',
    title: '考试超人(小四门)',
    icon: 'exam'
  },
  children: [
    {
      path: 'course',
      component: () => import('@/views/studyExamPassed/course/index'),
      name: 'course',
      meta: {
        perm: 'm:studyExamPassed:course',
        title: '课程管理'
      }
    },
    {
      path: 'videoConfig',
      component: () => import('@/views/studyExamPassed/videoConfig/index'),
      name: 'videoConfig',
      meta: { perm: 'm:studyExamPassed:videoConfig', title: '视频管理' }
    },
    {
      path: 'courseVideoConfig',
      component: () => import('@/views/studyExamPassed/course/CourseVideoConfig'),
      name: 'courseVideoConfig',
      hidden: true,
      meta: { perm: 'm:studyExamPassed:courseVideoConfig', title: '课程视频管理' }
    },
    {
      path: 'withdrawVideoConfig',
      component: () => import('@/views/studyExamPassed/widthdraCourseVideoConfig/index'),
      name: 'withdrawVideoConfig',
      meta: { perm: 'm:studyExamPassed:withdrawVideoConfig', title: '退课视频管理' }
    }
  ]
};
