<template>
  <div>
    <CustomDialog v-if="dialogVisible" title="开通确认单" :center="true" :value.sync="dialogVisible" width="500px" @close="handleOuterClose">
      <el-form ref="confirmFormRef" :model="form" label-width="120px">
        <el-form-item label="非标" prop="isStandardRegistration">
          <template #label>
            <div>
              非标
              <el-tooltip effect="dark" placement="bottom-start">
                <i class="el-icon-question" />
                <div slot="content" style="line-height: 20px">
                  有非标标签的渠道商，总
                  <br />
                  部不用向城市服务中心结
                  <br />
                  算服务费
                </div>
              </el-tooltip>
            </div>
          </template>
          <el-radio-group v-model="form.isStandardRegistration">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="俱乐部编号" prop="merchantCode">
          <el-input disabled v-model="form.merchantCode"></el-input>
        </el-form-item>
        <el-form-item label="俱乐部名称" prop="merchantName">
          <el-input disabled v-model="form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="realName">
          <el-input disabled type="person" v-model="form.realName"></el-input>
        </el-form-item>
        <el-form-item label="分配合伙人智能学习管理系统数">
          <el-slider v-model="form.sysNum" :max="maxNumber" show-input v-if="dialogVisible"></el-slider>
          分配最大数量不可超过自身剩余数量：
          <span style="color: red">{{ quantityNumber }}</span>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button :loading="loading" type="primary" @click="handleConfirm">确认开通</el-button>
        <el-button @click="handleOuterClose">取 消</el-button>
      </template>
    </CustomDialog>
  </div>
</template>

<script>
  import dealerPaylist from '@/api/operationsPayment';
  import CustomDialog from '@/components/customDialog';
  export default {
    name: 'confirmForm',
    props: {
      isShowClub: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          isStandardRegistration: 1,
          merchantCode: '',
          realName: '',
          merchantName: '',
          sysNum: 0
        },
        maxNumber: 999999,
        quantityNumber: 999999,
        loading: false
      };
    },
    components: {
      CustomDialog
    },
    computed: {
      dialogVisible: {
        get() {
          return this.isShowClub;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },
    methods: {
      handleOuterClose() {
        this.$emit('closeConfirmDialog');
        this.reset();
      },
      async handleConfirm() {
        if (this.loading) return;
        if (![0, 1].includes(this.form.isStandardRegistration)) {
          this.$message.error('请选择是否为非标');
          return;
        }
        this.loading = true;
        try {
          const res = await dealerPaylist.openOperationApi(this.form);
          if (res) {
            this.$message.success('操作成功');
            this.loading = false;
            this.reset();
            this.$emit('closeConfirmDialog', true);
          }
        } catch (error) {
          this.$message.error('操作失败');
          this.loading = false;
        }
      },

      reset() {
        this.form = {
          isStandardRegistration: 1,
          merchantCode: '',
          realName: '',
          merchantName: '',
          sysNum: 0
        };
      },

      /**
       *接收传值
       * @param data
       */
      setData(data) {
        console.log('🚀🥶💩~ data', data);
        this.form.merchantCode = data.merchantCode;
        this.form.merchantName = data.merchantName;
        this.form.realName = data.realName;
        this.form.sysNum = data.sysNum || 0;
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    text-align: right;
  }
</style>
