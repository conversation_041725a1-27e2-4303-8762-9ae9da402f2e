<script>
  import CustomDialog from '@/components/customDialog/index.vue';
  export default {
    name: 'InabilityReason',
    props: {
      dialogReasonVisible: {
        type: Boolean,
        default: false
      },
      reasonContent: {
        type: Array,
        default: () => []
      },
      workStep: {
        type: Number,
        default: 1
      },
      showTitleStatus: {
        type: Number,
        default: 1
      },
      isSubmitLoading: {
        type: Boolean,
        default: false
      },
      reasonType: {
        type: Number,
        default: 1
      }
    },
    data() {
      return {
        form: {},
        rules: {},
        reasonTitleMap: {
          1: '当前门店无法变更所属俱乐部',
          2: '当前门店无法变更推广大使',
          3: '当前俱乐部无法变更所属品牌',
          4: '当前俱乐部无法变更渠道合作伙伴'
        },
        submitLoading: false,
        successTip: {
          1: '确认变更后，系统将自动为原所属俱乐部与新所属俱乐部、新推广大使与当前门店生成《权益转让合同》、《课程推广大使合同》，无法取消变更',
          2: '确认变更后，系统将自动为原所属品牌与新所属品牌、新渠道合作伙伴与当前俱乐部生成《权益转让合同》、《渠道合作伙伴合同》，无法取消变更',
          3: '确认变更后，系统将自动为新推广大使与该门店生成《课程推广大使合同》，无法取消变更',
          4: '确认变更后，系统将自动为新渠道合作伙伴与该俱乐部生成《渠道合作伙伴合同》'
        }
      };
    },
    watch: {
      isSubmitLoading(val) {
        console.log(`🚀🥶💩🚀~ isSubmitLoading ~ val ~ L39:接受父组件传值 `, val);
        this.submitLoading = val;
        console.log(`🚀🥶💩🚀~ 子组件submitLoading状态更新为:`, this.submitLoading);
      }
    },
    computed: {
      dialogVisible: {
        get() {
          return this.dialogReasonVisible;
        },
        set(val) {
          this.$emit('update:dialogReasonVisible', val);
        }
      },
      reasonTitle() {
        return this.reasonTitleMap[this.showTitleStatus];
      }
    },
    components: {
      CustomDialog
    },

    methods: {
      handleConfirm() {
        this.dialogVisible = false;
        this.$emit('handleCloseDialog', 'closeDialog');
      },
      handleSubmit() {
        if (this.submitLoading) return;
        this.submitLoading = true;
        this.$emit('handleSubmit');
      }
    }
  };
</script>

<template>
  <div v-if="workStep === 1 || workStep === 2 || workStep === 3">
    <CustomDialog
      v-if="dialogVisible"
      :value.sync="dialogVisible"
      width="45%"
      :title="reasonTitle"
      center
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleConfirm"
    >
      <el-form :model="form" ref="channerManagerRef" :rules="rules" label-width="120px" style="font-size: 19px">
        <div style="color: red; margin-bottom: 10px" v-if="workStep === 1 || workStep === 3">原因：</div>
        <div v-for="item in reasonContent" :key="item" style="line-height: 35px; font-weight: 500; color: #000; text-align: center">{{ item }}</div>
      </el-form>
      <template slot="footer">
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </template>
    </CustomDialog>
  </div>
  <div v-else>
    <CustomDialog
      v-if="dialogVisible"
      :value.sync="dialogVisible"
      width="25%"
      center
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="
        () => {
          this.dialogVisible = false;
        }
      "
    >
      <div style="font-weight: 500; color: #000">{{ successTip[reasonType] }}</div>
      <div style="color: red; margin-top: 40px; text-align: center">请在7天内完成全部合同签署，若超出时间，则变更无效</div>
      <template slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">确 定</el-button>
      </template>
    </CustomDialog>
  </div>
</template>
<style scoped>
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px 0;
    border-top: 1px solid #e8e8e8;
  }

  /* ::v-deep .el-dialog__title {
    font-size: 24px;
  } */
</style>
