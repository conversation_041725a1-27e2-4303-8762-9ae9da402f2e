/* DxSelect 样式 - 现代化设计 */

.dx-select {
  display: inline-block;
  position: relative;
  width: 200px; /* 默认宽度 */
  min-width: 120px; /* 最小宽度 */
}

/* 下拉动画 */
.el-zoom-in-top-enter-active,
.el-zoom-in-top-leave-active {
  opacity: 1;
  transform: scaleY(1);
  transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center top;
}

.el-zoom-in-top-enter,
.el-zoom-in-top-leave-active {
  opacity: 0;
  transform: scaleY(0);
}

.dx-select .el-input__inner {
  cursor: pointer;
  padding-right: 35px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  color: #666;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  transition: all 0.3s ease;
}

.dx-select .el-input__inner:focus {
  border-color: #409eff;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.dx-select .el-input.is-disabled .el-input__inner {
  cursor: not-allowed;
  background-color: #f5f5f5;
  color: #c0c4cc;
}

.dx-select .el-input.is-disabled .el-input__inner:hover {
  border-color: #e4e7ed;
}

.dx-select .el-input.is-focus .el-input__inner {
  border-color: #409eff;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.dx-select > .el-input {
  display: block;
}

.dx-select:hover .el-input__inner {
  border-color: #b0b0b0;
  background-color: #fff;
}

.dx-select .el-input__icon {
  color: #c0c4cc;
  font-size: 14px;
  transition: transform 0.3s;
  transform: translateY(-50%);
  cursor: pointer;
}

.dx-select .el-input__icon.is-reverse {
  transform: translateY(-50%) rotateZ(180deg);
}

.dx-select .el-input__icon.is-show-close {
  font-size: 14px;
  text-align: center;
  transform: translateY(-50%);
  border-radius: 100%;
  color: #c0c4cc;
  transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.dx-select .el-input__icon.is-show-close:hover {
  color: #909399;
}

.dx-select__caret {
  color: #999;
  font-size: 16px;
  transition: all 0.3s ease;
  transform: translateY(-50%);
  cursor: pointer;
  position: absolute;
  top: 50%;
  right: 12px;
  line-height: 1;
}

.dx-select__caret.is-reverse {
  transform: translateY(-50%) rotateZ(180deg);
  color: #409eff;
}

.dx-select__caret.is-show-close {
  font-size: 14px;
  text-align: center;
  transform: translateY(-50%);
  border-radius: 100%;
  color: #c0c4cc;
  transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.dx-select__caret.is-show-close:hover {
  color: #909399;
}

.dx-select__tags {
  position: absolute;
  line-height: normal;
  white-space: normal;
  z-index: 1;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.dx-select .dx-select__tags-text {
  overflow: hidden;
  text-overflow: ellipsis;
}

.dx-select__input {
  border: none;
  outline: none;
  padding: 0;
  margin-left: 15px;
  color: #666;
  font-size: 14px;
  appearance: none;
  height: 28px;
  background-color: transparent;
}

.dx-select__input.is-mini {
  height: 14px;
  font-size: 12px;
}

.dx-select__input.is-small {
  height: 20px;
}

.dx-select__input.is-medium {
  height: 28px;
}

.dx-select--medium {
  font-size: 14px;
}

.dx-select--medium .dx-select__caret {
  font-size: 14px;
}

.dx-select--medium .dx-select__tags {
  transform: translateY(-50%);
}

.dx-select--small {
  font-size: 13px;
}

.dx-select--small .dx-select__caret {
  font-size: 13px;
}

.dx-select--small .dx-select__tags {
  transform: translateY(-50%);
}

.dx-select--mini {
  font-size: 12px;
}

.dx-select--mini .dx-select__caret {
  font-size: 12px;
}

.dx-select--mini .dx-select__tags {
  transform: translateY(-50%);
}

.dx-select-dropdown {
  position: absolute;
  z-index: 1001;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  margin: 8px 0;
  overflow: hidden;
}

.dx-select-dropdown.is-multiple .dx-select-dropdown__item.selected {
  color: #409eff;
  background-color: #fff;
}

.dx-select-dropdown.is-multiple .dx-select-dropdown__item.selected.hover {
  background-color: #f5f7fa;
}

.dx-select-dropdown.is-multiple .dx-select-dropdown__item.selected::after {
  position: absolute;
  right: 20px;
  font-family: element-icons;
  content: '\e6da';
  font-size: 12px;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
}

.dx-select-dropdown__empty {
  padding: 20px 0;
  margin: 0;
  text-align: center;
  color: #999;
  font-size: 14px;
  background-color: #fafafa;
}

.dx-select-dropdown__wrap {
  max-height: 300px;
  overflow-y: auto;
}

.dx-select-dropdown__wrap::-webkit-scrollbar {
  width: 6px;
}

.dx-select-dropdown__wrap::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dx-select-dropdown__wrap::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dx-select-dropdown__wrap::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dx-select-dropdown__list {
  list-style: none;
  padding: 8px 0;
  margin: 0;
  box-sizing: border-box;
}

.dx-select-dropdown__item {
  font-size: 14px;
  padding: 12px 20px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
  line-height: 1.4;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dx-select-dropdown__item:last-child {
  border-bottom: none;
}

.dx-select-dropdown__item.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
  background-color: #f9f9f9;
}

.dx-select-dropdown__item.is-disabled:hover {
  background-color: #f9f9f9;
}

.dx-select-dropdown__item.hover,
.dx-select-dropdown__item:hover {
  background-color: #f0f8ff;
  color: #409eff;
}

.dx-select-dropdown__item.selected {
  color: #409eff;
  font-weight: 600;
  background-color: #e6f4ff;
  position: relative;
}

.dx-select-dropdown__item.selected::after {
  content: '✓';
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #409eff;
  font-weight: bold;
  font-size: 16px;
}

.dx-select-group {
  margin: 0;
  padding: 0;
}

.dx-select-group__wrap {
  position: relative;
  list-style: none;
  margin: 0;
  padding: 0;
}

.dx-select-group__wrap:not(:last-of-type) {
  padding-bottom: 24px;
}

.dx-select-group__wrap:not(:last-of-type)::after {
  content: '';
  position: absolute;
  display: block;
  left: 20px;
  right: 20px;
  bottom: 12px;
  height: 1px;
  background: #e4e7ed;
}

.dx-select-group__title {
  padding-left: 20px;
  font-size: 12px;
  color: #909399;
  line-height: 30px;
}

.dx-select-group .dx-select-dropdown__item {
  padding-left: 20px;
}
