<template>
  <div class="search-bar" style="display: flex; align-items: center; gap: 16px">
    <el-form :inline="true" @submit.native.prevent>
      <el-form-item label="单词:">
        <el-input v-model="search.word" placeholder="请输入单词" size="small" clearable @clear="onClear" />
      </el-form-item>
      <el-form-item label="中文:">
        <el-input v-model="search.chinese" placeholder="请输入中文" size="small" clearable @clear="onClear" />
      </el-form-item>
      <el-form-item>
        <el-button type="warning" @click="onSearch" size="small" icon="el-icon-search">查找</el-button>
      </el-form-item>
      <el-form-item>
        <el-button @click="onReset" size="small" icon="el-icon-refresh">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'SearchBar',
    props: {
      value: {
        type: Object,
        default: () => ({ word: '', chinese: '' })
      }
    },
    data() {
      return {
        search: { ...this.value }
      };
    },
    watch: {
      value(val) {
        this.search = { ...val };
      }
    },
    methods: {
      onSearch() {
        this.$emit('search', { ...this.search });
      },
      onReset() {
        this.search = { word: '', chinese: '' };
        this.$emit('search', { ...this.search });
      },
      onClear() {
        // 清除时也重新查询
        this.$emit('search', { ...this.search });
      }
    }
  };
</script>
