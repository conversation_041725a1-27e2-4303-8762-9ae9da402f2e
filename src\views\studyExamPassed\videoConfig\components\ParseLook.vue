<template>
  <div class="question-container">
    <!-- 题干部分 -->
    <div class="question-stem" v-if="question">
      <div class="question-text" v-html="question.questionText ? indexLabel + renderFormula(question.questionText) : '无'"></div>
    </div>

    <!-- 内容有图时 -->
    <div class="image-container">
      <el-image v-if="question.caption.length == 1" class="stem-image" fit="cover" :src="question.caption[0]" :preview-src-list="question.caption" lazy></el-image>
      <div class="stem-images" v-else-if="question.caption.length > 1">
        <div class="image-wrapper" v-for="(img, idx) in question.caption" :key="idx">
          <el-image class="images" fit="cover" :src="img" :preview-src-list="question.caption" lazy></el-image>
        </div>
      </div>
    </div>

    <!-- 选项部分（纯文字，当所有选项都没有图片） -->
    <div class="options-container" v-if="(question.questionType == '1' || question.questionType == '2') && !hasAnyOptionImage">
      <div v-for="(option, idx) in question.xktOptionVoList" :key="idx" class="option-item">
        <span class="option-letter">{{ option.choiceOption }}.</span>
        <span class="option-content" v-html="option.content ? renderFormula(option.content) : '无'"></span>
      </div>
    </div>

    <!-- 选项有任意图片时，走图文栅格；单个缺图显示占位 -->
    <div class="option-images" v-if="(question.questionType == '1' || question.questionType == '2') && hasAnyOptionImage">
      <div class="image-wrapper" v-for="(option, idx) in question.xktOptionVoList" :key="idx">
        <el-tooltip effect="dark" placement="top" :disabled="!option.content" popper-class="option-tooltip">
          <div slot="content" v-if="option.content" v-html="renderFormula(option.choiceOption + '.' + option.content)"></div>

          <!-- 触发区域（保持你原来的结构） -->
          <div style="display: flex">
            <span class="image-letter">{{ option.choiceOption }}.</span>
            <span class="image-title" v-html="option.content ? renderFormula(option.content) : ''"></span>
          </div>
        </el-tooltip>
        <template v-if="option.optionImage">
          <el-image class="images" fit="cover" :src="option.optionImage" :preview-src-list="[option.optionImage]" lazy>
            <div slot="placeholder" class="img-fallback">加载中...</div>
            <div slot="error" class="img-fallback">加载失败</div>
          </el-image>
        </template>
        <!-- <template v-else>
          <div class="images image-fallback">
            <span class="fallback-text el-icon-picture-outline"></span>
          </div>
        </template> -->
      </div>
    </div>

    <!-- 填空问题(拍照)和主观 -->
    <div class="problem-item" v-if="question.questionType == '4' || question.questionType == '5'">
      <span v-for="(option, optIndex) in question.xktSubQuestionVoList" :key="optIndex">
        <span class="problem-content">{{ '问题' + (optIndex + 1) }}：</span>
        <span class="problem-content" v-html="option.subTitle ? renderFormula(option.subTitle) + '（' + option.score + '）' : '无'"></span>
      </span>
    </div>

    <!-- 分值 -->
    <div class="answer-section">
      <div class="answer-label">分值：</div>
      <div class="answer-content">{{ question.score || '无' }}</div>
    </div>
    <!-- 答案部分 -->
    <div
      :class="question.questionType == '3' ? 'answer-sections' : 'answer-section'"
      v-if="question.questionType == '1' || question.questionType == '2' || question.questionType == '3'"
    >
      <div class="answer-label">答案：</div>
      <div class="answer-content" v-if="question.questionType == '1' || question.questionType == '2'">{{ question.answer || '无' }}</div>
      <div class="opt-content answer-content" v-if="question.questionType == '3'">
        <span v-for="(option, optIndex) in question.xktOptionVoList" :key="optIndex">
          <span>{{ optIndex + 1 }}.</span>
          <span v-html="option.content ? renderFormula(option.content) + '（' + option.score + '）' : '无'"></span>
        </span>
      </div>
    </div>

    <!-- 解析部分 -->
    <div class="analysis-section">
      <div class="analysis-label">答案解析：</div>
      <div class="analysis-content" v-html="question.analysis ? renderFormula(question.analysis) : '无'"></div>
    </div>
    <!-- 解析有图时 -->
    <div class="analysis-image-container" v-if="question.analysisImage && question.analysisImage.length > 0">
      <div class="stem-images">
        <div class="image-wrapper" v-for="(img, idx) in question.analysisImage" :key="idx">
          <el-image class="images" fit="cover" :src="img" :preview-src-list="question.analysisImage" lazy></el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { renderFormulaAuto } from '@/utils/formulaRenderer';
  export default {
    name: 'QuestionContent',
    props: {
      question: { type: Object, required: true },
      index: { type: Number, default: 1 }
    },
    computed: {
      indexLabel() {
        return this.index + '.';
      },
      hasAnyOptionImage() {
        if (!this.question || !Array.isArray(this.question.xktOptionVoList) || !this.question.xktOptionVoList.length) return false;
        return this.question.xktOptionVoList.some((o) => o.optionImage && o.optionImage.trim() !== '');
      }
    },
    methods: {
      renderFormula(text) {
        return renderFormulaAuto(text);
      }
    }
  };
</script>

<style scoped lang="less">
  @normal-text: PingFangSC-regular;
  @normal-color: rgba(16, 16, 16, 1);
  .question-container {
    padding: 0 20px;
    max-height: 600px;
    overflow-y: auto;
  }
  .question-stem {
    margin-bottom: 20px;
    display: flex;
    .question-text {
      font-size: 16px;
      line-height: 1.8;
      color: @normal-color;
      font-weight: 500;
      font-family: @normal-text;
    }
  }
  .image-container,
  .analysis-image-container {
    margin-top: 9px;
    .stem-image {
      width: auto;
      height: 180px;
    }
    .stem-images {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      margin-top: 15px;
      .image-wrapper {
        padding: 5px;
        .images {
          width: auto;
          height: 170px;
        }
      }
    }
  }

  .problem-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;

    .problem-content {
      line-height: 30px;
      color: @normal-color;
      font-size: 16px;
      font-family: @normal-text;
    }
  }

  .options-container {
    .option-item {
      padding: 10px;
      margin-bottom: 5px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      .option-letter {
        font-size: 16px;
        margin-right: 8px;
      }
      .option-content {
        flex: 1;
        font-size: 16px;
        color: #303133;
        font-family: @normal-text;
      }
    }

    margin: 20px 0;
  }

  .option-images {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;

    .image-wrapper {
      width: 25%; // 一行最多四个
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding: 5px 10px 15px 10px;
      .image-letter {
        font-size: 16px;
        margin-right: 8px;
      }
      .image-title {
        color: @normal-color;
        font-size: 14px;
        font-family: @normal-text;
        // line-height: 1.4;
        // max-height: 2.8em; // 2 行 * 1.4 line-height
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2; // 兼容性补充
        -webkit-box-orient: vertical;
        word-break: break-all;
      }
      .images {
        width: 100%;
        height: 170px;
        margin-top: 8px;
        object-fit: cover;
      }
      .image-fallback {
        background: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #909399;
        font-size: 12px;
      }
      .img-fallback {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #909399;
        font-size: 12px;
      }
      .fallback-text {
        font-size: 18px;
      }
    }
  }
  .answer-sections {
    display: flex;
    margin-top: 20px;
  }
  .answer-section {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .answer-sections,
  .answer-section,
  .analysis-section {
    margin-bottom: 20px;
    font-family: @normal-text;
  }
  .answer-label,
  .analysis-label {
    font-weight: bold;
    color: @normal-color;
    margin-bottom: 3px;
    font-size: 18px;
  }
  .analysis-content {
    margin-top: 20px;
  }
  .answer-content,
  .analysis-content {
    font-size: 16px;
    line-height: 1.8;
    color: @normal-color;
    font-weight: 500;
  }
  .opt-content {
    display: flex;
    flex-direction: column;
  }

  /deep/.katex {
    white-space: normal !important;
    word-break: break-word;
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
    font-weight: inherit !important;
  }
</style>
