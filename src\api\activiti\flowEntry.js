import request from '@/utils/request'

export default {
  //分页
  list(data) {
    return request({
      url: '/activiti/flow/list',
      method: 'GET',
      params: data
    })
  },
  add(data) {
    return request({
      url: '/activiti/flow/add',
      method: 'POST',
      data
    })
  },
  update(data) {
    return request({
      url: '/activiti/flow/update',
      method: 'POST',
      data
    })
  },
  view(data) {
    return request({
      url: '/activiti/flow/view',
      method: 'GET',
      params: data
    })
  },

  delete(data){
    return request({
      url: '/activiti/flow/delete',
      method: 'POST',
      params: data
    })
  },

  /**
   * 获取流程图列表
   */
  listFlowEntryPublish(data) {
    return request({
      url: '/activiti/flow/listFlowEntryPublish',
      method: 'GET',
      params: data
    })
  },

  /**
   * 发布工作流
   */
  publish(data) {
    return request({
      url: '/activiti/flow/publish',
      method: 'POST',
      params: data
    })
  },

  /**
   * 挂起工作流指定版本
   */
  suspendFlowEntryPublish(data) {
    return request({
      url: '/activiti/flow/suspendFlowEntryPublish',
      method: 'POST',
      params: data
    })
  },

  /**
   * 激活工作流的指定发布版本
   */
  activateFlowEntryPublish(data) {
    return request({
      url: '/activiti/flow/activateFlowEntryPublish',
      method: 'POST',
      params: data
    })
  },

  /**
   * 切换指定工作的发布主版本
   */
  updateMainVersion(data) {
    return request({
      url: '/activiti/flow/updateMainVersion',
      method: 'POST',
      params: data
    })
  },

  /**
   * 根据流程Id，获取流程引擎需要的流程标识和流程名称
   */
  viewDict(data){
    return request({
      url: '/activiti/flow/viewDict',
      method: 'GET',
      params: data
    })
  }

}
