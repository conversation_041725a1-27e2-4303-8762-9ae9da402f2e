<template>
  <div class="app-container">
    <div class="section-title">编辑课程话术</div>
    <el-breadcrumb class="process-card-title" separator-class="el-icon-arrow-right" v-if="courseDetail">
      <el-breadcrumb-item>{{ courseDetail.coursePeriodNodeName }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ courseDetail.courseSubjectNodeName }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ courseDetail.courseName }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ filterType(courseDetail.courseType) }}</el-breadcrumb-item>
    </el-breadcrumb>
    <el-row style="margin: 10px 0">
      <div class="status-card warning">
        <i class="el-icon-success"></i>
        &nbsp;请先配置课程内容，再配置话术
      </div>
    </el-row>
    <!-- 富文本编辑器 -->
    <div>
      <div v-for="item in speechList" class="process-card">
        <div class="card-header">
          <div class="card-title">
            <i class="el-icon-s-grid" style="color: #bbbbbb"></i>
            <span style="margin-left: 6px">{{ item.stageName }}</span>
          </div>
        </div>
        <rich-text :value.sync="item.speechContent"></rich-text>
        <!-- {{ item.speechContent }} -->
      </div>
    </div>
    <!-- 提交按钮 -->

    <!-- 保存按钮 -->
    <el-row class="btn-row" type="flex" justify="center">
      <el-col :span="3" :offset="1">
        <el-button class="add-video-node-btn" type="none" size="medium" @click="handleEditClose()">取消</el-button>
      </el-col>
      <el-col :span="3" :offset="1">
        <el-button class="add-video-node-btn" type="primary" size="medium" @click="handleSaveClick()">保存</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import richText from './components/richText.vue';
  import courseManagementAPI from '@/api/mathApi/courseManagementAPI';

  export default {
    components: { richText },
    data() {
      return {
        // 课程详情
        courseDetail: {},
        speechList: []
      };
    },
    methods: {
      init() {
        const loading = this.$loading({
          lock: true,
          text: '课程内容加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        // 获取课程详情
        courseManagementAPI
          .getCourseSpeechAPI({ courseBaseMessageId: this.courseDetail.id })
          .then((res) => {
            console.log('🚀 ~ courseManagementAPI.getCourseSpeechAPI ~ res:', res);
            this.speechList = res.data;
            loading.close();
          })
          .catch((err) => {
            loading.close();
          });
      },
      handleSaveClick() {
        // 保存
        const loading = this.$loading({
          lock: true,
          text: '课程内容保存中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        courseManagementAPI
          /**
           * courseBaseMessageId 课程id
           * mathCourseSpeechCoList 课程话术列表
           */
          .saveCourseSpeechAPI({ courseBaseMessageId: this.courseDetail.id, mathCourseSpeechCoList: this.speechList })
          .then((res) => {
            this.$message.success('保存成功');
            this.$router.back();
          })
          .finally((err) => {
            loading.close();
          });
      },
      handleEditClose() {
        this.$router.back();
      },
      filterType(str) {
        if (!str) return '';
        let options = [
          { value: '1', label: '正课' },
          { value: '0', label: '试课' }
        ];
        let values = str.split(',');
        const labels = values
          .map((val) => options.find((option) => option.value === val)?.label)
          .filter(Boolean) // 过滤掉未匹配到的
          .join(',');

        return labels;
      }
    },
    mounted() {
      // 获取课程详情
      let courseDetail = localStorage.getItem('speechData');
      this.courseDetail = JSON.parse(courseDetail);
      console.log('🚀 ~ mounted ~ this.courseDetail:', this.courseDetail);
      if (!this.courseDetail) {
        this.$router.back();
      }
      // 初始化
      this.init();
    },
    beforeDestroy() {}
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-breadcrumb {
      font-size: 18px;
    }
  }
  .section-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 10px;
    // border-bottom: 1px solid #e8e8e8;
  }
  .btn-row {
    margin-top: 50px;
  }
  .process-card-title {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    box-sizing: border-box;
  }
  .process-card {
    margin-top: 10px;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 20px;
    box-sizing: border-box;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 20px;
  }

  .card-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
  }
  .status-card {
    background-color: #fef0f0;
    color: #f56c6c;
    // background-color: #f4f4f5;
    // color: #909399;
    padding: 10px 15px;
    border-radius: 5px;
    &.success {
      background-color: #f0f9eb;
      color: #67c23a;
    }
    &.info {
      background-color: #f4f4f5;
      color: #666;
    }
    &.warning {
      background-color: #fdf6ec;
      color: #e6a23c;
    }
    &.danger {
      background-color: #fef0f0;
      color: #f56c6c;
    }
    &.danger {
      background-color: #ecf5ff;
      color: #409eff;
    }
  }
</style>
