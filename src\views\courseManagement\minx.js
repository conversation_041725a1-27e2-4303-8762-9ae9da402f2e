export default {
  data() {
    return {
      multipleSelection: [],
      questType: { 0: '单选', 1: '填空', 2: '计算', 3: '解方程', 4: '证明题', 5: '几何综合题' },
      questDiff: {
        0: '低',
        1: '中',
        2: '高'
      },
      nameList: [
        { label: '序号', prop: 'index' },
        { label: 'ID', prop: 'id' },
        { label: '题目难度', prop: 'questionDifficulty', slot: true },
        { label: '题型', prop: 'questionType', slot: true },
        { label: '题干', prop: 'questionText', slot: true },
        { label: '关联知识点', prop: 'knowledgeNameList', slot: true },
        { label: '题目分值', prop: 'questionGrade', slot: true },
        { label: '操作', prop: 'scope', slot: true }
      ]
    };
  },
  watch: {},
  mounted() {
    this.multipleSelection = this.formData.courseProcessManualQuestionVoList.map((item) => {
      return {
        questionText: item.questionText,
        questionDifficulty: item.questionDifficulty,
        questionType: item.questionType,
        cpId: item.id,
        parperConfigId: item.parperConfigId || 0, // 如果是新增，parperConfigId 为 0,
        id: item.questionBankId,
        questionGrade: item.questionGrade,
        createTime: item.createTime,
        knowledgeNameList: item.knowledgeNameList,
        updateTime: item.updateTime || null,
        createUser: item.createUser || null,
        updateUser: item.updateUser || null,
        isDeleted: item.isDeleted
      };
    });
    // this.formData.processQuestionTypeCoListDel = []; // 记录删除的题型
  },
  computed: {
    filteredSelection() {
      const list = Array.isArray(this.multipleSelection) ? this.multipleSelection : [];
      return list.filter((i) => Number(i.isDeleted) == 0);
    }
  },
  methods: {
    deleteMultip(e) {
      console.log(this.multipleSelection, '---');
      console.log(e);
      if (e.cpId) {
        this.multipleSelection.forEach((item) => {
          if (item.id == e.id) {
            item.isDeleted = 1;
          }
        });
      } else {
        this.multipleSelection = this.multipleSelection.filter((item) => item.id != e.id);
      }
    },
    addTopic() {
      console.log(JSON.parse(JSON.stringify(this.multipleSelection)), '---------360');
      this.$refs.newQuestionAdded.init(
        true,
        JSON.parse(JSON.stringify(this.multipleSelection)).filter((item) => item.isDeleted != 1)
      );
    },
    confirmNewQuestionAdded(newSelection) {
      //this.multipleSelection  newSelectionCopy ，两个数组做对比
      //this.multipleSelection 是原数组，如果这里面的子项存在cpId,那么他就是已经入库的数据
      //newSelectionCopy是操作过后的数组，其中的可能也会存在cpid，如果有，也是已经入库的状态，没有就是新增状态
      //操作过后的数组需要跟原数组做比较，如果这条数据已经入库了，操作过后没有这条数据，那就修改这条数据的变量isDeleted为0
      //操作过后的数组需要跟原数组做比较 如果这条数据没有入库，但是老数据里面存在这条数据，那就判断老数据有无入库条件，如果有就修改状态，如果没有就删除当前这条数
      //操作过后的数组需要跟原数组做比较 如果这条数据没有入库，但是存在于老数据中，他的isDeleted参数为1，那么isDeleted需要改为0，并且需要存在于最终数组中
      //
      let newSelectionCopy = newSelection;
      let resultArray = []; // 最终结果数组
      let cpIdMap = new Map(); // 用于快速查找原数组中的数据，通过cpId作为键
      this.multipleSelection.forEach((item) => cpIdMap.set(item.cpId, item));
      newSelectionCopy.forEach((item) => {
        // 如果新数组中的数据有cpId，则为已入库状态
        if (item.cpId) {
          // 查找原数组中是否有对应的cpId
          let originalItem = cpIdMap.get(item.cpId);
          if (originalItem) {
            // 如果原数组中存在，说明这条数据在新数组中保留，直接保留
            resultArray.push(item);
          } else {
            // 如果原数组中不存在，说明这条数据是新增的，但已经入库了，所以直接保留
            resultArray.push(item);
          }
        } else {
          // 未入库状态，查找原数组中是否有相同数据（其他字段相同，但cpId可能不同）
          let matchIndex = this.multipleSelection.findIndex((originalItem) => originalItem.id === item.id);
          console.log(matchIndex, 'matchIndex');

          if (matchIndex !== -1) {
            // 如果原数组中存在该数据，判断是否满足入库条件
            let originalItem = this.multipleSelection[matchIndex];
            console.log(originalItem, 'originalItem');

            if (originalItem.cpId) {
              // 如果原数据已入库，则更新状态
              item.cpId = originalItem.cpId;
              if (originalItem.isDeleted == 1) {
                originalItem.isDeleted = 0;
                item.isDeleted = originalItem.isDeleted;
                resultArray.push({ questionGrade: originalItem.questionGrade, ...item });
              }
            } else {
              // 如果原数据未入库，则添加到结果数组中
              resultArray.push(item);
            }
            console.log(originalItem, 'originalItem11');
            // 如果不满足入库条件，则不添加到结果数组中
          } else {
            // 如果原数组中不存在，直接保留为新增状态
            resultArray.push(item);
          }
        }
      });
      // 处理原数组中存在的但未在新数组中出现的数据
      this.multipleSelection.forEach((originalItem) => {
        let isExistInNew = newSelectionCopy.some((newItem) => newItem.cpId === originalItem.cpId);
        if (!isExistInNew && originalItem.cpId) {
          // 如果原数组中的数据已入库但不在新数组中，设置isDeleted为0
          originalItem.isDeleted = 1;
          resultArray.push(originalItem);
        }
      });
      this.multipleSelection = resultArray; // 更新最终结果
      console.log(this.multipleSelection, '-----465');
      //   let newSelectionCopy = JSON.parse(JSON.stringify(newSelection));
      //   if (newSelection.length === 0) {
      //     this.multipleSelection = this.multipleSelection.map((item) => {
      //       return { ...item, isDeleted: 1 };
      //     });
      //   } else {
      //     // 获取新选择数组中所有元素的 id
      //     let existingIds = newSelectionCopy.map((item) => item.id);
      //     let deletedItems = JSON.parse(JSON.stringify(this.multipleSelection)).filter((item) => {
      //       return !existingIds.includes(item.id);
      //     });
      //     deletedItems.forEach((item) => {
      //       if (item.cpId) {
      //         item.isDeleted = 1;
      //       }
      //       newSelectionCopy.push(item);
      //     });
      //     let idS = this.multipleSelection.map((i) => {
      //       return i.id;
      //     });
      //     JSON.parse(JSON.stringify(newSelectionCopy)).forEach((item) => {
      //       if (idS.indexOf(item.id) == -1) {
      //         this.multipleSelection.push(item);
      //       } else {
      //         this.multipleSelection.forEach((i) => {
      //           if (i.id == item.id) {
      //             i.isDeleted = 1;
      //           }
      //         });
      //       }
      //     });
      //     this.multipleSelection = newSelectionCopy;
      //     console.log(this.multipleSelection, '有没有--1');
      //   }
      //   console.log(this.multipleSelection);
    }
  }
};
