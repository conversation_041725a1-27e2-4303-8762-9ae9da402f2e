// 语法管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/grammar',
  component: Layout,
  redirect: '/grammar/grammarQuestion',
  meta: {
    perm: 'm:grammar',
    title: '语法管理',
    icon: 'grammer'
  },
  children: [
    {
      path: 'grammarStudent',
      component: () => import('@/views/grammar/grammarStudent'),
      name: 'grammarStudent',
      meta: {
        perm: 'm:grammar:grammarStudent',
        title: '开通语法学员列表'
      }
    },
    {
      path: 'grammarMessage',
      component: () => import('@/views/grammar/grammarMessage'),
      name: 'grammarMessage',
      meta: {
        perm: 'm:grammar:grammarMessage',
        title: '学员寄语列表'
      }
    },
    {
      path: 'phaseCredentialList',
      component: () => import('@/views/grammar/phaseCredentialList'),
      name: 'phaseCredentialList',
      meta: {
        perm: 'm:grammar:phaseCredentialList',
        title: '学员结业证书打印列表'
      }
    },
    {
      path: 'phaseCreadentialPrint',
      hidden: true,
      component: () => import('@/views/grammar/phaseCreadentialPrint'),
      name: 'phaseCreadentialPrint',
      meta: {
        perm: 'm:grammar:phaseCreadentialPrint',
        title: '学员结业证书打印列表-打印'
      }
    },
    {
      path: 'handoutsPrintList',
      component: () => import('@/views/grammar/handoutsPrintList'),
      name: 'handoutsPrintList',
      meta: {
        perm: 'm:grammar:handoutsPrintList',
        title: '学员课件打印列表'
      }
    },
    {
      path: 'studentHandoutsPrint',
      hidden: true,
      component: () => import('@/views/grammar/studentHandoutsPrint'),
      name: 'studentHandoutsPrint',
      meta: {
        perm: 'm:grammar:studentHandoutsPrint',
        title: '学员课件打印列表-打印'
      }
    }
  ]
};
