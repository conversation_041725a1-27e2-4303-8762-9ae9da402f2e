import request from '@/utils/request';
export default {
  // 工具分页
  toolListAPI(query) {
    return request({
      url: '/znyy/curriculumTool/list',
      method: 'get',
      params: query
    });
  },
  // 工具启用状态
  isEnabledAPI(data) {
    return request({
      url: '/znyy/curriculumTool/isEnabled',
      method: 'post',
      data
    });
  },
  // 工具新增/编辑
  saveAPI(data) {
    return request({
      url: '/znyy/curriculumTool/save',
      method: 'post',
      data
    });
  },
  // 工具删除
  deleteAPI(query) {
    return request({
      url: '/znyy/curriculumTool/delete',
      method: 'get',
      params: query
    });
  },

  /* 关联工具部分 */
  // 关联工具分页
  curriculumListByPageAPI(query) {
    return request({
      url: '/znyy/curriculum/type/curriculumListByPage',
      method: 'get',
      params: query
    });
  },
  // 关联工具下拉框
  relatedListAPI(query) {
    return request({
      url: '/znyy/curriculumTool/relatedList',
      method: 'get',
      params: query
    });
  },
  // 关联工具新增/编辑
  relatedSaveAPI(data) {
    return request({
      url: '/znyy/curriculum/type/save',
      method: 'post',
      data
    });
  },
  updateAPI(data) {
    return request({
      url: '/znyy/curriculum/type/update',
      method: 'post',
      data
    });
  },
  // 关联工具启用
  enableAPI(query) {
    return request({
      url: '/znyy/curriculum/type/enable',
      method: 'get',
      params: query
    });
  }
};
