<template>
  <div class="batch-template-upload">
    <div class="download-template">
      <div class="info">
        <slot name="download-info">
          <div>
            <div class="title">下载模板文档</div>
            <div class="description">请将您的文档按照模板中的格式调整好</div>
          </div>
        </slot>
        <i class="el-icon-download icon-style" />
      </div>
      <div class="button-group simple-progress" :class="{ disabled: downloadStatus === 'downloading' }" @click="handleDownload">
        <div class="progress-fill" v-if="showDownloadProgress" :style="{ width: downloadProgress + '%', transition: downloadProgressTransition }" />
        <p>{{ downloadButtonText }}</p>
      </div>
    </div>

    <div class="upload-excel">
      <div class="info">
        <slot name="upload-info">
          <div>
            <div class="title">批量上传题目文档</div>
            <div class="description">请将您的文档按照模板中的格式调整好</div>
          </div>
        </slot>
        <i class="el-icon-upload2 icon-style" />
      </div>
      <div class="button-group simple-progress" @click="triggerSelect">
        <div class="progress-fill" v-if="showUploadProgress" :style="{ width: uploadProgress + '%', transition: uploadProgressTransition }" />
        <p>{{ uploadButtonText }}</p>
        <input ref="fileInput" type="file" :accept="accept" style="display: none" @change="onFileSelected" />
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'BatchTemplateUpload',
    props: {
      templateUrl: { type: String, required: true },
      fileName: { type: String, default: '模板.xlsx' },
      accept: { type: String, default: '.xlsx,.xls' },
      maxSizeMB: { type: Number, default: 100 },
      // 上传函数
      uploadFn: { type: Function, required: true },
      // 自定义上传进度回调键名，可选
      disabled: { type: Boolean, default: false }
    },
    data() {
      return {
        // 下载
        downloadStatus: 'idle', // idle | downloading | success | error
        downloadProgress: 0,
        showDownloadProgress: false,
        downloadProgressTransition: 'width 0.3s ease',
        downloadSuccessHoldDelay: 600,
        // 上传
        uploadStatus: 'idle',
        uploadProgress: 0,
        showUploadProgress: true,
        uploadProgressTransition: 'width 0.3s ease',
        uploadSuccessHoldDelay: 600,
        currentFile: null
      };
    },
    computed: {
      downloadButtonText() {
        switch (this.downloadStatus) {
          case 'downloading':
            return '下载中...';
          case 'success':
            return '下载Excel模板';
          case 'error':
            return '重试下载';
          default:
            return '下载Excel模板';
        }
      },
      uploadButtonText() {
        if (this.uploadStatus === 'uploading') return '上传中...';
        return '上传Excel文档';
      }
    },
    methods: {
      // 下载逻辑
      handleDownload() {
        if (this.disabled) return;
        if (this.downloadStatus === 'downloading') {
          this.$message.warning('模板正在下载中，请稍候');
          return;
        }
        if (this.downloadStatus === 'success') {
          this.downloadProgress = 0;
          this.showDownloadProgress = false;
          this.downloadStatus = 'idle';
        }
        this.downloadWithProgress();
      },
      downloadWithProgress() {
        this.downloadStatus = 'downloading';
        this.downloadProgress = 0;
        this.showDownloadProgress = true;
        this.downloadProgressTransition = 'width 0.3s ease';
        const xhr = new XMLHttpRequest();
        xhr.open('GET', this.templateUrl, true);
        xhr.responseType = 'blob';
        xhr.onprogress = (evt) => {
          if (evt.lengthComputable) {
            const percent = Math.round((evt.loaded / evt.total) * 85);
            if (this.downloadStatus === 'downloading') this.downloadProgress = Math.min(85, percent);
          } else if (this.downloadStatus === 'downloading' && this.downloadProgress < 60) {
            this.downloadProgress += 2;
          }
        };
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            this.finishDownload(true, xhr.response);
          } else this.finishDownload(false);
        };
        xhr.onerror = () => this.finishDownload(false);
        xhr.send();
      },
      finishDownload(success, blob) {
        const start = this.downloadProgress;
        const remain = 100 - start;
        if (success) {
          const duration = Math.min(800, Math.max(250, remain * 12));
          const startTime = performance.now();
          const animate = () => {
            const ratio = (performance.now() - startTime) / duration;
            if (ratio < 1) {
              this.downloadProgress = Math.round(start + remain * ratio);
              requestAnimationFrame(animate);
            } else {
              this.downloadProgress = 100;
              this.downloadStatus = 'success';
              if (blob) {
                const a = document.createElement('a');
                const objectUrl = URL.createObjectURL(blob);
                a.href = objectUrl;
                a.download = this.fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(objectUrl);
              }
              this.$message.success('模板下载完成');
              setTimeout(() => {
                this.downloadProgressTransition = 'none';
                this.showDownloadProgress = false;
                this.downloadProgress = 0;
                this.$nextTick(() => {
                  this.downloadProgressTransition = 'width 0.3s ease';
                });
              }, this.downloadSuccessHoldDelay);
            }
          };
          requestAnimationFrame(animate);
        } else {
          this.downloadStatus = 'error';
          this.$message.error('模板下载失败，请重新尝试！');
          if (this.downloadProgress < 10) this.downloadProgress = 15;
          setTimeout(() => {
            this.downloadProgressTransition = 'none';
            this.showDownloadProgress = false;
            this.downloadProgress = 0;
            this.downloadStatus = 'idle';
            this.$nextTick(() => {
              this.downloadProgressTransition = 'width 0.3s ease';
            });
          }, 300);
        }
      },
      // 上传逻辑
      triggerSelect() {
        if (this.disabled) return;
        if (this.uploadStatus === 'uploading') {
          this.$message.warning('文件正在上传，请稍候');
          return;
        }
        if (this.uploadStatus === 'success') {
          this.uploadStatus = 'idle';
          this.uploadProgress = 0;
          this.currentFile = null;
          this.showUploadProgress = false;
          if (this.$refs.fileInput) this.$refs.fileInput.value = '';
        }
        this.$refs.fileInput && this.$refs.fileInput.click();
      },
      onFileSelected(e) {
        const file = e.target.files[0];
        if (!file) return;
        const sizeLimit = this.maxSizeMB * 1024 * 1024;
        const extOk = this.accept.split(',').some((ext) => file.name.toLowerCase().endsWith(ext.replace('.', '').toLowerCase()));
        if (!extOk) {
          this.$message.error('文件格式不符合要求');
          e.target.value = '';
          return;
        }
        if (file.size > sizeLimit) {
          this.$message.error(`文件大小不能超过${this.maxSizeMB}MB`);
          e.target.value = '';
          return;
        }
        this.currentFile = file;
        this.uploadProgress = 0;
        this.uploadStatus = 'uploading';
        this.showUploadProgress = true;
        this.uploadProgressTransition = 'width 0.3s ease';
        this.doUpload();
      },
      async doUpload() {
        if (!this.currentFile) return;
        try {
          const formData = new FormData();
          formData.append('file', this.currentFile);
          const result = await this.uploadFn(formData, {
            onUploadProgress: (evt) => {
              if (!evt.total) return;
              const percent = Math.round((evt.loaded / evt.total) * 85);
              if (this.uploadStatus === 'uploading') this.uploadProgress = Math.min(85, percent);
            }
          });
          if (this.uploadProgress < 90) this.uploadProgress = 90;
          this.finishUpload(true, result);
        } catch (e) {
          console.error('上传失败', e);
          this.finishUpload(false, e);
        }
      },
      finishUpload(success, payload) {
        const start = this.uploadProgress;
        const remain = 100 - start;
        if (success) {
          const duration = Math.min(800, Math.max(250, remain * 12));
          const startTime = performance.now();
          const step = () => {
            const ratio = (performance.now() - startTime) / duration;
            if (ratio < 1) {
              this.uploadProgress = Math.round(start + remain * ratio);
              requestAnimationFrame(step);
            } else {
              this.uploadProgress = 100;
              this.uploadStatus = 'success';
              this.$message.success('文件上传成功！');
              this.$emit('upload-success', payload);
              setTimeout(() => {
                this.uploadProgressTransition = 'none';
                this.showUploadProgress = false;
                this.uploadProgress = 0;
                this.$nextTick(() => {
                  this.uploadProgressTransition = 'width 0.3s ease';
                });
              }, this.uploadSuccessHoldDelay);
            }
          };
          requestAnimationFrame(step);
        } else {
          this.$message.error('上传失败');
          if (this.uploadProgress < 5) this.uploadProgress = 15;
          setTimeout(() => {
            this.uploadProgressTransition = 'none';
            this.showUploadProgress = false;
            this.uploadProgress = 0;
            this.uploadStatus = 'idle';
            this.currentFile = null;
            if (this.$refs.fileInput) this.$refs.fileInput.value = '';
            this.$nextTick(() => {
              this.uploadProgressTransition = 'width 0.3s ease';
            });
          }, 80);
        }
      }
    }
  };
</script>

<style scoped lang="less">
  .batch-template-upload {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    gap: 20px;
    width: 90%;
    background-color: rgba(180, 253, 255, 0.3);
    padding: 20px;
  }
  .download-template {
    border-right: 1px solid rgba(206, 206, 206, 1);
  }
  .download-template,
  .upload-excel {
    flex: 1;
    padding: 30px 40px;
  }
  .info {
    display: flex;
    justify-content: space-between;
  }
  .icon-style {
    font-size: 50px;
    color: #4095e5;
  }
  .title {
    font-size: 24px;
    font-weight: bold;
    color: #101010;
    margin-bottom: 10px;
  }
  .description {
    color: #101010;
    font-size: 18px;
    margin-top: 26px;
    margin-bottom: 48px;
    font-family: PingFangSC-regular;
  }
  .button-group {
    width: 197px;
    height: 53px;
    border-radius: 4px;
    background: #fff;
    color: #4095e5;
    font-size: 18px;
    border: 1px solid #4095e5;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: relative;
    padding: 0 12px;
    text-align: center;
    overflow: hidden;
    transition: all 0.2s;
  }
  .button-group.simple-progress p {
    position: relative;
    z-index: 2;
    margin: 0;
  }
  .progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, #93d2f3, #93d2f3);
    transition: width 0.3s ease;
    z-index: 1;
  }
  .button-group.disabled {
    opacity: 0.8;
    cursor: not-allowed;
  }
</style>
