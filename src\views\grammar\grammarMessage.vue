<template>
  <div class="app-container">
    <el-card style="margin-bottom: 16px">
      <el-form label-width="90px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6" :xs="24">
            <el-form-item label="阶段:" label-width="60px">
              <el-select style="width: 100%" v-model="dataQuery.phase" filterable value-key="value" placeholder="请选择" clearable>
                <el-option v-for="(item, index) in phaseTypeList" :key="index" :label="item.value" :value="item.type" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="学员编号：">
              <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="学员姓名：">
              <el-input v-model="dataQuery.studentName" placeholder="请输入学员姓名" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label-width="0">
              <el-button type="primary" @click="fetchData02()">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card v-if="tablePage.totalItems > 0">
      <el-table
        class="common-table"
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ list: 'children', hasChildren: 'true' }"
        :row-class-name="tableRowClassName"
      >
        <el-table-column prop="phaseName" label="学段"></el-table-column>
        <el-table-column prop="studentCode" label="学员编号"></el-table-column>
        <el-table-column prop="id" label="操作" sortable>
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="openEdit(scope.row.id)">编辑</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="studentName" label="姓名"></el-table-column>
        <el-table-column prop="typeName" label="类型"></el-table-column>
        <el-table-column prop="grammarName" label="语法点"></el-table-column>
        <el-table-column prop="message" label="寄语"></el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-row type="flex" justify="end" style="height: 28px; margin-top: 36px; line-height: 28px">
        <el-pagination
          :current-page="tablePage.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tablePage.totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
    </el-card>
    <el-card v-else>
      <NoMore></NoMore>
    </el-card>

    <!-- 编辑弹窗 -->

    <CustomDialog title="寄语编辑" :value.sync="showEdit" width="60%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'updateMessage'" :model="updateMessage" label-position="left" label-width="80px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateMessage.id"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="寄语" prop="question">
          <el-col :xs="24" :span="12">
            <el-input type="textarea" resize="none" :rows="4" v-model="updateMessage.message"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="editMessage('updateMessage')">确定</el-button>
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </CustomDialog>
  </div>
</template>

<style>
  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }
</style>

<script>
  import grammarApi from '@/api/grammar';
  import Tinymce from '@/components/Tinymce';
  import { pageParamNames } from '@/utils/constants';
  import merchantAccountFlowApi from '@/api/merchantAccountFlow';
  import ls from '@/api/sessionStorage';
  import NoMore from '@/components/NoMore/index.vue';
  import CustomDialog from '@/components/customDialog/index.vue';

  export default {
    components: { NoMore, CustomDialog },
    data() {
      return {
        recharge: {
          course: 0,
          toAccountCourse: 0,
          sumCoursePrice: 0
        },
        tableLoading: false,
        disabledF: false,
        disabledA: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          studentCode: '',
          studentName: '',
          phase: ''
        },
        updateMessage: {},
        showEdit: false, //编辑弹窗
        phaseTypeList: [] //课程分类
        // tooltip: '',
        // showTooltip: false,
      };
    },
    created() {
      this.fetchData();
      this.getPhaseType();
    },
    methods: {
      //学业检测用不同的颜色标注
      tableRowClassName({ row, rowIndex }) {
        if (row.type === 'PHASE') {
          return 'warning-row';
        }
        return '';
      },
      // 获取分类返回类型
      getPhaseType() {
        grammarApi.getPhaseType().then((res) => {
          this.phaseTypeList = res.data;
        });
      },
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchData02() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        window.localStorage.setItem('grammarMessageStudentCode', '');
        this.fetchData();
      },
      fetchData() {
        const that = this;
        that.tableLoading = true;
        // that.showTooltip=false;
        // that.tooltip='';
        let grammarMessageStudentCode = window.localStorage.getItem('grammarMessageStudentCode');
        if (grammarMessageStudentCode !== null && grammarMessageStudentCode.length > 0) {
          that.dataQuery.studentCode = grammarMessageStudentCode;
        }
        grammarApi.getMessageList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // for(let i=0;i<that.tableData.length;i++){
          //   let type = that.tableData[i].type;
          //   if (type==="PHASE"){
          //     let message = that.tableData[i].message;
          //     if (message===null||message.length===0){
          //       that.tooltip=that.tooltip+that.tableData[i].studentName+" ";
          //       that.showTooltip=true;
          //     }
          //   }
          // }
          // if (that.tooltip.length>0){
          //   that.tooltip=that.tooltip+'需配置结业寄语';
          // }
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 打开编辑题目
      openEdit(id) {
        grammarApi.getMessage(id).then((res) => {
          this.updateMessage = res.data;
        });
        this.showEdit = true;
      },
      //编辑单个题目
      editMessage(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '编辑寄语提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            grammarApi
              .editMessage(that.updateMessage)
              .then(() => {
                that.showEdit = false;
                loading.close();
                that.$nextTick(() => that.fetchData());
                that.$message.success('编写寄语成功');
              })
              .catch((err) => {
                // 关闭提示弹框
                loading.close();
              });
          } else {
            console.log('error submit!!');
            // loading.close();
            return false;
          }
        });
      },
      closeEdit() {
        this.showEdit = false;
      },
      // 关闭弹窗
      close() {
        this.dialogVisible = false;
      }
    },
    //生命周期结束 离开页面时销毁本地缓存
    beforeDestroy() {
      window.localStorage.setItem('grammarMessageStudentCode', '');
    }
  };
</script>

<style>
  .period-table td,
  .period-table th {
    text-align: center;
  }

  .mt20 {
    margin-top: 20px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  @media screen and (max-width: 767px) {
    .recharge-dialog .el-dialog {
      width: 90% !important;
    }

    .el-message-box {
      width: 80% !important;
    }
  }
</style>
