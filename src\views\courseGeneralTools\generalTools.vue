<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" style="padding: 30px 30px 0 30px">
      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item label="工具名称">
            <el-input placeholder="请输入工具名称" v-model="dataQuery.toolName"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-button type="primary" icon="el-icon-search" @click="fetchDataSearch()">查询</el-button>
          <el-button type="info" icon="el-icon-refresh" @click="fetchDataReset()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="addToolsHandle()">新增工具类型</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="course-table" :data="tableData" stripe border :default-sort="{ prop: 'addTime', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="toolName" label="工具名称" width="140"></el-table-column>
        <el-table-column prop="sort" label="工具权重"></el-table-column>
        <el-table-column prop="status" label="是否启用">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="0"
              :inactive-value="1"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="generalFlag" label="是否为通用工具"></el-table-column>
        <el-table-column label="图标">
          <template slot-scope="scope">
            <el-image :src="scope.row.iconAddress" style="width: 50px; height: 50px" fit="fill"></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="appJumpAddress" label="APP跳转路径" width="200"></el-table-column>
        <el-table-column prop="jumpAddress" label="小程序跳转路径" width="200"></el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button type="text" size="large" @click="editToolsHandle(scope.row)">编辑</el-button>
            <el-button type="text" size="large" @click="deleteTools(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page.sync="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        :page-size="tablePage.size"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 添加编辑弹窗 -->
    <el-dialog center :close-on-click-modal="false" :close-on-press-escape="false" :title="dialogTitle" :visible.sync="dialogVisible" width="40%" @close="dialogClose('ruleForm')">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="工具名称" prop="toolsName">
          <el-input v-model="ruleForm.toolsName" maxlength="15" show-word-limit placeholder="请输入工具名称"></el-input>
        </el-form-item>
        <el-form-item label="工具权重" prop="toolsSort">
          <el-input v-model="ruleForm.toolsSort" type="number" @input="handleInput" placeholder="请输入工具权重"></el-input>
        </el-form-item>
        <el-form-item label="是否为通用工具" required label-width="140px" v-if="operationType === 'add'">
          <el-radio-group v-model="radio">
            <el-radio label="1">通用</el-radio>
            <el-radio label="0">非通用</el-radio>
          </el-radio-group>
          <div style="color: red; font-size: 12px">注：添加成功后不支持修改是否为通用工具</div>
        </el-form-item>

        <el-form-item label="工具图标" required>
          <image-upload :showTip="false" @handleSuccess="imageSuccess" @handleRemove="imageRemove" :fullUrl="true" :file-list="imageList" :limit="1" />
        </el-form-item>
        <el-form-item label="未解锁图标" required>
          <image-upload :showTip="false" @handleSuccess="imageAshSuccess" @handleRemove="imageAshRemove" :fullUrl="true" :file-list="imageAshList" :limit="1" />
        </el-form-item>
        <el-form-item label="展示渠道" required>
          <el-checkbox-group v-model="toolsShow">
            <el-checkbox :label="0">APP</el-checkbox>
            <el-checkbox :label="1">小程序</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="APP 跳转路径" v-if="toolsShow.includes(0)" prop="APPPath" label-width="140px">
          <el-input v-model="ruleForm.APPPath" placeholder="请输入APP 跳转路径"></el-input>
        </el-form-item>

        <el-form-item label="小程序跳转路径" v-if="toolsShow.includes(1)" prop="Path" label-width="140px">
          <el-input v-model="ruleForm.Path" placeholder="请输入小程序跳转路径"></el-input>
        </el-form-item>
        <div v-if="toolsShow.includes(0) || toolsShow.includes(1)" style="color: red; font-size: 12px; margin-left: 28px">注：若需跳转至鼎学能小程序，跳转路径请配置为:DXN</div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogClose('ruleForm')">取 消</el-button>
        <el-button type="primary" @click="dialogConfirm('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import courseTools from '@/api/courseTools/courseTools';
import ImageUpload from '@/components/Upload/studyUpload.vue';
import { pageParamNames } from '@/utils/constants';
export default {
  name: 'generalTools',
  components: { ImageUpload },
  data() {
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableLoading: false,
      dataQuery: {
        toolName: '' // 工具名称
      },
      isRouterAlive: true, //局部刷新
      tableData: [], //表格数据
      dialogTitle: '',
      dialogVisible: false,
      ruleForm: {
        toolsName: '', // 工具名称
        toolsSort: '', // 工具权重
        APPPath: '', // APP 跳转路径
        Path: '' // 小程序跳转路径
      },
      radio: '', // 默认选中通用工具
      imageList: [], // 图片列表
      imageAshList: [], // 未解锁图标列表
      toolsShow: [''], // 展示渠道
      rules: {
        toolsName: [{ required: true, message: '请输入工具名称', trigger: 'blur' }],
        toolsSort: [{ required: true, message: '请输入权重', trigger: 'blur' }],
        APPPath: [{ required: true, message: '请输入APP 跳转路径', trigger: 'blur' }],
        Path: [{ required: true, message: '请输入小程序跳转路径', trigger: 'blur' }]
      },
      operationType: '', // 新增编辑标识符
      editId: ''
    };
  },
  watch: {
    toolsShow(newVal) {
      console.log('toolsShow 的新值是:', newVal);

      // 如果取消选中APP，则清空APPPath字段
      if (!newVal.includes(0)) {
        this.ruleForm.APPPath = '';
      }

      // 如果取消选中小程序，则清空Path字段
      if (!newVal.includes(1)) {
        this.ruleForm.Path = '';
      }
    }
  },
  created() {
    this.fetchData();
  },

  methods: {
    fetchDataSearch() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      };
      this.fetchData();
    },
    fetchDataReset() {
      (this.dataQuery = {
        toolName: '' // 工具类型
      }),
        this.fetchDataSearch();
    },
    handleInput() {
      if (Number(this.ruleForm.toolsSort) > 100) {
        this.$message.warning('输入排序不能大于 100');
        this.ruleForm.toolsSort = '';
      } else if (Number(this.ruleForm.toolsSort) < 1 && this.ruleForm.toolsSort !== '') {
        this.$message.warning('输入排序不能小于 1');
        this.ruleForm.toolsSort = '';
      } else if (this.ruleForm.toolsSort.indexOf('.') > -1) {
        this.$message.warning('不能输入小数');
        this.ruleForm.toolsSort = '';
      }
    },

    // 查询表格列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      let params = {
        toolName: that.dataQuery.toolName,
        pageSize: that.tablePage.size,
        pageNum: that.tablePage.currentPage
      };
      courseTools.toolListAPI(params).then((res) => {
        that.tableData = res.data.data;
        that.tableLoading = false;
        pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name] || 0)));
      });
    },
    handleStatusChange(row) {
      courseTools.isEnabledAPI({ id: row.id }).then((res) => {
        if (res.data.success) {
          this.$message.success('操作成功');
          this.fetchData();
        }
      });
    },
    // 新增工具
    addToolsHandle() {
      this.dialogTitle = '添加工具';
      this.ruleForm = {
        toolsName: '',
        toolsSort: '',
        APPPath: '',
        Path: ''
      };
      this.imageList = [];
      this.toolsShow = [];
      this.radio = '';
      this.operationType = 'add'; // 设置操作类型为新增
      this.dialogVisible = true;
    },
    // 编辑工具
    editToolsHandle(row) {
      this.editId = row.id;
      this.dialogTitle = '编辑工具';
      this.operationType = 'edit'; // 设置操作类型为编辑
      this.ruleForm.toolsName = row.toolName; // 设置工具名称
      this.ruleForm.toolsSort = row.sort; // 设置工具权重
      this.ruleForm.APPPath = row.appJumpAddress; // 设置APP跳转路径
      this.ruleForm.Path = row.jumpAddress; // 设置小程序跳转路径
      this.imageList = row.iconAddress ? [{ url: row.iconAddress }] : []; // 设置图片列表
      this.imageAshList = row.ashIconAddress ? [{ url: row.ashIconAddress }] : []; // 设置未解锁图标列表
      this.toolsShow = row.showChannel ? row.showChannel.split(',').map((item) => Number(item)) : []; // 设置展示渠道
      this.radio = row.generalFlag === 1 ? '1' : '0'; // 设置通用工具选项
      this.dialogVisible = true;
    },
    // 删除工具
    deleteTools(id) {
      const that = this;
      this.$confirm('确定操作吗?', '删除数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          courseTools.deleteAPI({ id: id }).then((res) => {
            that.$message.success('删除成功！');
            that.fetchData();
          });
        })
        .catch((err) => {});
    },
    imageSuccess(url) {
      this.imageList.push(url);
    },
    imageAshSuccess(url) {
      this.imageAshList.push(url);
    },
    imageAshRemove() {
      this.imageAshList = [];
    },
    imageRemove() {
      this.imageList = [];
    },
    validateDialog() {
      if (this.radio === '') {
        this.$message.error('请选择是否为通用工具');
        return false;
      }
      if (this.imageList.length === 0) {
        this.$message.error('请上传工具图标');
        return false;
      }
      if (this.imageAshList.length === 0) {
        this.$message.error('请上传未解锁图标');
        return false;
      }
      if (this.toolsShow.length === 0) {
        this.$message.error('请选择展示渠道');
        return false;
      }
      return true;
    },
    // 弹窗验证
    dialogConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 检查 validateDialog 的返回值
          if (!this.validateDialog()) {
            return;
          }
          let textToast = this.operationType === 'add' ? '新增工具' : '编辑工具';
          const loading = this.$loading({
            lock: true,
            text: textToast,
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          let params = {
            id: this.editId,
            toolName: this.ruleForm.toolsName,
            sort: this.ruleForm.toolsSort,
            appJumpAddress: this.ruleForm.APPPath,
            jumpAddress: this.ruleForm.Path,
            iconAddress: this.imageList.length > 0 ? this.imageList[0].url || this.imageList[0].response?.url || this.imageList[0] : '', // 图标地址
            ashIconAddress: this.imageAshList.length > 0 ? this.imageAshList[0].url || this.imageAshList[0].response?.url || this.imageAshList[0] : '', // 未解锁图标地址
            showChannel: this.toolsShow.join(','), // 展示渠道
            generalFlag: this.radio === '1' ? 1 : 0 // 是否为通用工具
          };
          if (this.operationType === 'add') {
            courseTools
              .saveAPI(params)
              .then((res) => {
                if (res.success) {
                  this.$message.success('添加成功！');
                  loading.close();
                  this.dialogClose(formName);
                  this.fetchData();
                }
              })
              .catch((err) => {
                loading.close();
              });
          } else if (this.operationType === 'edit') {
            courseTools
              .saveAPI({ ...params, id: this.editId })
              .then((res) => {
                if (res.success) {
                  this.$message.success('编辑成功！');
                  loading.close();
                  this.dialogClose(formName);
                  this.fetchData();
                }
              })
              .catch((err) => {
                loading.close();
              });
          }
        } else {
          return false;
        }
      });
    },
    // 关闭弹窗 重置表单
    dialogClose(formName) {
      this.dialogVisible = false;
      this.$refs[formName].resetFields();
      this.imageList = []; // 清空图片列表
      this.imageAshList = [];
      this.radio = ''; // 重置通用工具选项
      this.toolsShow = []; // 重置展示渠道
      this.editId = ''; // 清空编辑ID
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    }
  }
};
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
  margin: 15px 0;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

.clearfix {
  color: red;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
