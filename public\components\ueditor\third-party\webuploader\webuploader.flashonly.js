!function(i){function o(e,t){var i,n,r;if("string"==typeof e)return s(e);for(i=[],n=e.length,r=0;r<n;r++)i.push(s(e[r]));return t.apply(null,i)}var e,v,t,n,u={},r=function(e,t,i){var n,r={exports:t};"function"==typeof t&&(i.length||(i=[o,r.exports,r]),void 0!==(n=t.apply(null,i))&&(r.exports=n)),u[e]=r.exports},s=function(e){var t=u[e]||i[e];if(!t)throw new Error("`"+e+"` is undefined");return t},a=(v=i,n=o,(t=function(e,t,i){2===arguments.length&&(i=t,t=null),o(t||[],function(){r(e,i,arguments)})})("dollar-third",[],function(){return v.jQuery||v.<PERSON>}),t("dollar",["dollar-third"],function(e){return e}),t("promise-third",["dollar"],function(e){return{Deferred:e.Deferred,when:e.when,isPromise:function(e){return e&&"function"==typeof e.then}}}),t("promise",["promise-third"],function(e){return e}),t("base",["dollar","promise"],function(s,e){function t(){}var n,i,r,o,u,a,c,l,f,h,p,d,g,m,_=Function.call;function b(e,t){return function(){return e.apply(t,arguments)}}return{version:"0.1.2",$:s,Deferred:e.Deferred,isPromise:e.isPromise,when:e.when,browser:(c=navigator.userAgent,l={},f=c.match(/WebKit\/([\d.]+)/),h=c.match(/Chrome\/([\d.]+)/)||c.match(/CriOS\/([\d.]+)/),p=c.match(/MSIE\s([\d\.]+)/)||c.match(/(?:trident)(?:.*rv:([\w.]+))?/i),d=c.match(/Firefox\/([\d.]+)/),g=c.match(/Safari\/([\d.]+)/),m=c.match(/OPR\/([\d.]+)/),f&&(l.webkit=parseFloat(f[1])),h&&(l.chrome=parseFloat(h[1])),p&&(l.ie=parseFloat(p[1])),d&&(l.firefox=parseFloat(d[1])),g&&(l.safari=parseFloat(g[1])),m&&(l.opera=parseFloat(m[1])),l),os:(r=navigator.userAgent,o={},u=r.match(/(?:Android);?[\s\/]+([\d.]+)?/),a=r.match(/(?:iPad|iPod|iPhone).*OS\s([\d_]+)/),u&&(o.android=parseFloat(u[1])),a&&(o.ios=parseFloat(a[1].replace(/_/g,"."))),o),inherits:function(e,t,i){var n,r,o;return"function"==typeof t?(n=t,t=null):n=t&&t.hasOwnProperty("constructor")?t.constructor:function(){return e.apply(this,arguments)},s.extend(!0,n,e,i||{}),n.__super__=e.prototype,n.prototype=(r=e.prototype,Object.create?Object.create(r):((o=function(){}).prototype=r,new o)),t&&s.extend(!0,n.prototype,t),n},noop:t,bindFn:b,log:v.console?b(console.log,console):t,nextTick:function(e){setTimeout(e,1)},slice:(i=[].slice,function(){return _.apply(i,arguments)}),guid:(n=0,function(e){for(var t=(+new Date).toString(32),i=0;i<5;i++)t+=Math.floor(65535*Math.random()).toString(32);return(e||"wu_")+t+(n++).toString(32)}),formatSize:function(e,t,i){var n;for(i=i||["B","K","M","G","TB"];(n=i.shift())&&1024<e;)e/=1024;return("B"===n?e:e.toFixed(t||2))+n}}}),t("mediator",["base"],function(e){var t,r=e.$,o=[].slice,s=/\s+/;function u(e,t,i,n){return r.grep(e,function(e){return e&&(!t||e.e===t)&&(!i||e.cb===i||e.cb._cb===i)&&(!n||e.ctx===n)})}function a(e,i,n){r.each((e||"").split(s),function(e,t){n(t,i)})}function c(e,t){for(var i,n=!1,r=-1,o=e.length;++r<o;)if(!1===(i=e[r]).cb.apply(i.ctx2,t)){n=!0;break}return!n}return t={on:function(e,t,n){var r,o=this;return t&&(r=this._events||(this._events=[]),a(e,t,function(e,t){var i={e:e};i.cb=t,i.ctx=n,i.ctx2=n||o,i.id=r.length,r.push(i)})),this},once:function(e,t,n){var r=this;return t&&a(e,t,function(e,t){var i=function(){return r.off(e,i),t.apply(n||r,arguments)};i._cb=t,r.on(e,i,n)}),r},off:function(e,t,i){var n=this._events;return n&&(e||t||i?a(e,t,function(e,t){r.each(u(n,e,t,i),function(){delete n[this.id]})}):this._events=[]),this},trigger:function(e){var t,i,n;return this._events&&e?(t=o.call(arguments,1),i=u(this._events,e),n=u(this._events,"all"),c(i,t)&&c(n,arguments)):this}},r.extend({installTo:function(e){return r.extend(e,t)}},t)}),t("uploader",["base","mediator"],function(e,r){var o=e.$;function i(e){this.options=o.extend(!0,{},i.options,e),this._init(this.options)}return i.options={},r.installTo(i.prototype),o.each({upload:"start-upload",stop:"stop-upload",getFile:"get-file",getFiles:"get-files",addFile:"add-file",addFiles:"add-file",sort:"sort-files",removeFile:"remove-file",skipFile:"skip-file",retry:"retry",isInProgress:"is-in-progress",makeThumb:"make-thumb",getDimension:"get-dimension",addButton:"add-btn",getRuntimeType:"get-runtime-type",refresh:"refresh",disable:"disable",enable:"enable",reset:"reset"},function(e,t){i.prototype[e]=function(){return this.request(t,arguments)}}),o.extend(i.prototype,{state:"pending",_init:function(e){var t=this;t.request("init",e,function(){t.state="ready",t.trigger("ready")})},option:function(e,t){var i=this.options;if(!(1<arguments.length))return e?i[e]:i;o.isPlainObject(t)&&o.isPlainObject(i[e])?o.extend(i[e],t):i[e]=t},getStats:function(){var e=this.request("get-stats");return{successNum:e.numOfSuccess,cancelNum:e.numOfCancel,invalidNum:e.numOfInvalid,uploadFailNum:e.numOfUploadFailed,queueNum:e.numOfQueue}},trigger:function(e){var t=[].slice.call(arguments,1),i=this.options,n="on"+e.substring(0,1).toUpperCase()+e.substring(1);return!(!1===r.trigger.apply(this,arguments)||o.isFunction(i[n])&&!1===i[n].apply(this,t)||o.isFunction(this[n])&&!1===this[n].apply(this,t)||!1===r.trigger.apply(r,[this,e].concat(t)))},request:e.noop}),e.create=i.create=function(e){return new i(e)},e.Uploader=i}),t("runtime/runtime",["base","mediator"],function(t,e){function n(e){for(var t in e)if(e.hasOwnProperty(t))return t;return null}var r=t.$,o={};function s(e){this.options=r.extend({container:document.body},e),this.uid=t.guid("rt_")}return r.extend(s.prototype,{getContainer:function(){var e,t,i=this.options;return this._container?this._container:(e=r(i.container||document.body),(t=r(document.createElement("div"))).attr("id","rt_"+this.uid),t.css({position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.append(t),e.addClass("webuploader-container"),this._container=t)},init:t.noop,exec:t.noop,destroy:function(){this._container&&this._container.parentNode.removeChild(this.__container),this.off()}}),s.orders="html5,flash",s.addRuntime=function(e,t){o[e]=t},s.hasRuntime=function(e){return!!(e?o[e]:n(o))},s.create=function(e,t){var i;if(t=t||s.orders,r.each(t.split(/\s*,\s*/g),function(){if(o[this])return i=this,!1}),!(i=i||n(o)))throw new Error("Runtime Error");return new o[i](e)},e.installTo(s.prototype),s}),t("runtime/client",["base","mediator","runtime/runtime"],function(o,e,s){var u,n;function t(t,i){var n,e,r=o.Deferred();this.uid=o.guid("client_"),this.runtimeReady=function(e){return r.done(e)},this.connectRuntime=function(e,t){if(n)throw new Error("already connected!");return r.done(t),"string"==typeof e&&u.get(e)&&(n=u.get(e)),(n=n||u.get(null,i))?(o.$.extend(n.options,e),n.__promise.then(r.resolve),n.__client++):((n=s.create(e,e.runtimeOrder)).__promise=r.promise(),n.once("ready",r.resolve),n.init(),u.add(n),n.__client=1),i&&(n.__standalone=i),n},this.getRuntime=function(){return n},this.disconnectRuntime=function(){n&&(n.__client--,n.__client<=0&&(u.remove(n),delete n.__promise,n.destroy()),n=null)},this.exec=function(){if(n){var e=o.slice(arguments);return t&&e.unshift(t),n.exec.apply(this,e)}},this.getRuid=function(){return n&&n.uid},this.destroy=(e=this.destroy,function(){e&&e.apply(this,arguments),this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()})}return n={},u={add:function(e){n[e.uid]=e},get:function(e,t){var i;if(e)return n[e];for(i in n)if(!t||!n[i].__standalone)return n[i];return null},remove:function(e){delete n[e.uid]}},e.installTo(t.prototype),t}),t("lib/blob",["base","runtime/client"],function(e,i){function t(e,t){this.source=t,this.ruid=e,i.call(this,"Blob"),this.uid=t.uid||this.uid,this.type=t.type||"",this.size=t.size||0,e&&this.connectRuntime(e)}return e.inherits(i,{constructor:t,slice:function(e,t){return this.exec("slice",e,t)},getSource:function(){return this.source}}),t}),t("lib/file",["base","lib/blob"],function(e,n){var r=1,o=/\.([^.]+)$/;return e.inherits(n,function(e,t){var i;n.apply(this,arguments),this.name=t.name||"untitled"+r++,!(i=o.exec(t.name)?RegExp.$1.toLowerCase():"")&&this.type&&(i=/\/(jpg|jpeg|png|gif|bmp)$/i.exec(this.type)?RegExp.$1.toLowerCase():"",this.name+="."+i),!this.type&&~"jpg,jpeg,png,gif,bmp".indexOf(i)&&(this.type="image/"+("jpg"===i?"jpeg":i)),this.ext=i,this.lastModifiedDate=t.lastModifiedDate||(new Date).toLocaleString()})}),t("lib/filepicker",["base","runtime/client","lib/file"],function(e,t,o){var s=e.$;function i(e){if((e=this.options=s.extend({},i.options,e)).container=s(e.id),!e.container.length)throw new Error("按钮指定错误");e.innerHTML=e.innerHTML||e.label||e.container.html()||"",e.button=s(e.button||document.createElement("div")),e.button.html(e.innerHTML),e.container.html(e.button),t.call(this,"FilePicker",!0)}return i.options={button:null,container:null,label:null,innerHTML:null,multiple:!0,accept:null,name:"file"},e.inherits(t,{constructor:i,init:function(){var i=this,n=i.options,r=n.button;r.addClass("webuploader-pick"),i.on("all",function(e){var t;switch(e){case"mouseenter":r.addClass("webuploader-pick-hover");break;case"mouseleave":r.removeClass("webuploader-pick-hover");break;case"change":t=i.exec("getFiles"),i.trigger("select",s.map(t,function(e){return(e=new o(i.getRuid(),e))._refer=n.container,e}),n.container)}}),i.connectRuntime(n,function(){i.refresh(),i.exec("init",n),i.trigger("ready")}),s(v).on("resize",function(){i.refresh()})},refresh:function(){var e=this.getRuntime().getContainer(),t=this.options.button,i=t.outerWidth?t.outerWidth():t.width(),n=t.outerHeight?t.outerHeight():t.height(),r=t.offset();i&&n&&e.css({bottom:"auto",right:"auto",width:i+"px",height:n+"px"}).offset(r)},enable:function(){this.options.button.removeClass("webuploader-pick-disable"),this.refresh()},disable:function(){var e=this.options.button;this.getRuntime().getContainer().css({top:"-99999px"}),e.addClass("webuploader-pick-disable")},destroy:function(){this.runtime&&(this.exec("destroy"),this.disconnectRuntime())}}),i}),t("widgets/widget",["base","uploader"],function(f,e){var h=f.$,t=e.prototype._init,p={},r=[];function o(e){this.owner=e,this.options=e.options}return h.extend(o.prototype,{init:f.noop,invoke:function(e,t){var i=this.responseMap;return i&&e in i&&i[e]in this&&h.isFunction(this[i[e]])?this[i[e]].apply(this,t):p},request:function(){return this.owner.request.apply(this.owner,arguments)}}),h.extend(e.prototype,{_init:function(){var i=this,n=i._widgets=[];return h.each(r,function(e,t){n.push(new t(i))}),t.apply(i,arguments)},request:function(e,t,i){var n,r,o,s=0,u=this._widgets,a=u.length,c=[],l=[];for(t=function(e){if(e){var t=e.length,i=h.type(e);return 1===e.nodeType&&t||"array"===i||"function"!==i&&"string"!==i&&(0===t||"number"==typeof t&&0<t&&t-1 in e)}}(t)?t:[t];s<a;s++)(n=u[s].invoke(e,t))!==p&&(f.isPromise(n)?l.push(n):c.push(n));return i||l.length?(r=f.when.apply(f,l))[o=r.pipe?"pipe":"then"](function(){var e=f.Deferred(),t=arguments;return setTimeout(function(){e.resolve.apply(e,t)},1),e.promise()})[o](i||f.noop):c[0]}}),e.register=o.register=function(e,t){var i,n={init:"init"};return 1===arguments.length?(t=e).responseMap=n:t.responseMap=h.extend(n,e),i=f.inherits(o,t),r.push(i),i},o}),t("widgets/filepicker",["base","uploader","lib/filepicker","widgets/widget"],function(u,e,a){var c=u.$;return c.extend(e.options,{pick:null,accept:null}),e.register({"add-btn":"addButton",refresh:"refresh",disable:"disable",enable:"enable"},{init:function(e){return this.pickers=[],e.pick&&this.addButton(e.pick)},refresh:function(){c.each(this.pickers,function(){this.refresh()})},addButton:function(e){var t,i,n,r=this,o=r.options,s=o.accept;if(e)return n=u.Deferred(),c.isPlainObject(e)||(e={id:e}),t=c.extend({},e,{accept:c.isPlainObject(s)?[s]:s,swf:o.swf,runtimeOrder:o.runtimeOrder}),(i=new a(t)).once("ready",n.resolve),i.on("select",function(e){r.owner.request("add-file",[e])}),i.init(),this.pickers.push(i),n.promise()},disable:function(){c.each(this.pickers,function(){this.disable()})},enable:function(){c.each(this.pickers,function(){this.enable()})}})}),t("lib/image",["base","runtime/client","lib/blob"],function(t,i,n){var r=t.$;function o(e){this.options=r.extend({},o.options,e),i.call(this,"Image"),this.on("load",function(){this._info=this.exec("info"),this._meta=this.exec("meta")})}return o.options={quality:90,crop:!1,preserveHeaders:!0,allowMagnify:!0},t.inherits(i,{constructor:o,info:function(e){return e?(this._info=e,this):this._info},meta:function(e){return e?(this._meta=e,this):this._meta},loadFromBlob:function(e){var t=this,i=e.getRuid();this.connectRuntime(i,function(){t.exec("init",t.options),t.exec("loadFromBlob",e)})},resize:function(){var e=t.slice(arguments);return this.exec.apply(this,["resize"].concat(e))},getAsDataUrl:function(e){return this.exec("getAsDataUrl",e)},getAsBlob:function(e){var t=this.exec("getAsBlob",e);return new n(this.getRuid(),t)}}),o}),t("widgets/image",["base","uploader","lib/image","widgets/widget"],function(t,e,s){var u,n,r,a=t.$;function o(){for(var e;r.length&&n<5242880;)e=r.shift(),n+=e[0],e[1]()}return n=0,r=[],u=function(e,t,i){r.push([t,i]),e.once("destroy",function(){n-=t,setTimeout(o,1)}),setTimeout(o,1)},a.extend(e.options,{thumb:{width:110,height:110,quality:70,allowMagnify:!0,crop:!0,preserveHeaders:!1,type:"image/jpeg"},compress:{width:1600,height:1600,quality:90,allowMagnify:!1,crop:!1,preserveHeaders:!0}}),e.register({"make-thumb":"makeThumb","before-send-file":"compressImage"},{makeThumb:function(e,t,i,n){var r,o;(e=this.request("get-file",e)).type.match(/^image/)?(r=a.extend({},this.options.thumb),a.isPlainObject(i)&&(r=a.extend(r,i),i=null),i=i||r.width,n=n||r.height,(o=new s(r)).once("load",function(){e._info=e._info||o.info(),e._meta=e._meta||o.meta(),o.resize(i,n)}),o.once("complete",function(){t(!1,o.getAsDataUrl(r.type)),o.destroy()}),o.once("error",function(){t(!0),o.destroy()}),u(o,e.source.size,function(){e._info&&o.info(e._info),e._meta&&o.meta(e._meta),o.loadFromBlob(e.source)})):t(!0)},compressImage:function(i){var n,r,o=this.options.compress||this.options.resize,e=o&&o.compressSize||307200;if(i=this.request("get-file",i),o&&~"image/jpeg,image/jpg".indexOf(i.type)&&!(i.size<e)&&!i._compressed)return o=a.extend({},o),r=t.Deferred(),n=new s(o),r.always(function(){n.destroy(),n=null}),n.once("error",r.reject),n.once("load",function(){i._info=i._info||n.info(),i._meta=i._meta||n.meta(),n.resize(o.width,o.height)}),n.once("complete",function(){var e,t;try{e=n.getAsBlob(o.type),t=i.size,e.size<t&&(i.source=e,i.size=e.size,i.trigger("resize",e.size,t)),i._compressed=!0,r.resolve()}catch(e){r.resolve()}}),i._info&&n.info(i._info),i._meta&&n.meta(i._meta),n.loadFromBlob(i.source),r.promise()}})}),t("file",["base","mediator"],function(e,t){var i=e.$,n="WU_FILE_",r=0,o=/\.([^.]+)$/,s={};function u(e){this.name=e.name||"Untitled",this.size=e.size||0,this.type=e.type||"application",this.lastModifiedDate=e.lastModifiedDate||+new Date,this.id=n+r++,this.ext=o.exec(this.name)?RegExp.$1:"",this.statusText="",s[this.id]=u.Status.INITED,this.source=e,this.loaded=0,this.on("error",function(e){this.setStatus(u.Status.ERROR,e)})}return i.extend(u.prototype,{setStatus:function(e,t){var i=s[this.id];void 0!==t&&(this.statusText=t),e!==i&&(s[this.id]=e,this.trigger("statuschange",e,i))},getStatus:function(){return s[this.id]},getSource:function(){return this.source},destory:function(){delete s[this.id]}}),t.installTo(u.prototype),u.Status={INITED:"inited",QUEUED:"queued",PROGRESS:"progress",ERROR:"error",COMPLETE:"complete",CANCELLED:"cancelled",INTERRUPT:"interrupt",INVALID:"invalid"},u}),t("queue",["base","mediator","file"],function(e,t,i){var o=e.$,r=i.Status;function n(){this.stats={numOfQueue:0,numOfSuccess:0,numOfCancel:0,numOfProgress:0,numOfUploadFailed:0,numOfInvalid:0},this._queue=[],this._map={}}return o.extend(n.prototype,{append:function(e){return this._queue.push(e),this._fileAdded(e),this},prepend:function(e){return this._queue.unshift(e),this._fileAdded(e),this},getFile:function(e){return"string"!=typeof e?e:this._map[e]},fetch:function(e){var t,i,n=this._queue.length;for(e=e||r.QUEUED,t=0;t<n;t++)if(e===(i=this._queue[t]).getStatus())return i;return null},sort:function(e){"function"==typeof e&&this._queue.sort(e)},getFiles:function(){for(var e,t=[].slice.call(arguments,0),i=[],n=0,r=this._queue.length;n<r;n++)e=this._queue[n],t.length&&!~o.inArray(e.getStatus(),t)||i.push(e);return i},_fileAdded:function(e){var i=this;this._map[e.id]||(this._map[e.id]=e).on("statuschange",function(e,t){i._onFileStatusChange(e,t)}),e.setStatus(r.QUEUED)},_onFileStatusChange:function(e,t){var i=this.stats;switch(t){case r.PROGRESS:i.numOfProgress--;break;case r.QUEUED:i.numOfQueue--;break;case r.ERROR:i.numOfUploadFailed--;break;case r.INVALID:i.numOfInvalid--}switch(e){case r.QUEUED:i.numOfQueue++;break;case r.PROGRESS:i.numOfProgress++;break;case r.ERROR:i.numOfUploadFailed++;break;case r.COMPLETE:i.numOfSuccess++;break;case r.CANCELLED:i.numOfCancel++;break;case r.INVALID:i.numOfInvalid++}}}),t.installTo(n.prototype),n}),t("widgets/queue",["base","uploader","queue","file","lib/file","runtime/client","widgets/widget"],function(c,e,l,t,i,f){var h=c.$,n=/\.\w+$/,o=t.Status;return e.register({"sort-files":"sortFiles","add-file":"addFiles","get-file":"getFile","fetch-file":"fetchFile","get-stats":"getStats","get-files":"getFiles","remove-file":"removeFile",retry:"retry",reset:"reset","accept-file":"acceptFile"},{init:function(e){var t,i,n,r,o,s,u,a=this;if(h.isPlainObject(e.accept)&&(e.accept=[e.accept]),e.accept){for(o=[],n=0,i=e.accept.length;n<i;n++)(r=e.accept[n].extensions)&&o.push(r);o.length&&(s="\\."+o.join(",").replace(/,/g,"$|\\.").replace(/\*/g,".*")+"$"),a.accept=new RegExp(s,"i")}if(a.queue=new l,a.stats=a.queue.stats,"html5"===this.request("predict-runtime-type"))return t=c.Deferred(),(u=new f("Placeholder")).connectRuntime({runtimeOrder:"html5"},function(){a._ruid=u.getRuid(),t.resolve()}),t.promise()},_wrapFile:function(e){if(!(e instanceof t)){if(!(e instanceof i)){if(!this._ruid)throw new Error("Can't add external files.");e=new i(this._ruid,e)}e=new t(e)}return e},acceptFile:function(e){return!(!e||e.size<6||this.accept&&n.exec(e.name)&&!this.accept.test(e.name))},_addFile:function(e){var t=this;if(e=t._wrapFile(e),t.owner.trigger("beforeFileQueued",e)){if(t.acceptFile(e))return t.queue.append(e),t.owner.trigger("fileQueued",e),e;t.owner.trigger("error","Q_TYPE_DENIED",e)}},getFile:function(e){return this.queue.getFile(e)},addFiles:function(e){var t=this;e.length||(e=[e]),e=h.map(e,function(e){return t._addFile(e)}),t.owner.trigger("filesQueued",e),t.options.auto&&t.request("start-upload")},getStats:function(){return this.stats},removeFile:function(e){(e=e.id?e:this.queue.getFile(e)).setStatus(o.CANCELLED),this.owner.trigger("fileDequeued",e)},getFiles:function(){return this.queue.getFiles.apply(this.queue,arguments)},fetchFile:function(){return this.queue.fetch.apply(this.queue,arguments)},retry:function(e,t){var i,n,r;if(e)return(e=e.id?e:this.queue.getFile(e)).setStatus(o.QUEUED),void(t||this.request("start-upload"));for(n=0,r=(i=this.queue.getFiles(o.ERROR)).length;n<r;n++)(e=i[n]).setStatus(o.QUEUED);this.request("start-upload")},sortFiles:function(){return this.queue.sort.apply(this.queue,arguments)},reset:function(){this.queue=new l,this.stats=this.queue.stats}})}),t("widgets/runtime",["uploader","runtime/runtime","widgets/widget"],function(e,r){return e.support=function(){return r.hasRuntime.apply(r,arguments)},e.register({"predict-runtime-type":"predictRuntmeType"},{init:function(){if(!this.predictRuntmeType())throw Error("Runtime Error")},predictRuntmeType:function(){var e,t,i=this.options.runtimeOrder||r.orders,n=this.type;if(!n)for(e=0,t=(i=i.split(/\s*,\s*/g)).length;e<t;e++)if(r.hasRuntime(i[e])){this.type=n=i[e];break}return n}})}),t("lib/transport",["base","runtime/client","mediator"],function(e,i,t){var n=e.$;function r(e){var t=this;e=t.options=n.extend(!0,{},r.options,e||{}),i.call(this,"Transport"),this._blob=null,this._formData=e.formData||{},this._headers=e.headers||{},this.on("progress",this._timeout),this.on("load error",function(){t.trigger("progress",1),clearTimeout(t._timer)})}return r.options={server:"",method:"POST",withCredentials:!1,fileVal:"file",timeout:12e4,formData:{},headers:{},sendAsBinary:!1},n.extend(r.prototype,{appendBlob:function(e,t,i){var n=this,r=n.options;n.getRuid()&&n.disconnectRuntime(),n.connectRuntime(t.ruid,function(){n.exec("init")}),n._blob=t,r.fileVal=e||r.fileVal,r.filename=i||r.filename},append:function(e,t){"object"==typeof e?n.extend(this._formData,e):this._formData[e]=t},setRequestHeader:function(e,t){"object"==typeof e?n.extend(this._headers,e):this._headers[e]=t},send:function(e){this.exec("send",e),this._timeout()},abort:function(){return clearTimeout(this._timer),this.exec("abort")},destroy:function(){this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()},getResponse:function(){return this.exec("getResponse")},getResponseAsJson:function(){return this.exec("getResponseAsJson")},getStatus:function(){return this.exec("getStatus")},_timeout:function(){var e=this,t=e.options.timeout;t&&(clearTimeout(e._timer),e._timer=setTimeout(function(){e.abort(),e.trigger("error","timeout")},t))}}),t.installTo(r.prototype),r}),t("widgets/upload",["base","uploader","file","lib/transport","widgets/widget"],function(l,e,t,f){var h=l.$,o=l.isPromise,p=t.Status;h.extend(e.options,{prepareNextFile:!1,chunked:!1,chunkSize:5242880,chunkRetry:2,threads:3,formData:null}),e.register({"start-upload":"start","stop-upload":"stop","skip-file":"skipFile","is-in-progress":"isInProgress"},{init:function(){var e=this.owner;this.runing=!1,this.pool=[],this.pending=[],this.remaning=0,this.__tick=l.bindFn(this._tick,this),e.on("uploadComplete",function(e){e.blocks&&h.each(e.blocks,function(e,t){t.transport&&(t.transport.abort(),t.transport.destroy()),delete t.transport}),delete e.blocks,delete e.remaning})},start:function(){var n=this;h.each(n.request("get-files",p.INVALID),function(){n.request("remove-file",this)}),n.runing||(n.runing=!0,h.each(n.pool,function(e,t){var i=t.file;i.getStatus()===p.INTERRUPT&&(i.setStatus(p.PROGRESS),n._trigged=!1,t.transport&&t.transport.send())}),n._trigged=!1,n.owner.trigger("startUpload"),l.nextTick(n.__tick))},stop:function(e){!1!==this.runing&&(this.runing=!1,e&&h.each(this.pool,function(e,t){t.transport&&t.transport.abort(),t.file.setStatus(p.INTERRUPT)}),this.owner.trigger("stopUpload"))},isInProgress:function(){return!!this.runing},getStats:function(){return this.request("get-stats")},skipFile:function(e,t){(e=this.request("get-file",e)).setStatus(t||p.COMPLETE),e.skipped=!0,e.blocks&&h.each(e.blocks,function(e,t){var i=t.transport;i&&(i.abort(),i.destroy(),delete t.transport)}),this.owner.trigger("uploadSkip",e)},_tick:function(){var e,t,i=this,n=i.options;if(i._promise)return i._promise.always(i.__tick);i.pool.length<n.threads&&(t=i._nextBlock())?(i._trigged=!1,e=function(e){i._promise=null,e&&e.file&&i._startSend(e),l.nextTick(i.__tick)},i._promise=o(t)?t.always(e):e(t)):i.remaning||i.getStats().numOfQueue||(i.runing=!1,i._trigged||l.nextTick(function(){i.owner.trigger("uploadFinished")}),i._trigged=!0)},_nextBlock:function(){var e,t,i=this,n=i._act,r=i.options;return n&&n.has()&&n.file.getStatus()===p.PROGRESS?(r.prepareNextFile&&!i.pending.length&&i._prepareNextFile(),n.fetch()):i.runing?(!i.pending.length&&i.getStats().numOfQueue&&i._prepareNextFile(),e=i.pending.shift(),t=function(e){return e?(n=function(e,t){for(var i,n=[],r=e.source.size,o=t?Math.ceil(r/t):1,s=0,u=0;u<o;)i=Math.min(t,r-s),n.push({file:e,start:s,end:t?s+i:r,total:r,chunks:o,chunk:u++}),s+=i;return e.blocks=n.concat(),e.remaning=n.length,{file:e,has:function(){return!!n.length},fetch:function(){return n.shift()}}}(e,r.chunked?r.chunkSize:0),(i._act=n).fetch()):null},o(e)?e[e.pipe?"pipe":"then"](t):t(e)):void 0},_prepareNextFile:function(){var t,i=this,n=i.request("fetch-file"),r=i.pending;n&&((t=i.request("before-send-file",n,function(){return n.getStatus()===p.QUEUED?(i.owner.trigger("uploadStart",n),n.setStatus(p.PROGRESS),n):i._finishFile(n)})).done(function(){var e=h.inArray(t,r);~e&&r.splice(e,1,n)}),t.fail(function(e){n.setStatus(p.ERROR,e),i.owner.trigger("uploadError",n,e),i.owner.trigger("uploadComplete",n)}),r.push(t))},_popBlock:function(e){var t=h.inArray(e,this.pool);this.pool.splice(t,1),e.file.remaning--,this.remaning--},_startSend:function(e){var t=this,i=e.file;t.pool.push(e),t.remaning++,e.blob=1===e.chunks?i.source:i.source.slice(e.start,e.end),t.request("before-send",e,function(){i.getStatus()===p.PROGRESS?t._doSend(e):(t._popBlock(e),l.nextTick(t.__tick))}).fail(function(){1===i.remaning?t._finishFile(i).always(function(){e.percentage=1,t._popBlock(e),t.owner.trigger("uploadComplete",i),l.nextTick(t.__tick)}):(e.percentage=1,t._popBlock(e),l.nextTick(t.__tick))})},_doSend:function(n){var i,r,t=this,o=t.owner,s=t.options,u=n.file,a=new f(s),e=h.extend({},s.formData),c=h.extend({},s.headers);(n.transport=a).on("destroy",function(){delete n.transport,t._popBlock(n),l.nextTick(t.__tick)}),a.on("progress",function(e){var t=0,i=0;t=n.percentage=e,1<n.chunks&&(h.each(u.blocks,function(e,t){i+=(t.percentage||0)*(t.end-t.start)}),t=i/u.size),o.trigger("uploadProgress",u,t||0)}),i=function(t){var e;return(r=a.getResponseAsJson()||{})._raw=a.getResponse(),e=function(e){t=e},o.trigger("uploadAccept",n,r,e)||(t=t||"server"),t},a.on("error",function(e,t){n.retried=n.retried||0,1<n.chunks&&~"http,abort".indexOf(e)&&n.retried<s.chunkRetry?(n.retried++,a.send()):(t||"server"!==e||(e=i(e)),u.setStatus(p.ERROR,e),o.trigger("uploadError",u,e),o.trigger("uploadComplete",u))}),a.on("load",function(){var e;(e=i())?a.trigger("error",e,!0):1===u.remaning?t._finishFile(u,r):a.destroy()}),e=h.extend(e,{id:u.id,name:u.name,type:u.type,lastModifiedDate:u.lastModifiedDate,size:u.size}),1<n.chunks&&h.extend(e,{chunks:n.chunks,chunk:n.chunk}),o.trigger("uploadBeforeSend",n,e,c),a.appendBlob(s.fileVal,n.blob,u.name),a.append(e),a.setRequestHeader(c),a.send()},_finishFile:function(t,e,i){var n=this.owner;return n.request("after-send-file",arguments,function(){t.setStatus(p.COMPLETE),n.trigger("uploadSuccess",t,e,i)}).fail(function(e){t.getStatus()===p.PROGRESS&&t.setStatus(p.ERROR,e),n.trigger("uploadError",t,e)}).always(function(){n.trigger("uploadComplete",t)})}})}),t("widgets/validator",["base","uploader","file","widgets/widget"],function(e,t,i){var n,r=e.$,o={};return n={addValidator:function(e,t){o[e]=t},removeValidator:function(e){delete o[e]}},t.register({init:function(){var e=this;r.each(o,function(){this.call(e.owner)})}}),n.addValidator("fileNumLimit",function(){var e=this,t=e.options,i=0,n=t.fileNumLimit>>0,r=!0;n&&(e.on("beforeFileQueued",function(e){return n<=i&&r&&(r=!1,this.trigger("error","Q_EXCEED_NUM_LIMIT",n,e),setTimeout(function(){r=!0},1)),!(n<=i)}),e.on("fileQueued",function(){i++}),e.on("fileDequeued",function(){i--}),e.on("uploadFinished",function(){i=0}))}),n.addValidator("fileSizeLimit",function(){var e=this,t=e.options,i=0,n=t.fileSizeLimit>>0,r=!0;n&&(e.on("beforeFileQueued",function(e){var t=i+e.size>n;return t&&r&&(r=!1,this.trigger("error","Q_EXCEED_SIZE_LIMIT",n,e),setTimeout(function(){r=!0},1)),!t}),e.on("fileQueued",function(e){i+=e.size}),e.on("fileDequeued",function(e){i-=e.size}),e.on("uploadFinished",function(){i=0}))}),n.addValidator("fileSingleSizeLimit",function(){var t=this.options.fileSingleSizeLimit;t&&this.on("beforeFileQueued",function(e){if(e.size>t)return e.setStatus(i.Status.INVALID,"exceed_size"),this.trigger("error","F_EXCEED_SIZE",e),!1})}),n.addValidator("duplicate",function(){var e=this.options,i={};e.duplicate||(this.on("beforeFileQueued",function(e){var t=e.__hash||(e.__hash=function(e){for(var t=0,i=0,n=e.length;i<n;i++)t=e.charCodeAt(i)+(t<<6)+(t<<16)-t;return t}(e.name+e.size+e.lastModifiedDate));if(i[t])return this.trigger("error","F_DUPLICATE",e),!1}),this.on("fileQueued",function(e){var t=e.__hash;t&&(i[t]=!0)}),this.on("fileDequeued",function(e){var t=e.__hash;t&&delete i[t]}))}),n}),t("runtime/compbase",[],function(){return function(e,t){this.owner=e,this.options=e.options,this.getRuntime=function(){return t},this.getRuid=function(){return t.uid},this.trigger=function(){return e.trigger.apply(e,arguments)}}}),t("runtime/flash/runtime",["base","runtime/runtime","runtime/compbase"],function(a,i,n){var r=a.$,c={};function e(){var o={},s={},e=this.destory,u=this,t=a.guid("webuploader_");i.apply(u,arguments),u.type="flash",u.exec=function(e,t){var i,n=this.uid,r=a.slice(arguments,2);return s[n]=this,c[e]&&(o[n]||(o[n]=new c[e](this,u)),(i=o[n])[t])?i[t].apply(i,r):u.flashExec.apply(this,arguments)},v[t]=function(){var e=arguments;setTimeout(function(){(function(e,t){var i,n,r=e.type||e;n=(i=r.split("::"))[0],"Ready"===(r=i[1])&&n===u.uid?u.trigger("ready"):s[n]&&s[n].trigger(r.toLowerCase(),e,t)}).apply(null,e)},1)},this.jsreciver=t,this.destory=function(){return e&&e.apply(this,arguments)},this.flashExec=function(e,t){var i=u.getFlash(),n=a.slice(arguments,2);return i.exec(this.uid,e,t,n)}}return a.inherits(i,{constructor:e,init:function(){var e,t=this.getContainer(),i=this.options;t.css({position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),e='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+i.swf+'" ',a.browser.ie&&(e+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),e+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+i.swf+'" /><param name="flashvars" value="uid='+this.uid+"&jsreciver="+this.jsreciver+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>',t.html(e)},getFlash:function(){return this._flash||(this._flash=r("#"+this.uid).get(0)),this._flash}}),e.register=function(e,t){return t=c[e]=a.inherits(n,r.extend({flashExec:function(){var e=this.owner;return this.getRuntime().flashExec.apply(e,arguments)}},t))},11.4<=function(){var t;try{t=(t=navigator.plugins["Shockwave Flash"]).description}catch(e){try{t=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(e){t="0.0"}}return t=t.match(/\d+/g),parseFloat(t[0]+"."+t[1],10)}()&&i.addRuntime("flash",e),e}),t("runtime/flash/filepicker",["base","runtime/flash/runtime"],function(e,t){var r=e.$;return t.register("FilePicker",{init:function(e){var t,i,n=r.extend({},e);for(t=n.accept&&n.accept.length,i=0;i<t;i++)n.accept[i].title||(n.accept[i].title="Files");delete n.button,delete n.container,this.flashExec("FilePicker","init",n)},destroy:function(){}})}),t("runtime/flash/image",["runtime/flash/runtime"],function(e){return e.register("Image",{loadFromBlob:function(e){var t=this.owner;t.info()&&this.flashExec("Image","info",t.info()),t.meta()&&this.flashExec("Image","meta",t.meta()),this.flashExec("Image","loadFromBlob",e.uid)}})}),t("runtime/flash/transport",["base","runtime/flash/runtime","runtime/client"],function(e,t,r){var s=e.$;return t.register("Transport",{init:function(){this._status=0,this._response=null,this._responseJson=null},send:function(){var e,t=this.owner,i=this.options,n=this._initAjax(),r=t._blob,o=i.server;n.connectRuntime(r.ruid),i.sendAsBinary?(o+=(/\?/.test(o)?"&":"?")+s.param(t._formData),e=r.uid):(s.each(t._formData,function(e,t){n.exec("append",e,t)}),n.exec("appendBlob",i.fileVal,r.uid,i.filename||t._formData.name||"")),this._setRequestHeader(n,i.headers),n.exec("send",{method:i.method,url:o},e)},getStatus:function(){return this._status},getResponse:function(){return this._response},getResponseAsJson:function(){return this._responseJson},abort:function(){var e=this._xhr;e&&(e.exec("abort"),e.destroy(),this._xhr=e=null)},destroy:function(){this.abort()},_initAjax:function(){var i=this,n=new r("XMLHttpRequest");return n.on("uploadprogress progress",function(e){return i.trigger("progress",e.loaded/e.total)}),n.on("load",function(){var e=n.exec("getStatus"),t="";return n.off(),i._xhr=null,200<=e&&e<300?(i._response=n.exec("getResponse"),i._responseJson=n.exec("getResponseAsJson")):t=500<=e&&e<600?(i._response=n.exec("getResponse"),i._responseJson=n.exec("getResponseAsJson"),"server"):"http",n.destroy(),n=null,t?i.trigger("error",t):i.trigger("load")}),n.on("error",function(){n.off(),i._xhr=null,i.trigger("error","http")}),i._xhr=n},_setRequestHeader:function(i,e){s.each(e,function(e,t){i.exec("setRequestHeader",e,t)})}})}),t("preset/flashonly",["base","widgets/filepicker","widgets/image","widgets/queue","widgets/runtime","widgets/upload","widgets/validator","runtime/flash/filepicker","runtime/flash/image","runtime/flash/transport"],function(e){return e}),t("webuploader",["preset/flashonly"],function(e){return e}),n("webuploader"));!function(e){var t,i,n,r,o,s;for(t in s=function(e){return e&&e.charAt(0).toUpperCase()+e.substr(1)},u)if(i=e,u.hasOwnProperty(t)){for(o=s((n=t.split("/")).pop());r=s(n.shift());)i[r]=i[r]||{},i=i[r];i[o]=u[t]}}(a),"object"==typeof module&&"object"==typeof module.exports?module.exports=a:"function"==typeof define&&define.amd?define([],a):(e=i.WebUploader,i.WebUploader=a,i.WebUploader.noConflict=function(){i.WebUploader=e})}(this);