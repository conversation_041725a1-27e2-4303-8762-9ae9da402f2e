<template>
  <div>
    <el-table ref="tableRef" :data="tableData" border style="width: 100%" :empty-text="tableData.length == 0 ? `暂无${typeText}词库，可点击左上角下载模板填充后进行上传` : ''">
      <el-table-column type="index" label="序号" width="60">
        <template slot-scope="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>

      <!-- 动态列 -->
      <el-table-column v-for="col in columns" :key="col.prop" :prop="col.prop" :label="col.label" :width="col.width" :align="col.align || 'left'">
        <template slot-scope="scope">
          <!-- 编辑状态 -->
          <template v-if="scope.row._editing">
            <el-select
              v-if="col.type === 'select'"
              :value="col.prop === 'grade' && (!scope.row[col.prop] || scope.row[col.prop] === 0) ? undefined : scope.row[col.prop]"
              @input="
                (val) => {
                  scope.row[col.prop] = val;
                  handleBlur(scope.row);
                }
              "
              placeholder="请选择"
              size="mini"
            >
              <el-option v-for="item in col.options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-input v-else-if="col.type === 'textarea'" type="textarea" v-model="scope.row[col.prop]" :rows="2" @blur="handleBlur(scope.row)" />
            <el-input v-else v-model="scope.row[col.prop]" size="mini" @blur="handleBlur(scope.row)" />
          </template>
          <!-- 非编辑状态 -->
          <template v-else>
            <span class="cell-text">
              <!-- 年级和组别为0时不展示 -->
              <template v-if="(col.prop === 'grade' || col.prop === 'group') && (!scope.row[col.prop] || scope.row[col.prop] === 0)">
                <!-- 不显示内容 -->
              </template>
              <template v-else>
                {{ col.type === 'select' ? col.options?.find((opt) => opt.value === scope.row[col.prop])?.label || scope.row[col.prop] : scope.row[col.prop] }}
              </template>
            </span>
          </template>
        </template>
      </el-table-column>

      <!-- 固定列：系统发音 / 音频 / 更新时间 / 操作 -->
      <el-table-column label="系统发音" width="80" align="center">
        <template slot-scope="scope">
          <i v-if="scope.row.word" class="el-icon-microphone" style="font-size: 26px" @click="playAudio(scope.row.systemVoice, 'system', scope.row.id)"></i>
        </template>
      </el-table-column>

      <el-table-column label="音频" width="160">
        <template slot-scope="scope">
          <div style="display: flex; justify-content: space-around; align-items: center">
            <div style="position: relative" class="audio_container" v-if="scope.row.uploadVoice">
              <i class="el-icon-microphone" style="font-size: 26px" @click="playAudio(scope.row.uploadVoice, 'upload', scope.row.id)"></i>
              <i class="el-icon-close" style="position: absolute; right: -6px" @click="removeAudio(scope.row.uploadVoice, scope.row)"></i>
            </div>
            <div v-if="scope.row.uploadVoice">|</div>
            <el-upload
              ref="uploadAudio"
              class="upload-demo"
              :disabled="scope.row.uploadVoice != ''"
              :show-file-list="false"
              :file-list="audioFileList"
              action=""
              :http-request="audioUploadHttpRequest"
              :data="{ rowIndex: scope.$index, rowId: scope.row.id, row: scope.row }"
              :on-success="handleWordAudioSuccess"
              :accept="'.mp3,.wav,.flac,.alac,.aac,.amr,.opus,.aiff,.m4a'"
            >
              <div class="el-upload__text">
                <el-button size="small" type="primary" :disabled="scope.row.uploadVoice != ''">
                  <i class="el-icon-plus"></i>
                  上传
                </el-button>
              </div>
            </el-upload>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="updateTime" label="更新时间" width="138" />

      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <div style="display: flex; justify-content: space-around; align-items: center" v-if="scope.row.word">
            <el-button type="text" size="mini" @click="editRow(scope.$index)">
              <i class="el-icon-edit-outline"></i>
            </el-button>
            <el-button type="text" size="mini" @click="deleteRow(scope.row.id)">
              <i class="el-icon-delete"></i>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加单词按钮 -->
    <div style="text-align: center; margin-top: 10px">
      <el-button type="text" @click="addRow" :disabled="isAddRow">+ 添加单词</el-button>
    </div>

    <!-- 分页器 -->
    <div style="margin-top: 20px; text-align: right">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
  import { ossPrClient } from '@/api/alibaba';
  import spellStrongThesaurusAPI from '@/api/course/spellStrongThesaurus';
  export default {
    name: 'ReusableTable',
    props: {
      value: {
        type: Array,
        default: () => []
      },
      columns: {
        type: Array,
        default: () => []
      },
      type: {
        type: Number,
        default: 1 // 默认类型
      },
      total: {
        type: Number,
        default: 0 // 总条数
      },
      currentPage: {
        type: Number,
        default: 1 // 当前页
      },
      pageSize: {
        type: Number,
        default: 10 // 每页条数
      }
    },
    computed: {
      typeText() {
        const typeMap = {
          2: '校内',
          3: '小学考纲',
          4: '初中考纲',
          default: '同步'
        };
        return typeMap[this.type] || typeMap.default;
      }
    },
    data() {
      return {
        tableData: this.value.map((row) => ({ ...row, _editing: false })),
        editCache: null, // 编辑前的缓存数据
        editingIndex: null, // 当前编辑行索引
        loading: false, // 是否正在加载更多数据（已废弃）
        audioElement: null, // 音频元素
        audioFileList: [], // 上传的音频文件列表
        isAddRow: false // 是否允许添加新行
      };
    },
    watch: {
      value(newVal) {
        this.tableData = newVal.map((row) => ({ ...row, _editing: false }));
      }
    },
    methods: {
      // 新增一行空数据
      addRow() {
        const newRow = {};
        this.columns.forEach((col) => {
          newRow[col.prop] = '';
        });
        newRow._editing = true;
        newRow._isAdd = true; // 标记为新增行
        newRow.uploadVoice = '';
        newRow.systemVoice = '';
        this.$emit('update:currentPage', Math.ceil((this.total + 1) / this.pageSize));
        console.log(`新增一行空数据`, this.currentPage, this.total, this.pageSize, Math.ceil((this.total + 1) / this.pageSize));
        setTimeout(() => {
          this.tableData.push(newRow);
          this.editingIndex = this.tableData.length - 1;
          this.editCache = { ...newRow };
        }, 800);
        this.isAddRow = true; // 添加后禁用添加按钮，直到保存或取消
      },
      // 删除行
      deleteRow(id) {
        this.$emit('delete-row', id);
      },
      // 编辑行
      editRow(index) {
        this.tableData.forEach((row, i) => {
          row._editing = i === index;
        });
        this.editingIndex = index;
        this.editCache = { ...this.tableData[index] };
      },
      // 保存行
      saveRow(index) {
        const row = { ...this.tableData[index] };
        row._editing = false;

        // ⭐ emit 给父组件，父组件自己调接口 & 刷新数据
        this.$emit('update-row', row);

        this.editingIndex = null;
        this.editCache = null;
      },
      // 失去焦点时弹窗确认
      handleBlur(row) {
        if (row._isAdd) {
          if (row.word) {
            row._isAdd = false;
            row._editing = false;
            // 调用添加单词接口
            this.$emit('add-row', row);
            this.isAddRow = false; // 保存后启用添加按钮
          }
          return;
        }
        if (this._blurHandling) return;
        this._blurHandling = true;
        this.$confirm('是否确认保存修改？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            if (this.editingIndex !== null) {
              this.saveRow(this.editingIndex);
            }
            this._blurHandling = false;
          })
          .catch(() => {
            if (this.editingIndex !== null && this.editCache) {
              this.$set(this.tableData, this.editingIndex, { ...this.editCache, _editing: false });
            }
            this.editingIndex = null;
            this.editCache = null;
            this._blurHandling = false;
          });
      },

      // 分页器相关
      handleCurrentChange(page) {
        this.isAddRow = false; // 切换页码时重置
        this.$emit('page-change', { page, pageSize: this.pageSize });
      },
      handleSizeChange(size) {
        this.isAddRow = false; // 切换每页条数时重置
        this.$emit('page-change', { page: 1, pageSize: size });
      },

      // 音频上传
      async audioUploadHttpRequest({ file, data }) {
        const that = this;
        const fileName = `manage/${Date.parse(new Date())}${file.name}`;
        console.log(`阿里云OSS上传词库文件名`, fileName);
        console.log(`阿里云OSS上传词库文件名`, file.type);
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, file, res, url, name);
                // 上传成功后，修改当前行的音频地址
                if (data.rowIndex !== null && that.tableData[data.rowIndex]) {
                  that.$set(that.tableData[data.rowIndex], 'uploadVoice', url);
                  // emit 整条数据给父组件，父组件调接口保存
                  if (that.tableData[data.rowIndex].word) {
                    that.$emit('update-row', that.tableData[data.rowIndex]);
                  }
                }
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      handleWordAudioSuccess() {
        console.log(`上传成功回调`);
      },
      // 播放音频
      playAudio(audioUrl, type, id) {
        if (type === 'system') {
          spellStrongThesaurusAPI.thesaurusWordDetail({ id }).then((res) => {
            console.log(`thesaurusWordDetail`, res);
            if (res.code === 20000) {
              if (res.data.systemVoiceStatus === 1) {
                this.$message.warning('该单词系统发音生成失败');
                return;
              } else if (res.data.systemVoice === '') {
                this.$message.info('该单词系统发音生成中，请稍后再试');
                return;
              }
              if (res.data.systemVoice !== audioUrl) {
                audioUrl = res.data.systemVoice;
                // this.$emit('page-change', { page: this.currentPage, pageSize: this.pageSize });
              }
              if (this.audioElement) {
                this.audioElement.src = audioUrl;
                this.audioElement.play().catch((error) => {
                  // this.$message.warning('音频播放失败，请检查音频是否损害');
                  console.error(audioUrl, '播放失败1:', error);
                });
              }
            }
          });
        } else {
          if (!audioUrl) {
            this.$message.warning('该单词暂无音频');
            return;
          }
          if (this.audioElement) {
            this.audioElement.src = audioUrl;
            this.audioElement.play().catch((error) => {
              console.error(audioUrl, '播放失败2:', error);
              this.$message.warning('音频播放失败，请检查音频是否受损');
            });
          }
        }
      },
      removeAudio(audioUrl, row) {
        this.$confirm('确定是否需要删除此音频？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            console.log(`删除音频`, audioUrl, row);
            // return;
            const that = this;
            // 删除音频后，修改当前行的音频地址为空
            that.$set(row, 'uploadVoice', '');
            // emit 整条数据给父组件，父组件调接口保存
            if (row.word) {
              that.$emit('update-row', row, 'delete');
            }
          })
          .catch(() => {
            // 取消删除
            this.$message({
              message: '删除音频失败',
              type: 'info'
            });
          });
      }
    },
    mounted() {
      this.audioElement = new Audio();
      this.audioElement.src = 'http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1736251666000_1b9KaIpanda.wav';
    },
    beforeDestroy() {
      // 滚动监听已经删除，不需要解绑
    }
  };
</script>

<style scoped>
  .cell-text {
    white-space: pre-wrap; /* 保留换行和空格 */
    word-break: break-word; /* 自动换行 */
  }
  .audio_container .el-icon-close {
    display: none;
  }
  .audio_container:hover .el-icon-close {
    display: inline-block;
    cursor: pointer;
  }
</style>
