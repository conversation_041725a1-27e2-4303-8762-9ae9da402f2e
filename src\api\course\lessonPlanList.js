import request from '@/utils/request'
export function getLessonPlanList(data) {
  return request({
    url: `znyy/wap/lessonPlanManager/list/${data.pageNum}/${data.pageSize}`,
    method: 'GET',
    params: data
  })
}
export function addLessonPlan(data) {
  return request({
    url: 'znyy/wap/lessonPlanManager/add',
    method: 'PUT',
    data
  })
}
export function editLessonPlan(data) {
  return request({
    url: 'znyy/wap/lessonPlanManager/edit',
    method: 'POST',
    data
  })
}
export function deleteLessonPlan(data) {
  return request({
    url: 'znyy/wap/lessonPlanManager/delete',
    method: 'DELETE',
    params: data
  })
}