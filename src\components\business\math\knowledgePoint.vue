<template>
  <div>
    <el-row>
      <el-col :span="6" :offset="0">
        <el-tree ref="tree" :data="categoryTreeData" :props="propsTree" @node-click="categoryClick" v-loading="treeLoading">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{ node.label }}</span>
            <span v-if="data.number">{{ data.number }}</span>
          </span>
        </el-tree>
      </el-col>
      <el-col :span="18" :offset="0">
        <el-row>
          <el-col :span="20" :offset="4"></el-col>
          <el-col :span="4" :offset="0">
            <el-checkbox v-model="allCheck" @change="allclick">全选</el-checkbox>
          </el-col>
        </el-row>
        <el-table v-loading="tableLoading" :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" row-key="id" :reserve-selection="true">
          <el-table-column type="selection" :reserve-selection="true" width="55"></el-table-column>
          <el-table-column type="index" label="序号" center></el-table-column>
          <el-table-column label="知识点" center prop="knowledgeName"></el-table-column>
        </el-table>
        <el-pagination @current-change="handlePageChange" :current-page="tablePage.currentPage" :page-size="tablePage.pageSize" :total="tablePage.totalItems"></el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import forStudent from '@/api/testPaper/management';
  import { type } from 'jquery';
  export default {
    name: 'Knowledge-point',
    props: {
      // 课程id
      curriculumId: {
        type: String,
        default: '1356667161917542400'
      },
      // 知识点id
      knowledgePointIds: {
        type: Array,
        default: () => []
      },
      //学科id
      disciplineId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        tablePage: {
          currentPage: 1,
          pageSize: 10,
          totalItems: 0
        },
        tableLoading: false,
        allCheck: false,
        categoryTreeData: [],
        currentNodeKey: '', // 当前点击节点的id
        currentNodeLevel: '', // 当前点击节点的分类层级
        currentNodeData: {}, // 当前点击节点的数据
        total: 0,
        multipleSelection: [], // 所有选中的数据（跨页面保持）
        selectedIds: new Set(), // 选中的ID集合，用于快速查找
        isRestoringSelection: false, // 标记是否正在恢复选中状态
        list: [],
        yuAllcheck: [],
        tableData: [],
        data: [],
        temp: false,
        propsTree: {
          children: 'children',
          label: 'label'
        }
      };
    },
    created() {
      this.getCategoryTree();
      this.multipleSelection = [...this.knowledgePointIds];
      // 初始化选中的ID集合
      this.selectedIds = new Set(this.knowledgePointIds.map((item) => item.id));
    },
    methods: {
      // 全选
      async allclick(res) {
        this.isRestoringSelection = true;

        if (res) {
          // 全选当前分类下的所有数据
          this.yuAllcheck.forEach((item) => {
            if (!this.multipleSelection.some((selected) => selected.id === item.id)) {
              this.multipleSelection.push(item);
            }
          });
          // 更新选中ID集合
          this.selectedIds = new Set(this.multipleSelection.map((item) => item.id));
          this.$refs.multipleTable.toggleAllSelection();
        } else {
          // 取消选中当前分类下的所有数据
          const yuAllcheckIds = new Set(this.yuAllcheck.map((item) => item.id));
          this.multipleSelection = this.multipleSelection.filter((item) => !yuAllcheckIds.has(item.id));
          // 更新选中ID集合
          this.selectedIds = new Set(this.multipleSelection.map((item) => item.id));
          this.$refs.multipleTable.clearSelection();
        }

        await this.$nextTick();
        this.isRestoringSelection = false;
      },
      // 获取课程分类树
      getCategoryTree() {
        this.treeLoading = true;
        this.categoryTreeData = [];
        const that = this;
        // let params = { noCount: 0 };
        let data = {
          curriculumId: this.curriculumId,
          nodeLevel: 4
        };
        // 1365386256284794880
        forStudent
          .selectTree(data)
          .then((res) => {
            if (this.disciplineId) {
              //过滤学科
              let result = res.data.filter((item) => item.id == this.disciplineId);
              that.categoryTreeData = that.deepReplace(result);
            } else {
              that.categoryTreeData = that.deepReplace(res.data);
            }
            that.treeLoading = false;
          })
          .catch((err) => {
            that.treeLoading = false;
          });
      },

      // 点击分类节点
      categoryClick(data, node) {
        if (node.level == 3) {
          this.childrenId = node.childNodes[0].data.id;
          this.parentId = node.parent.data.id;
          this.moreParentId = node.parent.parent.data.id;
        } else if (node.level == 4) {
          this.parentId = node.parent.data.id;
          this.moreParentId = node.parent.parent.data.id;
          this.mostParentId = node.parent.parent.parent.data.id;
        }
        this.currentNodeKey = data.id;
        this.currentNodeLevel = data.nodeLevel;
        this.currentNodeData = data;
        this.tablePage.currentPage = 1;
        this.allCheck = false;
        this.allsubmitForm();
        this.submitForm();
      },
      // 查询
      allsubmitForm() {
        let data = { ...this.dataQuery };
        data.pageNum = this.tablePage.currentPage;
        data.pageSize = 10000;
        if (this.currentNodeData.nodeLevel == 1) {
          data.courseSubjectNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 2) {
          data.coursePeriodNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 3) {
          data.chapterNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 4) {
          data.knowledgeSummaryNodeId = this.currentNodeData.id;
        } else {
          data.courseSubjectNodeId = this.form.categoryCode;
        }
        forStudent
          .searchForKnowledge(data)
          .then((res) => {
            if (res.success) {
              this.yuAllcheck = res.data.data || [];
              // 更新全选状态
              this.updateAllCheckStatus();
            }
          })
          .catch((err) => {
            this.$message.error('查询失败，请重试！' + err);
          });
      },
      // 查询
      submitForm() {
        this.tableLoading = true;
        let data = { ...this.dataQuery };
        data.pageNum = this.tablePage.currentPage;
        data.pageSize = this.tablePage.pageSize;
        if (this.currentNodeData.nodeLevel == 1) {
          data.courseSubjectNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 2) {
          data.coursePeriodNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 3) {
          data.chapterNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 4) {
          data.knowledgeSummaryNodeId = this.currentNodeData.id;
        } else {
          data.courseSubjectNodeId = this.form.categoryCode;
        }
        forStudent
          .searchForKnowledge(data)
          .then((res) => {
            if (res.success) {
              this.tableData = res.data.data || [];
              this.tablePage.totalItems = Number(res.data.totalItems);
              this.tablePage.currentPage = Number(res.data.currentPage);

              // 恢复当前页面的选中状态
              this.$nextTick(() => {
                this.restoreSelection();
              });

              this.tableLoading = false;
            }
          })
          .catch((err) => {
            this.$message.error('查询失败，请重试！' + err);
            this.tableLoading = false;
          });
      },
      containsAllObjects(a, b) {
        const compareFn = (c, d) => c.id === d.id;
        return b.every((bItem) => a.some((aItem) => compareFn(aItem, bItem)));
      },
      deepReplace(array) {
        if (array instanceof Array && array.length >= 1) {
          return array.map((el) => {
            return {
              id: el.id,
              label: el.nodeName,
              children: this.deepReplace(el.childList),
              ...el
            };
          });
        } else {
          return [];
        }
      },
      handleSelectionChange(val) {
        // 防止在恢复选中状态时触发此方法导致数据混乱
        if (this.isRestoringSelection) {
          return;
        }

        // 获取当前页面的数据ID集合
        const currentPageIds = new Set(this.tableData.map((item) => item.id));

        // 移除当前页面中不再选中的项
        this.multipleSelection = this.multipleSelection.filter((item) => {
          // 如果不是当前页面的数据，保留
          if (!currentPageIds.has(item.id)) {
            return true;
          }
          // 如果是当前页面的数据，检查是否还在选中列表中
          return val.some((selectedItem) => selectedItem.id === item.id);
        });

        // 添加当前页面新选中的项
        val.forEach((item) => {
          if (!this.multipleSelection.some((selected) => selected.id === item.id)) {
            this.multipleSelection.push(item);
          }
        });

        // 更新选中ID集合
        this.selectedIds = new Set(this.multipleSelection.map((item) => item.id));

        // 更新全选状态
        this.updateAllCheckStatus();

        console.log('当前选中的数据:', this.multipleSelection);
      },
      uniqueByProp(arr, prop) {
        return arr.filter((obj, index, self) => index === self.findIndex((o) => o[prop] === obj[prop]));
      },
      handlePageChange(val) {
        this.tablePage.currentPage = val;
        this.submitForm();
      },

      // 恢复当前页面的选中状态
      restoreSelection() {
        if (this.tableData && this.tableData.length > 0) {
          this.isRestoringSelection = true;

          this.tableData.forEach((item) => {
            if (this.selectedIds.has(item.id)) {
              this.$refs.multipleTable.toggleRowSelection(item, true);
            } else {
              this.$refs.multipleTable.toggleRowSelection(item, false);
            }
          });

          // 延迟重置标志，确保所有选中状态都已恢复
          this.$nextTick(() => {
            this.isRestoringSelection = false;
          });
        }
      },

      // 更新全选状态
      updateAllCheckStatus() {
        if (this.yuAllcheck && this.yuAllcheck.length > 0) {
          // 检查当前分类下的所有数据是否都被选中
          const allSelected = this.yuAllcheck.every((item) => this.selectedIds.has(item.id));
          this.allCheck = allSelected;
        } else {
          this.allCheck = false;
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .custom-tree-node {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
</style>
