<template>
  <div>
    <el-table ref="tableRef" :data="tableData" border style="width: 100%" :empty-text="tableData.length == 0 ? `暂无${typeText}词库，可点击左上角下载模板填充后进行上传` : ''">
      <el-table-column type="index" label="序号" width="60">
        <template slot-scope="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>

      <!-- 动态列 -->
      <el-table-column v-for="col in columns" :key="col.prop" :prop="col.prop" :label="col.label" :width="col.width" :align="col.align || 'left'">
        <template slot-scope="scope">
          <template v-if="col.prop === 'errMsg'">
            <span class="cell-text" style="color: red">{{ scope.row[col.prop] || '' }}</span>
          </template>
          <!-- 编辑状态 -->
          <template v-else-if="scope.row._editing">
            <el-select
              v-if="col.type === 'select'"
              :value="col.prop === 'grade' && (!scope.row[col.prop] || scope.row[col.prop] === 0) ? undefined : scope.row[col.prop]"
              @input="
                (val) => {
                  scope.row[col.prop] = val;
                  handleBlur(scope.row);
                }
              "
              placeholder="请选择"
              size="mini"
            >
              <el-option v-for="item in col.options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-input v-else-if="col.type === 'textarea'" type="textarea" v-model="scope.row[col.prop]" :rows="2" @blur="handleBlur(scope.row)" />
            <el-input v-else v-model="scope.row[col.prop]" size="mini" @blur="handleBlur(scope.row)" />
          </template>
          <!-- 非编辑状态 -->
          <template v-else>
            <span class="cell-text" v-html="renderDisplayCell(scope.row, col)"></span>
          </template>
        </template>
      </el-table-column>

      <!-- 固定列：操作 -->

      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <div style="display: flex; justify-content: space-around; align-items: center">
            <el-button type="text" size="mini" @click="editRow(scope.$index)">
              <i class="el-icon-edit-outline"></i>
            </el-button>
            <!-- <el-button type="text" size="mini" @click="deleteRow(scope.row.id)">
              <i class="el-icon-delete"></i>
            </el-button> -->
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <!-- <div style="margin-top: 20px; text-align: right">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div> -->
  </div>
</template>

<script>
  import { ossPrClient } from '@/api/alibaba';
  import { renderFormulaAuto, escapeHtml } from '@/utils/formulaRenderer';
  export default {
    name: 'ReusableTable',
    props: {
      value: {
        type: Array,
        default: () => []
      },
      columns: {
        type: Array,
        default: () => []
      },
      type: {
        type: Number,
        default: 1 // 默认类型
      },
      total: {
        type: Number,
        default: 0 // 总条数
      },
      currentPage: {
        type: Number,
        default: 1 // 当前页
      },
      pageSize: {
        type: Number,
        default: 10 // 每页条数
      }
    },
    computed: {
      typeText() {
        const typeMap = {
          1: '课程',
          2: '视频',
          default: ''
        };
        return typeMap[this.type] || typeMap.default;
      }
    },
    data() {
      return {
        tableData: this.value.map((row, index) => ({
          ...row,
          _editing: false,
          _index: index // 添加索引标识，用于新行识别
        })),
        editCache: null, // 编辑前的缓存数据
        editingIndex: null, // 当前编辑行索引
        _formulaCache: Object.create(null) // 渲染缓存避免重复 KaTeX 计算
      };
    },
    watch: {
      value(newVal) {
        this.tableData = newVal.map((row, index) => ({
          ...row,
          _editing: false,
          _index: index
        }));
        // 数据源整体替换时清空公式缓存，防止旧 key 残留
        this._formulaCache = Object.create(null);
      }
    },
    methods: {
      // 删除行
      deleteRow(id) {
        this.$emit('delete-row', id);
      },
      // 仅展示态渲染公式：编辑态保持原文
      renderDisplayCell(row, col) {
        try {
          if (!row || !col) return '';
          const prop = col.prop;
          if (!prop) return '';
          const val = row[prop];
          if ((prop === 'grade' || prop === 'group') && (!val || val === 0)) return '';
          if (col.type === 'select') {
            const label = col.options?.find((opt) => opt.value === val)?.label || val || '';
            return escapeHtml(String(label));
          }
          // 题型描述、分值不进行公式渲染：
          if (prop === 'questionTypeDesc' || prop === 'score') {
            return escapeHtml(val == null ? '' : String(val));
          }
          if (val == null || val === '') return '';
          const key = prop + '::' + val;
          if (this._formulaCache[key]) return this._formulaCache[key];
          const html = renderFormulaAuto(String(val));
          this._formulaCache[key] = html;
          return html;
        } catch (e) {
          //直接回退原始文本转义
          try {
            return escapeHtml(String(row && col && row[col.prop] != null ? row[col.prop] : ''));
          } catch (_) {
            return '';
          }
        }
      },
      // 编辑行
      editRow(index) {
        this.tableData.forEach((row, i) => {
          row._editing = i === index;
        });
        this.editingIndex = index;
        this.editCache = { ...this.tableData[index] };
      },
      // 保存行
      saveRow(index) {
        const row = { ...this.tableData[index] };
        row._editing = false;

        // ⭐ emit 给父组件，父组件自己调接口 & 刷新数据
        this.$emit('update-row', row);

        this.editingIndex = null;
        this.editCache = null;
      },
      // 失去焦点时弹窗确认
      handleBlur(row) {
        if (row._isAdd) {
          if (row.questionText) {
            row._isAdd = false;
            row._editing = false;
            // 通知父组件添加新行
            this.$emit('update-row', row);
            this.$emit(
              'input',
              this.tableData.map(({ _editing, _index, ...rest }) => rest)
            );
          }
          return;
        }
        if (this._blurHandling) return;
        this._blurHandling = true;

        this.$confirm('是否确认保存修改？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            if (this.editingIndex !== null) {
              // 确认保存，通知父组件更新
              this.$emit('update-row', this.tableData[this.editingIndex]);
              // 同步更新表格数据
              this.$emit(
                'input',
                this.tableData.map(({ _editing, _index, ...rest }) => rest)
              );
              this.tableData[this.editingIndex]._editing = false;
            }
            this._blurHandling = false;
            this.editingIndex = null;
          })
          .catch(() => {
            // 取消修改，恢复原始数据
            if (this.editingIndex !== null && this.editCache) {
              this.$set(this.tableData, this.editingIndex, { ...this.editCache, _editing: false });
            }
            this.editingIndex = null;
            this.editCache = null;
            this._blurHandling = false;
          });
        // 编辑结束后清除该行相关缓存（避免旧值残留）
        if (this.editCache) {
          Object.keys(this.editCache).forEach((k) => {
            const oldVal = this.editCache[k];
            const cacheKey = k + '::' + oldVal;
            if (this._formulaCache[cacheKey]) delete this._formulaCache[cacheKey];
          });
        }
      },

      // 分页器相关
      handleCurrentChange(page) {
        this.$emit('page-change', { page, pageSize: this.pageSize });
      },
      handleSizeChange(size) {
        this.$emit('page-change', { page: 1, pageSize: size });
      },

      handleWordAudioSuccess() {
        console.log(`上传成功回调`);
      }
    },
    mounted() {
      this.audioElement = new Audio();
    },
    beforeDestroy() {
      // 滚动监听已经删除，不需要解绑
    }
  };
</script>

<style scoped>
  .cell-text {
    white-space: pre-wrap; /* 保留换行和空格 */
    word-break: break-word; /* 自动换行 */
  }
  .audio_container .el-icon-close {
    display: none;
  }
  .audio_container:hover .el-icon-close {
    display: inline-block;
    cursor: pointer;
  }
</style>
