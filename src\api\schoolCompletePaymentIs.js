/**
 * 门店完款相关接口
 */
import request from '@/utils/request';

export default {
  goPay(data) {
    return request({
      // url: '/znyy/school/page/' + pageNum + '/' + pageSize, // 旧
      url: '/znyy/V2/merchant/payment',
      method: 'get',
      params: data
    });
  },
  // 查看对应初始完款所需金额及系统数
  getSchoolCompletePaymentIs(params) {
    return request({
      url: '/znyy/V2/merchant/amountConfig',
      method: 'get',
      params
    });
  },
  // 查询学习系统管理合同签署状态
  getContractStatus(params) {
    return request({
      url: '/znyy/operations/v2/operationsPaymentQuery',
      method: 'get',
      params
    });
  },
  // 查看二维码及其状态
  fetchContractQrLink(data) {
    return request({
      url: '/znyy/sign/contract/qr-link',
      method: 'GET',
      params: data
    });
  },
  // 查询订单支付回调
  judgeOrderStatus(params) {
    return request({
      url: '/znyy/operations/v2/judgeOrderStatus',
      method: 'GET',
      params
    });
  }
};
