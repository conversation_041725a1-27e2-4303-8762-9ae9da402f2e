!function(){var f={r:function(t){if(f[t].inited)return f[t].value;if("function"!=typeof f[t].value)return f[t].inited=!0,f[t].value;var e={exports:{}},i=f[t].value(null,e.exports,e);if(f[t].inited=!0,void 0!==(f[t].value=i))return i;for(var n in e.exports)if(e.exports.hasOwnProperty(n))return f[t].inited=!0,f[t].value=e.exports,e.exports}};f[0]={value:function(t){function p(t){this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var u={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};for(var e in u)t==e&&(t=u[e]);for(var c=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<c.length;i++){var n=c[i].re,r=c[i].process,s=n.exec(t);s&&(channels=r(s),this.r=channels[0],this.g=channels[1],this.b=channels[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:255<this.r?255:this.r,this.g=this.g<0||isNaN(this.g)?0:255<this.g?255:this.g,this.b=this.b<0||isNaN(this.b)?0:255<this.b?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),i=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==i.length&&(i="0"+i),"#"+t+e+i},this.getHelpXML=function(){for(var t=new Array,e=0;e<c.length;e++)for(var i=c[e].example,n=0;n<i.length;n++)t[t.length]=i[n];for(var r in u)t[t.length]=r;var s=document.createElement("ul");s.setAttribute("id","rgbcolor-examples");for(e=0;e<t.length;e++)try{var a=document.createElement("li"),o=new p(t[e]),h=document.createElement("div");h.style.cssText="margin: 3px; border: 1px solid black; background:"+o.toHex()+"; color:"+o.toHex(),h.appendChild(document.createTextNode("test"));var l=document.createTextNode(" "+t[e]+" -> "+o.toRGB()+" -> "+o.toHex());a.appendChild(h),a.appendChild(l),s.appendChild(a)}catch(t){}return s}}var W=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],$=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function c(t,e,i,n,r,s){if(!(isNaN(s)||s<1)){s|=0;var a,o=document.getElementById(t).getContext("2d");try{try{a=o.getImageData(e,i,n,r)}catch(t){try{netscape.security.PrivilegeManager.enablePrivilege("UniversalBrowserRead"),a=o.getImageData(e,i,n,r)}catch(t){throw alert("Cannot access local image"),new Error("unable to access local image data: "+t)}}}catch(t){throw alert("Cannot access image"),new Error("unable to access image data: "+t)}var h,l,u,c,p,f,d,x,g,m,y,b,v,w,E,P,B,S,C,M,k,T,O,V,R=a.data,A=s+s+1,F=n-1,q=r-1,N=s+1,D=N*(N+1)/2,z=new j,I=z;for(u=1;u<A;u++)if(I=I.next=new j,u==N)var L=I;I.next=z;var H=null,U=null;d=f=0;var X=W[s],G=$[s];for(l=0;l<r;l++){for(P=B=S=C=x=g=m=y=0,b=N*(M=R[f]),v=N*(k=R[f+1]),w=N*(T=R[f+2]),E=N*(O=R[f+3]),x+=D*M,g+=D*k,m+=D*T,y+=D*O,I=z,u=0;u<N;u++)I.r=M,I.g=k,I.b=T,I.a=O,I=I.next;for(u=1;u<N;u++)c=f+((F<u?F:u)<<2),x+=(I.r=M=R[c])*(V=N-u),g+=(I.g=k=R[c+1])*V,m+=(I.b=T=R[c+2])*V,y+=(I.a=O=R[c+3])*V,P+=M,B+=k,S+=T,C+=O,I=I.next;for(H=z,U=L,h=0;h<n;h++)R[f+3]=O=y*X>>G,0!=O?(O=255/O,R[f]=(x*X>>G)*O,R[f+1]=(g*X>>G)*O,R[f+2]=(m*X>>G)*O):R[f]=R[f+1]=R[f+2]=0,x-=b,g-=v,m-=w,y-=E,b-=H.r,v-=H.g,w-=H.b,E-=H.a,c=d+((c=h+s+1)<F?c:F)<<2,x+=P+=H.r=R[c],g+=B+=H.g=R[c+1],m+=S+=H.b=R[c+2],y+=C+=H.a=R[c+3],H=H.next,b+=M=U.r,v+=k=U.g,w+=T=U.b,E+=O=U.a,P-=M,B-=k,S-=T,C-=O,U=U.next,f+=4;d+=n}for(h=0;h<n;h++){for(B=S=C=P=g=m=y=x=0,b=N*(M=R[f=h<<2]),v=N*(k=R[f+1]),w=N*(T=R[f+2]),E=N*(O=R[f+3]),x+=D*M,g+=D*k,m+=D*T,y+=D*O,I=z,u=0;u<N;u++)I.r=M,I.g=k,I.b=T,I.a=O,I=I.next;for(p=n,u=1;u<=s;u++)f=p+h<<2,x+=(I.r=M=R[f])*(V=N-u),g+=(I.g=k=R[f+1])*V,m+=(I.b=T=R[f+2])*V,y+=(I.a=O=R[f+3])*V,P+=M,B+=k,S+=T,C+=O,I=I.next,u<q&&(p+=n);for(f=h,H=z,U=L,l=0;l<r;l++)R[(c=f<<2)+3]=O=y*X>>G,0<O?(O=255/O,R[c]=(x*X>>G)*O,R[c+1]=(g*X>>G)*O,R[c+2]=(m*X>>G)*O):R[c]=R[c+1]=R[c+2]=0,x-=b,g-=v,m-=w,y-=E,b-=H.r,v-=H.g,w-=H.b,E-=H.a,c=h+((c=l+N)<q?c:q)*n<<2,x+=P+=H.r=R[c],g+=B+=H.g=R[c+1],m+=S+=H.b=R[c+2],y+=C+=H.a=R[c+3],H=H.next,b+=M=U.r,v+=k=U.g,w+=T=U.b,E+=O=U.a,P-=M,B-=k,S-=T,C-=O,U=U.next,f+=n}o.putImageData(a,e,i)}}function j(){this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}return function(){this.canvg=function(t,e,n){if(null!=t||null!=e||null!=n){n=n||{},"string"==typeof t&&(t=document.getElementById(t)),null!=t.svg&&t.svg.stop();var R,r=((R={FRAMERATE:30,MAX_VIRTUAL_PIXELS:3e4}).init=function(t){var e=0;R.UniqueId=function(){return"canvg"+ ++e},R.Definitions={},R.Styles={},R.Animations=[],R.Images=[],R.ctx=t,R.ViewPort=new function(){this.viewPorts=[],this.Clear=function(){this.viewPorts=[]},this.SetCurrent=function(t,e){this.viewPorts.push({width:t,height:e})},this.RemoveCurrent=function(){this.viewPorts.pop()},this.Current=function(){return this.viewPorts[this.viewPorts.length-1]},this.width=function(){return this.Current().width},this.height=function(){return this.Current().height},this.ComputeSize=function(t){return null!=t&&"number"==typeof t?t:"x"==t?this.width():"y"==t?this.height():Math.sqrt(Math.pow(this.width(),2)+Math.pow(this.height(),2))/Math.sqrt(2)}}},R.init(),R.ImagesLoaded=function(){for(var t=0;t<R.Images.length;t++)if(!R.Images[t].loaded)return!1;return!0},R.trim=function(t){return t.replace(/^\s+|\s+$/g,"")},R.compressSpaces=function(t){return t.replace(/[\s\r\t\n]+/gm," ")},R.ajax=function(t){var e;return(e=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"))?(e.open("GET",t,!1),e.send(null),e.responseText):null},R.parseXml=function(t){if(window.DOMParser)return(new DOMParser).parseFromString(t,"text/xml");t=t.replace(/<!DOCTYPE svg[^>]*>/,"");var e=new ActiveXObject("Microsoft.XMLDOM");return e.async="false",e.loadXML(t),e},R.Property=function(t,e){this.name=t,this.value=e},R.Property.prototype.getValue=function(){return this.value},R.Property.prototype.hasValue=function(){return null!=this.value&&""!==this.value},R.Property.prototype.numValue=function(){if(!this.hasValue())return 0;var t=parseFloat(this.value);return(this.value+"").match(/%$/)&&(t/=100),t},R.Property.prototype.valueOrDefault=function(t){return this.hasValue()?this.value:t},R.Property.prototype.numValueOrDefault=function(t){return this.hasValue()?this.numValue():t},R.Property.prototype.addOpacity=function(t){var e=this.value;if(null!=t&&""!=t&&"string"==typeof this.value){var i=new p(this.value);i.ok&&(e="rgba("+i.r+", "+i.g+", "+i.b+", "+t+")")}return new R.Property(this.name,e)},R.Property.prototype.getDefinition=function(){var t=this.value.match(/#([^\)'"]+)/);return t=(t=t&&t[1])||this.value,R.Definitions[t]},R.Property.prototype.isUrlDefinition=function(){return 0==this.value.indexOf("url(")},R.Property.prototype.getFillStyleDefinition=function(t,e){var i=this.getDefinition();if(null!=i&&i.createGradient)return i.createGradient(R.ctx,t,e);if(null!=i&&i.createPattern){if(i.getHrefAttribute().hasValue()){var n=i.attribute("patternTransform");i=i.getHrefAttribute().getDefinition(),n.hasValue()&&(i.attribute("patternTransform",!0).value=n.value)}return i.createPattern(R.ctx,t)}return null},R.Property.prototype.getDPI=function(t){return 96},R.Property.prototype.getEM=function(t){var e=12,i=new R.Property("fontSize",R.Font.Parse(R.ctx.font).fontSize);return i.hasValue()&&(e=i.toPixels(t)),e},R.Property.prototype.getUnits=function(){return(this.value+"").replace(/[0-9\.\-]/g,"")},R.Property.prototype.toPixels=function(t,e){if(!this.hasValue())return 0;var i=this.value+"";if(i.match(/em$/))return this.numValue()*this.getEM(t);if(i.match(/ex$/))return this.numValue()*this.getEM(t)/2;if(i.match(/px$/))return this.numValue();if(i.match(/pt$/))return this.numValue()*this.getDPI(t)*(1/72);if(i.match(/pc$/))return 15*this.numValue();if(i.match(/cm$/))return this.numValue()*this.getDPI(t)/2.54;if(i.match(/mm$/))return this.numValue()*this.getDPI(t)/25.4;if(i.match(/in$/))return this.numValue()*this.getDPI(t);if(i.match(/%$/))return this.numValue()*R.ViewPort.ComputeSize(t);var n=this.numValue();return e&&n<1?n*R.ViewPort.ComputeSize(t):n},R.Property.prototype.toMilliseconds=function(){if(!this.hasValue())return 0;var t=this.value+"";return t.match(/s$/)?1e3*this.numValue():(t.match(/ms$/),this.numValue())},R.Property.prototype.toRadians=function(){if(!this.hasValue())return 0;var t=this.value+"";return t.match(/deg$/)?this.numValue()*(Math.PI/180):t.match(/grad$/)?this.numValue()*(Math.PI/200):t.match(/rad$/)?this.numValue():this.numValue()*(Math.PI/180)},R.Font=new function(){this.Styles="normal|italic|oblique|inherit",this.Variants="normal|small-caps|inherit",this.Weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit",this.CreateFont=function(t,e,i,n,r,s){var a=null!=s?this.Parse(s):this.CreateFont("","","","","",R.ctx.font);return{fontFamily:r||a.fontFamily,fontSize:n||a.fontSize,fontStyle:t||a.fontStyle,fontWeight:i||a.fontWeight,fontVariant:e||a.fontVariant,toString:function(){return[this.fontStyle,this.fontVariant,this.fontWeight,this.fontSize,this.fontFamily].join(" ")}}};var a=this;this.Parse=function(t){for(var e={},i=R.trim(R.compressSpaces(t||"")).split(" "),n={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1},r="",s=0;s<i.length;s++)n.fontStyle||-1==a.Styles.indexOf(i[s])?n.fontVariant||-1==a.Variants.indexOf(i[s])?n.fontWeight||-1==a.Weights.indexOf(i[s])?n.fontSize?"inherit"!=i[s]&&(r+=i[s]):("inherit"!=i[s]&&(e.fontSize=i[s].split("/")[0]),n.fontStyle=n.fontVariant=n.fontWeight=n.fontSize=!0):("inherit"!=i[s]&&(e.fontWeight=i[s]),n.fontStyle=n.fontVariant=n.fontWeight=!0):("inherit"!=i[s]&&(e.fontVariant=i[s]),n.fontStyle=n.fontVariant=!0):("inherit"!=i[s]&&(e.fontStyle=i[s]),n.fontStyle=!0);return""!=r&&(e.fontFamily=r),e}},R.ToNumberArray=function(t){for(var e=R.trim(R.compressSpaces((t||"").replace(/,/g," "))).split(" "),i=0;i<e.length;i++)e[i]=parseFloat(e[i]);return e},R.Point=function(t,e){this.x=t,this.y=e},R.Point.prototype.angleTo=function(t){return Math.atan2(t.y-this.y,t.x-this.x)},R.Point.prototype.applyTransform=function(t){var e=this.x*t[0]+this.y*t[2]+t[4],i=this.x*t[1]+this.y*t[3]+t[5];this.x=e,this.y=i},R.CreatePoint=function(t){var e=R.ToNumberArray(t);return new R.Point(e[0],e[1])},R.CreatePath=function(t){for(var e=R.ToNumberArray(t),i=[],n=0;n<e.length;n+=2)i.push(new R.Point(e[n],e[n+1]));return i},R.BoundingBox=function(t,e,n,r){this.x1=Number.NaN,this.y1=Number.NaN,this.x2=Number.NaN,this.y2=Number.NaN,this.x=function(){return this.x1},this.y=function(){return this.y1},this.width=function(){return this.x2-this.x1},this.height=function(){return this.y2-this.y1},this.addPoint=function(t,e){null!=t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),null!=e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))},this.addX=function(t){this.addPoint(t,null)},this.addY=function(t){this.addPoint(null,t)},this.addBoundingBox=function(t){this.addPoint(t.x1,t.y1),this.addPoint(t.x2,t.y2)},this.addQuadraticCurve=function(t,e,i,n,r,s){var a=t+2/3*(i-t),o=e+2/3*(n-e),h=a+1/3*(r-t),l=o+1/3*(s-e);this.addBezierCurve(t,e,a,h,o,l,r,s)},this.addBezierCurve=function(t,e,n,r,s,a,o,h){var l=[t,e],u=[n,r],c=[s,a],p=[o,h];for(this.addPoint(l[0],l[1]),this.addPoint(p[0],p[1]),i=0;i<=1;i++){function f(t){return Math.pow(1-t,3)*l[i]+3*Math.pow(1-t,2)*t*u[i]+3*(1-t)*Math.pow(t,2)*c[i]+Math.pow(t,3)*p[i]}var d=6*l[i]-12*u[i]+6*c[i],x=-3*l[i]+9*u[i]-9*c[i]+3*p[i],g=3*u[i]-3*l[i];if(0!=x){var m=Math.pow(d,2)-4*g*x;if(!(m<0)){var y=(-d+Math.sqrt(m))/(2*x);0<y&&y<1&&(0==i&&this.addX(f(y)),1==i&&this.addY(f(y)));var b=(-d-Math.sqrt(m))/(2*x);0<b&&b<1&&(0==i&&this.addX(f(b)),1==i&&this.addY(f(b)))}}else{if(0==d)continue;var v=-g/d;0<v&&v<1&&(0==i&&this.addX(f(v)),1==i&&this.addY(f(v)))}}},this.isPointInBox=function(t,e){return this.x1<=t&&t<=this.x2&&this.y1<=e&&e<=this.y2},this.addPoint(t,e),this.addPoint(n,r)},R.Transform=function(t){var e=this;this.Type={},this.Type.translate=function(t){this.p=R.CreatePoint(t),this.apply=function(t){t.translate(this.p.x||0,this.p.y||0)},this.unapply=function(t){t.translate(-1*this.p.x||0,-1*this.p.y||0)},this.applyToPoint=function(t){t.applyTransform([1,0,0,1,this.p.x||0,this.p.y||0])}},this.Type.rotate=function(t){var e=R.ToNumberArray(t);this.angle=new R.Property("angle",e[0]),this.cx=e[1]||0,this.cy=e[2]||0,this.apply=function(t){t.translate(this.cx,this.cy),t.rotate(this.angle.toRadians()),t.translate(-this.cx,-this.cy)},this.unapply=function(t){t.translate(this.cx,this.cy),t.rotate(-1*this.angle.toRadians()),t.translate(-this.cx,-this.cy)},this.applyToPoint=function(t){var e=this.angle.toRadians();t.applyTransform([1,0,0,1,this.p.x||0,this.p.y||0]),t.applyTransform([Math.cos(e),Math.sin(e),-Math.sin(e),Math.cos(e),0,0]),t.applyTransform([1,0,0,1,-this.p.x||0,-this.p.y||0])}},this.Type.scale=function(t){this.p=R.CreatePoint(t),this.apply=function(t){t.scale(this.p.x||1,this.p.y||this.p.x||1)},this.unapply=function(t){t.scale(1/this.p.x||1,1/this.p.y||this.p.x||1)},this.applyToPoint=function(t){t.applyTransform([this.p.x||0,0,0,this.p.y||0,0,0])}},this.Type.matrix=function(t){this.m=R.ToNumberArray(t),this.apply=function(t){t.transform(this.m[0],this.m[1],this.m[2],this.m[3],this.m[4],this.m[5])},this.applyToPoint=function(t){t.applyTransform(this.m)}},this.Type.SkewBase=function(t){this.base=e.Type.matrix,this.base(t),this.angle=new R.Property("angle",t)},this.Type.SkewBase.prototype=new this.Type.matrix,this.Type.skewX=function(t){this.base=e.Type.SkewBase,this.base(t),this.m=[1,0,Math.tan(this.angle.toRadians()),1,0,0]},this.Type.skewX.prototype=new this.Type.SkewBase,this.Type.skewY=function(t){this.base=e.Type.SkewBase,this.base(t),this.m=[1,Math.tan(this.angle.toRadians()),0,1,0,0]},this.Type.skewY.prototype=new this.Type.SkewBase,this.transforms=[],this.apply=function(t){for(var e=0;e<this.transforms.length;e++)this.transforms[e].apply(t)},this.unapply=function(t){for(var e=this.transforms.length-1;0<=e;e--)this.transforms[e].unapply(t)},this.applyToPoint=function(t){for(var e=0;e<this.transforms.length;e++)this.transforms[e].applyToPoint(t)};for(var i=R.trim(R.compressSpaces(t)).replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/),n=0;n<i.length;n++){var r=R.trim(i[n].split("(")[0]),s=i[n].split("(")[1].replace(")",""),a=new this.Type[r](s);a.type=r,this.transforms.push(a)}},R.AspectRatio=function(t,e,i,n,r,s,a,o,h,l){var u=(e=(e=R.compressSpaces(e)).replace(/^defer\s/,"")).split(" ")[0]||"xMidYMid",c=e.split(" ")[1]||"meet",p=i/n,f=r/s,d=Math.min(p,f),x=Math.max(p,f);"meet"==c&&(n*=d,s*=d),"slice"==c&&(n*=x,s*=x),h=new R.Property("refX",h),l=new R.Property("refY",l),h.hasValue()&&l.hasValue()?t.translate(-d*h.toPixels("x"),-d*l.toPixels("y")):(u.match(/^xMid/)&&("meet"==c&&d==f||"slice"==c&&x==f)&&t.translate(i/2-n/2,0),u.match(/YMid$/)&&("meet"==c&&d==p||"slice"==c&&x==p)&&t.translate(0,r/2-s/2),u.match(/^xMax/)&&("meet"==c&&d==f||"slice"==c&&x==f)&&t.translate(i-n,0),u.match(/YMax$/)&&("meet"==c&&d==p||"slice"==c&&x==p)&&t.translate(0,r-s)),"none"==u?t.scale(p,f):"meet"==c?t.scale(d,d):"slice"==c&&t.scale(x,x),t.translate(null==a?0:-a,null==o?0:-o)},R.Element={},R.EmptyProperty=new R.Property("EMPTY",""),R.Element.ElementBase=function(t){if(this.attributes={},this.styles={},this.children=[],this.attribute=function(t,e){var i=this.attributes[t];return null!=i?i:(1==e&&(i=new R.Property(t,""),this.attributes[t]=i),i||R.EmptyProperty)},this.getHrefAttribute=function(){for(var t in this.attributes)if(t.match(/:href$/))return this.attributes[t];return R.EmptyProperty},this.style=function(t,e){var i=this.styles[t];if(null!=i)return i;var n=this.attribute(t);if(null!=n&&n.hasValue())return this.styles[t]=n;var r=this.parent;if(null!=r){var s=r.style(t);if(null!=s&&s.hasValue())return s}return 1==e&&(i=new R.Property(t,""),this.styles[t]=i),i||R.EmptyProperty},this.render=function(t){if("none"!=this.style("display").value&&"hidden"!=this.attribute("visibility").value){if(t.save(),this.attribute("mask").hasValue()){var e=this.attribute("mask").getDefinition();null!=e&&e.apply(t,this)}else if(this.style("filter").hasValue()){var i=this.style("filter").getDefinition();null!=i&&i.apply(t,this)}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}},this.setContext=function(t){},this.clearContext=function(t){},this.renderChildren=function(t){for(var e=0;e<this.children.length;e++)this.children[e].render(t)},this.addChild=function(t,e){var i=t;e&&(i=R.CreateElement(t)),(i.parent=this).children.push(i)},null!=t&&1==t.nodeType){for(var e=0;e<t.childNodes.length;e++){var i=t.childNodes[e];if(1==i.nodeType&&this.addChild(i,!0),this.captureTextNodes&&3==i.nodeType){var n=i.nodeValue||i.text||"";""!=R.trim(R.compressSpaces(n))&&this.addChild(new R.Element.tspan(i),!1)}}for(e=0;e<t.attributes.length;e++){var r=t.attributes[e];this.attributes[r.nodeName]=new R.Property(r.nodeName,r.nodeValue)}if(null!=(h=R.Styles[t.nodeName]))for(var s in h)this.styles[s]=h[s];if(this.attribute("class").hasValue())for(var a=R.compressSpaces(this.attribute("class").value).split(" "),o=0;o<a.length;o++){if(null!=(h=R.Styles["."+a[o]]))for(var s in h)this.styles[s]=h[s];if(null!=(h=R.Styles[t.nodeName+"."+a[o]]))for(var s in h)this.styles[s]=h[s]}if(this.attribute("id").hasValue()&&null!=(h=R.Styles["#"+this.attribute("id").value]))for(var s in h)this.styles[s]=h[s];if(this.attribute("style").hasValue()){var h=this.attribute("style").value.split(";");for(e=0;e<h.length;e++)if(""!=R.trim(h[e])){var l=h[e].split(":"),u=(s=R.trim(l[0]),R.trim(l[1]));this.styles[s]=new R.Property(s,u)}}this.attribute("id").hasValue()&&null==R.Definitions[this.attribute("id").value]&&(R.Definitions[this.attribute("id").value]=this)}},R.Element.RenderedElementBase=function(t){this.base=R.Element.ElementBase,this.base(t),this.setContext=function(t){var e;if(this.style("fill").isUrlDefinition())null!=(e=this.style("fill").getFillStyleDefinition(this,this.style("fill-opacity")))&&(t.fillStyle=e);else if(this.style("fill").hasValue()){var i;"currentColor"==(i=this.style("fill")).value&&(i.value=this.style("color").value),t.fillStyle="none"==i.value?"rgba(0,0,0,0)":i.value}if(this.style("fill-opacity").hasValue()&&(i=(i=new R.Property("fill",t.fillStyle)).addOpacity(this.style("fill-opacity").value),t.fillStyle=i.value),this.style("stroke").isUrlDefinition())null!=(e=this.style("stroke").getFillStyleDefinition(this,this.style("stroke-opacity")))&&(t.strokeStyle=e);else if(this.style("stroke").hasValue()){var n;"currentColor"==(n=this.style("stroke")).value&&(n.value=this.style("color").value),t.strokeStyle="none"==n.value?"rgba(0,0,0,0)":n.value}if(this.style("stroke-opacity").hasValue()&&(n=(n=new R.Property("stroke",t.strokeStyle)).addOpacity(this.style("stroke-opacity").value),t.strokeStyle=n.value),this.style("stroke-width").hasValue()){var r=this.style("stroke-width").toPixels();t.lineWidth=0==r?.001:r}if(this.style("stroke-linecap").hasValue()&&(t.lineCap=this.style("stroke-linecap").value),this.style("stroke-linejoin").hasValue()&&(t.lineJoin=this.style("stroke-linejoin").value),this.style("stroke-miterlimit").hasValue()&&(t.miterLimit=this.style("stroke-miterlimit").value),this.style("stroke-dasharray").hasValue()){var s=R.ToNumberArray(this.style("stroke-dasharray").value);void 0!==t.setLineDash?t.setLineDash(s):void 0!==t.webkitLineDash?t.webkitLineDash=s:void 0!==t.mozDash&&(t.mozDash=s);var a=this.style("stroke-dashoffset").numValueOrDefault(1);void 0!==t.lineDashOffset?t.lineDashOffset=a:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=a:void 0!==t.mozDashOffset&&(t.mozDashOffset=a)}if(void 0!==t.font&&(t.font=R.Font.CreateFont(this.style("font-style").value,this.style("font-variant").value,this.style("font-weight").value,this.style("font-size").hasValue()?this.style("font-size").toPixels()+"px":"",this.style("font-family").value).toString()),this.attribute("transform").hasValue()&&new R.Transform(this.attribute("transform").value).apply(t),this.style("clip-path").hasValue()){var o=this.style("clip-path").getDefinition();null!=o&&o.apply(t)}this.style("opacity").hasValue()&&(t.globalAlpha=this.style("opacity").numValue())}},R.Element.RenderedElementBase.prototype=new R.Element.ElementBase,R.Element.PathElementBase=function(t){this.base=R.Element.RenderedElementBase,this.base(t),this.path=function(t){return null!=t&&t.beginPath(),new R.BoundingBox},this.renderChildren=function(t){this.path(t),R.Mouse.checkPath(this,t),""!=t.fillStyle&&(this.attribute("fill-rule").hasValue()?t.fill(this.attribute("fill-rule").value):t.fill()),""!=t.strokeStyle&&t.stroke();var e=this.getMarkers();if(null!=e){if(this.style("marker-start").isUrlDefinition()&&(i=this.style("marker-start").getDefinition()).render(t,e[0][0],e[0][1]),this.style("marker-mid").isUrlDefinition())for(var i=this.style("marker-mid").getDefinition(),n=1;n<e.length-1;n++)i.render(t,e[n][0],e[n][1]);this.style("marker-end").isUrlDefinition()&&(i=this.style("marker-end").getDefinition()).render(t,e[e.length-1][0],e[e.length-1][1])}},this.getBoundingBox=function(){return this.path()},this.getMarkers=function(){return null}},R.Element.PathElementBase.prototype=new R.Element.RenderedElementBase,R.Element.svg=function(t){this.base=R.Element.RenderedElementBase,this.base(t),this.baseClearContext=this.clearContext,this.clearContext=function(t){this.baseClearContext(t),R.ViewPort.RemoveCurrent()},this.baseSetContext=this.setContext,this.setContext=function(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4,this.baseSetContext(t),this.attribute("x").hasValue()||(this.attribute("x",!0).value=0),this.attribute("y").hasValue()||(this.attribute("y",!0).value=0),t.translate(this.attribute("x").toPixels("x"),this.attribute("y").toPixels("y"));var e=R.ViewPort.width(),i=R.ViewPort.height();if(this.attribute("width").hasValue()||(this.attribute("width",!0).value="100%"),this.attribute("height").hasValue()||(this.attribute("height",!0).value="100%"),void 0===this.root){e=this.attribute("width").toPixels("x"),i=this.attribute("height").toPixels("y");var n=0,r=0;this.attribute("refX").hasValue()&&this.attribute("refY").hasValue()&&(n=-this.attribute("refX").toPixels("x"),r=-this.attribute("refY").toPixels("y")),t.beginPath(),t.moveTo(n,r),t.lineTo(e,r),t.lineTo(e,i),t.lineTo(n,i),t.closePath(),t.clip()}if(R.ViewPort.SetCurrent(e,i),this.attribute("viewBox").hasValue()){var s=R.ToNumberArray(this.attribute("viewBox").value),a=s[0],o=s[1];e=s[2],i=s[3],R.AspectRatio(t,this.attribute("preserveAspectRatio").value,R.ViewPort.width(),e,R.ViewPort.height(),i,a,o,this.attribute("refX").value,this.attribute("refY").value),R.ViewPort.RemoveCurrent(),R.ViewPort.SetCurrent(s[2],s[3])}}},R.Element.svg.prototype=new R.Element.RenderedElementBase,R.Element.rect=function(t){this.base=R.Element.PathElementBase,this.base(t),this.path=function(t){var e=this.attribute("x").toPixels("x"),i=this.attribute("y").toPixels("y"),n=this.attribute("width").toPixels("x"),r=this.attribute("height").toPixels("y"),s=this.attribute("rx").toPixels("x"),a=this.attribute("ry").toPixels("y");return this.attribute("rx").hasValue()&&!this.attribute("ry").hasValue()&&(a=s),this.attribute("ry").hasValue()&&!this.attribute("rx").hasValue()&&(s=a),s=Math.min(s,n/2),a=Math.min(a,r/2),null!=t&&(t.beginPath(),t.moveTo(e+s,i),t.lineTo(e+n-s,i),t.quadraticCurveTo(e+n,i,e+n,i+a),t.lineTo(e+n,i+r-a),t.quadraticCurveTo(e+n,i+r,e+n-s,i+r),t.lineTo(e+s,i+r),t.quadraticCurveTo(e,i+r,e,i+r-a),t.lineTo(e,i+a),t.quadraticCurveTo(e,i,e+s,i),t.closePath()),new R.BoundingBox(e,i,e+n,i+r)}},R.Element.rect.prototype=new R.Element.PathElementBase,R.Element.circle=function(t){this.base=R.Element.PathElementBase,this.base(t),this.path=function(t){var e=this.attribute("cx").toPixels("x"),i=this.attribute("cy").toPixels("y"),n=this.attribute("r").toPixels();return null!=t&&(t.beginPath(),t.arc(e,i,n,0,2*Math.PI,!0),t.closePath()),new R.BoundingBox(e-n,i-n,e+n,i+n)}},R.Element.circle.prototype=new R.Element.PathElementBase,R.Element.ellipse=function(t){this.base=R.Element.PathElementBase,this.base(t),this.path=function(t){var e=(Math.sqrt(2)-1)/3*4,i=this.attribute("rx").toPixels("x"),n=this.attribute("ry").toPixels("y"),r=this.attribute("cx").toPixels("x"),s=this.attribute("cy").toPixels("y");return null!=t&&(t.beginPath(),t.moveTo(r,s-n),t.bezierCurveTo(r+e*i,s-n,r+i,s-e*n,r+i,s),t.bezierCurveTo(r+i,s+e*n,r+e*i,s+n,r,s+n),t.bezierCurveTo(r-e*i,s+n,r-i,s+e*n,r-i,s),t.bezierCurveTo(r-i,s-e*n,r-e*i,s-n,r,s-n),t.closePath()),new R.BoundingBox(r-i,s-n,r+i,s+n)}},R.Element.ellipse.prototype=new R.Element.PathElementBase,R.Element.line=function(t){this.base=R.Element.PathElementBase,this.base(t),this.getPoints=function(){return[new R.Point(this.attribute("x1").toPixels("x"),this.attribute("y1").toPixels("y")),new R.Point(this.attribute("x2").toPixels("x"),this.attribute("y2").toPixels("y"))]},this.path=function(t){var e=this.getPoints();return null!=t&&(t.beginPath(),t.moveTo(e[0].x,e[0].y),t.lineTo(e[1].x,e[1].y)),new R.BoundingBox(e[0].x,e[0].y,e[1].x,e[1].y)},this.getMarkers=function(){var t=this.getPoints(),e=t[0].angleTo(t[1]);return[[t[0],e],[t[1],e]]}},R.Element.line.prototype=new R.Element.PathElementBase,R.Element.polyline=function(t){this.base=R.Element.PathElementBase,this.base(t),this.points=R.CreatePath(this.attribute("points").value),this.path=function(t){var e=new R.BoundingBox(this.points[0].x,this.points[0].y);null!=t&&(t.beginPath(),t.moveTo(this.points[0].x,this.points[0].y));for(var i=1;i<this.points.length;i++)e.addPoint(this.points[i].x,this.points[i].y),null!=t&&t.lineTo(this.points[i].x,this.points[i].y);return e},this.getMarkers=function(){for(var t=[],e=0;e<this.points.length-1;e++)t.push([this.points[e],this.points[e].angleTo(this.points[e+1])]);return t.push([this.points[this.points.length-1],t[t.length-1][1]]),t}},R.Element.polyline.prototype=new R.Element.PathElementBase,R.Element.polygon=function(t){this.base=R.Element.polyline,this.base(t),this.basePath=this.path,this.path=function(t){var e=this.basePath(t);return null!=t&&(t.lineTo(this.points[0].x,this.points[0].y),t.closePath()),e}},R.Element.polygon.prototype=new R.Element.polyline,R.Element.path=function(t){this.base=R.Element.PathElementBase,this.base(t);var e=this.attribute("d").value;e=(e=(e=(e=(e=(e=(e=(e=e.replace(/,/gm," ")).replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2")).replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2")).replace(/([MmZzLlHhVvCcSsQqTtAa])([^\s])/gm,"$1 $2")).replace(/([^\s])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2")).replace(/([0-9])([+\-])/gm,"$1 $2")).replace(/(\.[0-9]*)(\.)/gm,"$1 $2")).replace(/([Aa](\s+[0-9]+){3})\s+([01])\s*([01])/gm,"$1 $3 $4 "),e=R.compressSpaces(e),e=R.trim(e),this.PathParser=new function(t){this.tokens=t.split(" "),this.reset=function(){this.i=-1,this.command="",this.previousCommand="",this.start=new R.Point(0,0),this.control=new R.Point(0,0),this.current=new R.Point(0,0),this.points=[],this.angles=[]},this.isEnd=function(){return this.i>=this.tokens.length-1},this.isCommandOrEnd=function(){return!!this.isEnd()||null!=this.tokens[this.i+1].match(/^[A-Za-z]$/)},this.isRelativeCommand=function(){switch(this.command){case"m":case"l":case"h":case"v":case"c":case"s":case"q":case"t":case"a":case"z":return!0}return!1},this.getToken=function(){return this.i++,this.tokens[this.i]},this.getScalar=function(){return parseFloat(this.getToken())},this.nextCommand=function(){this.previousCommand=this.command,this.command=this.getToken()},this.getPoint=function(){var t=new R.Point(this.getScalar(),this.getScalar());return this.makeAbsolute(t)},this.getAsControlPoint=function(){var t=this.getPoint();return this.control=t},this.getAsCurrentPoint=function(){var t=this.getPoint();return this.current=t},this.getReflectedControlPoint=function(){return"c"!=this.previousCommand.toLowerCase()&&"s"!=this.previousCommand.toLowerCase()&&"q"!=this.previousCommand.toLowerCase()&&"t"!=this.previousCommand.toLowerCase()?this.current:new R.Point(2*this.current.x-this.control.x,2*this.current.y-this.control.y)},this.makeAbsolute=function(t){return this.isRelativeCommand()&&(t.x+=this.current.x,t.y+=this.current.y),t},this.addMarker=function(t,e,i){null!=i&&0<this.angles.length&&null==this.angles[this.angles.length-1]&&(this.angles[this.angles.length-1]=this.points[this.points.length-1].angleTo(i)),this.addMarkerAngle(t,null==e?null:e.angleTo(t))},this.addMarkerAngle=function(t,e){this.points.push(t),this.angles.push(e)},this.getMarkerPoints=function(){return this.points},this.getMarkerAngles=function(){for(var t=0;t<this.angles.length;t++)if(null==this.angles[t])for(var e=t+1;e<this.angles.length;e++)if(null!=this.angles[e]){this.angles[t]=this.angles[e];break}return this.angles}}(e),this.path=function(t){var e=this.PathParser;e.reset();var i=new R.BoundingBox;for(null!=t&&t.beginPath();!e.isEnd();)switch(e.nextCommand(),e.command){case"M":case"m":var n=e.getAsCurrentPoint();for(e.addMarker(n),i.addPoint(n.x,n.y),null!=t&&t.moveTo(n.x,n.y),e.start=e.current;!e.isCommandOrEnd();)n=e.getAsCurrentPoint(),e.addMarker(n,e.start),i.addPoint(n.x,n.y),null!=t&&t.lineTo(n.x,n.y);break;case"L":case"l":for(;!e.isCommandOrEnd();){var r=e.current;n=e.getAsCurrentPoint(),e.addMarker(n,r),i.addPoint(n.x,n.y),null!=t&&t.lineTo(n.x,n.y)}break;case"H":case"h":for(;!e.isCommandOrEnd();){var s=new R.Point((e.isRelativeCommand()?e.current.x:0)+e.getScalar(),e.current.y);e.addMarker(s,e.current),e.current=s,i.addPoint(e.current.x,e.current.y),null!=t&&t.lineTo(e.current.x,e.current.y)}break;case"V":case"v":for(;!e.isCommandOrEnd();)s=new R.Point(e.current.x,(e.isRelativeCommand()?e.current.y:0)+e.getScalar()),e.addMarker(s,e.current),e.current=s,i.addPoint(e.current.x,e.current.y),null!=t&&t.lineTo(e.current.x,e.current.y);break;case"C":case"c":for(;!e.isCommandOrEnd();){var a=e.current,o=e.getPoint(),h=e.getAsControlPoint(),l=e.getAsCurrentPoint();e.addMarker(l,h,o),i.addBezierCurve(a.x,a.y,o.x,o.y,h.x,h.y,l.x,l.y),null!=t&&t.bezierCurveTo(o.x,o.y,h.x,h.y,l.x,l.y)}break;case"S":case"s":for(;!e.isCommandOrEnd();)a=e.current,o=e.getReflectedControlPoint(),h=e.getAsControlPoint(),l=e.getAsCurrentPoint(),e.addMarker(l,h,o),i.addBezierCurve(a.x,a.y,o.x,o.y,h.x,h.y,l.x,l.y),null!=t&&t.bezierCurveTo(o.x,o.y,h.x,h.y,l.x,l.y);break;case"Q":case"q":for(;!e.isCommandOrEnd();)a=e.current,h=e.getAsControlPoint(),l=e.getAsCurrentPoint(),e.addMarker(l,h,h),i.addQuadraticCurve(a.x,a.y,h.x,h.y,l.x,l.y),null!=t&&t.quadraticCurveTo(h.x,h.y,l.x,l.y);break;case"T":case"t":for(;!e.isCommandOrEnd();)a=e.current,h=e.getReflectedControlPoint(),e.control=h,l=e.getAsCurrentPoint(),e.addMarker(l,h,h),i.addQuadraticCurve(a.x,a.y,h.x,h.y,l.x,l.y),null!=t&&t.quadraticCurveTo(h.x,h.y,l.x,l.y);break;case"A":case"a":for(;!e.isCommandOrEnd();){a=e.current;var u=e.getScalar(),c=e.getScalar(),p=e.getScalar()*(Math.PI/180),f=e.getScalar(),d=e.getScalar(),x=(l=e.getAsCurrentPoint(),new R.Point(Math.cos(p)*(a.x-l.x)/2+Math.sin(p)*(a.y-l.y)/2,-Math.sin(p)*(a.x-l.x)/2+Math.cos(p)*(a.y-l.y)/2)),g=Math.pow(x.x,2)/Math.pow(u,2)+Math.pow(x.y,2)/Math.pow(c,2);1<g&&(u*=Math.sqrt(g),c*=Math.sqrt(g));var m=(f==d?-1:1)*Math.sqrt((Math.pow(u,2)*Math.pow(c,2)-Math.pow(u,2)*Math.pow(x.y,2)-Math.pow(c,2)*Math.pow(x.x,2))/(Math.pow(u,2)*Math.pow(x.y,2)+Math.pow(c,2)*Math.pow(x.x,2)));function y(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function b(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(E(t,e))}isNaN(m)&&(m=0);var v=new R.Point(m*u*x.y/c,m*-c*x.x/u),w=new R.Point((a.x+l.x)/2+Math.cos(p)*v.x-Math.sin(p)*v.y,(a.y+l.y)/2+Math.sin(p)*v.x+Math.cos(p)*v.y),E=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(y(t)*y(e))},P=b([1,0],[(x.x-v.x)/u,(x.y-v.y)/c]),B=[(x.x-v.x)/u,(x.y-v.y)/c],S=[(-x.x-v.x)/u,(-x.y-v.y)/c],C=b(B,S);E(B,S)<=-1&&(C=Math.PI),1<=E(B,S)&&(C=0);var M=1-d?1:-1,k=P+C/2*M,T=new R.Point(w.x+u*Math.cos(k),w.y+c*Math.sin(k));if(e.addMarkerAngle(T,k-M*Math.PI/2),e.addMarkerAngle(l,k-M*Math.PI),i.addPoint(l.x,l.y),null!=t){E=c<u?u:c;var O=c<u?1:u/c,V=c<u?c/u:1;t.translate(w.x,w.y),t.rotate(p),t.scale(O,V),t.arc(0,0,E,P,P+C,1-d),t.scale(1/O,1/V),t.rotate(-p),t.translate(-w.x,-w.y)}}break;case"Z":case"z":null!=t&&t.closePath(),e.current=e.start}return i},this.getMarkers=function(){for(var t=this.PathParser.getMarkerPoints(),e=this.PathParser.getMarkerAngles(),i=[],n=0;n<t.length;n++)i.push([t[n],e[n]]);return i}},R.Element.path.prototype=new R.Element.PathElementBase,R.Element.pattern=function(t){this.base=R.Element.ElementBase,this.base(t),this.createPattern=function(t,e){var i=this.attribute("width").toPixels("x",!0),n=this.attribute("height").toPixels("y",!0),r=new R.Element.svg;r.attributes.viewBox=new R.Property("viewBox",this.attribute("viewBox").value),r.attributes.width=new R.Property("width",i+"px"),r.attributes.height=new R.Property("height",n+"px"),r.attributes.transform=new R.Property("transform",this.attribute("patternTransform").value),r.children=this.children;var s=document.createElement("canvas");s.width=i,s.height=n;var a=s.getContext("2d");this.attribute("x").hasValue()&&this.attribute("y").hasValue()&&a.translate(this.attribute("x").toPixels("x",!0),this.attribute("y").toPixels("y",!0));for(var o=-1;o<=1;o++)for(var h=-1;h<=1;h++)a.save(),a.translate(o*s.width,h*s.height),r.render(a),a.restore();return t.createPattern(s,"repeat")}},R.Element.pattern.prototype=new R.Element.ElementBase,R.Element.marker=function(t){this.base=R.Element.ElementBase,this.base(t),this.baseRender=this.render,this.render=function(t,e,i){t.translate(e.x,e.y),"auto"==this.attribute("orient").valueOrDefault("auto")&&t.rotate(i),"strokeWidth"==this.attribute("markerUnits").valueOrDefault("strokeWidth")&&t.scale(t.lineWidth,t.lineWidth),t.save();var n=new R.Element.svg;n.attributes.viewBox=new R.Property("viewBox",this.attribute("viewBox").value),n.attributes.refX=new R.Property("refX",this.attribute("refX").value),n.attributes.refY=new R.Property("refY",this.attribute("refY").value),n.attributes.width=new R.Property("width",this.attribute("markerWidth").value),n.attributes.height=new R.Property("height",this.attribute("markerHeight").value),n.attributes.fill=new R.Property("fill",this.attribute("fill").valueOrDefault("black")),n.attributes.stroke=new R.Property("stroke",this.attribute("stroke").valueOrDefault("none")),n.children=this.children,n.render(t),t.restore(),"strokeWidth"==this.attribute("markerUnits").valueOrDefault("strokeWidth")&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"==this.attribute("orient").valueOrDefault("auto")&&t.rotate(-i),t.translate(-e.x,-e.y)}},R.Element.marker.prototype=new R.Element.ElementBase,R.Element.defs=function(t){this.base=R.Element.ElementBase,this.base(t),this.render=function(t){}},R.Element.defs.prototype=new R.Element.ElementBase,R.Element.GradientBase=function(t){this.base=R.Element.ElementBase,this.base(t),this.gradientUnits=this.attribute("gradientUnits").valueOrDefault("objectBoundingBox"),this.stops=[];for(var e=0;e<this.children.length;e++){var i=this.children[e];"stop"==i.type&&this.stops.push(i)}this.getGradient=function(){},this.createGradient=function(t,e,i){var n=this;function r(t){return i.hasValue()?new R.Property("color",t).addOpacity(i.value).value:t}this.getHrefAttribute().hasValue()&&(n=this.getHrefAttribute().getDefinition());var s=this.getGradient(t,e);if(null==s)return r(n.stops[n.stops.length-1].color);for(var a=0;a<n.stops.length;a++)s.addColorStop(n.stops[a].offset,r(n.stops[a].color));if(this.attribute("gradientTransform").hasValue()){var o=R.ViewPort.viewPorts[0],h=new R.Element.rect;h.attributes.x=new R.Property("x",-R.MAX_VIRTUAL_PIXELS/3),h.attributes.y=new R.Property("y",-R.MAX_VIRTUAL_PIXELS/3),h.attributes.width=new R.Property("width",R.MAX_VIRTUAL_PIXELS),h.attributes.height=new R.Property("height",R.MAX_VIRTUAL_PIXELS);var l=new R.Element.g;l.attributes.transform=new R.Property("transform",this.attribute("gradientTransform").value),l.children=[h];var u=new R.Element.svg;u.attributes.x=new R.Property("x",0),u.attributes.y=new R.Property("y",0),u.attributes.width=new R.Property("width",o.width),u.attributes.height=new R.Property("height",o.height),u.children=[l];var c=document.createElement("canvas");c.width=o.width,c.height=o.height;var p=c.getContext("2d");return p.fillStyle=s,u.render(p),p.createPattern(c,"no-repeat")}return s}},R.Element.GradientBase.prototype=new R.Element.ElementBase,R.Element.linearGradient=function(t){this.base=R.Element.GradientBase,this.base(t),this.getGradient=function(t,e){var i=e.getBoundingBox();this.attribute("x1").hasValue()||this.attribute("y1").hasValue()||this.attribute("x2").hasValue()||this.attribute("y2").hasValue()||(this.attribute("x1",!0).value=0,this.attribute("y1",!0).value=0,this.attribute("x2",!0).value=1,this.attribute("y2",!0).value=0);var n="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("x1").numValue():this.attribute("x1").toPixels("x"),r="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("y1").numValue():this.attribute("y1").toPixels("y"),s="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("x2").numValue():this.attribute("x2").toPixels("x"),a="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("y2").numValue():this.attribute("y2").toPixels("y");return n==s&&r==a?null:t.createLinearGradient(n,r,s,a)}},R.Element.linearGradient.prototype=new R.Element.GradientBase,R.Element.radialGradient=function(t){this.base=R.Element.GradientBase,this.base(t),this.getGradient=function(t,e){var i=e.getBoundingBox();this.attribute("cx").hasValue()||(this.attribute("cx",!0).value="50%"),this.attribute("cy").hasValue()||(this.attribute("cy",!0).value="50%"),this.attribute("r").hasValue()||(this.attribute("r",!0).value="50%");var n="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("cx").numValue():this.attribute("cx").toPixels("x"),r="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("cy").numValue():this.attribute("cy").toPixels("y"),s=n,a=r;this.attribute("fx").hasValue()&&(s="objectBoundingBox"==this.gradientUnits?i.x()+i.width()*this.attribute("fx").numValue():this.attribute("fx").toPixels("x")),this.attribute("fy").hasValue()&&(a="objectBoundingBox"==this.gradientUnits?i.y()+i.height()*this.attribute("fy").numValue():this.attribute("fy").toPixels("y"));var o="objectBoundingBox"==this.gradientUnits?(i.width()+i.height())/2*this.attribute("r").numValue():this.attribute("r").toPixels();return t.createRadialGradient(s,a,0,n,r,o)}},R.Element.radialGradient.prototype=new R.Element.GradientBase,R.Element.stop=function(t){this.base=R.Element.ElementBase,this.base(t),this.offset=this.attribute("offset").numValue(),this.offset<0&&(this.offset=0),1<this.offset&&(this.offset=1);var e=this.style("stop-color");this.style("stop-opacity").hasValue()&&(e=e.addOpacity(this.style("stop-opacity").value)),this.color=e.value},R.Element.stop.prototype=new R.Element.ElementBase,R.Element.AnimateBase=function(t){this.base=R.Element.ElementBase,this.base(t),R.Animations.push(this),this.duration=0,this.begin=this.attribute("begin").toMilliseconds(),this.maxDuration=this.begin+this.attribute("dur").toMilliseconds(),this.getProperty=function(){var t=this.attribute("attributeType").value,e=this.attribute("attributeName").value;return"CSS"==t?this.parent.style(e,!0):this.parent.attribute(e,!0)},this.initialValue=null,this.initialUnits="",this.removed=!1,this.calcValue=function(){return""},this.update=function(t){if(null==this.initialValue&&(this.initialValue=this.getProperty().value,this.initialUnits=this.getProperty().getUnits()),this.duration>this.maxDuration){if("indefinite"!=this.attribute("repeatCount").value&&"indefinite"!=this.attribute("repeatDur").value)return"remove"==this.attribute("fill").valueOrDefault("remove")&&!this.removed&&(this.removed=!0,this.getProperty().value=this.initialValue,!0);this.duration=0}this.duration=this.duration+t;var e=!1;if(this.begin<this.duration){var i=this.calcValue();this.attribute("type").hasValue()&&(i=this.attribute("type").value+"("+i+")"),this.getProperty().value=i,e=!0}return e},this.from=this.attribute("from"),this.to=this.attribute("to"),this.values=this.attribute("values"),this.values.hasValue()&&(this.values.value=this.values.value.split(";")),this.progress=function(){var t={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(this.values.hasValue()){var e=t.progress*(this.values.value.length-1),i=Math.floor(e),n=Math.ceil(e);t.from=new R.Property("from",parseFloat(this.values.value[i])),t.to=new R.Property("to",parseFloat(this.values.value[n])),t.progress=(e-i)/(n-i)}else t.from=this.from,t.to=this.to;return t}},R.Element.AnimateBase.prototype=new R.Element.ElementBase,R.Element.animate=function(t){this.base=R.Element.AnimateBase,this.base(t),this.calcValue=function(){var t=this.progress();return t.from.numValue()+(t.to.numValue()-t.from.numValue())*t.progress+this.initialUnits}},R.Element.animate.prototype=new R.Element.AnimateBase,R.Element.animateColor=function(t){this.base=R.Element.AnimateBase,this.base(t),this.calcValue=function(){var t=this.progress(),e=new p(t.from.value),i=new p(t.to.value);if(e.ok&&i.ok){var n=e.r+(i.r-e.r)*t.progress,r=e.g+(i.g-e.g)*t.progress,s=e.b+(i.b-e.b)*t.progress;return"rgb("+parseInt(n,10)+","+parseInt(r,10)+","+parseInt(s,10)+")"}return this.attribute("from").value}},R.Element.animateColor.prototype=new R.Element.AnimateBase,R.Element.animateTransform=function(t){this.base=R.Element.AnimateBase,this.base(t),this.calcValue=function(){for(var t=this.progress(),e=R.ToNumberArray(t.from.value),i=R.ToNumberArray(t.to.value),n="",r=0;r<e.length;r++)n+=e[r]+(i[r]-e[r])*t.progress+" ";return n}},R.Element.animateTransform.prototype=new R.Element.animate,R.Element.font=function(t){this.base=R.Element.ElementBase,this.base(t),this.horizAdvX=this.attribute("horiz-adv-x").numValue(),this.isRTL=!1,this.isArabic=!1,this.fontFace=null,this.missingGlyph=null,this.glyphs=[];for(var e=0;e<this.children.length;e++){var i=this.children[e];"font-face"==i.type?(this.fontFace=i).style("font-family").hasValue()&&(R.Definitions[i.style("font-family").value]=this):"missing-glyph"==i.type?this.missingGlyph=i:"glyph"==i.type&&(""!=i.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[i.unicode]&&(this.glyphs[i.unicode]=[]),this.glyphs[i.unicode][i.arabicForm]=i):this.glyphs[i.unicode]=i)}},R.Element.font.prototype=new R.Element.ElementBase,R.Element.fontface=function(t){this.base=R.Element.ElementBase,this.base(t),this.ascent=this.attribute("ascent").value,this.descent=this.attribute("descent").value,this.unitsPerEm=this.attribute("units-per-em").numValue()},R.Element.fontface.prototype=new R.Element.ElementBase,R.Element.missingglyph=function(t){this.base=R.Element.path,this.base(t),this.horizAdvX=0},R.Element.missingglyph.prototype=new R.Element.path,R.Element.glyph=function(t){this.base=R.Element.path,this.base(t),this.horizAdvX=this.attribute("horiz-adv-x").numValue(),this.unicode=this.attribute("unicode").value,this.arabicForm=this.attribute("arabic-form").value},R.Element.glyph.prototype=new R.Element.path,R.Element.text=function(t){this.captureTextNodes=!0,this.base=R.Element.RenderedElementBase,this.base(t),this.baseSetContext=this.setContext,this.setContext=function(t){this.baseSetContext(t),this.style("dominant-baseline").hasValue()&&(t.textBaseline=this.style("dominant-baseline").value),this.style("alignment-baseline").hasValue()&&(t.textBaseline=this.style("alignment-baseline").value)},this.getBoundingBox=function(){return new R.BoundingBox(this.attribute("x").toPixels("x"),this.attribute("y").toPixels("y"),0,0)},this.renderChildren=function(t){this.x=this.attribute("x").toPixels("x"),this.y=this.attribute("y").toPixels("y"),this.x+=this.getAnchorDelta(t,this,0);for(var e=0;e<this.children.length;e++)this.renderChild(t,this,e)},this.getAnchorDelta=function(t,e,i){var n=this.style("text-anchor").valueOrDefault("start");if("start"==n)return 0;for(var r=0,s=i;s<e.children.length;s++){var a=e.children[s];if(i<s&&a.attribute("x").hasValue())break;r+=a.measureTextRecursive(t)}return-1*("end"==n?r:r/2)},this.renderChild=function(t,e,i){var n=e.children[i];for(n.attribute("x").hasValue()?n.x=n.attribute("x").toPixels("x")+this.getAnchorDelta(t,e,i):(this.attribute("dx").hasValue()&&(this.x+=this.attribute("dx").toPixels("x")),n.attribute("dx").hasValue()&&(this.x+=n.attribute("dx").toPixels("x")),n.x=this.x),this.x=n.x+n.measureText(t),n.attribute("y").hasValue()?n.y=n.attribute("y").toPixels("y"):(this.attribute("dy").hasValue()&&(this.y+=this.attribute("dy").toPixels("y")),n.attribute("dy").hasValue()&&(this.y+=n.attribute("dy").toPixels("y")),n.y=this.y),this.y=n.y,n.render(t),i=0;i<n.children.length;i++)this.renderChild(t,n,i)}},R.Element.text.prototype=new R.Element.RenderedElementBase,R.Element.TextElementBase=function(t){this.base=R.Element.RenderedElementBase,this.base(t),this.getGlyph=function(t,e,i){var n=e[i],r=null;if(t.isArabic){var s="isolated";(0==i||" "==e[i-1])&&i<e.length-2&&" "!=e[i+1]&&(s="terminal"),0<i&&" "!=e[i-1]&&i<e.length-2&&" "!=e[i+1]&&(s="medial"),0<i&&" "!=e[i-1]&&(i==e.length-1||" "==e[i+1])&&(s="initial"),void 0!==t.glyphs[n]&&null==(r=t.glyphs[n][s])&&"glyph"==t.glyphs[n].type&&(r=t.glyphs[n])}else r=t.glyphs[n];return null==r&&(r=t.missingGlyph),r},this.renderChildren=function(t){var e=this.parent.style("font-family").getDefinition();if(null==e)""!=t.fillStyle&&t.fillText(R.compressSpaces(this.getText()),this.x,this.y),""!=t.strokeStyle&&t.strokeText(R.compressSpaces(this.getText()),this.x,this.y);else{var i=this.parent.style("font-size").numValueOrDefault(R.Font.Parse(R.ctx.font).fontSize),n=this.parent.style("font-style").valueOrDefault(R.Font.Parse(R.ctx.font).fontStyle),r=this.getText();e.isRTL&&(r=r.split("").reverse().join(""));for(var s=R.ToNumberArray(this.parent.attribute("dx").value),a=0;a<r.length;a++){var o=this.getGlyph(e,r,a),h=i/e.fontFace.unitsPerEm;t.translate(this.x,this.y),t.scale(h,-h);var l=t.lineWidth;t.lineWidth=t.lineWidth*e.fontFace.unitsPerEm/i,"italic"==n&&t.transform(1,0,.4,1,0,0),o.render(t),"italic"==n&&t.transform(1,0,-.4,1,0,0),t.lineWidth=l,t.scale(1/h,-1/h),t.translate(-this.x,-this.y),this.x+=i*(o.horizAdvX||e.horizAdvX)/e.fontFace.unitsPerEm,void 0===s[a]||isNaN(s[a])||(this.x+=s[a])}}},this.getText=function(){},this.measureTextRecursive=function(t){for(var e=this.measureText(t),i=0;i<this.children.length;i++)e+=this.children[i].measureTextRecursive(t);return e},this.measureText=function(t){var e=this.parent.style("font-family").getDefinition();if(null!=e){var i=this.parent.style("font-size").numValueOrDefault(R.Font.Parse(R.ctx.font).fontSize),n=0,r=this.getText();e.isRTL&&(r=r.split("").reverse().join(""));for(var s=R.ToNumberArray(this.parent.attribute("dx").value),a=0;a<r.length;a++)n+=(this.getGlyph(e,r,a).horizAdvX||e.horizAdvX)*i/e.fontFace.unitsPerEm,void 0===s[a]||isNaN(s[a])||(n+=s[a]);return n}var o=R.compressSpaces(this.getText());if(!t.measureText)return 10*o.length;t.save(),this.setContext(t);var h=t.measureText(o).width;return t.restore(),h}},R.Element.TextElementBase.prototype=new R.Element.RenderedElementBase,R.Element.tspan=function(t){this.captureTextNodes=!0,this.base=R.Element.TextElementBase,this.base(t),this.text=t.nodeValue||t.text||"",this.getText=function(){return this.text}},R.Element.tspan.prototype=new R.Element.TextElementBase,R.Element.tref=function(t){this.base=R.Element.TextElementBase,this.base(t),this.getText=function(){var t=this.getHrefAttribute().getDefinition();if(null!=t)return t.children[0].getText()}},R.Element.tref.prototype=new R.Element.TextElementBase,R.Element.a=function(t){this.base=R.Element.TextElementBase,this.base(t),this.hasText=!0;for(var e=0;e<t.childNodes.length;e++)3!=t.childNodes[e].nodeType&&(this.hasText=!1);this.text=this.hasText?t.childNodes[0].nodeValue:"",this.getText=function(){return this.text},this.baseRenderChildren=this.renderChildren,this.renderChildren=function(t){if(this.hasText){this.baseRenderChildren(t);var e=new R.Property("fontSize",R.Font.Parse(R.ctx.font).fontSize);R.Mouse.checkBoundingBox(this,new R.BoundingBox(this.x,this.y-e.toPixels("y"),this.x+this.measureText(t),this.y))}else{var i=new R.Element.g;i.children=this.children,i.parent=this,i.render(t)}},this.onclick=function(){window.open(this.getHrefAttribute().value)},this.onmousemove=function(){R.ctx.canvas.style.cursor="pointer"}},R.Element.a.prototype=new R.Element.TextElementBase,R.Element.image=function(t){this.base=R.Element.RenderedElementBase,this.base(t);var e=this.getHrefAttribute().value,s=e.match(/\.svg$/);if(R.Images.push(this),this.loaded=!1,s)this.img=R.ajax(e),this.loaded=!0;else{this.img=document.createElement("img");var i=this;this.img.onload=function(){i.loaded=!0},this.img.onerror=function(){"undefined"!=typeof console&&(console.log('ERROR: image "'+e+'" not found'),i.loaded=!0)},this.img.src=e}this.renderChildren=function(t){var e=this.attribute("x").toPixels("x"),i=this.attribute("y").toPixels("y"),n=this.attribute("width").toPixels("x"),r=this.attribute("height").toPixels("y");0!=n&&0!=r&&(t.save(),s?t.drawSvg(this.img,e,i,n,r):(t.translate(e,i),R.AspectRatio(t,this.attribute("preserveAspectRatio").value,n,this.img.width,r,this.img.height,0,0),t.drawImage(this.img,0,0)),t.restore())},this.getBoundingBox=function(){var t=this.attribute("x").toPixels("x"),e=this.attribute("y").toPixels("y"),i=this.attribute("width").toPixels("x"),n=this.attribute("height").toPixels("y");return new R.BoundingBox(t,e,t+i,e+n)}},R.Element.image.prototype=new R.Element.RenderedElementBase,R.Element.g=function(t){this.base=R.Element.RenderedElementBase,this.base(t),this.getBoundingBox=function(){for(var t=new R.BoundingBox,e=0;e<this.children.length;e++)t.addBoundingBox(this.children[e].getBoundingBox());return t}},R.Element.g.prototype=new R.Element.RenderedElementBase,R.Element.symbol=function(t){this.base=R.Element.RenderedElementBase,this.base(t),this.baseSetContext=this.setContext,this.setContext=function(t){if(this.baseSetContext(t),this.attribute("viewBox").hasValue()){var e=R.ToNumberArray(this.attribute("viewBox").value),i=e[0],n=e[1];width=e[2],height=e[3],R.AspectRatio(t,this.attribute("preserveAspectRatio").value,this.attribute("width").toPixels("x"),width,this.attribute("height").toPixels("y"),height,i,n),R.ViewPort.SetCurrent(e[2],e[3])}}},R.Element.symbol.prototype=new R.Element.RenderedElementBase,R.Element.style=function(t){this.base=R.Element.ElementBase,this.base(t);for(var e="",i=0;i<t.childNodes.length;i++)e+=t.childNodes[i].nodeValue;e=e.replace(/(\/\*([^*]|[\r\n]|(\*+([^*\/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"");var n=(e=R.compressSpaces(e)).split("}");for(i=0;i<n.length;i++)if(""!=R.trim(n[i]))for(var r=n[i].split("{"),s=r[0].split(","),a=r[1].split(";"),o=0;o<s.length;o++){var h=R.trim(s[o]);if(""!=h){for(var l={},u=0;u<a.length;u++){var c=a[u].indexOf(":"),p=a[u].substr(0,c),f=a[u].substr(c+1,a[u].length-c);null!=p&&null!=f&&(l[R.trim(p)]=new R.Property(R.trim(p),R.trim(f)))}if(R.Styles[h]=l,"@font-face"==h)for(var d=l["font-family"].value.replace(/"/g,""),x=l.src.value.split(","),g=0;g<x.length;g++)if(0<x[g].indexOf('format("svg")'))for(var m=x[g].indexOf("url"),y=x[g].indexOf(")",m),b=x[g].substr(m+5,y-m-6),v=R.parseXml(R.ajax(b)).getElementsByTagName("font"),w=0;w<v.length;w++){var E=R.CreateElement(v[w]);R.Definitions[d]=E}}}},R.Element.style.prototype=new R.Element.ElementBase,R.Element.use=function(t){this.base=R.Element.RenderedElementBase,this.base(t),this.baseSetContext=this.setContext,this.setContext=function(t){this.baseSetContext(t),this.attribute("x").hasValue()&&t.translate(this.attribute("x").toPixels("x"),0),this.attribute("y").hasValue()&&t.translate(0,this.attribute("y").toPixels("y"))},this.getDefinition=function(){var t=this.getHrefAttribute().getDefinition();return this.attribute("width").hasValue()&&(t.attribute("width",!0).value=this.attribute("width").value),this.attribute("height").hasValue()&&(t.attribute("height",!0).value=this.attribute("height").value),t},this.path=function(t){var e=this.getDefinition();null!=e&&e.path(t)},this.getBoundingBox=function(){var t=this.getDefinition();if(null!=t)return t.getBoundingBox()},this.renderChildren=function(t){var e=this.getDefinition();if(null!=e){var i=e.parent;e.parent=null,e.render(t),e.parent=i}}},R.Element.use.prototype=new R.Element.RenderedElementBase,R.Element.mask=function(t){this.base=R.Element.ElementBase,this.base(t),this.apply=function(t,e){var i=this.attribute("x").toPixels("x"),n=this.attribute("y").toPixels("y"),r=this.attribute("width").toPixels("x"),s=this.attribute("height").toPixels("y");if(0==r&&0==s){for(var a=new R.BoundingBox,o=0;o<this.children.length;o++)a.addBoundingBox(this.children[o].getBoundingBox());i=Math.floor(a.x1),n=Math.floor(a.y1),r=Math.floor(a.width()),s=Math.floor(a.height())}var h=e.attribute("mask").value;e.attribute("mask").value="";var l=document.createElement("canvas");l.width=i+r,l.height=n+s;var u=l.getContext("2d");this.renderChildren(u);var c=document.createElement("canvas");c.width=i+r,c.height=n+s;var p=c.getContext("2d");e.render(p),p.globalCompositeOperation="destination-in",p.fillStyle=u.createPattern(l,"no-repeat"),p.fillRect(0,0,i+r,n+s),t.fillStyle=p.createPattern(c,"no-repeat"),t.fillRect(0,0,i+r,n+s),e.attribute("mask").value=h},this.render=function(t){}},R.Element.mask.prototype=new R.Element.ElementBase,R.Element.clipPath=function(t){this.base=R.Element.ElementBase,this.base(t),this.apply=function(t){for(var e=0;e<this.children.length;e++){var i=this.children[e];if(void 0!==i.path){var n=null;i.attribute("transform").hasValue()&&(n=new R.Transform(i.attribute("transform").value)).apply(t),i.path(t),t.clip(),n&&n.unapply(t)}}},this.render=function(t){}},R.Element.clipPath.prototype=new R.Element.ElementBase,R.Element.filter=function(t){this.base=R.Element.ElementBase,this.base(t),this.apply=function(t,e){var i=e.getBoundingBox(),n=Math.floor(i.x1),r=Math.floor(i.y1),s=Math.floor(i.width()),a=Math.floor(i.height()),o=e.style("filter").value;e.style("filter").value="";for(var h=0,l=0,u=0;u<this.children.length;u++){var c=this.children[u].extraFilterDistance||0;h=Math.max(h,c),l=Math.max(l,c)}var p=document.createElement("canvas");p.width=s+2*h,p.height=a+2*l;var f=p.getContext("2d");for(f.translate(-n+h,-r+l),e.render(f),u=0;u<this.children.length;u++)this.children[u].apply(f,0,0,s+2*h,a+2*l);t.drawImage(p,0,0,s+2*h,a+2*l,n-h,r-l,s+2*h,a+2*l),e.style("filter",!0).value=o},this.render=function(t){}},R.Element.filter.prototype=new R.Element.ElementBase,R.Element.feMorphology=function(t){this.base=R.Element.ElementBase,this.base(t),this.apply=function(t,e,i,n,r){}},R.Element.feMorphology.prototype=new R.Element.ElementBase,R.Element.feColorMatrix=function(t){function o(t,e,i,n,r,s){return t[i*n*4+4*e+s]}function h(t,e,i,n,r,s,a){t[i*n*4+4*e+s]=a}this.base=R.Element.ElementBase,this.base(t),this.apply=function(t,e,i,n,r){var s=t.getImageData(0,0,n,r);for(i=0;i<r;i++)for(e=0;e<n;e++){var a=(o(s.data,e,i,n,0,0)+o(s.data,e,i,n,0,1)+o(s.data,e,i,n,0,2))/3;h(s.data,e,i,n,0,0,a),h(s.data,e,i,n,0,1,a),h(s.data,e,i,n,0,2,a)}t.clearRect(0,0,n,r),t.putImageData(s,0,0)}},R.Element.feColorMatrix.prototype=new R.Element.ElementBase,R.Element.feGaussianBlur=function(t){this.base=R.Element.ElementBase,this.base(t),this.blurRadius=Math.floor(this.attribute("stdDeviation").numValue()),this.extraFilterDistance=this.blurRadius,this.apply=function(t,e,i,n,r){t.canvas.id=R.UniqueId(),t.canvas.style.display="none",document.body.appendChild(t.canvas),c(t.canvas.id,e,i,n,r,this.blurRadius),document.body.removeChild(t.canvas)}},R.Element.feGaussianBlur.prototype=new R.Element.ElementBase,R.Element.title=function(t){},R.Element.title.prototype=new R.Element.ElementBase,R.Element.desc=function(t){},R.Element.desc.prototype=new R.Element.ElementBase,R.Element.MISSING=function(t){"undefined"!=typeof console&&console.log("ERROR: Element '"+t.nodeName+"' not yet implemented.")},R.Element.MISSING.prototype=new R.Element.ElementBase,R.CreateElement=function(t){var e=t.nodeName.replace(/^[^:]+:/,"");e=e.replace(/\-/g,"");var i=null;return(i=void 0!==R.Element[e]?new R.Element[e](t):new R.Element.MISSING(t)).type=t.nodeName,i},R.load=function(t,e){R.loadXml(t,R.ajax(e))},R.loadXml=function(t,e){R.loadXmlDoc(t,R.parseXml(e))},R.loadXmlDoc=function(s,a){function i(t){for(var e=s.canvas;e;)t.x-=e.offsetLeft,t.y-=e.offsetTop,e=e.offsetParent;return window.scrollX&&(t.x+=window.scrollX),window.scrollY&&(t.y+=window.scrollY),t}function n(){R.ViewPort.Clear(),s.canvas.parentNode&&R.ViewPort.SetCurrent(s.canvas.parentNode.clientWidth,s.canvas.parentNode.clientHeight),1!=R.opts.ignoreDimensions&&(o.style("width").hasValue()&&(s.canvas.width=o.style("width").toPixels("x"),s.canvas.style.width=s.canvas.width+"px"),o.style("height").hasValue()&&(s.canvas.height=o.style("height").toPixels("y"),s.canvas.style.height=s.canvas.height+"px"));var t=s.canvas.clientWidth||s.canvas.width,e=s.canvas.clientHeight||s.canvas.height;if(1==R.opts.ignoreDimensions&&o.style("width").hasValue()&&o.style("height").hasValue()&&(t=o.style("width").toPixels("x"),e=o.style("height").toPixels("y")),R.ViewPort.SetCurrent(t,e),null!=R.opts.offsetX&&(o.attribute("x",!0).value=R.opts.offsetX),null!=R.opts.offsetY&&(o.attribute("y",!0).value=R.opts.offsetY),null!=R.opts.scaleWidth&&null!=R.opts.scaleHeight){var i=1,n=1,r=R.ToNumberArray(o.attribute("viewBox").value);o.attribute("width").hasValue()?i=o.attribute("width").toPixels("x")/R.opts.scaleWidth:isNaN(r[2])||(i=r[2]/R.opts.scaleWidth),o.attribute("height").hasValue()?n=o.attribute("height").toPixels("y")/R.opts.scaleHeight:isNaN(r[3])||(n=r[3]/R.opts.scaleHeight),o.attribute("width",!0).value=R.opts.scaleWidth,o.attribute("height",!0).value=R.opts.scaleHeight,o.attribute("viewBox",!0).value="0 0 "+t*i+" "+e*n,o.attribute("preserveAspectRatio",!0).value="none"}1!=R.opts.ignoreClear&&s.clearRect(0,0,t,e),o.render(s),h&&(h=!1,"function"==typeof R.opts.renderCallback&&R.opts.renderCallback(a))}R.init(s),1!=R.opts.ignoreMouse&&(s.canvas.onclick=function(t){var e=i(new R.Point(null!=t?t.clientX:event.clientX,null!=t?t.clientY:event.clientY));R.Mouse.onclick(e.x,e.y)},s.canvas.onmousemove=function(t){var e=i(new R.Point(null!=t?t.clientX:event.clientX,null!=t?t.clientY:event.clientY));R.Mouse.onmousemove(e.x,e.y)});var o=R.CreateElement(a.documentElement),h=o.root=!0,r=!0;R.ImagesLoaded()&&(r=!1,n()),R.intervalID=setInterval(function(){var t=!1;if(r&&R.ImagesLoaded()&&(t=!(r=!1)),1!=R.opts.ignoreMouse&&(t|=R.Mouse.hasEvents()),1!=R.opts.ignoreAnimation)for(var e=0;e<R.Animations.length;e++)t|=R.Animations[e].update(1e3/R.FRAMERATE);"function"==typeof R.opts.forceRedraw&&1==R.opts.forceRedraw()&&(t=!0),t&&(n(),R.Mouse.runEvents())},1e3/R.FRAMERATE)},R.stop=function(){R.intervalID&&clearInterval(R.intervalID)},R.Mouse=new function(){this.events=[],this.hasEvents=function(){return 0!=this.events.length},this.onclick=function(t,e){this.events.push({type:"onclick",x:t,y:e,run:function(t){t.onclick&&t.onclick()}})},this.onmousemove=function(t,e){this.events.push({type:"onmousemove",x:t,y:e,run:function(t){t.onmousemove&&t.onmousemove()}})},this.eventElements=[],this.checkPath=function(t,e){for(var i=0;i<this.events.length;i++){var n=this.events[i];e.isPointInPath&&e.isPointInPath(n.x,n.y)&&(this.eventElements[i]=t)}},this.checkBoundingBox=function(t,e){for(var i=0;i<this.events.length;i++){var n=this.events[i];e.isPointInBox(n.x,n.y)&&(this.eventElements[i]=t)}},this.runEvents=function(){R.ctx.canvas.style.cursor="";for(var t=0;t<this.events.length;t++)for(var e=this.events[t],i=this.eventElements[t];i;)e.run(i),i=i.parent;this.events=[],this.eventElements=[]}},R);1==t.childNodes.length&&"OBJECT"==t.childNodes[0].nodeName||(t.svg=r),r.opts=n;var s=t.getContext("2d");void 0!==e.documentElement?r.loadXmlDoc(s,e):"<"==e.substr(0,1)?r.loadXml(s,e):r.load(s,e)}else for(var a=document.getElementsByTagName("svg"),o=0;o<a.length;o++){var h=a[o],l=document.createElement("canvas");l.width=h.clientWidth,l.height=h.clientHeight,h.parentNode.insertBefore(l,h),h.parentNode.removeChild(h);var u=document.createElement("div");u.appendChild(h),canvg(l,u.innerHTML)}}}(),"undefined"!=typeof CanvasRenderingContext2D&&(CanvasRenderingContext2D.prototype.drawSvg=function(t,e,i,n,r){canvg(this.canvas,t,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:e,offsetY:i,scaleWidth:n,scaleHeight:r})}),canvg}},f[1]={value:function(t){var e=f.r(34),s=f.r(0);return e.createClass("Output",{constructor:function(t){this.formula=t},toJPG:function(t){i(this.formula,"image/jpeg",t)},toPNG:function(t){i(this.formula,"image/png",t)}});function i(t,e,i){var n,r,s,a=t.container.getRenderBox();return function(t,e,i,n){var r=arguments;{o.apply(null,r)}}(t.node.ownerDocument,{width:a.width,height:a.height,content:(n=t.node,r=n.ownerDocument.createElement("div"),s=['<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="',n.getAttribute("width"),'" height="',n.getAttribute("height"),'">'],r.appendChild(n.cloneNode(!0)),r.innerHTML.replace(/<svg[^>]+?>/i,s.join("")).replace(/&nbsp;/g,""))},e,i)}function a(t,e,i,n){var r=t.createElement("canvas"),s=r.getContext("2d");return r.width=e,r.height=i,"image/png"!==n&&(s.fillStyle="white",s.fillRect(0,0,r.width,r.height)),r}function o(t,e,i,n){var r=a(t,e.width,e.height,i);r.style.cssText="position: absolute; top: 0; left: 100000px; z-index: -1;",window.setTimeout(function(){t.body.appendChild(r),s(r,e.content),t.body.removeChild(r),n(r.toDataURL(i))},0)}}},f[2]={value:function(){return["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","&#x237;","&#x131;","&#x3b1;","&#x3b2;","&#x3b3;","&#x3b4;","&#x3b5;","&#x3b6;","&#x3b7;","&#x3b8;","&#x3b9;","&#x3ba;","&#x3bb;","&#x3bc;","&#x3bd;","&#x3be;","&#x3bf;","&#x3c0;","&#x3c1;","&#x3c2;","&#x3c3;","&#x3c4;","&#x3c5;","&#x3c6;","&#x3c7;","&#x3c8;","&#x3c9;","&#x3d1;","&#x3d5;","&#x3d6;","&#x3de;","&#x3dc;","&#x3f5;","&#x3f1;","&#x3f9;","&#x211c;","&#x2135;","&#x2111;","&#x2127;","&#x2136;","&#x2137;","&#x2138;","&#xf0;","&#x210f;","&#x2141;","&#x210e;","&#x2202;","&#x2118;","&#x214c;","&#x2132;","&#x2201;","&#x2113;","&#x24c8;","(",")","&#x393;","&#x394;","&#x395;","&#x396;","&#x397;","&#x398;","&#x399;","&#x39a;","&#x39b;","&#x39c;","&#x39d;","&#x39e;","&#x39f;","&#x3a0;","&#x3a1;","&#x3a3;","&#x3a4;","&#x3a5;","&#x3a6;","&#x3a7;","&#x3a8;","&#x3a9;","&#x391;","&#x392;","#","!","$","%","&#x26;","&#x2220;","&#x2032;","&#x2035;","&#x2605;","&#x25c6;","&#x25a0;","&#x25b2;","&#x25bc;","&#x22a4;","&#x22a5;","&#x2663;","&#x2660;","&#x2662;","&#x2661;","&#x2203;","&#x2204;","&#x266d;","&#x266e;","&#x266f;","&#x2200;","&#x221e;","&#x2221;","&#x2207;","&#xac;","&#x2222;","&#x221a;","&#x25b3;","&#x25bd;","&#x2205;","&#xf8;","&#x25c7;","&#x25c0;","&#x25b8;","[","]","{","}","&#x3008;","&#x3009;","&#x3f0;",",",".","/",":",";","?","\\","&#x22ee;","&#x22ef;","&#x22f0;","&#x2026;","@","&#x22;","'","|","^","`","&#x201c;","_","*","+","-","&#x2210;","&#x22bc;","&#x22bb;","&#x25ef;","&#x22a1;","&#x229f;","&#x229e;","&#x22a0;","&#x2022;","&#x2229;","&#x222a;","&#x22d2;","&#x22d3;","&#x22d0;","&#x22d1;","&#xb7;","&#x25aa;","&#x25e6;","&#x229b;","&#x229a;","&#x2296;","&#x2299;","&#x229d;","&#x2295;","&#x2297;","&#x2298;","&#xb1;","&#x2213;","&#x22cf;","&#x22ce;","&#x2020;","&#x2021;","&#x22c4;","&#xf7;","&#x22c7;","&#x2214;","&#x232d;","&#x22d7;","&#x22d6;","&#x22c9;","&#x22ca;","&#x22cb;","&#x22cc;","&#x2293;","&#x2294;","&#x2291;","&#x2292;","&#x228f;","&#x2290;","&#x22c6;","&#xd7;","&#x22b3;","&#x22b2;","&#x22b5;","&#x22b4;","&#x228e;","&#x2228;","&#x2227;","&#x2240;","&#x3c;","=","&#x3e;","&#x2248;","&#x2247;","&#x224d;","&#x2252;","&#x2253;","&#x224a;","&#x223d;","&#x2241;","&#x2242;","&#x2243;","&#x22cd;","&#x224f;","&#x224e;","&#x2257;","&#x2245;","&#x22de;","&#x22df;","&#x2250;","&#x2251;","&#x2256;","&#x2a96;","&#x2a95;","&#x2261;","&#x2265;","&#x2264;","&#x2266;","&#x2267;","&#x2a7e;","&#x2a7d;","&#x226b;","&#x226a;","&#x2268;","&#x2269;","&#x22d8;","&#x22d9;","&#x2a87;","&#x2a88;","&#x2a89;","&#x2a8a;","&#x22e7;","&#x22e6;","&#x2a86;","&#x2a85;","&#x22db;","&#x22da;","&#x2a8b;","&#x2a8c;","&#x2277;","&#x2276;","&#x2273;","&#x2272;","&#x232e;","&#x232f;","&#x226f;","&#x2271;","&#x2270;","&#x226e;","&#x2331;","&#x2330;","&#x2332;","&#x2333;","&#x226c;","&#x2280;","&#x2281;","&#x22e0;","&#x22e1;","&#x227a;","&#x227b;","&#x227c;","&#x227d;","&#x227e;","&#x227f;","&#x2282;","&#x2283;","&#x2288;","&#x2289;","&#x2286;","&#x2287;","&#x228a;","&#x228b;","&#x2ab7;","&#x2ab8;","&#x2aaf;","&#x2ab0;","&#x2ab9;","&#x2aba;","&#x2ab5;","&#x2ab6;","&#x22e8;","&#x22e9;","&#x223c;","&#x225c;","&#x21b6;","&#x21b7;","&#x21ba;","&#x21bb;","&#x21be;","&#x21bf;","&#x21c2;","&#x21c3;","&#x21c4;","&#x21c6;","&#x21c8;","&#x21ca;","&#x21cb;","&#x21cc;","&#x21cd;","&#x21ce;","&#x21cf;","&#x21d0;","&#x21d1;","&#x21d2;","&#x21d3;","&#x21d4;","&#x21d5;","&#x21da;","&#x21db;","&#x21dd;","&#x21ab;","&#x21ac;","&#x21ad;","&#x21ae;","&#x2190;","&#x2191;","&#x2192;","&#x2193;","&#x2194;","&#x2195;","&#x2196;","&#x2197;","&#x2198;","&#x2199;","&#x219e;","&#x21a0;","&#x21a2;","&#x21a3;","&#x21b0;","&#x21b1;","&#x22a2;","&#x22a3;","&#x22a8;","&#x22a9;","&#x22aa;","&#x22ad;","&#x22af;","&#x22b8;","&#x22ba;","&#x22d4;","&#x22ea;","&#x22eb;","&#x22ec;","&#x22ed;","&#x2308;","&#x2309;","&#x230a;","&#x230b;","&#x2acb;","&#x2acc;","&#x2ac5;","&#x2ac6;","&#x2208;","&#x220b;","&#x221d;","&#x2224;","&#x2226;","&#x2234;","&#x2235;","&#x220d;","&#x22c8;","&#x2322;","&#x2323;","&#x2223;","&#x2225;","&#x23d0;","&#x23d1;","&#x23d2;","&#x23d3;","&#x2ac7;","&#x2ac8;","&#x22ae;","&#x22ac;","&#x2ac9;","&#x23d4;","&#x23d5;","&#x23d6;","&#x23d7;","&#x21c7;","&#x21c9;","&#x21bc;","&#x21bd;","&#x21c0;","&#x21c1;","&#x219a;","&#x219b;","&#x27f5;","&#x27f6;","&#x27f7;","&#x27f9;","&#x27f8;","&#x27fa;","&#x2262;","&#x2260;","&#x2209;"]}},f[3]={value:function(){return{defaultFont:"KF AMS MAIN"}}},f[4]={value:function(t){var n=f.r(34),r=document.createElement("div");function e(t){var e,i=new n.Text;return"innerHTML"in i.node?i.node.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"):-1!=t.indexOf(" ")&&(e=t,r.innerHTML='<svg><text gg="asfdas">'+e.replace(/\s/gi,"&nbsp;")+"</text></svg>",t=r.firstChild.firstChild.textContent),i.setContent(t),i}return{create:function(t){return e(t)}}}},f[5]={value:function(t){var i=f.r(34),e=f.r(47).font,r=f.r(25),n=f.r(4);return i.createClass("Text",{base:f.r(46),constructor:function(t,e){this.callBase(),this.fontFamily=e,this.fontSize=50,this.content=t||"",this.box.remove(),this.translationContent=this.translation(this.content),this.contentShape=new i.Group,this.contentNode=this.createContent(),this.contentShape.addShape(this.contentNode),this.addShape(this.contentShape)},createContent:function(){var t=n.create(this.translationContent);return t.setAttr({"font-family":this.fontFamily,"font-size":50,x:0,y:e.offset}),t},setFamily:function(t){this.fontFamily=t,this.contentNode.setAttr("font-family",t)},setFontSize:function(t){this.fontSize=t,this.contentNode.setAttr("font-size",t+"px"),this.contentNode.setAttr("y",t/50*e.offset)},getBaseHeight:function(){for(var t=this.contentShape.getItems(),e=null,i=0,n=0;e=t[i];)n=Math.max(n,e.getHeight()),i++;return n},translation:function(t){var n=this.fontFamily;return t.replace(/``/g,"“").replace(/\\([a-zA-Z,]+)\\/g,function(t,e){if(","===e)return" ";var i=r.getCharacterValue(e,n);return i||""})}})}},f[6]={value:function(){return{UNKNOWN:-1,EXP:0,COMPOUND_EXP:1,OP:2}}},f[7]={value:function(){return{SIDE:"side",FOLLOW:"follow"}}},f[8]={value:function(t){return f.r(34).createClass("SubscriptExpression",{base:f.r(17),constructor:function(t,e){this.callBase(t,null,e),this.setFlag("Subscript")}})}},f[9]={value:function(t){return f.r(34).createClass("SuperscriptExpression",{base:f.r(17),constructor:function(t,e){this.callBase(t,e,null),this.setFlag("Superscript")}})}},f[10]={value:function(t){return f.r(34).createClass("BinaryExpression",{base:f.r(19),constructor:function(t,e){this.callBase(),this.setFirstOperand(t),this.setLastOperand(e)},setFirstOperand:function(t){return this.setOperand(t,0)},getFirstOperand:function(){return this.getOperand(0)},setLastOperand:function(t){return this.setOperand(t,1)},getLastOperand:function(){return this.getOperand(1)}})}},f[11]={value:function(t){var e=f.r(34),n=f.r(35);return e.createClass("BracketsExpression",{base:f.r(19),constructor:function(t,e,i){this.callBase(),this.setFlag("Brackets"),2===arguments.length&&(i=e,e=t),this.leftSymbol=t,this.rightSymbol=e,this.setOperator(new n),this.setOperand(i,0)},getLeftSymbol:function(){return this.leftSymbol},getRightSymbol:function(){return this.rightSymbol}})}},f[12]={value:function(t){var n=f.r(34),i=f.r(47).font,e=f.r(36);return n.createClass("CombinationExpression",{base:f.r(19),constructor:function(){this.callBase(),this.setFlag("Combination"),this.setOperator(new e),n.Utils.each(arguments,function(t,e){this.setOperand(t,e)},this)},getRenderBox:function(t){var e=this.callBase(t);return 0===this.getOperands().length&&(e.height=i.spaceHeight),e},getBaseline:function(e){var i=0,t=this.getOperands();return 0===t.length?this.callBase(e):(n.Utils.each(t,function(t){i=Math.max(t.getBaseline(e),i)}),i)},getMeanline:function(e){var i=1e7,t=this.getOperands();return 0===t.length?this.callBase(e):(n.Utils.each(t,function(t){i=Math.min(t.getMeanline(e),i)}),i)}})}},f[13]={value:function(t){var e=f.r(34),i=f.r(38);return e.createClass("FractionExpression",{base:f.r(10),constructor:function(t,e){this.callBase(t,e),this.setFlag("Fraction"),this.setOperator(new i)},getBaseline:function(t){var e=this.getOperand(1),i=e.getRenderBox(t);return i.y+e.getBaselineProportion()*i.height},getMeanline:function(t){var e=this.getOperand(0),i=e.getRenderBox(t);return e.getMeanlineProportion()*i.height}})}},f[14]={value:function(t){var e=f.r(34),i=f.r(47).func,r=f.r(39);return e.createClass("FunctionExpression",{base:f.r(19),constructor:function(t,e,i,n){this.callBase(),this.setFlag("Func"),this.funcName=t,this.setOperator(new r(t)),this.setExpr(e),this.setSuperscript(i),this.setSubscript(n)},isSideScript:function(){return!i["ud-script"][this.funcName]},setExpr:function(t){return this.setOperand(t,0)},setSuperscript:function(t){return this.setOperand(t,1)},setSubscript:function(t){return this.setOperand(t,2)}})}},f[15]={value:function(t){var e=f.r(34),n=f.r(40);return e.createClass("IntegrationExpression",{base:f.r(19),constructor:function(t,e,i){this.callBase(),this.setFlag("Integration"),this.setOperator(new n),this.setIntegrand(t),this.setSuperscript(e),this.setSubscript(i)},setType:function(t){return this.getOperator().setType(t),this},resetType:function(){return this.getOperator().resetType(),this},setIntegrand:function(t){this.setOperand(t,0)},setSuperscript:function(t){this.setOperand(t,1)},setSubscript:function(t){this.setOperand(t,2)}})}},f[16]={value:function(t){var e=f.r(34),i=f.r(42);return e.createClass("RadicalExpression",{base:f.r(10),constructor:function(t,e){this.callBase(t,e),this.setFlag("Radicand"),this.setOperator(new i)},setRadicand:function(t){return this.setFirstOperand(t)},getRadicand:function(){return this.getFirstOperand()},setExponent:function(t){return this.setLastOperand(t)},getExponent:function(){return this.getLastOperand()}})}},f[17]={value:function(t){var e=f.r(34),n=f.r(43);return e.createClass("ScriptExpression",{base:f.r(19),constructor:function(t,e,i){this.callBase(),this.setFlag("Script"),this.setOperator(new n),this.setOpd(t),this.setSuperscript(e),this.setSubscript(i)},setOpd:function(t){this.setOperand(t,0)},setSuperscript:function(t){this.setOperand(t,1)},setSubscript:function(t){this.setOperand(t,2)}})}},f[18]={value:function(t){var e=f.r(34),n=f.r(44);return e.createClass("SummationExpression",{base:f.r(19),constructor:function(t,e,i){this.callBase(),this.setFlag("Summation"),this.setOperator(new n),this.setExpr(t),this.setSuperscript(e),this.setSubscript(i)},setExpr:function(t){this.setOperand(t,0)},setSuperscript:function(t){this.setOperand(t,1)},setSubscript:function(t){this.setOperand(t,2)}})}},f[19]={value:function(t){var e=f.r(34),i=f.r(6),n=f.r(21);return e.createClass("CompoundExpression",{base:f.r(21),constructor:function(){this.callBase(),this.type=i.COMPOUND_EXP,this.operands=[],this.operator=null,this.operatorBox=new e.Group,this.operatorBox.setAttr("data-type","kf-editor-exp-op-box"),this.operandBox=new e.Group,this.operandBox.setAttr("data-type","kf-editor-exp-operand-box"),this.setChildren(0,this.operatorBox),this.setChildren(1,this.operandBox)},setOperator:function(t){return void 0===t?this:(this.operator&&this.operator.remove(),this.operatorBox.addShape(t),this.operator=t,this.operator.setParentExpression(this),t.expression=this)},getOperator:function(){return this.operator},setOperand:function(t,e,i){return!1===i?this.operands[e]=t:(t=n.wrap(t),this.operands[e]&&this.operands[e].remove(),this.operands[e]=t,this.operandBox.addShape(t)),this},getOperand:function(t){return this.operands[t]},getOperands:function(){return this.operands},addedCall:function(){return this.operator.applyOperand.apply(this.operator,this.operands),this}})}},f[20]={value:function(t){var e=f.r(34),i=f.r(47).font,n=f.r(21),r=e.createClass("EmptyExpression",{base:n,constructor:function(){this.callBase(),this.setFlag("Empty")},getRenderBox:function(){return{width:0,height:i.spaceHeight,x:0,y:0}}});return r.isEmpty=function(t){return t instanceof r},n.registerWrap("empty",function(t){if(null==t)return new r}),r}},f[21]={value:function(t){var n=f.r(34),e=f.r(6),i=f.r(47).font,r=[],s={},a=n.createClass("Expression",{base:f.r(46),constructor:function(){this.callBase(),this.type=e.EXP,this._offset={top:0,bottom:0},this.children=[],this.box.fill("transparent").setAttr("data-type","kf-editor-exp-box"),this.box.setAttr("data-type","kf-editor-exp-bg-box"),this.expContent=new n.Group,this.expContent.setAttr("data-type","kf-editor-exp-content-box"),this.addShape(this.expContent)},getChildren:function(){return this.children},getChild:function(t){return this.children[t]||null},getTopOffset:function(){return this._offset.top},getBottomOffset:function(){return this._offset.bottom},getOffset:function(){return this._offset},setTopOffset:function(t){this._offset.top=t},setBottomOffset:function(t){this._offset.bottom=t},setOffset:function(t,e){this._offset.top=t,this._offset.bottom=e},setFlag:function(t){this.setAttr("data-flag",t||"Expression")},setChildren:function(t,e){this.children[t]&&this.children[t].remove(),this.children[t]=e,this.expContent.addShape(e)},getBaselineProportion:function(){return i.baselinePosition},getMeanlineProportion:function(){return i.meanlinePosition},getBaseline:function(t){return this.getRenderBox(t).height*i.baselinePosition-3},getMeanline:function(t){return this.getRenderBox(t).height*i.meanlinePosition-1},getAscenderline:function(){return this.getFixRenderBox().height*i.ascenderPosition},getDescenderline:function(){return this.getFixRenderBox().height*i.descenderPosition},translateElement:function(t,e){this.expContent.translate(t,e)},expand:function(t,e){var i=this.getFixRenderBox();this.setBoxSize(i.width+t,i.height+e)},getBaseWidth:function(){return this.getWidth()},getBaseHeight:function(){return this.getHeight()},updateBoxSize:function(){var t=this.expContent.getFixRenderBox();this.setBoxSize(t.width,t.height)},getBox:function(){return this.box}});return n.Utils.extend(a,{registerWrap:function(t,e){s[t]=r.length,r.push(e)},revokeWrap:function(t){var e=null;return t in s&&(e=r[s[t]],r[s[t]]=null,delete s[t]),e},wrap:function(e){var i;return n.Utils.each(r,function(t){if(t)return!(i=t(e))&&void 0}),i}}),a}},f[22]={value:function(t){var i=f.r(5),n=f.r(34),r=f.r(3),e=f.r(21),s=n.createClass("TextExpression",{base:f.r(21),constructor:function(t,e){this.callBase(),this.fontFamily=e||r.defaultFont,this.setFlag("Text"),this.content=t+"",this.textContent=new i(this.content,this.fontFamily),this.setChildren(0,this.textContent),this.setChildren(1,new n.Rect(0,0,0,0).fill("transparent"))},setFamily:function(t){this.textContent.setFamily(t)},setFontSize:function(t){this.textContent.setFontSize(t)},addedCall:function(){var t=this.textContent.getFixRenderBox();return this.getChild(1).setSize(t.width,t.height),this.updateBoxSize(),this}});return e.registerWrap("text",function(t){var e=typeof t;return"number"!=e&&"string"!=e||(t=new s(t)),t}),s}},f[23]={value:function(){return['<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">','<text id="abcd" font-family="KF AMS MAIN" font-size="50" x="0" y="0">x</text>',"</svg>"]}},f[24]={value:function(t){var s=f.r(34),e=f.r(25),l=f.r(33),u=f.r(47).font,c=f.r(2),p=[];return s.createClass("FontInstaller",{constructor:function(t,e){this.callBase(),this.resource=e||"../src/resource/",this.doc=t},mount:function(i){var t=e.getFontList(),n=0,r=this;s.Utils.each(t,function(t){var a,o,h;n++,t.meta.src=r.resource+t.meta.src,r.createFontStyle(t),a=r.doc,o=t,h=function(){var t,e;0===--n&&(t=r.doc,e=i,window.setTimeout(function(){!function(t){var e=t.createElement("div");e.style.cssText="position: absolute; top: 0; left: -100000px;",e.innerHTML=f.r(23).join(""),t.body.appendChild(e);var i=e.getElementsByTagName("text")[0].getBBox();u.spaceHeight=i.height,u.topSpace=-i.y-u.baseline,u.bottomSpace=u.spaceHeight-u.topSpace-u.baseHeight,u.offset=u.baseline+u.topSpace,u.baselinePosition=(u.topSpace+u.baseline)/u.spaceHeight,u.meanlinePosition=(u.topSpace+u.meanline)/u.spaceHeight,u.ascenderPosition=u.topSpace/u.spaceHeight,u.descenderPosition=(u.topSpace+u.baseHeight)/u.spaceHeight,t.body.removeChild(e)}(t),s.Utils.each(p,function(t){t.parentNode.removeChild(t)}),p=[],e()},100))},l.get(o.meta.src,function(t,e){var i,n,r,s;"success"===e&&(i=a,n=o,r=document.createElement("div"),s=n.meta.fontFamily,r.style.cssText="position: absolute; top: -10000px; left: -100000px;",r.style.fontFamily=s,r.innerHTML=c.join(""),i.body.appendChild(r),p.push(r)),h()})})},createFontStyle:function(t){var e=this.doc.createElement("style");e.setAttribute("type","text/css"),e.innerHTML='@font-face{\nfont-family: "${fontFamily}";\nsrc: url("${src}");\n}'.replace("${fontFamily}",t.meta.fontFamily).replace("${src}",t.meta.src),this.doc.head.appendChild(e)}})}},f[25]={value:function(t){var i={},e=f.r(34),n=f.r(47).font.list;return e.Utils.each(n,function(t){i[t.meta.fontFamily]=t}),{getFontList:function(){return i},getCharacterValue:function(t,e){return i[e]&&i[e].map[t]||null}}}},f[26]={value:function(){return{meta:{fontFamily:"KF AMS BB",src:"KF_AMS_BB.woff"}}}},f[27]={value:function(){return{meta:{fontFamily:"KF AMS CAL",src:"KF_AMS_CAL.woff"}}}},f[28]={value:function(){return{meta:{fontFamily:"KF AMS FRAK",src:"KF_AMS_FRAK.woff"}}}},f[29]={value:function(){return{meta:{fontFamily:"KF AMS MAIN",src:"KF_AMS_MAIN.woff"},map:{Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",varkappa:"ϰ",chi:"χ",psi:"ψ",omega:"ω",digamma:"Ϝ",varepsilon:"ϵ",varrho:"ϱ",varphi:"ϕ",vartheta:"ϑ",varpi:"ϖ",varsigma:"Ϲ",aleph:"ℵ",beth:"ℶ",daleth:"ℸ",gimel:"ℷ",eth:"ð",hbar:"ℎ",hslash:"ℏ",mho:"℧",partial:"∂",wp:"℘",Game:"⅁",Bbbk:"⅌",Finv:"Ⅎ",Im:"ℑ",Re:"ℜ",complement:"∁",ell:"ℓ",circledS:"Ⓢ",imath:"ı",jmath:"ȷ",doublecap:"⋒",Cap:"⋒",doublecup:"⋓",Cup:"⋓",ast:"*",divideontimes:"⋇",rightthreetimes:"⋌",leftthreetimes:"⋋",cdot:"·",odot:"⊙",dotplus:"∔",rtimes:"⋊",ltimes:"⋉",centerdot:"▪",doublebarwedge:"⌭",setminus:"⒁",amalg:"∐",circ:"◦",bigcirc:"◯",gtrdot:"⋗",lessdot:"⋖",smallsetminus:"⒅",circledast:"⊛",circledcirc:"⊚",sqcap:"⊓",sqcup:"⊔",barwedge:"⊼",circleddash:"⊝",star:"⋆",bigtriangledown:"▽",bigtriangleup:"△",cup:"∪",cap:"∩",times:"×",mp:"∓",pm:"±",triangleleft:"⊲",triangleright:"⊳",boxdot:"⊡",curlyvee:"⋏",curlywedge:"⋎",boxminus:"⊟",boxtimes:"⊠",ominus:"⊖",oplus:"⊕",oslash:"⊘",otimes:"⊗",uplus:"⊎",boxplus:"⊞",dagger:"†",ddagger:"‡",vee:"∨",lor:"∨",veebar:"⊻",bullet:"•",diamond:"⋄",wedge:"∧",land:"∧",div:"÷",wr:"≀",geqq:"≧",lll:"⋘",llless:"⋘",ggg:"⋙",gggtr:"⋙",preccurlyeq:"≼",geqslant:"⩾",lnapprox:"⪉",preceq:"⪯",gg:"≫",lneq:"⪇",precnapprox:"⪹",approx:"≈",lneqq:"≨",precneqq:"⪵",approxeq:"≊",gnapprox:"⪊",lnsim:"⋦",precnsim:"⋨",asymp:"≍",gneq:"⪈",lvertneqq:"⌮",precsim:"≾",backsim:"∽",gneqq:"≩",ncong:"≇",risingdotseq:"≓",backsimeq:"⋍",gnsim:"⋧",sim:"∼",simeq:"≃",bumpeq:"≏",gtrapprox:"⪆",ngeq:"≱",Bumpeq:"≎",gtreqless:"⋛",ngeqq:"⌱",succ:"≻",circeq:"≗",gtreqqless:"⪌",ngeqslant:"⌳",succapprox:"⪸",cong:"≅",gtrless:"≷",ngtr:"≯",succcurlyeq:"≽",curlyeqprec:"⋞",gtrsim:"≳",nleq:"≰",succeq:"⪰",curlyeqsucc:"⋟",gvertneqq:"⌯",neq:"≠",ne:"≠",nequiv:"≢",nleqq:"⌰",succnapprox:"⪺",doteq:"≐",leq:"≤",le:"≤",nleqslant:"⌲",succneqq:"⪶",doteqdot:"≑",Doteq:"≑",leqq:"≦",nless:"≮",succnsim:"⋩",leqslant:"⩽",nprec:"⊀",succsim:"≿",eqsim:"≂",lessapprox:"⪅",npreceq:"⋠",eqslantgtr:"⪖",lesseqgtr:"⋚",nsim:"≁",eqslantless:"⪕",lesseqqgtr:"⪋",nsucc:"⊁",triangleq:"≜",eqcirc:"≖",equiv:"≡",lessgtr:"≶",nsucceq:"⋡",fallingdotseq:"≒",lesssim:"≲",prec:"≺",geq:"≥",ge:"≥",ll:"≪",precapprox:"⪷",uparrow:"↑",downarrow:"↓",updownarrow:"↕",Uparrow:"⇑",Downarrow:"⇓",Updownarrow:"⇕",circlearrowleft:"↺",circlearrowright:"↻",curvearrowleft:"↶",curvearrowright:"↷",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",leftarrow:"←",gets:"←",Leftarrow:"⇐",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",Leftrightarrow:"⇔",leftrightarrows:"⇄",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",Lleftarrow:"⇚",looparrowleft:"↫",looparrowright:"↬",multimap:"⊸",nLeftarrow:"⇍",nRightarrow:"⇏",nLeftrightarrow:"⇎",nearrow:"↗",nleftarrow:"↚",nleftrightarrow:"↮",nrightarrow:"↛",nwarrow:"↖",rightarrow:"→",to:"→",Rightarrow:"⇒",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇆",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"⇝",Rrightarrow:"⇛",searrow:"↘",swarrow:"↙",twoheadleftarrow:"↞",twoheadrightarrow:"↠",upharpoonleft:"↿",upharpoonright:"↾",restriction:"↾",upuparrows:"⇈",Lsh:"↰",Rsh:"↱",longleftarrow:"⟵",longrightarrow:"⟶",Longleftarrow:"⟸",Longrightarrow:"⟹",implies:"⟹",longleftrightarrow:"⟷",Longleftrightarrow:"⟺",backepsilon:"∍",because:"∵",therefore:"∴",between:"≬",blacktriangleleft:"◀",blacktriangleright:"▸",dashv:"⊣",bowtie:"⋈",frown:"⌢",in:"∈",notin:"∉",mid:"∣",parallel:"∥",models:"⊨",ni:"∋",owns:"∋",nmid:"∤",nparallel:"∦",nshortmid:"⏒",nshortparallel:"⏓",nsubseteq:"⊈",nsubseteqq:"⫇",nsupseteq:"⊉",nsupseteqq:"⫈",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nvdash:"⊬",nVdash:"⊮",nvDash:"⊭",nVDash:"⊯",perp:"⊥",pitchfork:"⋔",propto:"∝",shortmid:"⏐",shortparallel:"⏑",smile:"⌣",sqsubset:"⊏",sqsubseteq:"⊑",sqsupset:"⊐",sqsupseteq:"⊒",subset:"⊂",Subset:"⋐",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",supset:"⊃",Supset:"⋑",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",trianglelefteq:"⊴",trianglerighteq:"⊵",varpropto:"⫉",varsubsetneq:"⏔",varsubsetneqq:"⏖",varsupsetneq:"⏕",varsupsetneqq:"⏗",vdash:"⊢",Vdash:"⊩",vDash:"⊨",Vvdash:"⊪",vert:"|",Vert:"ǁ","|":"ǁ","{":"{","}":"}",backslash:"\\",langle:"〈",rangle:"〉",lceil:"⌈",rceil:"⌉",lbrace:"{",rbrace:"}",lfloor:"⌊",rfloor:"⌋",cdots:"⋯",ddots:"⋰",vdots:"⋮",dots:"…",ldots:"…","#":"#",bot:"⊥",angle:"∠",backprime:"‵",bigstar:"★",blacklozenge:"◆",blacksquare:"■",blacktriangle:"▲",blacktriangledown:"▼",clubsuit:"♣",diagdown:"⒁",diagup:"⒂",diamondsuit:"♢",emptyset:"ø",exists:"∃",flat:"♭",forall:"∀",heartsuit:"♡",infty:"∞",lozenge:"◇",measuredangle:"∡",nabla:"∇",natural:"♮",neg:"¬",lnot:"¬",nexists:"∄",prime:"′",sharp:"♯",spadesuit:"♠",sphericalangle:"∢",surd:"√",top:"⊤",varnothing:"∅",triangle:"△",triangledown:"▽"}}}},f[30]={value:function(){return{meta:{fontFamily:"KF AMS ROMAN",src:"KF_AMS_ROMAN.woff"}}}},f[31]={value:function(t){var r=f.r(34),s=f.r(6),e=f.r(25),i=f.r(24),n={fontsize:50,autoresize:!0,padding:[0]},a=f.r(1),o=r.createClass("ExpressionWrap",{constructor:function(t,e){this.wrap=new r.Group,this.bg=new r.Rect(0,0,0,0).fill("transparent"),this.exp=t,this.config=e,this.wrap.setAttr("data-type","kf-exp-wrap"),this.bg.setAttr("data-type","kf-exp-wrap-bg"),this.wrap.addShape(this.bg),this.wrap.addShape(this.exp)},getWrapShape:function(){return this.wrap},getExpression:function(){return this.exp},getBackground:function(){return this.bg},resize:function(){var t=this.config.padding,e=this.exp.getFixRenderBox();1===t.length&&(t[1]=t[0]),this.bg.setSize(2*t[1]+e.width,2*t[0]+e.height),this.exp.translate(t[1],t[0])}}),h=r.createClass("Formula",{base:f.r(32),constructor:function(t,e){this.callBase(t),this.expressions=[],this.fontInstaller=new i(this),this.config=r.Utils.extend({},n,e),this.initEnvironment(),this.initInnerFont()},getContentContainer:function(){return this.container},initEnvironment:function(){this.zoom=this.config.fontsize/50,"width"in this.config&&this.setWidth(this.config.width),"height"in this.config&&this.setHeight(this.config.height),this.node.setAttribute("font-size",n.fontsize)},initInnerFont:function(){var t=e.getFontList(),n=this;r.Utils.each(t,function(t){var e,i;e=t,(i=n.doc.createElement("style")).setAttribute("type","text/css"),i.innerHTML='@font-face{font-family: "${fontFamily}";font-style: normal;src: url("${src}") format("woff");}'.replace("${fontFamily}",e.meta.fontFamily).replace("${src}",e.meta.src),n.resourceNode.appendChild(i)})},insertExpression:function(t,e){var i=this.wrap(t);this.container.clearTransform(),this.expressions.splice(e,0,i.getWrapShape()),this.addShape(i.getWrapShape()),function t(e){var i=0;if(!e)return;if(e.getType()===s.EXP)for(var n=0,i=e.getChildren().length;n<i;n++)t(e.getChild(n));else if(e.getType()===s.COMPOUND_EXP){for(var n=0,i=e.getOperands().length;n<i;n++)t(e.getOperand(n));t(e.getOperator())}e.addedCall&&e.addedCall()}.call(this,i.getExpression()),i.resize(),function(){var i=0;return r.Utils.each(this.expressions,function(t){var e;t&&(t.setMatrix(new r.Matrix(1,0,0,1,0,0)),e=t.getFixRenderBox(),t.translate(0-e.x,i),i+=e.height+10)}),this}.call(this),this.resetZoom(),this.config.autoresize&&this.resize()},appendExpression:function(t){this.insertExpression(t,this.expressions.length)},resize:function(){var t=this.container.getRenderBox("paper");this.node.setAttribute("width",t.width),this.node.setAttribute("height",t.height)},resetZoom:function(){var t=this.zoom/this.getBaseZoom();0!=t&&this.container.scale(t)},wrap:function(t){return new o(t,this.config)},clear:function(){this.callBase(),this.expressions=[]},clearExpressions:function(){r.Utils.each(this.expressions,function(t){t.remove()}),this.expressions=[]},toJPG:function(t){new a(this).toJPG(t)},toPNG:function(t){new a(this).toPNG(t)}});return r.Utils.extend(h,{registerFont:function(t){e.registerFont(t)}}),h}},f[32]={value:function(t){var e=f.r(34);return e.createClass("FPaper",{base:e.Paper,constructor:function(t){this.callBase(t),this.doc=t.ownerDocument,this.container=new e.Group,this.container.setAttr("data-type","kf-container"),this.background=new e.Group,this.background.setAttr("data-type","kf-bg"),this.baseZoom=1,this.zoom=1,this.base("addShape",this.background),this.base("addShape",this.container)},getZoom:function(){return this.zoom},getBaseZoom:function(){return this.baseZoom},addShape:function(t,e){return this.container.addShape(t,e)},getBackground:function(){return this.background},removeShape:function(t){return this.container.removeShape(t)},clear:function(){return this.container.clear()}})}},f[33]={value:function(){if(!window.jQuery)throw new Error("Missing jQuery");return window.jQuery}},f[34]={value:function(){if(!window.kity)throw new Error("Missing Kity Graphic Lib");return window.kity}},f[35]={value:function(t){var h=f.r(34),l=f.r(5);return h.createClass("BracketsOperator",{base:f.r(41),constructor:function(){this.callBase("Brackets")},applyOperand:function(t){(function(t){var e=this.getParentExpression().getLeftSymbol(),i=this.getParentExpression().getRightSymbol(),n=t.getFixRenderBox().height,r=new h.Group,s=0,a=new l(e,"KF AMS MAIN").fill("black"),o=new l(i,"KF AMS MAIN").fill("black");a.setFontSize(n),o.setFontSize(n),this.addOperatorShape(r.addShape(a).addShape(o)),s+=a.getFixRenderBox().width,t.translate(s,0),s+=t.getFixRenderBox().width,o.translate(s,0)}).call(this,t)}})}},f[36]={value:function(t){var e=f.r(34);return e.createClass("CombinationOperator",{base:f.r(41),constructor:function(){this.callBase("Combination")},applyOperand:function(){var n=0,t=arguments,r=0,s=0,a=0,o=[],h=[];e.Utils.each(t,function(t){var e=t.getFixRenderBox(),i=t.getOffset();e.height-=i.top+i.bottom,o.push(e),h.push(i),s=Math.max(i.top,s),a=Math.max(i.bottom,a),r=Math.max(e.height,r)}),e.Utils.each(t,function(t,e){var i=o[e];t.translate(n-i.x,(r-(i.y+i.height))/2+a-h[e].bottom),n+=i.width}),this.parentExpression.setOffset(s,a),this.parentExpression.updateBoxSize()}})}},f[37]={value:function(t){var s=f.r(34),a=f.r(20),o={subOffset:0,supOffset:0,zoom:.66};return s.createClass("ScriptController",{constructor:function(t,e,i,n,r){this.observer=t.getParentExpression(),this.target=e,this.sup=i,this.sub=n,this.options=s.Utils.extend({},o,r)},applyUpDown:function(){var t=this.target,e=this.sup,i=this.sub,n=this.options;e.scale(n.zoom),i.scale(n.zoom);var r=t.getFixRenderBox();return a.isEmpty(e)&&a.isEmpty(i)?{width:r.width,height:r.height,top:0,bottom:0}:!a.isEmpty(e)&&a.isEmpty(i)?this.applyUp(t,e):a.isEmpty(e)&&!a.isEmpty(i)?this.applyDown(t,i):this.applyUpDownScript(t,e,i)},applySide:function(){var t=this.target,e=this.sup,i=this.sub;if(a.isEmpty(e)&&a.isEmpty(i)){var n=t.getRenderBox(this.observer);return{width:n.width,height:n.height,top:0,bottom:0}}return a.isEmpty(e)&&!a.isEmpty(i)?this.applySideSub(t,i):!a.isEmpty(e)&&a.isEmpty(i)?this.applySideSuper(t,e):this.applySideScript(t,e,i)},applySideSuper:function(t,e){e.scale(this.options.zoom);var i=t.getRenderBox(this.observer),n=e.getRenderBox(this.observer),r=t.getMeanline(this.observer),s=e.getBaseline(this.observer)-r,a={top:0,bottom:0,width:i.width+n.width,height:i.height};return e.translate(i.width,0),this.options.supOffset&&e.translate(this.options.supOffset,0),0<s?(t.translate(0,s),a.bottom=s,a.height+=s):e.translate(0,-s),a},applySideSub:function(t,e){e.scale(this.options.zoom);var i=t.getRenderBox(this.observer),n=e.getRenderBox(this.observer),r=e.getOffset(),s=t.getBaseline(this.observer),a=(n.height+r.top+r.bottom)/2,o=i.height-s-a,h={top:0,bottom:0,width:i.width+n.width,height:i.height};return e.translate(i.width,r.top+s-a),this.options.subOffset&&e.translate(this.options.subOffset,0),o<0&&(h.top=-o,h.height-=o),h},applySideScript:function(t,e,i){e.scale(this.options.zoom),i.scale(this.options.zoom);var n=t.getRenderBox(this.observer),r=i.getRenderBox(this.observer),s=e.getRenderBox(this.observer),a=t.getMeanline(this.observer),o=t.getBaseline(this.observer),h=e.getBaseline(this.observer),l=i.getAscenderline(this.observer),u=a+2*(o-a)/3,c=a-h,p=n.height-u-(r.height-l),f={top:0,bottom:0,width:n.width+Math.max(r.width,s.width),height:n.height};return e.translate(n.width,c),i.translate(n.width,u-l),this.options.supOffset&&e.translate(this.options.supOffset,0),this.options.subOffset&&i.translate(this.options.subOffset,0),0<c?p<0&&(n.height-=p,f.top=-p):(t.translate(0,-c),e.translate(0,-c),i.translate(0,-c),f.height-=c,0<p?f.bottom=-c:(f.height-=p,(p=-p)<(c=-c)?f.bottom=c-p:f.top=p-c)),f},applyUp:function(t,e){var i=e.getFixRenderBox(),n=t.getFixRenderBox(),r={width:Math.max(n.width,i.width),height:i.height+n.height,top:0,bottom:i.height};return e.translate((r.width-i.width)/2,0),t.translate((r.width-n.width)/2,i.height),r},applyDown:function(t,e){var i=e.getFixRenderBox(),n=t.getFixRenderBox(),r={width:Math.max(n.width,i.width),height:i.height+n.height,top:i.height,bottom:0};return e.translate((r.width-i.width)/2,n.height),t.translate((r.width-n.width)/2,0),r},applyUpDownScript:function(t,e,i){var n=e.getFixRenderBox(),r=i.getFixRenderBox(),s=t.getFixRenderBox(),a={width:Math.max(s.width,n.width,r.width),height:n.height+r.height+s.height,top:0,bottom:0};return e.translate((a.width-n.width)/2,0),t.translate((a.width-s.width)/2,n.height),i.translate((a.width-r.width)/2,n.height+s.height),a}})}},f[38]={value:function(t){var c=f.r(34),p=f.r(47).zoom;return c.createClass("FractionOperator",{base:f.r(41),constructor:function(){this.callBase("Fraction")},applyOperand:function(t,e){t.scale(p),e.scale(p);var i,n,r=Math.ceil(t.getWidth()),s=Math.ceil(e.getWidth()),a=Math.ceil(t.getHeight()),o=Math.ceil(e.getHeight()),h=Math.max(r,s),l=Math.max(a,o),u=(i=h,n=3,new c.Rect(i+2*n,1).fill("black"));this.addOperatorShape(u),t.translate((h-r)/2+3,0),u.translate(0,a+1),e.translate((h-s)/2+3,a+u.getHeight()+2),this.parentExpression.setOffset(l-a,l-o),this.parentExpression.expand(2,2),this.parentExpression.translateElement(1,1)}})}},f[39]={value:function(t){var e=f.r(34),h=f.r(5),l=f.r(37);return e.createClass("FunctionOperator",{base:f.r(41),constructor:function(t){this.callBase("Function: "+t),this.funcName=t},applyOperand:function(t,e,i){var n=function(){var t=new h(this.funcName,"KF AMS ROMAN");return this.addOperatorShape(t),t.getBaseline=function(){return t.getFixRenderBox().height},t.getMeanline=function(){return 0},t}.call(this),r=t.getFixRenderBox(),s=this.parentExpression.isSideScript()?"applySide":"applyUpDown",a=new l(this,n,e,i,{zoom:.5})[s](),o=(a.height+a.top+a.bottom-r.height)/2;n.translate(0,a.top),e.translate(0,a.top),i.translate(0,a.top),0<=o?t.translate(a.width+5,o):(o=-o,n.translate(0,o),e.translate(0,o),i.translate(0,o),t.translate(a.width+5,0)),this.parentExpression.expand(5,10),this.parentExpression.translateElement(5,5)}})}},f[40]={value:function(t){var a=f.r(34),o=f.r(37);return a.createClass("IntegrationOperator",{base:f.r(41),constructor:function(t){this.callBase("Integration"),this.opType=t||1},setType:function(t){this.opType=0|t},resetType:function(){this.opType=1},applyOperand:function(t,e,i){var n=this.getOperatorShape(),r=t.getFixRenderBox(),s=new o(this,n,e,i,{supOffset:3,subOffset:-15}).applySide(),a=(s.height+s.top-r.height)/2;n.translate(0,s.top),e.translate(0,s.top),i.translate(0,s.top),0<=a?t.translate(s.width+3,a):(a=-a,n.translate(0,a),e.translate(0,a),i.translate(0,a),t.translate(s.width+3,0)),this.parentExpression.expand(3,6),this.parentExpression.translateElement(3,3)},getOperatorShape:function(){var t=new a.Group,e=new a.Group,i=new a.Path("M1.318,48.226c0,0,0.044,0.066,0.134,0.134c0.292,0.313,0.626,0.447,1.006,0.447c0.246,0.022,0.358-0.044,0.604-0.268   c0.782-0.782,1.497-2.838,2.324-6.727c0.514-2.369,0.938-4.693,1.586-8.448C8.559,24.068,9.9,17.878,11.978,9.52   c0.917-3.553,1.922-7.576,3.866-8.983C16.247,0.246,16.739,0,17.274,0c1.564,0,2.503,1.162,2.592,2.57   c0,0.827-0.424,1.386-1.273,1.386c-0.671,0-1.229-0.514-1.229-1.251c0-0.805,0.514-1.095,1.185-1.274   c0.022,0-0.291-0.29-0.425-0.379c-0.201-0.134-0.514-0.224-0.737-0.224c-0.067,0-0.112,0-0.157,0.022   c-0.469,0.134-0.983,0.939-1.453,2.234c-0.537,1.475-0.961,3.174-1.631,6.548c-0.424,2.101-0.693,3.464-1.229,6.727   c-1.608,9.185-2.949,15.487-5.006,23.756c-0.514,2.034-0.849,3.24-1.207,4.335c-0.559,1.698-1.162,2.95-1.811,3.799   c-0.514,0.715-1.385,1.408-2.436,1.408c-1.363,0-2.391-1.185-2.458-2.592c0-0.804,0.447-1.363,1.273-1.363   c0.671,0,1.229,0.514,1.229,1.251C2.503,47.757,1.989,48.047,1.318,48.226z").fill("black"),n=new a.Rect(0,0,0,0).fill("transparent"),r=null;e.addShape(i),t.addShape(n),t.addShape(e),this.addOperatorShape(t);for(var s=1;s<this.opType;s++)r=new a.Use(i).translate(i.getWidth()/2*s,0),e.addShape(r);return e.scale(1.6),r=null,t.getBaseline=function(){return e.getFixRenderBox().height},t.getMeanline=function(){return 10},t}})}},f[41]={value:function(t){var e=f.r(34),i=f.r(6);return e.createClass("Operator",{base:f.r(46),constructor:function(t){this.callBase(),this.type=i.OP,this.parentExpression=null,this.operatorName=t,this.operatorShape=new e.Group,this.addShape(this.operatorShape)},applyOperand:function(){throw new Error("applyOperand is abstract")},setParentExpression:function(t){this.parentExpression=t},getParentExpression:function(){return this.parentExpression},clearParentExpression:function(){this.parentExpression=null},addOperatorShape:function(t){return this.operatorShape.addShape(t)},getOperatorShape:function(){return this.operatorShape}})}},f[42]={value:function(t){var s=f.r(34),o=1,e=2*Math.PI/360,a=Math.sin(15*e),h=Math.cos(15*e),l=Math.tan(15*e);return s.createClass("RadicalOperator",{base:f.r(41),constructor:function(){this.callBase("Radical")},applyOperand:function(t,e){(function(t,e){var i=function(t){var e=new s.Path,i=o,n=t.getHeight()/3,r=e.getDrawer();return r.moveTo(0,h*i*6),r.lineBy(a*i,h*i),r.lineBy(h*i*3,-a*i*3),r.lineBy(l*n,n),r.lineBy(a*i*3,-h*i*3),r.lineBy(-a*n,-n),r.close(),e.fill("black")}(t),n=function(t){var e=new s.Path,i=.9*t.getHeight(),n=e.getDrawer();return n.moveTo(l*i,0),n.lineTo(0,i),n.lineBy(a*o*3,h*o*3),n.lineBy(l*i+a*o*3,-(i+3*o*h)),n.close(),e.fill("black")}(t),r=function(t){var e=t.getWidth()+2*o;return new s.Rect(e,2*o).fill("black")}(t);this.addOperatorShape(i),this.addOperatorShape(n),this.addOperatorShape(r),function(t,e,i,n){var r,s={x:0,y:0},a=e.getFixRenderBox();n.scale(.66),0<(r=n.getFixRenderBox()).width&&0<r.height&&(s.y=r.height-a.height/2,s.y<0&&(n.translate(0,-s.y),s.y=0),s.x=r.width+a.height/2*l-t.x);e.translate(s.x,s.y),i.translate(s.x+t.x+o,s.y+2*o)}.call(this,function(t,e,i){var n=t.getFixRenderBox(),r=e.getFixRenderBox();return e.translate(n.width-a*o*3,0),t.translate(0,r.height-n.height),r=e.getFixRenderBox(),i.translate(r.x+r.width-o/h,0),{x:r.x+r.width-o/h,y:0}}(i,n,r),this.operatorShape,t,e),this.parentExpression.expand(0,10),this.parentExpression.translateElement(0,5)}).call(this,t,e)}})}},f[43]={value:function(t){var e=f.r(34),s=f.r(37);return e.createClass("ScriptOperator",{base:f.r(41),constructor:function(t){this.callBase(t||"Script")},applyOperand:function(t,e,i){var n=this.parentExpression,r=new s(this,t,e,i).applySide();this.getOperatorShape(),r&&n.setOffset(r.top,r.bottom),n.expand(4,2),n.translateElement(2,1)}})}},f[44]={value:function(t){var r=f.r(34),o=f.r(37);return r.createClass("SummationOperator",{base:f.r(41),constructor:function(){this.callBase("Summation"),this.displayType="equation"},applyOperand:function(t,e,i){var n=this.getOperatorShape(),r=t.getFixRenderBox(),s=new o(this,n,e,i).applyUpDown(),a=(s.height-s.top-s.bottom-r.height)/2;0<=a?t.translate(s.width+0,a+s.bottom):(a=-a,n.translate(0,a),e.translate(0,a),i.translate(0,a),t.translate(s.width+0,s.bottom)),this.parentExpression.setOffset(s.top,s.bottom),this.parentExpression.expand(0,0),this.parentExpression.translateElement(0,0)},getOperatorShape:function(){var t,e=new r.Path("M0.672,33.603c-0.432,0-0.648,0-0.648-0.264c0-0.024,0-0.144,0.24-0.432l12.433-14.569L0,0.96c0-0.264,0-0.72,0.024-0.792   C0.096,0.024,0.12,0,0.672,0h28.371l2.904,6.745h-0.6C30.531,4.8,28.898,3.72,28.298,3.336c-1.896-1.2-3.984-1.608-5.28-1.8   c-0.216-0.048-2.4-0.384-5.617-0.384H4.248l11.185,15.289c0.168,0.24,0.168,0.312,0.168,0.36c0,0.12-0.048,0.192-0.216,0.384   L3.168,31.515h14.474c4.608,0,6.96-0.624,7.464-0.744c2.76-0.72,5.305-2.352,6.241-4.848h0.6l-2.904,7.681H0.672z").fill("black"),i=new r.Rect(0,0,0,0).fill("transparent"),n=new r.Group;return n.addShape(i),n.addShape(e),e.scale(1.6),this.addOperatorShape(n),t=e.getFixRenderBox(),"inline"===this.displayType?(e.translate(5,15),i.setSize(t.width+10,t.height+25)):(e.translate(2,5),i.setSize(t.width+4,t.height+8)),n}})}},f[45]={value:function(t){var i=f.r(34),n=[],r=f.r(47).resource,s=f.r(24),e=f.r(31),a=!1;return{ready:function(t,e){a||(a=!0,function(t){t=i.Utils.extend({},r,t),/^(https?:)?\/\//.test(t.path)||(t.path=function(t){var e=location.pathname.split("/");return--e.length,e=e.join("/")+"/",[location.protocol,"//",location.host,e,t.replace(/^\//,"")].join("")}(t.path));new s(document,t.path).mount(o)}(e)),n.push(t)}};function o(){i.Utils.each(n,function(t){t(e)})}}},f[46]={value:function(t){var e=f.r(34),i=f.r(6);return e.createClass("SignGroup",{base:e.Group,constructor:function(){this.callBase(),this.box=new e.Rect(0,0,0,0),this.type=i.UNKNOWN,this.addShape(this.box),this.zoom=1},setZoom:function(t){this.zoom=t},getZoom:function(){return this.zoom},setBoxSize:function(t,e){return this.box.setSize(t,e)},setBoxWidth:function(t){return this.box.setWidth(t)},setBoxHeight:function(t){return this.box.setHeight(t)},getType:function(){return this.type},getBaseHeight:function(){return this.getHeight()},getBaseWidth:function(){return this.getWidth()},addedCall:function(){}})}},f[47]={value:function(t){return{zoom:.66,font:{meanline:Math.round(19),baseline:Math.round(40),baseHeight:50,list:[f.r(29),f.r(27),f.r(28),f.r(26),f.r(30)]},resource:{path:"src/resource/"},func:{"ud-script":{lim:!0}}}}},f[48]={value:function(t){window.kf={ResourceManager:f.r(45),Operator:f.r(41),Expression:f.r(21),CompoundExpression:f.r(19),TextExpression:f.r(22),EmptyExpression:f.r(20),CombinationExpression:f.r(12),FunctionExpression:f.r(14),FractionExpression:f.r(13),IntegrationExpression:f.r(15),RadicalExpression:f.r(16),ScriptExpression:f.r(17),SuperscriptExpression:f.r(9),SubscriptExpression:f.r(8),SummationExpression:f.r(18),BracketsExpression:f.r(11)}}};var e={"kf.start":48};!function(){var t;kity.Shape.getRenderBox;kity.extendClass(kity.Shape,{getFixRenderBox:function(){return this.getRenderBox(this.container.container)},getTranslate:function(){return this.transform.translate}});try{t="kf.start",f.r([e[t]])}catch(t){}}()}();