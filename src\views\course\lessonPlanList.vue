<template>
  <div class="lesson-plan-list">
    <!-- 筛选表单 -->
    <el-form :inline="true" :model="queryData" class="filter-form" size="small">
      <el-row :gutter="20">
        <el-col :span="6" :offset="0">
          <el-form-item label="课程编号">
            <el-input v-model="queryData.coursePlanCode" maxlength="50" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-form-item label="阶段">
            <el-select v-model="queryData.stage" placeholder="请选择">
              <el-option v-for="(item, index) in stageOptions" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-form-item label="课程类型">
            <el-select v-model="queryData.courseType" placeholder="请选择">
              <el-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6" :offset="0">
          <el-form-item label="课程名称">
            <el-input v-model="queryData.courseName" maxlength="100" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-form-item label="关联课程编号">
            <el-input v-model="queryData.courseCode" maxlength="100" placeholder="请输入" />

            <!-- <el-select v-model="queryCourseCodes" multiple filterable allow-create default-first-option placeholder="请输入关联课程编号">
              <el-option v-for="item in queryCourseOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select> -->
          </el-form-item>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-form-item label="教材版本">
            <!-- <el-input v-model="queryData.courseEdition" maxlength="100" placeholder="请输入" /> -->
            <el-select v-model="queryData.courseEdition" placeholder="">
              <el-option v-for="(item, index) in bankType" :key="index" :label="item.label" :value="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-form-item>
            <el-button type="primary" @click="onSearch">搜索</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 添加按钮 -->
    <el-button type="primary" icon="el-icon-plus" @click="onAdd" style="margin-bottom: 16px">添加</el-button>

    <!-- 数据表格 -->
    <el-table :data="tableData" border style="width: 100%" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }" v-loading="tableLoading">
      <el-table-column v-for="(item, index) in tableHeadList" :key="index" :prop="item.value" :label="item.label" header-align="center">
        <template slot-scope="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="primary" size="mini" @click="onEdit(row)">编辑</el-button>
            <el-button type="danger" size="mini" @click="onDelete(row)">删除</el-button>
          </div>
          <div v-else-if="item.value == 'courseCode'">
            <el-tooltip class="item" effect="dark" :content="row[item.value]" placement="top">
              <span class="ellipsis-text">{{ getSome(row[item.value]) }}</span>
            </el-tooltip>
          </div>
          <div v-else-if="item.value == 'courseType'">{{ getTypeStr(row.courseType) }}</div>
          <div v-else-if="item.value == 'stage'">{{ getStage(row.stage) }}</div>
          <span v-else>{{ row[item.value] ? row[item.value] : '-' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination" style="margin-top: 16px; text-align: center">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="queryData.pageSize"
        :current-page="queryData.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑课程弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="dialogWidth"
      @close="onDialogClose"
      :close-on-click-modal="false"
      :before-close="onDialogClose"
      class="lesson-plan-dialog"
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="阶段" prop="stage">
          <el-select v-model="formData.stage" placeholder="请选择">
            <el-option v-for="(item, index) in stageOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="课程类型" prop="courseType">
          <el-select v-model="formData.courseType" placeholder="请选择">
            <el-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="教材版本" prop="courseEdition">
          <el-select v-model="formData.courseEdition" placeholder="请选择教材版本">
            <el-option v-for="(item, index) in bankType" :key="index" :label="item.label" :value="item.label"></el-option>
          </el-select>
          <!-- <el-input v-model="formData.courseEdition" placeholder="请输入" style="width: 40%" /> -->
        </el-form-item>
        <el-form-item label="课程名称" prop="courseName">
          <el-input v-model="formData.courseName" maxlength="100" placeholder="请输入" style="width: 40%" />
        </el-form-item>
        <el-form-item label="关联课程编号" prop="courseCode" v-if="formData.courseType == 1">
          <div class="input-list">
            <CourseCodeInputList v-model="formCourseCodes" :dialogWidth="gradWidth"></CourseCodeInputList>
          </div>
          <!-- <el-select v-model="formCourseCodes" multiple filterable allow-create default-first-option placeholder="请从创建课程的课程列表中复制">
            <el-option v-for="item in formCourseOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item label="课时（小时）" prop="courseHour">
          <el-input-number :min="1" v-model="formData.courseHour" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div style="text-align: center; margin-top: 30px">
        <el-button @click="onDialogClose">取消</el-button>
        <el-button type="primary" @click="onDialogConfirm" :loading="submitLoading">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getLessonPlanList, addLessonPlan, editLessonPlan, deleteLessonPlan } from '@/api/course/lessonPlanList';
  import CourseCodeInputList from './components/CourseCodeInputList.vue';
  import enTypes from '@/api/bstatus';

  import { mapGetters } from 'vuex';

  export default {
    name: 'LessonPlanList',
    components: { CourseCodeInputList },
    data() {
      return {
        submitLoading: false,
        tableHeadList: [
          {
            label: '课程编号',
            value: 'coursePlanCode'
          },
          {
            label: '阶段',
            value: 'stage'
          },
          {
            label: '课程类型',
            value: 'courseType'
          },
          {
            label: '课程名称',
            value: 'courseName'
          },
          {
            label: '教材版本',
            value: 'courseEdition'
          },
          {
            label: '关联课程编号',
            value: 'courseCode'
          },
          {
            label: '课时',
            value: 'courseHour'
          },
          {
            label: '操作',
            value: 'operate'
          }
        ],
        queryData: {
          pageNum: 1,
          pageSize: 10,
          coursePlanCode: '',
          courseName: '',
          courseType: '',
          courseEdition: '',
          courseCode: '',
          stage: ''
        },
        queryCourseOptions: [],
        formCourseOptions: [],
        queryCourseCodes: [],
        formCourseCodes: [''],
        stageOptions: [
          { label: '小学', value: 'XiaoXue' },
          { label: '初中', value: 'ChuZhong' },
          { label: '高中', value: 'GaoZhong' },
          { label: '大学', value: 'College' },
          { label: '雅思', value: 'YaSi' },
          { label: '托福', value: 'TuoFu' },
          { label: 'KET', value: 'KET' },
          { label: 'PET', value: 'PET' },
          { label: 'FCE', value: 'FCE' }
        ],
        typeOptions: [
          { label: '单词速记', value: 1 },
          { label: '渗透语法', value: 2 },
          { label: '超级阅读', value: 3 },
          { label: '全能听力', value: 4 }
        ],
        tableData: [],
        tableLoading: false,
        total: 14,
        dialogVisible: false,
        dialogTitle: '添加课时规划单',
        formData: {
          stage: '',
          courseType: '',
          courseName: '',
          courseEdition: '',
          courseCodeList: [],
          courseHour: ''
        },
        formRules: {
          stage: [{ required: true, message: '请选择阶段', trigger: 'change' }],
          courseType: [{ required: true, message: '请选择课程类型', trigger: 'change' }],
          courseEdition: [{ required: true, message: '请输入教材版本', trigger: 'change' }],
          courseName: [
            { required: true, message: '请输入课程名称', trigger: 'blur' },
            {
              pattern: /^[\u4e00-\u9fa5a-zA-Z0-9()（）【】\/\-—]+$/,
              message: '只能输入中英文、数字、()（）【】/、破折号等合法字符',
              trigger: 'blur'
            }
          ],
          // courseCode: [{ required: true, message: '请输入关联课程编号', trigger: 'blur' }],
          courseHour: [
            { required: true, message: '请输入课时', trigger: 'blur' },
            { pattern: /^\d+$/, message: '请输入整数', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (value > 200) {
                  callback(new Error('课时不能超过200'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ]
        },
        editData: [],
        bankType: [],
        gradWidth: '3',
        dialogWidth: '45%'
      };
    },
    computed: {
      ...mapGetters(['getClientWidth'])
    },
    mounted() {
      this.initData();
      this.getStady();
    },
    watch: {
      getClientWidth: {
        handler(val) {
          // console.log(val);
          // 根据屏幕宽度动态计算弹窗宽度
          if (val < 759) {
            this.gradWidth = '1';
            this.dialogWidth = '100%';
          } else if (val < 900) {
            this.gradWidth = '2';
            this.dialogWidth = '80%';
          } else if (val < 1380) {
            this.gradWidth = '3';
            this.dialogWidth = '65%';
          } else {
            this.gradWidth = '4';
            this.dialogWidth = '50%';
          }
        },
        immediate: true
      },
      formCourseCodes: {
        handler(val) {
          if (val) {
            val.forEach((item) => {
              if (item.includes('-')) {
                let arr = item.split('-');
                val = val.filter((i) => i !== item);
                val.push(...arr);
              }
            });
            this.formData.courseCode = this.removeEmpty(val.join('-'));
          } else {
            this.formData.courseCode = '';
          }
        },
        deep: true,
        immediate: true
      }
    },
    methods: {
      getStady() {
        var enType = 'TextbookVersion';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.bankType = res.data;
        });
      },
      isValidSame(arr) {
        const seen = new Set();
        for (const val of arr) {
          if (seen.has(val)) {
            return false; // 重复
          }
          seen.add(val);
        }
        return true; // 所有值合法且无重复
      },
      removeEmpty(str) {
        const result = str.replace(/\s+/g, '');
        return result;
      },
      getSome(str) {
        if (str) {
          let arr = str.split('-');
          if (arr && arr.length > 0) {
            return arr[0];
          }
        }
      },
      getStage(value) {
        switch (value) {
          case 'XiaoXue':
            return '小学';
          case 'ChuZhong':
            return '初中';
          case 'GaoZhong':
            return '高中';
          case 'College':
            return '大学';
          case 'YaSi':
            return '雅思';
          case 'TuoFu':
            return '托福';
          case 'KET':
            return 'KET';
          case 'PET':
            return 'PET';
          case 'FCE':
            return 'FCE';
          default:
            return '';
        }
      },
      getTypeStr(value) {
        switch (value) {
          case 1:
            return '单词速记';
          case 2:
            return '渗透语法';
          case 3:
            return '超级阅读';
          case 4:
            return '全能听力';
          default:
            return '';
        }
      },
      onSearch() {
        // 搜索逻辑
        this.queryData.pageNum = 1;
        this.initData();
      },
      async initData() {
        this.tableLoading = true;
        try {
          let { data } = await getLessonPlanList(this.queryData);
          this.total = +data.totalItems;
          this.tableData = data.data;
          this.tableLoading = false;
        } catch (error) {
          this.tableLoading = false;
        }
      },
      onReset() {
        this.queryCourseCodes = [];
        this.queryData = {
          pageNum: 1,
          pageSize: 10,
          coursePlanCode: '',
          courseName: '',
          courseType: '',
          courseEdition: '',
          courseCode: '',
          stage: ''
        };
        this.initData();
      },
      onAdd() {
        this.dialogTitle = '添加课时规划单';
        this.dialogVisible = true;
      },
      onEdit(row) {
        this.dialogTitle = '编辑课时规划单';
        this.formData = { ...row };
        console.log(row);
        let newData = row.courseCode ? row.courseCode.split('-') : [];
        this.editData = [...newData];
        // this.editData = newData.map((code) => ({
        //   courseCode: code,
        //   deleted: false
        // }));
        console.log(this.editData, '===================');
        this.formCourseCodes = row.courseCode ? row.courseCode.split('-').filter(Boolean) : [''];
        this.dialogVisible = true;
      },
      onDelete(row) {
        // 删除逻辑
        this.$confirm('确定要删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteLessonPlan({ id: row.id }).then((res) => {
            this.$message.success('删除成功！');
            this.onReset();
          });
        });
      },
      handleSizeChange(size) {
        this.queryData.pageSize = size;
        // 重新请求数据
        this.initData();
      },
      handleCurrentChange(page) {
        this.queryData.pageNum = page;
        // 重新请求数据
        this.initData();
      },
      onDialogClose() {
        this.submitLoading = false;
        this.formCourseCodes = [''];
        this.formData = {
          stage: '',
          courseType: '',
          courseName: '',
          courseEdition: '',
          courseCode: '',
          courseHour: '',
          courseCodeList: []
        };
        this.$refs.formRef && this.$refs.formRef.resetFields();
        this.dialogVisible = false;
      },
      diffAndMerge(arr1, arr2) {
        const duplicates = arr1.filter((item) => arr2.includes(item));
        const arr1Filtered = arr1.filter((item) => !duplicates.includes(item));
        const arr2Filtered = arr2.filter((item) => !duplicates.includes(item));
        const merged = Array.from(new Set([...arr1Filtered, ...arr2Filtered]));

        return {
          merged,
          duplicates
        };
      },
      onDialogConfirm() {
        let that = this;
        this.$refs.formRef.validate(async (valid) => {
          if (valid) {
            // 如果是单词速记类型，需要验证关联课程编号
            if (this.formData.courseType === 1) {
              // 过滤掉空值
              const validCodes = this.formCourseCodes.filter((code) => code.trim());
              if (validCodes.length === 0) {
                return this.$message.error('请输入至少一个关联课程编号');
              }
              let A = that.formData.courseCode.split('-');
              if (!that.isValidSame(A.filter(Boolean))) {
                return that.$message.error('请检查重复');
              }
              let B = that.formData.id ? [...that.editData] : [];
              const result = that.diffAndMerge(A, B);
              const inB = result.merged.filter((item) => B.includes(item));
              const notInB = result.merged.filter((item) => !B.includes(item));
              let arrA = inB.filter(Boolean).map((item) => {
                return {
                  courseCode: item,
                  deleted: 1
                };
              });
              let arrB =
                notInB.filter(Boolean).length > 0
                  ? notInB.filter(Boolean).map((item) => {
                      return {
                        courseCode: item,
                        deleted: 0
                      };
                    })
                  : [];
              that.formData.courseCodeList = [...arrA, ...arrB];
              this.formData.courseCode = validCodes.join(',');
            } else {
              this.formData.courseCode = '';
            }

            try {
              this.submitLoading = true;
              if (this.formData.id) {
                // 编辑
                await editLessonPlan(this.formData);
              } else {
                // 新增
                await addLessonPlan(this.formData);
              }
              this.$message({
                type: 'success',
                message: '操作成功'
              });
              this.dialogVisible = false;
              this.initData(); // 刷新表格数据
            } catch (error) {
              console.error('提交失败:', error);
            } finally {
              this.submitLoading = false;
            }
          }
        });
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      }
    }
  };
</script>

<style lang="less" scoped>
  .lesson-plan-list {
    padding: 20px;
  }
  .filter-form {
    margin-bottom: 16px;
  }
  .input-list {
    max-height: 150px;
    overflow-y: auto;
  }

  /* 弹窗响应式样式 */
  /deep/ .lesson-plan-dialog .el-dialog {
    margin-top: 5vh !important;
    max-height: 90vh;
    overflow-y: auto;
  }

  /* 移动端适配 */
  @media screen and (max-width: 768px) {
    .filter-form .el-form-item {
      margin-bottom: 10px;
    }

    /deep/ .lesson-plan-dialog .el-dialog {
      margin: 3vh auto !important;
      max-height: 94vh;
    }

    /deep/ .lesson-plan-dialog .el-dialog__body {
      padding: 15px !important;
    }

    /deep/ .el-form-item__label {
      width: 100px !important;
    }

    .input-list {
      width: 100%;
    }
  }

  /* 平板适配 */
  @media screen and (min-width: 769px) and (max-width: 1200px) {
    /deep/ .lesson-plan-dialog .el-dialog {
      margin-top: 8vh !important;
    }

    .input-list {
      width: 90%;
    }
  }
</style>
