<template>
  <div class="dashboard-container" style="padding: 30px">
    <dx-select v-model="value" placeholder="请选择">
      <dx-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></dx-option>
    </dx-select>
    <h1>DxTable showOverflowTooltip 默认行为测试</h1>

    <div class="test-section">
      <h2>默认行为测试（showOverflowTooltip 应该默认为 true）</h2>
      <p>以下表格列没有显式设置 showOverflowTooltip 属性，应该默认显示 tooltip</p>
      <dx-table :data="longTextData" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="120"></dx-table-column>
        <dx-table-column prop="description" label="描述（默认tooltip）" width="200"></dx-table-column>
        <dx-table-column prop="address" label="地址（默认tooltip）" width="250"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>显式设置 showOverflowTooltip 为 false</h2>
      <p>以下表格列显式设置了 showOverflowTooltip="false"，不应该显示 tooltip</p>
      <dx-table :data="longTextData" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="120"></dx-table-column>
        <dx-table-column prop="description" label="描述（无tooltip）" width="200" :show-overflow-tooltip="false"></dx-table-column>
        <dx-table-column prop="address" label="地址（无tooltip）" width="250" :show-overflow-tooltip="false"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>显式设置 showOverflowTooltip 为 true</h2>
      <p>以下表格列显式设置了 showOverflowTooltip="true"，应该显示 tooltip</p>
      <dx-table :data="longTextData" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="120"></dx-table-column>
        <dx-table-column prop="description" label="描述（显式tooltip）" width="200" :show-overflow-tooltip="true"></dx-table-column>
        <dx-table-column prop="address" label="地址（显式tooltip）" width="250" :show-overflow-tooltip="true"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>混合设置测试</h2>
      <p>混合使用默认、true、false 设置</p>
      <dx-table :data="longTextData" style="width: 100%" border>
        <dx-table-column prop="id" label="ID" width="80"></dx-table-column>
        <dx-table-column prop="name" label="姓名（默认）" width="150"></dx-table-column>
        <dx-table-column prop="description" label="描述（false）" width="200" :show-overflow-tooltip="false"></dx-table-column>
        <dx-table-column prop="address" label="地址（true）" width="200" :show-overflow-tooltip="true"></dx-table-column>
      </dx-table>
    </div>

    <div class="instructions">
      <h3>测试说明：</h3>
      <ul>
        <li>将鼠标悬停在表格单元格上，观察是否显示 tooltip</li>
        <li>第一个表格：所有列都应该显示 tooltip（因为默认值为 true）</li>
        <li>第二个表格：描述和地址列不应该显示 tooltip</li>
        <li>第三个表格：描述和地址列应该显示 tooltip</li>
        <li>第四个表格：姓名和地址列应该显示 tooltip，描述列不显示</li>
      </ul>
    </div>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <span>{{ contens }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-if="roleDialog == 'School'"
      title="温馨提示："
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :visible.sync="dialogToast"
      width="360px"
      :before-close="handlePartnerClose"
    >
      <span class="school-toast" v-if="dialogShow === 1">
        为了后续可以帮您更好的开展业务，请您尽快加入鼎校甄选教育版企微。
        <br />
        （可联系您的推荐人或渠道经理，获取加入企微的申请链接和操作方式）
      </span>
      <span class="school-toast" v-if="dialogShow === 2">亲爱的鼎校超级合伙人，您好！新人训陪跑期间，请您完成相关课程的学习，并同步完成对应课程的在线考核。</span>
      <span class="school-toast" v-if="dialogShow === 3">
        亲爱的鼎校超级合伙人，您好！很遗憾您未通过本次新人训陪跑期间课程的在线考核，请您复习课程后，重新参与考核，祝您考核通过。
      </span>
      <span class="school-toast" v-if="dialogShow === 4">
        亲爱的鼎校超级合伙人，您好！很遗憾您连续三次没有通过本次新人训陪跑期间课程考核，请及时联系您的渠道经理对接后续工作安排。
      </span>
      <span v-if="dialogShow === 1" slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="
            dialogToast = false;
            handlePartnerClose();
          "
        >
          我已知晓
        </el-button>
      </span>
      <span v-if="dialogShow !== 1" slot="footer" class="dialog-footer">
        <el-button
          v-if="dialogShow !== 4"
          @click="
            dialogToast = false;
            handlePartnerClose();
          "
        >
          暂不前往
        </el-button>
        <el-button v-if="dialogShow !== 4" type="primary" @click="goStudyCenter">立即前往</el-button>
        <el-button
          v-if="dialogShow === 4"
          type="primary"
          @click="
            dialogToast = false;
            handlePartnerClose();
          "
        >
          我已知晓
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import adminDashboard from './admin';
  import editorDashboard from './editor';
  import remindDialogAPI from '@/api/newPartner/remindDialog';
  import brandDashboard from './brand';
  import newsApi from '@/api/news';

  export default {
    name: 'Dashboard',
    components: { adminDashboard, editorDashboard, brandDashboard },
    data() {
      return {
        currentRole: 'adminDashboard',
        dialogVisible: false,
        dialogToast: false, // 合伙人新人陪跑提示弹窗
        roleDialog: '', // 根据角色判断弹窗是否显示
        dialogShow: 1, // 合伙人新人陪跑弹窗区分
        contens: '', // 弹窗内容
        merchantCode: '', // 商户编号
        dialogQueue: [], // 弹窗队列
        value: '',
        
        longTextData: [
          {
            id: 1,
            name: '张三',
            description: '这是一段很长很长的描述文字，用来测试表格单元格内容溢出时是否会显示tooltip提示信息，这段文字应该会超出单元格的宽度限制',
            address: '北京市朝阳区建国门外大街1号国贸大厦A座1001室，这是一个很长的地址信息用来测试tooltip功能'
          },
          {
            id: 2,
            name: '李四',
            description: '另一段超长的描述内容，包含了大量的文字信息，目的是为了验证当文字内容超出表格列宽度时的显示效果和tooltip行为',
            address: '上海市浦东新区陆家嘴金融贸易区世纪大道100号上海环球金融中心88层，这也是一个超长的地址'
          },
          {
            id: 3,
            name: '王五',
            description: '第三条测试数据的描述信息，同样是一段很长的文字内容，用于测试表格组件在处理长文本时的表现和用户体验',
            address: '广州市天河区珠江新城花城大道5号南天广场2801室，广东省广州市的一个详细地址信息'
          },
          {
            id: 4,
            name: '赵六',
            description: '最后一条测试记录的描述，这里放置了足够长的文本内容来确保能够触发表格单元格的溢出显示机制',
            address: '深圳市南山区科技园南区高新南一道6号TCL大厦B座15楼，深圳市的一个具体办公地址'
          }
        ]
      };
    },
    computed: {
      ...mapGetters(['roles', 'studyUrl'])
    },
    beforeCreate() {
      if (this.$route.query.token) {
        this.$router.push({
          path: '/dashboard'
        });
      }
    },
    created() {
      console.log(this.roles, 9999);
      if (this.roles[0].val == 'zxBrand') {
        this.currentRole = 'brandDashboard';
      } else if (!this.roles.includes('admin')) {
        this.currentRole = 'editorDashboard';
      }
      const sysUserInfo = JSON.parse(localStorage.getItem('sysUserInfo'));
      this.merchantCode = sysUserInfo.merchantCode;
      this.roleDialog = sysUserInfo.roles[0].val;
      this.getMessageDialog();
    },
    methods: {
      getMessageDialog() {
        remindDialogAPI.messageDetail({ merchantCode: this.merchantCode }).then((res) => {
          console.log(res, '消息弹窗');
          if (res.success && res.data && res.data.length > 0) {
            this.dialogQueue = res.data; // 存储弹窗标识
            this.showNextDialog(); // 显示第一个弹窗
          }
        });
      },
      getNews() {
        let data = localStorage.getItem('newsShow');
        console.log(data);
        if (data != null) {
          newsApi.getoneNotice().then((res) => {
            console.log(res);
            if (res.data != null) {
              this.contens = res.data.title;
              this.dialogVisible = true;
            }
            localStorage.removeItem('newsShow');
          });
        }
      },
      // 关闭
      handleClose() {},
      // 合伙人陪跑立即前往
      goStudyCenter() {
        this.dialogToast = false;
        // 跳转学习中心
        this.$router.push({
          path: '/trainingCenter/learnCenter'
        });
        this.handlePartnerClose(); // 显示下一个弹窗
      },
      handlePartnerClose() {
        this.dialogToast = false;
        this.showNextDialog(); // 显示下一个弹窗
      },
      showNextDialog() {
        if (this.dialogQueue.length > 0) {
          this.dialogShow = parseInt(this.dialogQueue.shift()); // 获取并移除第一个弹窗标识
          this.dialogToast = true; // 显示弹窗
        }
      }
    },
    mounted() {
      this.getNews();
    }
  };
</script>

<style lang="scss" scoped>
  .textStatus {
    font-size: 12px;
    margin-right: 10px;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep .el-dialog__body {
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .school-toast {
    font-size: 16px;
    color: #333;
  }
</style>
