<template>
  <div>
    <div style="display: flex; margin-bottom: 20px" v-if="type == 2 || type == 3">
      <el-form :inline="true">
        <el-form-item :label="label1">
          <el-input :placeholder="placeholderData1" v-model="searchDataTitle" />
        </el-form-item>
        <el-form-item :label="label2">
          <el-input :placeholder="placeholderData2" v-model="searchDataId" @input="handleNumberInput" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch()">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row>
      <el-col :span="6" :offset="0">
        <el-tree ref="tree" node-key="id" :data="categoryTreeData" :props="propsTree" @node-click="categoryClick"
                 v-loading="treeLoading" highlight-current default-expand-all
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{ node.label }}</span>
            <span v-if="data.number">{{ data.number }}</span>
          </span>
        </el-tree>
      </el-col>
      <el-col :span="18" :offset="0">
        <el-row>
          <el-col :span="20" :offset="4"></el-col>
          <el-col :span="4" :offset="0">
            <el-checkbox style="margin-left: 10px" v-if="(type == '1' || type == '3' || type == 1 || type == 3)"
                         v-model="allCheck" @change="allclick" :disabled="!isDataLoaded"
            >全选
            </el-checkbox>
          </el-col>
        </el-row>
        <el-table v-loading="tableLoading" :data="tableData" ref="multipleTable"
                  @selection-change="handleSelectionChange" row-key="id" :reserve-selection="true"
        >
          <el-table-column v-if="type == '1' || type == '3'" type="selection" :reserve-selection="true" width="55"
          ></el-table-column>
          <el-table-column v-else type="index" width="55">
            <template slot-scope="scope">
              <el-radio
                v-model="selectedMaterialId"
                :label="scope.row.id"
                @change="handleMaterialSelect(scope.row)"
              >
                &nbsp;
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column type="index" label="序号" center></el-table-column>
          <el-table-column :label="labelName" center :prop="columnName"></el-table-column>
        </el-table>
        <el-pagination @current-change="handlePageChange" :current-page="tablePage.currentPage"
                       :page-size="tablePage.pageSize" :total="tablePage.totalItems"
        ></el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  getAllQuestionMaterialList,
  getCourseClassificationTree,
  getKnowledgeList, getQuestionList
} from '@/api/superReaderAPI/testBaseManagement';

export default {
  name: 'Knowledge-point',
  props: {
    // 课程id
    curriculumId: {
      type: String,
      default: '1356667161917542400'
    },
    // 知识点id
    knowledgePointIds: {
      type: Array,
      default: () => []
    },
    // 题干材料id
    stemIds: {
      type: Array,
      default: () => []
    },
    // 题目id
    questionIds: {
      type: Array,
      default: () => []
    },
    //学科id
    disciplineId: {
      type: String,
      default: ''
    },
    // 类型
    type: {
      type: String,
      default: ''
    },
    questionType: {
      type: Number,
      default: 0
    },
    questionMaterialId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        totalItems: 0
      },
      tableLoading: false,
      allCheck: false,
      categoryTreeData: [],
      currentNodeKey: '', // 当前点击节点的id
      currentNodeLevel: '', // 当前点击节点的分类层级
      currentNodeData: {}, // 当前点击节点的数据
      total: 0,
      multipleSelection: [], // 所有选中的知识点数据（跨页面保持）
      materialSelection: [], // 所有选中的题干材料数据（跨页面保持）
      questionSelection: [], // 所有选中的题目数据（跨页面保持）
      selectedIds: new Set(), // 选中的知识点ID集合，用于快速查找
      selectedMaterialIds: new Set(), // 选中的题干材料ID集合，用于快速查找
      selectedQuestionIds: new Set(), // 所有选中的题目ID集合，用于快速查找
      selectedMaterialId: '', // 当前选中的题干材料ID（单选）
      isRestoringSelection: false, // 标记是否正在恢复选中状态
      list: [],
      yuAllcheck: [],
      tableData: [],
      data: [],
      temp: false,
      propsTree: {
        children: 'children',
        label: 'label'
      },
      labelTitle: '',
      placeholderData1: '',
      placeholderData2: '',
      searchDataTitle: '',
      searchDataId: '',
      label1: '',
      label2: '',
      clickType: '',
      isDataLoaded: false,
    };
  },
  watch: {
    // 监听type变化，当type变化时重新初始化组件
    type: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.initComponent();
        }
      },
      immediate: false
    }
  },
  created() {
    console.log('type', this.type);
    this.labelTitle = this.type == '1' ? '知识点名称' : this.type == '2' ? '题干材料名称' : '题目名称';
    this.placeholderData1 = this.type == '2' ? '请输入题干材料名称' : '请输入题目名称';
    this.placeholderData2 = this.type == '2' ? '请输入题干材料ID' : '请输入题目ID';
    this.label1 = this.type == '2' ? '题干材料标题：' : '题目标题：';
    this.label2 = this.type == '2' ? '题干材料ID：' : '题目ID：';
    this.getCategoryTree();
    this.initComponent();
  },
  computed: {
    labelName() {
      return this.type == 1 ? '知识点名称' : this.type == 2 ? '题干材料名称' : '题目名称';
    },
    columnName() {
      if (this.type == 1) {
        return 'knowledgeName';
      } else if (this.type == 3) {
        return 'questionText';
      } else {
        return 'questionMaterialTitle';
      }
    }
  },
  methods: {
    // 初始化组件状态
    initComponent() {
      if (this.type == '1') {
        // 知识点模式初始化
        this.multipleSelection = [...this.knowledgePointIds];
        // 初始化选中的ID集合
        this.selectedIds = new Set(this.knowledgePointIds.map((item) => item.id));
      } else if (this.type == '3') {
        // 题目模式初始化
        this.questionSelection = [...this.questionIds];
        // 初始化选中的ID集合
        this.selectedQuestionIds = new Set(this.questionIds.map((item) => item.id));
      } else {
        // 题干材料模式初始化
        if (this.stemIds && this.stemIds.length > 0) {
          // 如果有默认选中的题干材料ID，则设置为选中状态
          this.selectedMaterialId = this.stemIds[0]; // 单选只选中第一个
          // 初始化选中的题干材料ID集合
          this.selectedMaterialIds = new Set(this.stemIds);
          // 初始化选中的题干材料数据数组
          // 注意：这里只是设置了ID，实际的数据会在表格加载后通过restoreSelection方法处理
          this.materialSelection = []; // 实际数据会在restoreSelection中填充
        }
      }

      // 重置分页和选中状态
      this.tablePage.currentPage = 1;
      this.allCheck = false;
      // 如果已经有currentNodeData，则重新加载数据
      if (this.currentNodeData && this.currentNodeData.id) {
        this.allSubmitForm();
        this.submitForm();
      }
    },
    // 点击搜索
    handleSearch() {
      this.allSubmitForm();
      this.submitForm();
    },
    handleNumberInput(value) {
      // 只允许输入数字
      this.searchDataId = value.replace(/[^\d]/g, '');
    },

    // 全选
    async allclick(res) {
      // 只有在知识点模式和题目模式下才处理全选
      if (this.type != 1 && this.type != 3) {
        return;
      }

      this.isRestoringSelection = true;

      if (this.type == 1) {
        // 知识点全选处理
        if (res) {
          // 全选当前分类下的所有数据
          this.yuAllcheck.forEach((item) => {
            if (!this.multipleSelection.some((selected) => selected.id === item.id)) {
              this.multipleSelection.push(item);
            }
          });
          // 更新选中ID集合
          this.selectedIds = new Set(this.multipleSelection.map((item) => item.id));
          this.$refs.multipleTable.toggleAllSelection();
        } else {
          // 取消选中当前分类下的所有数据
          const yuAllcheckIds = new Set(this.yuAllcheck.map((item) => item.id));
          this.multipleSelection = this.multipleSelection.filter((item) => !yuAllcheckIds.has(item.id));
          // 更新选中ID集合
          this.selectedIds = new Set(this.multipleSelection.map((item) => item.id));
          this.$refs.multipleTable.clearSelection();
        }
      } else if (this.type == 3) {
        // 题目全选处理
        if (res) {
          // 全选当前分类下的所有数据
          this.yuAllcheck.forEach((item) => {
            if (!this.questionSelection.some((selected) => selected.id === item.id)) {
              this.questionSelection.push(item);
            }
          });
          // 更新选中ID集合
          this.selectedQuestionIds = new Set(this.questionSelection.map((item) => item.id));
          this.$refs.multipleTable.toggleAllSelection();
        } else {
          // 取消选中当前分类下的所有数据
          const yuAllcheckIds = new Set(this.yuAllcheck.map((item) => item.id));
          this.questionSelection = this.questionSelection.filter((item) => !yuAllcheckIds.has(item.id));
          // 更新选中ID集合
          this.selectedQuestionIds = new Set(this.questionSelection.map((item) => item.id));
          this.$refs.multipleTable.clearSelection();
        }
      }

      await this.$nextTick();
      this.isRestoringSelection = false;
    },

    // 处理题干材料单选
    handleMaterialSelect(row) {
      // 清除之前的选中项
      this.materialSelection = [];
      this.selectedMaterialIds.clear();

      // 添加当前选中项
      if (row && row.id) {
        // 处理正常选项
        this.materialSelection.push(row);
        this.selectedMaterialIds.add(row.id);
        this.selectedMaterialId = row.id;
      } else if (row && row.id === '') {
        // 处理"无"选项的情况
        this.materialSelection = [];
        this.selectedMaterialIds.clear();
        this.selectedMaterialId = '';
      }
      console.log('当前选中的题干材料数据:', this.materialSelection);
    },

    // 获取课程分类树
    getCategoryTree() {
      this.treeLoading = true;
      this.categoryTreeData = [];
      const that = this;
      if (this.type == 2 || this.type == 3) {
        let data = {
          curriculumId: this.curriculumId,
          nodeLevel: 2
        };
        getCourseClassificationTree(data)
          .then((res) => {
            if (this.disciplineId) {
              //过滤学科
              let result = res.data.filter((item) => item.id == this.disciplineId);
              that.categoryTreeData = that.deepReplace(result);
            } else {
              that.categoryTreeData = that.deepReplace(res.data);
            }
            // 设置默认选中第一个一级节点
            that.$nextTick(() => {
              if (that.$refs.tree && that.categoryTreeData.length > 0) {
                that.$refs.tree.setCurrentKey(that.categoryTreeData[0].id);
                // 触发节点点击事件（可选）
                that.categoryClick(that.categoryTreeData[0], {
                  level: 1,
                  data: that.categoryTreeData[0]
                });
              }
            });
            that.treeLoading = false;
          })
          .catch((err) => {
            that.treeLoading = false;
          });
      } else {
        let data = {
          curriculumId: this.curriculumId,
          nodeLevel: 4
        };
        getCourseClassificationTree(data)
          .then((res) => {
            if (this.disciplineId) {
              //过滤学科
              let result = res.data.filter((item) => item.id == this.disciplineId);
              that.categoryTreeData = that.deepReplace(result);
            } else {
              that.categoryTreeData = that.deepReplace(res.data);
            }
            that.treeLoading = false;
          })
          .catch((err) => {
            that.treeLoading = false;
          });
      }
    },

    // 点击分类节点
    categoryClick(data, node) {
      this.yuAllcheck = [];
      if (node.level == 3) {
        this.childrenId = node.childNodes[0].data.id;
        this.parentId = node.parent.data.id;
        this.moreParentId = node.parent.parent.data.id;
      } else if (node.level == 4) {
        this.parentId = node.parent.data.id;
        this.moreParentId = node.parent.parent.data.id;
        this.mostParentId = node.parent.parent.parent.data.id;
      }
      this.currentNodeKey = data.id;
      this.currentNodeLevel = data.nodeLevel;
      this.currentNodeData = data;
      this.tablePage.currentPage = 1;
      this.allCheck = false;
      this.allSubmitForm();
      this.submitForm();
    },
    // 查询全部数据
    allSubmitForm() {
      let loading = true;
      this.isDataLoaded = false;
      if (this.type == '1') {
        let data = {
          curriculumId: this.curriculumId
        };
        data.pageNum = this.tablePage.currentPage;
        data.pageSize = 10000;
        console.log(this.currentNodeData);
        if (this.currentNodeData.nodeLevel == 1) {
          data.courseSubjectNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 2) {
          data.coursePeriodNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 3) {
          data.chapterNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 4) {
          data.knowledgeSummaryNodeId = this.currentNodeData.id;
        } else {
          data.courseSubjectNodeId = this.form.categoryCode;
        }
        getKnowledgeList(data)
          .then((res) => {
            if (res.success) {
              this.yuAllcheck = res.data.data || [];
              // 强制更新当前分类下的全选状态
              this.$nextTick(() => {
                this.updateAllCheckStatus();
              });
            }
          })
          .catch((err) => {
            this.$message.error('查询失败，请重试！' + err);
          })
          .finally(() => {
            loading = false;
          });
      } else if (this.type == '2') {
        let data = {
          id: this.searchDataId,
          questionMaterialTitle: this.searchDataTitle,
          curriculumId: this.curriculumId,
          pageNum: this.tablePage.currentPage,
          pageSize: 10000,
          gradeId: this.currentNodeData.nodeLevel == 1 ? '' : this.currentNodeData.id
        };
        getAllQuestionMaterialList(data).then((res) => {
          if (res.success) {
            this.yuAllcheck = res.data.data || [];
            // 更新全选状态（题干材料是单选，但为了保持一致性，这里也调用更新）
            this.updateAllCheckStatus();
          }
        })
          .catch((err) => {
            this.$message.error('查询失败，请重试！' + err);
          })
          .finally(() => {
            loading = false;
          });
      } else {
        let data = {
          id: this.searchDataId,
          questionText: this.searchDataTitle,
          curriculumId: this.curriculumId,
          pageNum: this.tablePage.currentPage,
          pageSize: 10000,
          questionType: this.questionType,
          questionMaterialId: this.questionMaterialId,
          gradeId: this.currentNodeData.nodeLevel == 1 ? '' : this.currentNodeData.id
        };
        getQuestionList(data).then((res) => {
          if (res.success) {
            this.yuAllcheck = res.data.data || [];
            // 更新全选状态
            this.updateAllCheckStatus();
          }
        })
          .finally(() => {
            loading = false;
            this.isDataLoaded = true;
          });
      }
    },
    // 查询分页数据
    submitForm() {
      this.tableLoading = true;
      this.isDataLoaded = false;

      if (this.type == '1') {
        this.tableLoading = true;
        let data = {
          curriculumId: this.curriculumId
        };
        data.pageNum = this.tablePage.currentPage;
        data.pageSize = this.tablePage.pageSize;
        if (this.currentNodeData.nodeLevel == 1) {
          data.courseSubjectNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 2) {
          data.coursePeriodNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 3) {
          data.chapterNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 4) {
          data.knowledgeSummaryNodeId = this.currentNodeData.id;
        } else {
          data.courseSubjectNodeId = this.form.categoryCode;
        }
        getKnowledgeList(data)
          .then((res) => {
            if (res.success) {
              this.tableData = res.data.data || [];
              this.tablePage.totalItems = Number(res.data.totalItems);
              this.tablePage.currentPage = Number(res.data.currentPage);

              // 恢复当前页面的选中状态
              this.$nextTick(() => {
                this.restoreSelection();
              });
              this.tableLoading = false;
            }
          })
          .catch((err) => {
            this.$message.error('查询失败，请重试！' + err);
            this.tableLoading = false;
            this.isDataLoaded = true;
          })
          .finally(() => {
            this.tableLoading = false;  // 无论成功或失败都关闭loading
            this.isDataLoaded = true;
          });
      } else if (this.type == '2') {
        let data = {
          id: this.searchDataId,
          questionMaterialTitle: this.searchDataTitle,
          curriculumId: this.curriculumId,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
          gradeId: this.currentNodeData.nodeLevel == 1 ? '' : this.currentNodeData.id
        };
        getAllQuestionMaterialList(data).then((res) => {
          if (res.success) {
            this.tableData = [...(res.data.data || [])];
            this.tablePage.totalItems = Number(res.data.totalItems);
            this.tablePage.currentPage = Number(res.data.currentPage);

            // 恢复当前页面的选中状态
            this.$nextTick(() => {
              this.restoreSelection();
            });
            this.tableLoading = false;
          }
        })
          .catch((err) => {
            this.$message.error('查询失败，请重试！' + err);
            this.tableLoading = false;
          })
          .finally(() => {
            this.tableLoading = false;  // 无论成功或失败都关闭loading
          });
      } else {
        let data = {
          id: this.searchDataId,
          questionText: this.searchDataTitle,
          curriculumId: this.curriculumId,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
          questionType: this.questionType,
          questionMaterialId: this.questionMaterialId,
          gradeId: this.currentNodeData.nodeLevel == 1 ? '' : this.currentNodeData.id
        };
        getQuestionList(data).then((res) => {
          if (res.success) {
            this.tableData = res.data.data || [];
            this.tablePage.totalItems = Number(res.data.totalItems);
            this.tablePage.currentPage = Number(res.data.currentPage);

            // 恢复当前页面的选中状态
            this.$nextTick(() => {
              this.restoreSelection();
            });
            this.tableLoading = false;
          }
        })
          .catch((err) => {
            this.$message.error('查询失败，请重试！' + err);
            this.tableLoading = false;
          })
          .finally(() => {
            this.tableLoading = false;  // 无论成功或失败都关闭loading
          });
      }
    },

    containsAllObjects(a, b) {
      const compareFn = (c, d) => c.id === d.id;
      return b.every((bItem) => a.some((aItem) => compareFn(aItem, bItem)));
    },
    deepReplace(array) {
      if (array instanceof Array && array.length >= 1) {
        return array.map((el) => {
          return {
            id: el.id,
            label: el.nodeName,
            children: this.deepReplace(el.childList),
            ...el
          };
        });
      } else {
        return [];
      }
    },

    handleSelectionChange(val) {
      // 只有在知识点模式和题目模式下才处理多选
      if (this.type != 1 && this.type != 3) {
        return;
      }

      // 防止在恢复选中状态时触发此方法导致数据混乱
      if (this.isRestoringSelection) {
        return;
      }

      if (this.type == 1) {
        // 获取当前页面的数据ID集合
        const currentPageIds = new Set(this.tableData.map((item) => item.id));

        // 知识点选中处理
        // 移除当前页面中不再选中的项
        this.multipleSelection = this.multipleSelection.filter((item) => {
          // 如果不是当前页面的数据，保留
          if (!currentPageIds.has(item.id)) {
            return true;
          }
          // 如果是当前页面的数据，检查是否还在选中列表中
          return val.some((selectedItem) => selectedItem.id === item.id);
        });

        // 添加当前页面新选中的项
        val.forEach((item) => {
          if (!this.multipleSelection.some((selected) => selected.id === item.id)) {
            this.multipleSelection.push(item);
          }
        });

        // 更新选中ID集合
        this.selectedIds = new Set(this.multipleSelection.map((item) => item.id));

        // 更新全选状态
        this.updateAllCheckStatus();

        console.log('当前选中的知识点数据:', this.multipleSelection);
      } else if (this.type == 3) {
        // 获取当前页面的数据ID集合
        const currentPageIds = new Set(this.tableData.map((item) => item.id));

        // 题目选中处理
        // 移除当前页面中不再选中的项
        this.questionSelection = this.questionSelection.filter((item) => {
          // 如果不是当前页面的数据，保留
          if (!currentPageIds.has(item.id)) {
            return true;
          }
          // 如果是当前页面的数据，检查是否还在选中列表中
          return val.some((selectedItem) => selectedItem.id === item.id);
        });

        // 添加当前页面新选中的项
        val.forEach((item) => {
          if (!this.questionSelection.some((selected) => selected.id === item.id)) {
            this.questionSelection.push(item);
          }
        });

        // 更新选中ID集合
        this.selectedQuestionIds = new Set(this.questionSelection.map((item) => item.id));

        // 更新全选状态
        this.updateAllCheckStatus();

        console.log('当前选中的题目数据:', this.questionSelection);
      }
    },

    uniqueByProp(arr, prop) {
      return arr.filter((obj, index, self) => index === self.findIndex((o) => o[prop] === obj[prop]));
    },
    handlePageChange(val) {
      this.tablePage.currentPage = val;
      this.submitForm();
    },

    // 恢复当前页面的选中状态
    restoreSelection() {
      if (this.tableData && this.tableData.length > 0) {
        this.isRestoringSelection = true;

        if (this.type == 1) {
          // 知识点多选状态恢复
          this.tableData.forEach((item) => {
            if (this.selectedIds.has(item.id)) {
              this.$refs.multipleTable.toggleRowSelection(item, true);
            } else {
              this.$refs.multipleTable.toggleRowSelection(item, false);
            }
          });
        } else if (this.type == 3) {
          // 题目多选状态恢复
          this.tableData.forEach((item) => {
            if (this.selectedQuestionIds.has(item.id)) {
              this.$refs.multipleTable.toggleRowSelection(item, true);
            } else {
              this.$refs.multipleTable.toggleRowSelection(item, false);
            }
          });
        } else {
          // 题干材料单选状态恢复
          // 先检查当前页面是否有默认选中的项
          const defaultSelected = this.tableData.find(item => this.selectedMaterialIds.has(item.id));
          if (defaultSelected) {
            this.selectedMaterialId = defaultSelected.id;
            // 同时更新materialSelection数组
            this.materialSelection = [defaultSelected];
          }

          // 如果之前已选中某项但在当前页未找到，则保持原来的选中状态
          this.tableData.forEach((item) => {
            if (this.selectedMaterialIds.has(item.id)) {
              this.selectedMaterialId = item.id;
            }
          });
        }

        // 延迟重置标志，确保所有选中状态都已恢复
        this.$nextTick(() => {
          this.isRestoringSelection = false;
        });
      }
    },

    // 更新全选状态
    updateAllCheckStatus() {
      // 只有在知识点模式和题目模式下才更新全选状态
      if (this.type != 1 && this.type != 3) {
        this.allCheck = false;
        return;
      }

      if (this.yuAllcheck && this.yuAllcheck.length > 0) {
        // 检查当前分类下的所有知识点数据是否都被选中
        let allSelected = false;
        if (this.type == 1) {
          allSelected = this.yuAllcheck.every((item) => this.selectedIds.has(item.id));
        } else if (this.type == 3) {
          allSelected = this.yuAllcheck.every((item) => this.selectedQuestionIds.has(item.id));
        }
        this.allCheck = allSelected;
      } else {
        this.allCheck = false;
      }
    }

  }
};
</script>

<style scoped lang="scss">
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>

