import request from '@/utils/request';

// 视频节点分页查询
const listVideoNodesAPI = ({ id }) => {
  return request({
    url: '/dsx/math/web/videoNodes/pagelist',
    method: 'get',
    params: {
      id,
      pageSize: 10,
      pageNum: 1
    }
  });
};
// 视频节点编辑
const editVideoNodesAPI = (data) => {
  return request({
    url: '/dsx/math/web/videoNodes/insertOrUpdate',
    method: 'post',
    data
  });
};

// 删除
const deleteVideoNodesAPI = (query) => {
  return request({
    url: '/dsx/math/web/videoNodes/delete',
    method: 'delete',
    params: query
  });
};

export {
  listVideoNodesAPI,
  editVideoNodesAPI,
  deleteVideoNodesAPI
}