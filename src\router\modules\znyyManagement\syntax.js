// 新版语法管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/syntax',
  component: Layout,
  redirect: '/syntax/grammarQuestion',
  meta: {
    perm: 'm:syntax',
    title: '新版语法管理',
    icon: 'grammer'
  },
  children: [
    {
      path: 'phaseCredentialList',
      component: () => import('@/views/syntax/phaseCredentialList'),
      name: 'syntaxphaseCredentialList',
      meta: {
        perm: 'm:grammar:phaseCredentialList',
        title: '学员结业证书打印列表'
      }
    },
    {
      path: 'phaseCreadentialPrint',
      hidden: true,
      component: () => import('@/views/syntax/phaseCreadentialPrint'),
      name: 'syntaxphaseCreadentialPrint',
      meta: {
        perm: 'm:grammar:phaseCreadentialPrint',
        title: '学员结业证书打印列表-打印'
      }
    },
    {
      path: 'handoutsPrintList',
      component: () => import('@/views/syntax/handoutsPrintList'),
      name: 'handoutsPrintList',
      meta: {
        perm: 'm:grammar:handoutsPrintList',
        title: '学员课件打印列表'
      }
    },
    {
      path: 'studentHandoutsPrint',
      hidden: true,
      component: () => import('@/views/syntax/studentHandoutsPrint'),
      name: 'studentHandoutsPrint',
      meta: {
        perm: 'm:grammar:studentHandoutsPrint',
        title: '学员课件打印列表-打印'
      }
    }
  ]
};
