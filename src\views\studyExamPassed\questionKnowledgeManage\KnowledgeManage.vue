<template>
  <div class="knowledge-manage">
    <div class="left-panel" v-loading="leftLoading" element-loading-text="处理中..." element-loading-spinner="el-icon-loading" element-loading-background="rgba(255,255,255,0.9)">
      <div class="category-select">
        <span class="label">课程大类：</span>
        <el-select v-model="currentCategory" multiple placeholder="请选择" @visible-change="confirmBlurCategory" @remove-tag="handleRemoveCategoryTag">
          <el-option v-for="c in categoryOptions" :key="c.value" :label="c.label" :value="c.value" :disabled="String(c.value) === String(initialId)" />
        </el-select>
      </div>
      <div class="add-root">
        <span class="label">新建知识点：</span>
        <i class="el-icon-plus plus-btn" @click="addRoot"></i>
      </div>
      <div class="knowledge-list">
        <transition-group name="list-fade" tag="div">
          <div
            class="k-item"
            v-for="item in flatList"
            :key="item.id"
            @click="selectItem(item)"
            :style="{ paddingLeft: 4 + (item.level - 1) * 16 + 'px' }"
            :class="[movingId === item.id ? 'is-moving' : '']"
          >
            <i
              class="toggle-icon"
              :class="hasChild(item) ? (item.collapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-down') : 'el-icon-document'"
              @click.stop="hasChild(item) && toggleCollapse(item)"
              :title="hasChild(item) ? (item.collapsed ? '展开' : '收起') : ''"
            ></i>
            <el-input
              ref="editInput"
              v-if="editingId === item.id"
              v-model="editName"
              :placeholder="item.placeholder"
              size="mini"
              maxlength="50"
              show-word-limit
              @keyup.enter.native="confirmEdit(item)"
              @blur="confirmEdit(item)"
            />
            <div v-else class="name-wrapper">
              <el-tooltip v-if="item.name || item.placeholder" :content="item.name || item.placeholder" placement="top" effect="dark" :disabled="!isOverflow(item.id)">
                <span class="name" @mouseenter="checkOverflow($event, item.id)">{{ item.name ? item.name : item.placeholder }}</span>
              </el-tooltip>
            </div>
            <div class="ops">
              <i v-if="item.level < 3" class="el-icon-plus" title="添加子知识点" @click.stop="addChild(item)"></i>
              <i class="el-icon-edit" title="编辑" @click.stop="startEdit(item)"></i>
              <i
                v-if="item.level === 1"
                :class="['el-icon-top', isFirstRoot(item) ? 'disabled-move' : '']"
                :title="isFirstRoot(item) ? '已在最上' : '上移'"
                @click.stop="!isFirstRoot(item) && moveUp(item)"
              ></i>
              <i
                v-if="item.level === 1"
                :class="['el-icon-bottom', isLastRoot(item) ? 'disabled-move' : '']"
                :title="isLastRoot(item) ? '已在最下' : '下移'"
                @click.stop="!isLastRoot(item) && moveDown(item)"
              ></i>
              <i class="el-icon-delete" title="删除" @click.stop="removeItem(item)"></i>
            </div>
          </div>
        </transition-group>
      </div>
    </div>
    <div class="divider"></div>
    <div class="right-panel">
      <div class="import-box">
        <el-dropdown @command="handleImportCommand" @visible-change="onImportDropdownVisible">
          <el-button>
            导入
            <i :class="[importDropdownVisible ? 'el-icon-arrow-down' : 'el-icon-arrow-up', 'el-icon--right']" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="template">下载模板</el-dropdown-item>
            <el-dropdown-item command="import">导入数据</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <input ref="importInput" type="file" accept=".xlsx,.xls" style="display: none" @change="onImportFileSelected" />
        <div class="progress-wrapper" v-if="uploadProgress > 0">
          <el-progress :percentage="uploadProgress" :show-text="false"></el-progress>
        </div>
      </div>
      <div class="content-box" v-show="tableTotal > 0">
        <div class="table-wrapper">
          <el-table :data="tableData" v-loading="tableLoading" border size="mini" class="knowledge-table" :header-cell-style="{ background: '#f5f7fa', fontWeight: 600 }">
            <el-table-column prop="index" align="center" label="序号" width="60" />
            <el-table-column prop="level1" label="一级知识点" />
            <el-table-column prop="level2" label="二级知识点" />
            <el-table-column prop="level3" label="三级知识点" />
          </el-table>
        </div>
        <div class="table-pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :current-page.sync="tablePage"
            :page-size.sync="tablePageSize"
            :page-sizes="[10, 20, 30, 40, 50]"
            :total="tableTotal"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
      <div v-show="tableTotal === 0" class="placeholder-overlay">暂无知识点，请点击新建或者通过模板导入</div>
    </div>
  </div>
</template>

<script>
  let idSeed = 1000;
  import knowledgeApi from '@/api/studyExamPassed/questionKnowledgeManage';
  import { restoreMissingRouteQuery, persistRouteParams } from '@/utils/routeParamCache';

  export default {
    name: 'KnowledgeManage',
    data() {
      return {
        initialId: '', //传参id
        categoryOptions: [], // 下拉展示的全部（绑定 + 未绑定）
        currentCategory: [], // 选中的课程大类 id 列表（界面绑定）
        bindList: [], // 已绑定课程大类列表 {id,label}
        unBindList: [], // 未绑定课程大类列表 {id,label}
        tree: [], // 平铺的知识点树（接口数据扁平化）
        treeDataBase: [], // 原始树形数据（接口返回）
        treeLoading: false,
        editingId: null,
        editName: '',
        tablePage: 1,
        tablePageSize: 10,
        tableLoading: false,
        tableData: [],
        tableTotal: 1,
        importDropdownVisible: false,
        // 上传相关
        uploadProgress: 0, // 实时百分比（0~100）
        uploadStatus: 'idle', // idle | uploading | success | error
        successHoldDelay: 600, // 成功满格后保留时间
        uploadingFile: null,
        // 直传相关（替换 OSS）
        rootSortSaving: false, // 根节点排序保存中
        deletingKnowledge: false, // 删除中防重复
        savingKnowledge: false, // 新增 / 编辑保存中（影响全局 loading）
        batchSavingKnowledge: false, // 批量保存（分类失焦）中
        importRefreshing: false, // 上传后静默刷新（不显示全局遮罩）
        overflowMap: {}, // 记录每个节点文本是否溢出
        collapsedStateMap: {}, // 记录各节点折叠状态，刷新后保持
        movingId: null // 当前正在移动的根节点 id
      };
    },
    created() {
      restoreMissingRouteQuery(this.$router, this.$route, { ns: 'KnowledgeManage', keys: ['curriculumId'] });
      this.initialId = this.$route.query.curriculumId ? this.$route.query.curriculumId : null;
      this.initCourseCategoryList();
      this.loadTableData();
      if (this.initialId) {
        this.fetchKnowledgeTreeList(this.initialId);
      }
    },

    activated() {
      restoreMissingRouteQuery(this.$router, this.$route, { ns: 'KnowledgeManage', keys: ['curriculumId'] });
      // 进入激活时禁止 body 滚动
      if (typeof document !== 'undefined') {
        document.body.classList.add('qm-no-scroll');
      }
    },

    mounted() {
      // 初次挂载也添加（切换进入第一次）
      if (typeof document !== 'undefined') {
        document.body.classList.add('qm-no-scroll');
      }
    },
    deactivated() {
      // keep-alive 切走时移除
      if (typeof document !== 'undefined') {
        document.body.classList.remove('qm-no-scroll');
      }
    },
    beforeDestroy() {
      // 组件销毁前移除，避免残留影响其它页面
      if (typeof document !== 'undefined') {
        document.body.classList.remove('qm-no-scroll');
      }
    },
    computed: {
      flatList() {
        return this.tree.filter((item) => {
          let pid = item.parentId;
          while (pid) {
            const p = this.tree.find((t) => t.id === pid);
            if (!p) break;
            if (p.collapsed) return false;
            pid = p.parentId;
          }
          return true;
        });
      },
      // 左侧局部 loading
      leftLoading() {
        return this.treeLoading || this.savingKnowledge || this.deletingKnowledge || this.rootSortSaving || this.batchSavingKnowledge;
      }
    },
    methods: {
      // 获取课程大类
      async initCourseCategoryList() {
        // 改为调用绑定/未绑定接口
        try {
          const res = await knowledgeApi.getBindCurriculumList({ curriculumId: this.initialId });
          const data = res.data || {};
          const bind = Array.isArray(data.bindBvStatusVo) ? data.bindBvStatusVo : [];
          const unBind = Array.isArray(data.unBindBvStatusVo) ? data.unBindBvStatusVo : [];
          this.bindList = bind.map((i) => ({ id: i.id, label: i.enName || i.name || '-', raw: i }));
          this.unBindList = unBind.map((i) => ({ id: i.id, label: i.enName || i.name || '-', raw: i }));
          this.currentCategory = this.bindList.map((b) => b.id);
          // 组装选项
          this.refreshCategoryOptions();
        } catch (e) {
          console.error('获取课程大类（绑定/未绑定）失败', e);
        }
      },
      refreshCategoryOptions() {
        // 始终展示：已绑定 + 未绑定（不因选择而消失）
        const merged = [...this.bindList, ...this.unBindList];
        this.categoryOptions = merged.map((i) => ({ label: i.label, value: i.id }));
      },
      // 全部展开（回显时强制展开所有层级）
      expandAll() {
        this.tree.forEach((n) => (n.collapsed = false));
      },
      // 计算某节点是否溢出（通过 mouseenter 触发测量）
      checkOverflow(e, id) {
        const el = e && e.target;
        if (!el) return;
        const overflow = el.scrollWidth > el.clientWidth + 1;
        // 使用 $set 确保响应式
        this.$set(this.overflowMap, id, overflow);
      },
      isOverflow(id) {
        return !!this.overflowMap[id];
      },
      async fetchKnowledgeTreeList(curriculumId) {
        if (!curriculumId) return;
        persistRouteParams(this.$route, { ns: 'KnowledgeManage', keys: ['curriculumId'] });
        this.treeLoading = true;
        try {
          // 读取当前已有折叠状态映射
          const prevCollapsedMap = { ...this.collapsedStateMap };
          const isFirstLoad = Object.keys(prevCollapsedMap).length === 0; // 首次加载：全部展开
          const res = await knowledgeApi.getKnowledgeTreeList({ curriculumId });
          this.treeDataBase = res.data || [];
          const list = Array.isArray(res.data) ? res.data : res.data?.list || [];
          const flat = [];
          const walk = (nodes, parentId = null) => {
            if (!Array.isArray(nodes)) return;
            nodes.forEach((n) => {
              const item = {
                id: n.id,
                name: n.knowledgeName,
                placeholder: '',
                level: n.knowledgeLevel,
                parentId: n.parentId || parentId,
                // 折叠策略：首次加载全部展开；后续根据历史，没有历史的保持原有规则（根展开，其他折叠）
                collapsed: isFirstLoad ? false : prevCollapsedMap.hasOwnProperty(n.id) ? prevCollapsedMap[n.id] : n.knowledgeLevel === 1 ? false : true,
                raw: n
              };
              flat.push(item);
              if (n.children && n.children.length) walk(n.children, n.id);
            });
          };
          walk(list, null);
          this.tree = flat;
          this.tablePage = 1;
          // 移除不存在的节点
          const newMap = {};
          flat.forEach((n) => (newMap[n.id] = n.collapsed));
          this.collapsedStateMap = newMap;
        } catch (e) {
          console.error('获取知识点树失败', e);
        } finally {
          this.treeLoading = false;
        }
      },
      // 基于当前 treeDataBase 重建平铺 tree（用于排序乐观更新），保留已有 collapsed 状态
      rebuildFlatTreeFromDataBase() {
        const prevCollapsedMap = { ...this.collapsedStateMap };
        const flat = [];
        const walk = (nodes, parentId = null) => {
          if (!Array.isArray(nodes)) return;
          nodes.forEach((n) => {
            const item = {
              id: n.id,
              name: n.knowledgeName,
              placeholder: '',
              level: n.knowledgeLevel,
              parentId: n.parentId || parentId,
              collapsed: true, // 默认折叠，稍后按历史状态或根节点展开
              raw: n
            };
            if (n.knowledgeLevel === 1) {
              // 根节点默认不折叠
              item.collapsed = false;
            }
            if (prevCollapsedMap.hasOwnProperty(n.id)) {
              item.collapsed = prevCollapsedMap[n.id];
            }
            flat.push(item);
            if (n.children && n.children.length) walk(n.children, n.id);
          });
        };
        walk(this.treeDataBase, null);
        this.tree = flat;
        const newMap = {};
        flat.forEach((n) => (newMap[n.id] = n.collapsed));
        this.collapsedStateMap = newMap;
      },
      // 失焦后批量保存
      confirmBlurCategory(isVisible) {
        // 仅在下拉关闭时执行
        if (isVisible) return;
        if (this.batchSavingKnowledge) return;
        if (!this.initialId) return;

        const knowledgeCodeList = this.treeDataBase.map((n) => n && n.knowledgeCode).filter(Boolean);
        // if (!knowledgeCodeList.length) return;

        const params = {
          curriculumId: this.initialId,
          curriculumBindId: this.currentCategory.filter(Boolean).join(',') || '',
          knowledgeCodeList
        };

        this.batchSavingKnowledge = true;
        knowledgeApi
          .addAllOrUpdateKnowledge(params)
          .then(() => {
            this.$message.success('知识点绑定课程大类成功');
            this.initCourseCategoryList();
            this.fetchKnowledgeTreeList(this.initialId);
            this.loadTableData();
          })
          .catch((e) => {
            this.$message.error('知识点绑定课程大类失败，请稍后重试');
          })
          .finally(() => {
            this.batchSavingKnowledge = false;
          });
      },
      // 多选模式下移除单个课程大类 tag 时触发，复用关闭下拉的保存逻辑
      handleRemoveCategoryTag(removedValue) {
        if (String(removedValue) === String(this.initialId)) {
          if (!this.currentCategory.includes(this.initialId)) {
            this.currentCategory.unshift(this.initialId);
          }
          this.$message.warning('该知识点已默认绑定该课程大类，不能取消');
          return;
        }
        this.confirmBlurCategory(false);
      },
      // 添加根节点
      addRoot() {
        const item = {
          id: ++idSeed,
          placeholder: '请输入一级知识点名称',
          name: '',
          level: 1,
          parentId: 0,
          collapsed: false,
          isNew: true // 标记为新建（未保存）
        };
        this.tree.push(item);
        this.movingId = item.id; // 新增时给一个运动标记
        setTimeout(() => (this.movingId = null), 400);
        this.startEdit(item);
      },
      // 新增子节点
      addChild(parent) {
        // 限制：最多 3 级
        if (parent.level >= 3) {
          return;
        }
        const [start, end] = this.getBlockRange(parent);
        const insertionIndex = end; // 在父节点所有后代之后插入
        const item = {
          id: ++idSeed,
          placeholder: `请输入${parent.level + 1 === 2 ? '二' : '三'}级知识点名称`,
          name: '',
          level: parent.level + 1,
          parentId: parent.id ? parent.id : 0,
          collapsed: false,
          raw: parent,
          isNew: true // 标记为新建（未保存）
        };
        this.tree.splice(insertionIndex, 0, item);
        // 展开父节点并持久化展开状态，避免首个三级保存后被重置为折叠
        parent.collapsed = false;
        this.$set(this.collapsedStateMap, parent.id, false);
        this.startEdit(item);
      },
      startEdit(item) {
        this.editingId = item.id;
        this.editName = item.name;
        this.$nextTick(() => {
          let ref = this.$refs.editInput;
          if (Array.isArray(ref)) ref = ref[0];
          if (ref) {
            //  focus 方法
            if (typeof ref.focus === 'function') {
              ref.focus();
            } else if (ref.$refs && ref.$refs.input) {
              ref.$refs.input.focus();
            }
          }
        });
      },
      confirmEdit(item) {
        if (this.editingId !== item.id) return;
        let name = (this.editName || '').trim();
        if (!name) {
          // 新增未输入则使用 placeholder 作为默认名称；编辑已有则仍提示
          if (item.isNew) {
            name = item.placeholder || '未命名知识点';
          } else {
            this.$message.warning('请输入知识点名称');
            return;
          }
        }
        const wasNew = !!item.isNew;
        const prevName = item.name; // 记录原名称以便失败回滚
        item.name = name; // 临时写回显示，失败可能回滚
        // 编辑 / 新增确认
        let addQuery, editQuery;
        if (item.isNew) {
          addQuery = {
            id: null,
            parentId: item.parentId,
            knowledgeName: name,
            knowledgeCode: item.raw ? item.raw.raw.knowledgeCode : null,
            knowledgeLevel: item.level,
            curriculumId: this.initialId || '',
            sortsNum:
              item.level == 1
                ? this.treeDataBase.length > 0
                  ? Number(this.treeDataBase[this.treeDataBase.length - 1].sortsNum) + 1
                  : 0
                : item.level == 2 || item.level == 3
                ? item.raw.raw.children
                  ? Number(item.raw.raw.children[item.raw.raw.children.length - 1].sortsNum) + 1
                  : 0
                : 0,
            curriculumBindId: this.currentCategory.concat(this.initialId).join(',') || '' // 多选时传逗号分隔字符串
          };
        } else {
          editQuery = {
            id: item.id,
            parentId: item.parentId,
            knowledgeName: name,
            knowledgeCode: item.raw ? item.raw.knowledgeCode : null,
            knowledgeLevel: item.level,
            curriculumId: this.initialId || '',
            sortsNum: item.raw ? item.raw.sortsNum : 0,
            curriculumBindId: this.currentCategory.concat(this.initialId).join(',') || ''
          };
        }
        this.savingKnowledge = true;
        knowledgeApi
          .addOrUpdateKnowledge(item.isNew ? addQuery : editQuery)
          .then(() => {
            this.$message.success('知识点已保存');
            this.fetchKnowledgeTreeList(this.initialId);
            this.loadTableData();
          })
          .catch((e) => {
            console.error('新增知识点失败', e);
            this.$message.error('知识点保存失败，请稍后重试');
            // 回滚：新增失败移除该节点；编辑失败恢复名称
            if (wasNew) {
              const idx = this.tree.indexOf(item);
              if (idx > -1) this.tree.splice(idx, 1);
            } else {
              item.name = prevName;
            }
            // 失败时也刷新右侧表格，保持同步
            this.loadTableData();
          })
          .finally(() => {
            this.savingKnowledge = false;
          });
        this.editingId = null;
        this.editName = '';
      },
      selectItem(item) {},
      // 上移
      moveUp(item) {
        if (item.level !== 1) return;
        if (this.rootSortSaving) return;
        const idx = this.treeDataBase.findIndex((a) => a.id === item.id);
        if (idx <= 0) return;
        this.rootSortSaving = true;
        this.movingId = item.id;
        const prevSnapshot = this.treeDataBase.slice();
        const targetPrev = this.treeDataBase[idx - 1];
        // 交换 sortsNum，保持后端期待的顺序
        const currentSort = item.raw.sortsNum;
        const prevSort = targetPrev.sortsNum;
        item.raw.sortsNum = prevSort;
        targetPrev.sortsNum = currentSort;
        // 实际数组位置交换
        [this.treeDataBase[idx - 1], this.treeDataBase[idx]] = [this.treeDataBase[idx], this.treeDataBase[idx - 1]];
        this.rebuildFlatTreeFromDataBase();
        // 构造接口参数（两个 id 与新的 sortsNum）
        const rootIds = [
          { id: item.id, sortsNum: item.raw.sortsNum },
          { id: targetPrev.id, sortsNum: targetPrev.sortsNum }
        ];
        this.saveRootOrder(rootIds, prevSnapshot);
        setTimeout(() => (this.movingId = null), 500);
      },
      // 下移
      moveDown(item) {
        if (item.level !== 1) return;
        if (this.rootSortSaving) return; // 排序中禁止再次触发
        const idx = this.treeDataBase.findIndex((a) => a.id === item.id);
        if (idx === -1 || idx >= this.treeDataBase.length - 1) return; // 已在最下
        this.rootSortSaving = true;
        this.movingId = item.id;
        const prevSnapshot = this.treeDataBase.slice();
        const targetNext = this.treeDataBase[idx + 1];
        const currentSort = item.raw.sortsNum;
        const nextSort = targetNext.sortsNum;
        item.raw.sortsNum = nextSort;
        targetNext.sortsNum = currentSort;
        [this.treeDataBase[idx + 1], this.treeDataBase[idx]] = [this.treeDataBase[idx], this.treeDataBase[idx + 1]];
        this.rebuildFlatTreeFromDataBase();
        const rootIds = [
          { id: item.id, sortsNum: item.raw.sortsNum },
          { id: targetNext.id, sortsNum: targetNext.sortsNum }
        ];
        this.saveRootOrder(rootIds, prevSnapshot);
        setTimeout(() => (this.movingId = null), 500);
      },
      getBlockRange(item) {
        const start = this.tree.indexOf(item);
        let end = start + 1;
        while (end < this.tree.length && this.tree[end].level > item.level) {
          end++;
        }
        return [start, end];
      },
      // 调用后端排序接口
      async saveRootOrder(newRootIds, rollbackSnapshot) {
        try {
          await knowledgeApi.updateKnowledgeSort(newRootIds);
          this.$message.success('排序已保存');
          // 成功后用最新后端数据确保顺序一致
          await this.fetchKnowledgeTreeList(this.initialId);
          await this.loadTableData();
        } catch (e) {
          console.error('updateKnowledgeSort failed', e);
          this.$message.error('排序保存失败，已回滚');
          if (Array.isArray(rollbackSnapshot) && rollbackSnapshot.length) {
            this.treeDataBase = rollbackSnapshot;
            this.rebuildFlatTreeFromDataBase();
          }
        } finally {
          this.rootSortSaving = false;
        }
      },
      // 删除知识点（包含级联确认 + 防重复 + 成功后刷新）
      async removeItem(item) {
        if (!item || !item.id) return;
        if (this.deletingKnowledge) return;
        const hasChildren = this.hasChild(item);
        const message = hasChildren && item.level < 3 ? '确认删除该知识点及其所有下级吗？' : '确认删除该知识点吗？';
        try {
          await this.$confirm(message, '删除知识点', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          });
        } catch (e) {
          return;
        }
        this.deletingKnowledge = true;
        try {
          await knowledgeApi.deleteKnowledge({ id: item.id });
          this.$message.success('删除成功');
          await this.fetchKnowledgeTreeList(this.initialId);
          await this.loadTableData();
        } catch (e) {
          console.error('删除知识点失败', e);
          this.$message.error('删除失败，请稍后重试');
        } finally {
          this.deletingKnowledge = false;
        }
      },
      handleImportCommand(cmd) {
        if (cmd === 'template') {
          const fileUrl = 'https://document.dxznjy.com/course/e26ce7ee7a7944289a817c6b9128fdd9.xlsx';
          fetch(fileUrl)
            .then((res) => res.blob())
            .then((blob) => {
              const a = document.createElement('a');
              const objectUrl = URL.createObjectURL(blob);
              a.href = objectUrl;
              a.download = '知识点导入模板.xlsx';
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(objectUrl);
              this.$message.success('模板下载完成');
            })
            .catch(() => {
              this.$message.error('模板下载失败');
            });
        } else if (cmd === 'import') {
          if (this.uploadStatus === 'uploading') {
            this.$message.warning('正在上传，请稍候');
            return;
          }
          if (this.uploadStatus === 'success' || this.uploadStatus === 'error') {
            this.uploadProgress = 0;
            this.uploadStatus = 'idle';
          }
          this.$refs.importInput && this.$refs.importInput.click();
        }
      },
      // 选择文件
      onImportFileSelected(e) {
        const file = e.target.files[0];
        if (!file) return;
        const allowed = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
        if (!allowed.includes(file.type)) {
          this.$message.error('只能上传Excel文件');
          e.target.value = '';
          return;
        }
        if (file.size > 100 * 1024 * 1024) {
          this.$message.error('文件大小不能超过100MB');
          e.target.value = '';
          return;
        }
        this.uploadingFile = file;
        this.uploadProgress = 0;
        this.uploadStatus = 'uploading';
        this.uploadKnowledgeFile();
      },
      // 发起直传（FormData 方式）
      async uploadKnowledgeFile() {
        if (!this.uploadingFile) return;
        const file = this.uploadingFile;
        try {
          const formData = new FormData();
          formData.append('file', file);
          formData.append('curriculumId', this.initialId || '');
          formData.append('curriculumBindId', this.currentCategory.concat(this.initialId).join(',') || '');
          console.log('上传文件', file, formData);
          await knowledgeApi.importKnowledgeFile(formData, {
            onUploadProgress: (evt) => {
              if (!evt.total) return;
              const percent = Math.round((evt.loaded / evt.total) * 85); // 解析前最高 85%
              if (this.uploadStatus === 'uploading') {
                this.uploadProgress = Math.min(85, percent);
              }
            }
          });
          // 模拟后台解析阶段占用 15% 到 100%
          if (this.uploadProgress < 90) this.uploadProgress = 90;
          this.finishUpload(true);
          this.importRefreshing = true;
          this.treeLoading = true;
          this.tableLoading = true;
          setTimeout(async () => {
            try {
              await this.fetchKnowledgeTreeList(this.initialId);
              await this.loadTableData();
            } catch (e) {
            } finally {
              this.importRefreshing = false;
              this.treeLoading = false;
              this.tableLoading = false;
            }
          }, 120);
        } catch (e) {
          console.error('文件上传失败', e);
          this.finishUpload(false);
        }
      },
      // 上传完成
      finishUpload(success) {
        if (success) {
          const start = this.uploadProgress;
          const remain = 100 - start;
          const duration = Math.min(800, Math.max(250, remain * 12));
          const startTime = performance.now();
          const step = () => {
            const ratio = (performance.now() - startTime) / duration;
            if (ratio < 1) {
              this.uploadProgress = Math.round(start + remain * ratio);
              requestAnimationFrame(step);
            } else {
              this.uploadProgress = 100;
              this.uploadStatus = 'success';
              this.$message.success('知识点导入成功');
              setTimeout(() => {
                this.uploadProgress = 0;
                this.uploadStatus = 'idle';
                this.uploadingFile = null;
                if (this.$refs.importInput) this.$refs.importInput.value = '';
              }, this.successHoldDelay);
            }
          };
          requestAnimationFrame(step);
        } else {
          this.uploadStatus = 'error';
          if (this.uploadProgress < 5) this.uploadProgress = 15;
          this.$message.error('导入失败');
          setTimeout(() => {
            this.uploadProgress = 0;
            this.uploadStatus = 'idle';
            this.uploadingFile = null;
            if (this.$refs.importInput) this.$refs.importInput.value = '';
          }, 600);
        }
      },
      // ========== 右侧表格接口加载 ==========
      async loadTableData() {
        const curriculumId = this.initialId;
        const params = {
          curriculumId,
          pageNum: this.tablePage,
          pageSize: this.tablePageSize
        };
        this.tableLoading = true;
        try {
          const res = await knowledgeApi.getKnowledgeList(params);
          const page = res.data || [];
          const list = page.data || {};
          const size = Number(page.totalItems);
          this.tableData = list.map((item, idx) => ({
            index: idx + 1,
            level1: item.xktKnowledgeVo1 ? item.xktKnowledgeVo1.knowledgeName : '------',
            level2: item.xktKnowledgeVo2 ? item.xktKnowledgeVo2.knowledgeName : '------',
            level3: item.xktKnowledgeVo3 ? item.xktKnowledgeVo3.knowledgeName : '------'
          }));
          this.tableTotal = size;
        } catch (e) {
          this.tableData = [];
          this.tableTotal = 0;
        } finally {
          this.tableLoading = false;
        }
      },
      handlePageChange(p) {
        this.tableLoading = true;
        this.tablePage = p;
        this.loadTableData();
      },
      handleSizeChange(size) {
        this.tableLoading = true;
        this.tablePageSize = size;
        this.tablePage = 1;
        this.loadTableData();
      },
      onImportDropdownVisible(val) {
        this.importDropdownVisible = val;
      },
      hasChild(item) {
        return this.tree.some((t) => t.parentId === item.id);
      },
      toggleCollapse(item) {
        const willCollapse = !item.collapsed; // true 表示折叠，false 表示展开
        if (item.level === 1 || item.level === 2) {
          if (willCollapse) {
            this.cascadeSetCollapse(item, true, true);
          } else {
            item.collapsed = false;
            this.$set(this.collapsedStateMap, item.id, false);
          }
        } else {
          item.collapsed = willCollapse;
          this.$set(this.collapsedStateMap, item.id, item.collapsed);
        }
      },
      cascadeSetCollapse(rootItem, collapsed, includeSelf = true) {
        const targetId = rootItem.id;
        this.tree.forEach((n) => {
          let isDescendant = false;
          if (n.id === targetId) {
            if (includeSelf) {
              n.collapsed = collapsed;
              this.$set(this.collapsedStateMap, n.id, n.collapsed);
            }
            return;
          }
          let pid = n.parentId;
          while (pid) {
            if (pid === targetId) {
              isDescendant = true;
              break;
            }
            const parentObj = this.tree.find((x) => x.id === pid);
            if (!parentObj) break;
            pid = parentObj.parentId;
          }
          if (isDescendant) {
            // level 1: 处理全部后代；level 2: 仅处理三级（及自身，已在上面处理）
            if (rootItem.level === 1) {
              n.collapsed = collapsed;
              this.$set(this.collapsedStateMap, n.id, n.collapsed);
            } else if (rootItem.level === 2) {
              if (n.level >= 3) {
                n.collapsed = collapsed;
                this.$set(this.collapsedStateMap, n.id, n.collapsed);
              }
            }
          }
        });
        // 最后更新 root 自身映射（如果未处理）
        if (!includeSelf) {
          this.$set(this.collapsedStateMap, rootItem.id, rootItem.collapsed);
        }
      },
      isFirstRoot(item) {
        if (item.level !== 1) return false;
        // 找到第一个 level=1 的节点
        return this.tree.find((t) => t.level === 1) === item;
      },
      isLastRoot(item) {
        if (item.level !== 1) return false;
        // 反向找到最后一个 level=1 节点
        for (let i = this.tree.length - 1; i >= 0; i--) {
          if (this.tree[i].level === 1) return this.tree[i] === item;
        }
        return false;
      }
    }
  };
</script>

<style lang="less" scoped>
  @normal-text: PingFangSC-regular;

  .knowledge-manage {
    display: flex;
    height: calc(100vh - 84px);
    padding: 40px 30px;
    box-sizing: border-box;

    .left-panel {
      width: 400px;
      display: flex;
      flex-direction: column;

      .category-select,
      .add-root {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .label {
          font-size: 18px;
          margin-right: 4px;
          font-family: @normal-text;
          color: rgb(78, 89, 105);
        }
      }

      .add-root {
        margin-top: 30px;

        .plus-btn {
          cursor: pointer;
          font-size: 20px;
          color: #1f1f1f;
        }
      }

      .knowledge-list {
        width: 400px;
        overflow: auto;
        margin-top: 15px;
      }

      .k-item {
        display: flex;
        align-items: center;
        padding: 0 4px;
        height: 32px;
        margin-bottom: 12px;
        border-radius: 4px;
        cursor: pointer;
        position: relative;

        &:last-child {
          margin-bottom: 0;
        }

        .toggle-icon {
          color: #606266;
          margin-right: 4px;
          font-size: 16px;
          width: 14px;
          text-align: center;
          cursor: pointer;
          &:hover {
            color: #409eff;
          }
        }

        .name-wrapper {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 100%;
          width: calc(100% - 135px);
          padding: 0 6px;
          background: #fff;
          box-sizing: border-box;
        }

        .name {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 32px;
          color: rgba(0, 0, 0, 1);
          font-family: @normal-text;
          font-size: 12px;
          font-weight: 500;
        }

        .count {
          font-size: 10px;
          color: #909399;
          margin-left: 4px;
        }

        .ops {
          margin-left: 5px;
          display: flex;
          align-items: center;
          gap: 2px;

          i {
            font-size: 16px;
            color: #606266;
            cursor: pointer;
            line-height: 1;
            padding: 2px;
            &:hover {
              color: #409eff;
            }
          }
          .disabled-move {
            color: #c0c4cc !important;
            cursor: not-allowed;
            &:hover {
              color: #c0c4cc !important;
            }
          }
        }
      }
    }

    .divider {
      width: 1px;
      background: #dcdfe6;
      margin: 0 20px;
      flex-shrink: 0; /* 防止在窗口过窄时被压缩为 0 宽 */
    }

    .right-panel {
      flex: 1;
      position: relative;
      display: flex;
      flex-direction: column;

      .import-box {
        position: absolute;
        top: 0;
        right: 0;
        .progress-wrapper {
          margin-top: 10px;
        }
      }

      .content-box {
        flex: 1;
        margin-top: 60px;
        height: calc(100vh - 84px);
        overflow: auto;
      }

      .knowledge-table {
        flex: 1;
      }

      .table-wrapper {
        position: relative;
        width: 100%;
        .knowledge-table {
          width: 100%;
        }
      }
      .placeholder-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #101010;
        font-size: 25px;
        font-family: @normal-text;
      }

      .table-pagination {
        margin-top: 10px;
        text-align: right;
        width: 100%;
      }
    }
    .list-fade-move {
      transition: transform 0.3s ease; // 平滑位移
      will-change: transform;
    }

    // 移动中的动画
    .k-item.is-moving {
      animation: movingPulse 0.35s ease;
    }
    @keyframes movingPulse {
      0% {
        opacity: 0.8;
      }
      100% {
        opacity: 1;
      }
    }
  }
</style>
