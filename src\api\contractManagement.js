/**
 * 商户管理-合同管理接口
 */
import request from '@/utils/request';
/**
 * 获取合同列表分页
 * @param {*} params
 * @returns
 */
export const getContractList = (params) => {
  return request({
    url: '/znyy/sign/contract/getContractList',
    method: 'GET',
    params
  });
};
/**
 * 获取合同签署详情
 * @param {*} params
 * @returns
 */
export const getContractDetail = (params) => {
  return request({
    url: '/znyy/sign/contract/getContractQrLink',
    method: 'GET',
    params
  });
};
/**
 * 获取合同二维码签署状态
 * @param {object} params
 * @param {string} params.flowId 流程id
 * @param {number} params.signSource 签署来源：0-e签宝 ，1-电子签
 * @param {string} params.participantFlag 签署方：甲方 ，乙方
 * @returns
 */
export const getContractSigingStatus = (params) => {
  return request({
    url: '/znyy/sign/contract/getContractQrLinkStatus',
    method: 'GET',
    params
  });
};
