// 流程管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/workFlow',
  component: Layout,
  redirect: '/workFlow',
  meta: {
    perm: 'm:workFlow',
    title: '流程管理',
    icon: 'workFlow'
  },
  children: [
    {
      path: 'flowCategory',
      component: () => import('@/views/activiti/workFlow/flowCategory/formFlowCategory'),
      name: 'flowCategory',
      meta: {
        perm: 'm:workFlow:flowCategory',
        title: '流程分类'
      }
    },
    {
      path: 'flowEntry',
      component: () => import('@/views/activiti/workFlow/flowEntry/formFlowEntry'),
      name: 'flowEntry',
      meta: {
        perm: 'm:workFlow:flowEntry',
        title: '流程设计'
      }
    },
    {
      path: 'flowInstance',
      component: () => import('@/views/activiti/workFlow/taskManager/formAllInstance'),
      name: 'flowInstance',
      meta: {
        perm: 'm:workFlow:flowInstance',
        title: '流程实例'
      }
    }
  ]
};
