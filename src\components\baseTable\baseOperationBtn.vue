<template>
  <div class="table-operation-btn-group" :ref="customRef">
    <div
      class="btn"
      v-for="btnItem in discreteBtnList"
      :key="btnItem.key"
      :class="getBtnColor(btnItem.color, 'class')"
      :style="getBtnColor(btnItem.color, 'style')"
      @click="btnItem.handleClick(row)"
    >
      {{ btnItem.name }}
    </div>
    <el-popover v-if="moreBtnList.length > 0" placement="bottom" width="200" trigger="click" popper-class="more-popover">
      <div class="more-popover">
        <div
          class="more-btn"
          v-for="btnItem in moreBtnList"
          :key="btnItem.key"
          :class="getBtnColor(btnItem.color, 'class')"
          :style="getBtnColor(btnItem.color, 'style')"
          @click="btnItem.handleClick(row)"
        >
          {{ btnItem.name }}
        </div>
      </div>
      <div slot="reference" class="btn btn-success">更多</div>
    </el-popover>
  </div>
</template>

<script>
  export default {
    name: 'baseOperationBtn',
    props: {
      customRef: {
        type: String,
        default: 'base_operation_btn_ref'
      },
      btnList: {
        type: Array,
        default: () => []
      },
      handleShow: {
        type: Function,
        default: (btnItem, row) => true
      },
      row: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        btnType: ['primary', 'success', 'warning', 'danger'],
        discreteBtnList: [],
        moreBtnList: []
      };
    },
    created() {
      this.btnList.forEach((btnItem) => {
        this.handleShow(btnItem, this.row) && (this.discreteBtnList.length > 2 ? this.moreBtnList.push({ ...btnItem }) : this.discreteBtnList.push({ ...btnItem }));
      });
    },
    methods: {
      getBtnColor(color, type) {
        color || (color = 'primary');
        const isClass = this.btnType.includes(color);
        if (type == 'class') {
          const classList = [];
          if (isClass) {
            classList.push(`btn-${color}`);
          }
          return classList;
        } else if (type == 'style') {
          const styleObj = {};
          if (!isClass) {
            styleObj.color = color;
          }
          return styleObj;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .table-operation-btn-group {
    display: flex;
    align-items: center;
    width: fit-content;
    .btn {
      cursor: pointer;
      padding: 0 10px;
      margin-left: 4px;
      white-space: nowrap;
      &:first-child {
        margin-left: 8px;
      }
    }
    .btn {
      &-primary {
        color: #567ee5;
      }
      &-success {
        color: #73bf8a;
      }
      &-warning {
        color: #ea8a32;
      }
      &-danger {
        color: #e55656;
      }
    }
  }
  .more-popover {
    text-align: center;
    padding: 10px 0;

    .more-btn {
      padding: 10px 15px;
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 4px;
      color: #1890ff;
    }
  }
</style>
