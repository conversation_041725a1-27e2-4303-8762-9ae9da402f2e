// 财务管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/finance',
  component: Layout,
  redirect: '/finance/periodValList',
  meta: {
    perm: 'm:fin',
    title: '财务管理',
    icon: 'finance'
  },
  children: [
    {
      path: 'CoursependingPayment',
      component: () => import('@/views/order/CoursependingPaymentList.vue'),
      name: 'CoursependingPaymentList',
      meta: {
        perm: 'm:fin:coursependingPayment',
        title: '课程等待付款列表'
      }
    },
    {
      path: 'rechargeCourseList',
      component: () => import('@/views/order/rechargeCourseIndex.vue'),
      name: 'rechargeCourseList',
      meta: {
        perm: 'm:fin:merchantCourseByList',
        title: '课程购买列表'
      }
    },
    {
      path: 'refundCourseList',
      component: () => import('@/views/order/refundCourseIndex'),
      name: 'refundCourseList',
      meta: {
        perm: 'm:fin:merchantCourseRefundList',
        title: '退课列表'
      }
    },
    {
      path: 'refundCourseLista',
      component: () => import('@/views/order/refundCourseIndexa'),
      name: 'refundCourseLista',
      meta: {
        perm: 'm:fin:financeInforM',
        title: '门店退课列表'
      }
    },
    {
      path: 'memberFundStatement',
      component: () => import('@/views/finance/memberFundStatement'),
      name: 'memberFundStatement',
      meta: {
        perm: 'm:fin:memberFundStatement',
        title: '会员资金流水'
      }
    },
    {
      path: 'financeInforM',
      component: () => import('@/views/finance/financeInforM'),
      name: 'financeInforM',
      meta: {
        perm: 'm:fin:financeInforM',
        title: '门店交付订单'
      }
    },
    {
      path: 'marketProfiAccountFlows',
      component: () => import('@/views/finance/marketProfiAccountFlows'),
      name: 'marketProfiAccountFlows',
      meta: {
        perm: 'm:fin:marketProfiAccountFlows',
        title: '推荐人分润流水'
      }
    },
    {
      path: 'onlineRecharge',
      component: () => import('@/views/finance/onlineRecharge'),
      name: 'onlineRecharge',
      meta: {
        perm: 'm:fin:onlineRecharge',
        title: '在线充值'
      }
    },
    {
      path: 'onlineCompanyRecharge',
      component: () => import('@/views/finance/onlineCompanyRecharge'),
      name: 'onlineCompanyRecharge',
      meta: {
        perm: 'm:fin:onlineCompanyRecharge',
        title: '分公司在线充值'
      }
    },

    {
      path: 'onlineCharge',
      component: () => import('@/views/finance/onlineCharge'),
      name: 'onlineCharge',
      meta: {
        perm: 'm:fin:onlineCharge',
        title: '在线扣款'
      }
    },
    {
      path: 'onlineCompanyCharge',
      component: () => import('@/views/finance/onlineCompanyCharge'),
      name: 'onlineCompanyCharge',
      meta: {
        perm: 'm:fin:onlineCompanyCharge',
        title: '分公司在线扣款'
      }
    },
    {
      path: 'merchantFlowList',
      component: () => import('@/views/finance/merchantFlowList'),
      name: 'merchantFlowList',
      meta: {
        perm: 'm:fin:merchantFlowList',
        title: '商户资金流水'
      }
    },
    {
      path: 'merchantFlowCourseList',
      component: () => import('@/views/finance/merchantFlowCourseList'),
      name: 'merchantFlowCourseList',
      meta: {
        perm: 'm:fin:merchantFlowCourseList',
        title: '商户资金学时流水'
      }
    },
    {
      path: 'investmentRecruitmentDetailsList',
      component: () => import('@/views/finance/investmentRecruitmentDetailsList'),
      name: 'merchantFlowList',
      meta: {
        perm: 'm:fin:investmentRecruitmentDetailsList',
        title: '招商招生明细'
      }
    },
    {
      path: 'merchantFlowListDealer',
      component: () => import('@/views/finance/merchantFlowListDealer'),
      name: 'merchantFlowListDealer',
      meta: {
        perm: 'm:fin:merchantFlowListDealer',
        title: '托管中心充值资金流水'
      }
    },
    {
      path: 'coursePackageProfit',
      component: () => import('@/views/finance/coursePackageProfit'),
      name: 'coursePackageProfit',
      meta: {
        // perm:'m:fin:coursePackageProfit',
        title: '收益'
      }
    },
    {
      path: 'schoolFlowList',
      component: () => import('@/views/finance/schoolFlowList'),
      name: 'schoolFlowList',
      meta: {
        perm: 'm:fin:schoolFlowList',
        title: '门店学时变动明细'
      }
    },
    {
      path: 'schoolFlowPackageList',
      component: () => import('@/views/finance/schoolFlowPackageList'),
      name: 'schoolFlowPackageList',
      meta: {
        perm: 'm:fin:schoolFlowPackageList',
        title: '课程包变动明细'
      }
    },
    {
      path: 'assistantFlowList',
      component: () => import('@/views/finance/assistantFlowList'),
      name: 'assistantFlowList',
      meta: {
        perm: 'm:fin:assistantFlowList',
        title: '学员课程变动明细'
      }
    },
    {
      path: 'offsiteDepositOrder',
      component: () => import('@/views/finance/offsiteDepositOrder'),
      name: 'offsiteDepositOrder',
      meta: {
        perm: 'm:fin:offsiteDepositOrder',
        title: '异地定金订单管理'
      }
    },
    {
      path: 'storeRevenue',
      component: () => import('@/views/finance/storeRevenue'),
      name: 'storeRevenue',
      meta: {
        perm: 'm:fin:storeRevenue',
        title: '门店收益'
      }
    }
  ]
};
