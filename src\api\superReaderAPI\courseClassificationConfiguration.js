import request from "@/utils/request";
// 课程分类配置接口

// 获取课程大类
export function getCourseCategories() {
  return request({
    url: '/znyy/curriculum/chinese',
    method: 'GET'
  });
}

// 分页获取课程列表
export function getCourseList(data) {
  return request({
    url: '/dyw/web/chinese/courseManagement/list',
    method: 'GET',
    params:data
  })
}

// 获取课程分类列表
export function getCourseCategoryList(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/listPage',
    method: 'GET',
    params:data
  })
}

// 获取课程版本列表
export function getCourseVersionList(data) {
  return request({
    url: '/dyw/web/chinese/basisConfig/selectVersionInfo',
    method: 'GET',
    params:data
  })
}

// 查询课程分类树及版本(需要版本ID)
export function getCourseTreeData(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/selectTreeVersion',
    method: 'GET',
    params:data
  })
}

// 查询课程分类树及版本(不需要版本呢ID)
export function getCourseTreeDataNoVersion(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/selectTree',
    method: 'GET',
    params:data
  })
}

// 关联版本列表
export function getCourseRelationList(data) {
  return request({
    url: '/dyw/web/chinese/basisVersion/versionListPage',
    method: 'GET',
    params:data
  })
}

// 新增学科
export function addCourseSubject(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/insertSubject',
    method: 'POST',
    data
  })
}

// 新增/编辑学段
export function addEditCourseSubject(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/insertOrUpdate',
    method: 'POST',
    data
  })
}

// 查询学科列表
export function getCourseSubjectList(data) {
  return request({
    url: '/dyw/web/chinese/basisConfig/selectSubjectInfo',
    method: 'GET',
    params:data
  })
}

// 删除学段
export function deleteCourseSubject(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/delete',
    method: 'DELETE',
    params:data
  })
}

// 删除关联版本
export function deleteCourseRelation(data) {
  return request({
    url: '/dyw/web/chinese/basisVersion/deleteVersion',
    method: 'DELETE',
    params:data
  })
}

// 更新版本排序
export function updateCourseRelationSort(data) {
  return request({
    url: '/dyw/web/chinese/basisVersion/updateVersionSort',
    method: 'POST',
    data
  })
}
