<template>
  <div class="app-container">
    <el-form ref="dataQuery" :model="dataQuery" :inline="true" class="SearchForm" label-width="100px" style="padding: 40px 0 30px 20px">
      <el-row style="padding-bottom: 15px">
        <el-col :span="8" :xs="24">
          <el-form-item label="阶段:" prop="phase">
            <el-select size="medium" v-model="dataQuery.phase" filterable value-key="value" placeholder="请选择阶段" clearable>
              <el-option v-for="(item, index) in mapPhaseType" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="题目类型:" prop="questionType">
            <el-select size="medium" v-model="dataQuery.questionType" filterable value-key="value" placeholder="请选择题型" clearable>
              <el-option v-for="(item, index) in mapQuestionTypeType" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="题目:" prop="questionName">
            <el-input size="medium" v-model="dataQuery.questionName" @keyup.enter.native="fetchData02()" style="width: 200px" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="知识点:" prop="knowledgeIds">
            <el-select size="medium" v-model="dataQuery.knowledgeIds" :disabled="!dataQuery.phase" filterable value-key="value" placeholder="请先选择阶段" clearable>
              <el-option v-for="(item, index) in grammarTypeListG" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="试题分类:" prop="questionAroundType">
            <el-select size="medium" v-model="dataQuery.questionAroundType" filterable value-key="value" placeholder="请选择试题分类" clearable>
              <el-option v-for="(item, index) in testCategoryList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="8" style="text-align: right; padding-top: 6px; padding-right: 100px">
          <el-button type="primary" icon="el-icon-search" size="medium" @click="fetchData02()" style="margin-right: 20px">搜索</el-button>
          <el-button type="info" icon="el-icon-refresh" size="medium" @click="resetData()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <el-button style="margin-bottom: 20px; margin: 5px 5px 5px 0" size="medium" type="success" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      <!-- 表格 -->
      <el-table
        class="common-table"
        :data="testTableData"
        stripe
        border
        max-height="500"
        :default-sort="{ prop: 'addTime', order: 'descending' }"
        v-loading="tableLoading"
        :empty-text="testTableData.length === 0 ? '暂无数据' : ''"
      >
        <el-table-column prop="id" label="题目编号"></el-table-column>
        <el-table-column prop="questionAroundType" label="试题分类">
          <template slot-scope="scope">
            {{ scope.row.questionAroundTypeName ? scope.row.questionAroundTypeName : '暂无数据' }}
          </template>
        </el-table-column>
        <el-table-column prop="sortNum" label="排序"></el-table-column>
        <el-table-column prop="phase" label="阶段">
          <template slot-scope="scope">
            {{ scope.row.phaseName ? scope.row.phaseName : '暂无数据' }}
          </template>
        </el-table-column>
        <el-table-column prop="questionType" label="题目类型">
          <template slot-scope="scope">
            {{ scope.row.questionTypeName ? scope.row.questionTypeName : '暂无数据' }}
          </template>
        </el-table-column>
        <el-table-column prop="questionName" label="题目" show-overflow-tooltip></el-table-column>
        <el-table-column prop="knowledgeList" show-overflow-tooltip label="知识点">
          <!-- <template slot-scope="scope">
            {{ scope.row.knowledgeIdList ? scope.row.phaseName : '暂无数据' }}
          </template> -->
        </el-table-column>
        <el-table-column prop="createTime" label="上传时间"></el-table-column>
        <el-table-column prop="updateTime" label="编辑时间"></el-table-column>
        <el-table-column label="操作" fixed="right" width="300px">
          <template slot-scope="scope">
            <el-button type="success" size="medium" icon="el-icon-edit-outline" @click="openEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="medium" icon="el-icon-delete" @click="deleteQuestion(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="dataQuery.pageNum"
        :page-sizes="[10, 50, 100, 150, 200]"
        :page-size="dataQuery.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <div>
      <AddQuestionDialog
        v-if="dialogVisible"
        ref="AddQuestionDialog"
        :dialog-visible.sync="dialogVisible"
        :title="title"
        :dataDialog="dataDialog"
        :mapPhaseType="mapPhaseType"
        :mapQuestionTypeType="mapQuestionTypeType"
        :grammarTypeListG="grammarTypeListG"
        :mapExamPoints="mapExamPoints"
        :testCategoryList="testCategoryList"
        @submit="onDialogSubmit"
        @resetDialogData="resetDialogData"
      />
      <!--  -->
    </div>
  </div>
</template>

<script>
  import { queryByTypeAPI, optionsAPI, aroundAPI, pageAPI, oneAPI, thirdNodeAPI, addOrUpdate, deleteAPI } from '@/api/grammar/prePostTest';
  import AddQuestionDialog from './component/AddQuestionDialog.vue';
  export default {
    name: 'syntaxprePostTestList',
    components: { AddQuestionDialog },
    data() {
      return {
        // 搜索
        dataQuery: {
          phase: '', // 阶段
          questionType: '', // 题目类型
          questionName: '', // 题目
          knowledgeIds: '', //知识点
          questionAroundType: '', // 试题分类
          pageNum: 1,
          pageSize: 10
        },
        total: null,
        mapQuestionTypeType: [], //题目类型集合
        mapPhaseType: [], // 阶段数据集合
        grammarTypeListG: [], //知识点数据集合
        mapExamPoints: [], //考点数据集合
        testCategoryList: [], //试题分类数据集合
        testTableData: [], //表格数据
        dataDialog: {},
        tableLoading: false,
        dialogVisible: false, // 修改弹窗是否展示
        title: '',
        updateSingle: {}
      };
    },
    created() {
      this.initData();
    },
    watch: {
      'dataQuery.phase'(newVal) {
        if (newVal) {
          this.getKnowIdList(newVal); // 传阶段ID获取知识点
          this.dataQuery.knowledgeIds = ''; // 清空下级选择
          this.mapExamPoints = []; // 清空考点
          this.dataQuery.examPoints = [];
        } else {
          this.grammarTypeListG = [];
          this.dataQuery.knowledgeIds = '';
        }
      },
      // 监听弹框状态，避免在编辑时清空知识点数据
      dialogVisible(newVal) {
        if (!newVal) {
          // 弹框关闭时，重置搜索表单的知识点选择，但不影响弹框内的数据
          this.dataQuery.knowledgeIds = '';
        }
      }
    },
    methods: {
      initData() {
        this.tableLoading = true; // 初始加载时显示 loading
        this.getType();
        this.getAroundAPI();
        this.fetchData();
      },
      getType() {
        // 0阶段 1 题目类型
        Promise.all([queryByTypeAPI({ dictType: 'grammar_phase' }), queryByTypeAPI({ dictType: 'grammar_question_type' })])
          .then((res) => {
            this.mapPhaseType = res[0].data.map((item) => ({
              id: item.dictValue,
              name: item.dictLabel
            }));
            this.mapQuestionTypeType = res[1].data.map((item) => ({
              id: item.dictValue,
              name: item.dictLabel
            }));
            console.log('🚀 ~ this.mapQuestionTypeType=res.data.map ~ this.mapQuestionTypeType:', this.mapQuestionTypeType);
          })
          .catch((error) => {
            console.error('获取字典数据失败', error);
          });
      },
      // 阶段————获取知识点类型
      getKnowIdList(phaseId) {
        optionsAPI({ knowledgeFlag: true, phase: phaseId }).then((res) => {
          this.grammarTypeListG = res.data;
          console.log('🚀 ~ optionsAPI ~ this.grammarTypeListG:', this.grammarTypeListG);
        });
      },
      // 获取考点列表
      getExamPoints() {
        // thirdNodeAPI({ knowledgeIdList: '1298992483308814336' }).then((response) => {
        thirdNodeAPI().then((response) => {
          this.mapExamPoints = response.data;
        });
      },
      // 获取试题分类
      getAroundAPI() {
        aroundAPI({}).then((res) => {
          this.testCategoryList = res.data;
        });
      },
      //搜索按钮
      fetchData02() {
        this.tableLoading = true; // 开始 loading
        console.log('this.dataQuery----------', this.dataQuery);
        this.dataQuery.pageNum = 1;
        this.fetchData();
      },
      // 获取表格数据
      fetchData() {
        pageAPI(this.dataQuery)
          .then((res) => {
            this.testTableData = res.data.data;
            this.total = Number(res.data.totalItems);
            console.log('==获取表格数据===========res', this.testTableData);
          })
          .finally(() => {
            this.tableLoading = false; // 数据加载完成，关闭 loading
          });
      },
      // 获取表格详情
      getTestDetail(val) {
        // knowledgeIdList
        return oneAPI({ id: val }).then((res) => {
          console.log('🚀 ~ 获取表格详情oneAPI ~ res:', res);
          this.dataDialog = res.data;
          console.log('🚀 ~ 获取表格详情oneAPI ~ this.dataDialog:', this.dataDialog);

          if (this.dataDialog.knowledgeIdList.length === 0) {
            this.dataDialog.examPoints = [];
          }
          return res.data;
        });
      },

      //重置列表
      resetData() {
        this.$refs.dataQuery.resetFields();
        this.dataQuery = {
          phase: '', // 阶段
          questionType: '', // 题目类型
          questionName: '', // 题目
          knowledgeIds: '', //知识点
          questionAroundType: '' // 试题分类
        };
        this.initData();
      },
      // 添加按钮
      clickAdd() {
        this.title = '新增试题';
        this.dialogVisible = true;
        // this.dataDialog = {}; // 清空数据
        this.dataDialog = {
          sortNum: 1,
          phase: '',
          questionType: '',
          questionName: '',
          questionDescription: '',
          questionAroundType: '',
          knowledgeIdList: [],
          examPoints: [],
          optionList: []
        };
        this.dataDialog.sortNum = 1;
        console.log('🚀 ~ clickAdd ~ this.dialogVisible:', this.dialogVisible);
      },
      openEdit(tav) {
        this.title = '编辑试题';
        // 先获取详情数据，再打开弹框
        this.getTestDetail(tav.id).then(async () => {
          // 确保有阶段数据后再获取知识点
          if (this.dataDialog.phase) {
            // await this.getKnowIdList(this.dataDialog.phase);
          }
          this.dialogVisible = true;
        });
        console.log('🚀 ~ 编辑试题this.dataDialog ~ val:', this.dataDialog);
      },
      onDialogSubmit() {
        this.fetchData();
      },
      resetDialogData() {
        this.dataDialog = {
          sortNum: 1,
          phase: '',
          questionType: '',
          questionName: '',
          questionDescription: '',
          questionAroundType: '',
          knowledgeIdList: [],
          examPoints: [],
          optionList: []
        };
      },
      deleteQuestion(id) {
        console.log('🚀 ~ deleteQuestion ~ id:', typeof id, id);

        this.$confirm('确定进行删除操作吗?', '删除操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return deleteAPI({ id });
          })
          .then(() => {
            this.fetchData();
            this.$message.success('删除成功!');
          });
      },
      // 分页
      handleSizeChange(val) {
        console.log('🚀 ~ handleSizeChange ~ val:', val);
        this.tableLoading = true;
        this.dataQuery.pageSize = val; // 设置每页条数
        this.dataQuery.pageNum = 1;
        this.fetchData();
      },
      handleCurrentChange(val) {
        console.log('🚀 ~ handleCurrentChange ~ val:', val);
        this.tableLoading = true;
        this.dataQuery.pageNum = val;
        this.fetchData();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .common-form .el-form-item {
    margin-bottom: 30px;
  }
</style>
<style>
  .el-tooltip__popper.is-dark {
    max-width: 400px;
  }
</style>
