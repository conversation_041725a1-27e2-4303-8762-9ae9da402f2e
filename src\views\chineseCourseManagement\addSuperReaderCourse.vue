<template>
  <div class="form-container" v-loading="loading">
    <div class="section-title">{{ isEdit ? '编辑' : '新增' }}课程基础信息</div>

    <el-form ref="courseForm" :model="courseForm" label-width="100px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="7">
          <el-form-item label="课程名称:" prop="courseName">
            <el-input v-model="courseForm.courseName" placeholder="请输入课程名称" maxlength="20" show-word-limit style="width: 200px"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="课程类型:" prop="courseType">
            <el-select v-model="courseType" placeholder="请选择" style="width: 65%" @change="changeCourseType">
              <el-option v-for="item in courseTypeOptions" :key="item.value" :label="item.label" :value="item.value" style="width: 200px"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="7">
          <el-form-item label="课程排序:" prop="courseSort">
            <el-input-number v-model="courseForm.courseSort" :min="1" :max="999999999" placeholder="请输入正整数" style="width: 200px"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="关联知识点:" prop="knowledge">
            <div class="knowledge-item" @click="handleKnowledgeScopeChange">
              <span>已选择{{ courseForm.courseKnowledgeCorrelationList.length }}个知识点</span>
              <i class="el-icon-arrow-down"></i>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col>
          <el-form-item label="课程解锁视频:" prop="courseIsLockVideo">
            <el-row style="margin-bottom: 10px">
              <el-radio-group v-model="courseForm.courseIsLockVideo">
                <el-radio label="0">解锁视频</el-radio>
                <el-radio label="1">不解锁视频</el-radio>
              </el-radio-group>
            </el-row>
            <!-- 当选择解锁视频时显示的子选项 -->
            <el-row v-if="courseForm.courseIsLockVideo == 0" style="margin-top: 10px">
              <el-radio-group v-model="courseForm.courseLockType">
                <el-radio label="0">和课程视频相同</el-radio>
                <el-radio label="1">自定义</el-radio>
              </el-radio-group>
              <!-- 当选择视频时需要弹出弹框，获取选择的视频数 -->
              <div class="knowledge-item" style="display: inline-block; margin-left: 10px" v-if="courseForm.courseLockType == 1" @click="openVideoDialog">
                <span>已选择{{ courseForm.courseProcessUnlockVideoCoList.length }}个视频</span>
                <i class="el-icon-arrow-down" style="float: right; line-height: 32px; cursor: pointer"></i>
              </div>
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 这里存放组件 -->
    <div>
      <div style="display: flex; align-items: center; gap: 10px">
        <h3 style="margin: 0">流程配置</h3>
        <span style="font-size: 12px; color: #6c6c6c; margin-top: 8px">预估总时长：{{ sumMinute || 0 }}分钟</span>
      </div>
      <template v-for="(process, index) in courseForm.courseProcessConfigCoList">
        <!-- 根据不同的类型渲染不同的组件 -->
        <superReaderFixedImageComponent
          :ref="`component${index}`"
          v-if="process.processType == 1"
          :key="index"
          :formData="process"
          @delete="handleDeleteProcess(index)"
          @update="updateProcessData($event, index)"
        ></superReaderFixedImageComponent>
        <superReaderFixedManyImageComponent
          :ref="`component${index}`"
          v-if="process.processType == 6"
          :key="index"
          :index="index"
          :formData="process"
          @delete="handleDeleteProcess(index)"
          @update="updateProcessData($event, index)"
        ></superReaderFixedManyImageComponent>
        <superReaderVideoComponent
          :ref="`component${index}`"
          v-if="process.processType == 2"
          :key="index"
          :formData="process"
          @delete="handleDeleteProcess(index)"
          @update="updateProcessData($event, index)"
        ></superReaderVideoComponent>
        <superReaderBeforText
          :ref="`component${index}`"
          v-if="process.processType == 3"
          :key="index"
          :formData="process"
          @delete="handleDeleteProcess(index)"
          @update="updateProcessData($event, index)"
        ></superReaderBeforText>
        <superReaderMiddleText
          :ref="`component${index}`"
          v-if="process.processType == 5"
          :key="index"
          :formData="process"
          @delete="handleDeleteProcess(index)"
          @update="updateProcessData($event, index)"
        ></superReaderMiddleText>
        <superReaderAfterText
          :ref="`component${index}`"
          v-if="process.processType == 4"
          :key="index"
          :formData="process"
          @delete="handleDeleteProcess(index)"
          @update="updateProcessData($event, index)"
        ></superReaderAfterText>
        <superReaderLearningReport
          :ref="`component${index}`"
          v-if="process.processType == 7"
          :key="index"
          :formData="process"
          @delete="handleDeleteProcess(index)"
          @update="updateProcessData($event, index)"
        ></superReaderLearningReport>

      </template>
    </div>

    <div style="margin-top: 20px">
      <el-button icon="el-icon-plus" style="width: 100%; height: 40px" @click="handleAddProcess">添加流程</el-button>
    </div>
    <div style="text-align: center; padding: 20px; margin-top: 20px">
      <el-button plain @click="handleCancel" style="margin-right: 12px; width: 180px">取消</el-button>
      <el-button plain @click="handleSaveAsDraft" style="margin-right: 12px; width: 180px" v-if="courseForm.courseFlag == 2 || courseForm.courseFlag !== '0'">保存草稿</el-button>
      <el-button @click="handleSave" style="width: 180px">保存</el-button>
    </div>

    <!-- 流程新增 -->
    <el-dialog title="添加流程" :visible.sync="processDialogVisible" width="500px" :close-on-click-modal="false" :show-close="true" @close="closeProcess" append-to-body>
      <el-form ref="processForm" :model="processForm" label-width="80px" class="process-form">
        <el-form-item label="组件:" prop="processType" :rules="[{ required: true, message: '请选择组件', trigger: 'change' }]">
          <el-select v-model="processForm.processType" placeholder="选择组件">
            <el-option v-for="item in processTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeProcess">取消</el-button>
        <el-button type="primary" @click="saveProcess">保存</el-button>
      </div>
    </el-dialog>

    <!-- 添加视频选择弹框 -->
    <el-dialog title="添加视频" :visible.sync="videoDialogVisible" width="50%" class="video-selection-dialog" :before-close="handleVideoDialogClose">
      <div class="search-container">
        <div class="search-input">
          <el-input placeholder="请输入视频名称搜索" v-model="videoPageParams.videoName" size="small"></el-input>
        </div>
        <div class="search-btns">
          <el-button size="small" @click="resetVideoSelection">重置</el-button>
          <el-button size="small" type="primary" @click="handleVideoSearch">搜索</el-button>
        </div>
      </div>
      <el-table ref="table" :data="videoList" @select="handleSelectionChange" @select-all="handleSelectAll" size="small" style="width: 100%" v-loading="tableLoading" row-key="id">
        <el-table-column type="selection" width="60" align="center" :reserve-selection="true"></el-table-column>
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column prop="id" label="视频ID" width="" align="center"></el-table-column>
        <el-table-column prop="videoName" label="视频名称" align="center"></el-table-column>
        <el-table-column prop="courseVersionNodeName" label="版本" align="center"></el-table-column>
        <el-table-column prop="coursePeriodNodeName" label="学段" align="center"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="videoPageParams.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="videoPageParams.pageSize"
          layout="prev, pager, next, jumper"
          :total="videoTotal"
          small
        ></el-pagination>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="videoDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmVideoSelection">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选择知识点" :visible.sync="knowledgeDialogVisible" width="70%" :before-close="dialogBeforeClose" :close-on-click-modal="false">
      <div>
        <KnowledgePoint
          ref="knowledgePoint"
          v-if="knowledgeDialogVisible"
          :disciplineId="courseNodeData.courseSubjectNodeId"
          :curriculumId="curriculumId"
          :knowledgePointIds="knowledgeIds"
          :type = this.type
        ></KnowledgePoint>
      </div>
      <div slot="footer">
        <el-button @click="dialogBeforeClose">取 消</el-button>
        <el-button type="primary" @click="confirmKonwLedge">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import courseManagementAPI from '@/api/mathApi/courseManagementAPI';
import superReaderFixedImageComponent from '@/views/chineseCourseManagement/addSuperReaderComponents/superReaderFixedImageComponent.vue'
import superReaderFixedManyImageComponent from '@/views/chineseCourseManagement/addSuperReaderComponents/superReaderFixedManyImageComponent.vue'
import superReaderVideoComponent from '@/views/chineseCourseManagement/addSuperReaderComponents/superReaderVideoComponent.vue'
import superReaderBeforText from '@/views/chineseCourseManagement/addSuperReaderComponents/superReaderBeforText.vue'
import superReaderMiddleText from '@/views/chineseCourseManagement/addSuperReaderComponents/superReaderMiddleText.vue'
import superReaderAfterText from '@/views/chineseCourseManagement/addSuperReaderComponents/superReaderAfterText.vue'
import superReaderLearningReport from '@/views/chineseCourseManagement/addSuperReaderComponents/superReaderLearningReport.vue'
import KnowledgePoint from '@/views/chineseCourseManagement/components/knowledgePoint.vue';
import { addCourse, getVideoListAPI, saveDraftAPI } from '@/api/superReaderAPI/courseManagement';
export default {
  name: 'CourseForm',
  components: {
    superReaderFixedImageComponent,
    superReaderFixedManyImageComponent,
    superReaderVideoComponent,
    superReaderBeforText,
    superReaderMiddleText,
    superReaderAfterText,
    superReaderLearningReport,
    KnowledgePoint
  },
  computed: {},
  data() {
    return {
      type: '1',
      sumMinute: 0,
      knowledgeIds: [],
      knowledgeDialogVisible: false,
      courseForm: {
        courseFlag: '',
        courseName: '',
        courseType: '',
        courseIsLockVideo: '0',
        courseSort: '', // 添加课程排序字段
        courseLockType: '0',
        courseProcessUnlockVideoCoList: [], //解锁视频
        courseKnowledgeCorrelationList: [], //知识点关联
        courseProcessConfigCoList: [] // 流程配置数组
      },
      tableLoading: false,
      courseType: '',
      courseTypeOptions: [
        { value: '1', label: '正课' },
        { value: '0', label: '试课' }
      ],
      rules: {
        courseName: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
        courseType: [{ required: true, message: '请选择课程类型', trigger: 'change' }],
        courseSort: [{ required: true, message: '请输入课程排序号', trigger: 'blur' }]
      },
      // 流程弹框
      processDialogVisible: false,
      // 流程表单
      processForm: {
        processType: ''
      },
      // 流程类型 1 固定图片 2 视频播放 3学前测试 4课后作业 5学后测试
      processTypeOptions: [
        { label: '固定图片', value: 1 },
        { label: '固定多图', value: 6 },
        { label: '视频播放', value: 2 },
        { label: '学前测试', value: 3 },
        { label: '学中测试', value: 5 },
        { label: '学后测试', value: 4 },
        { label: '学情报告', value: 7 },
      ],
      // showFixedImage: true, // 添加控制固定图片显示的数据
      processIdCounter: 0, // 用于生成唯一的 id

      // 所有选择视频内容
      videoDialogVisible: false, // 控制视频选择弹框的显示
      videoSearchKeyword: '', // 视频搜索关键词
      videoLoading: false, // 视频加载中
      videoList: [], // 视频列表
      videoPageParams: {
        pageNum: 1,
        pageSize: 10,
        videoName: ''
      },
      videoTotal: 0, // 视频总数
      selectedVideos: [], // 选中的视频
      curriculumId: '',
      courseKnowledgeCorrelationListOld: [], // 知识点关联原数据
      courseProcessUnlockVideoCoListOld: [], // 解锁视频原数据
      courseNodeData: {},
      sortedItems: [],
      isEdit: false,
      isAllSelected: false,
      loading: false,
      // 删除的课程流程节点
      deleteList: []
    };
  },
  created() {
    let courseNodeData = JSON.parse(sessionStorage.getItem('courseNodeData'));
    this.courseNodeData = courseNodeData;
    this.curriculumId = courseNodeData.curriculumId ? courseNodeData.curriculumId : '';
    let courseData = JSON.parse(sessionStorage.getItem('courseData'));
    if (courseData) {
      this.isEdit = true; // 标记为编辑状态
      this.courseForm = courseData;
      let newArr = JSON.parse(JSON.stringify(courseData.courseKnowledgeCorrelationVoList));
      this.courseKnowledgeCorrelationListOld = JSON.parse(JSON.stringify(newArr)); // 知识点关联原数据
      newArr.forEach((item) => (item.id = item.knowledgeId));
      this.knowledgeIds = newArr;
      this.courseForm.courseKnowledgeCorrelationList = newArr;
      this.courseProcessUnlockVideoCoListOld = JSON.parse(JSON.stringify(courseData.courseProcessUnlockVideoCoList)); // 解锁视频原数据
      this.courseType = this.courseForm.courseType
      this.courseForm.courseProcessConfigCoList = courseData.courseProcessConfigVoList;
      this.selectedVideos = this.courseForm.courseProcessUnlockVideoVoList;
    }
  },
  watch: {
    'courseForm.courseProcessConfigCoList': {
      handler(val) {
        let arr = JSON.parse(JSON.stringify(val));
        let newArr = this.courseForm.courseProcessConfigCoList && this.courseForm.courseProcessConfigCoList.map((i) => (i.stageDuration || 0) * 1);
        let newArr1 = arr && arr.map((i) => (i.solveStageDuration || 0) * 1);
        let sum1 = 0;
        let sum2 = 0;
        if (newArr && newArr.length > 0) {
          sum1 = newArr.reduce((a, b) => a * 1 + b * 1, 0);
        } else {
          sum1 = 0;
        }
        if (newArr1 && newArr1.length > 0) {
          sum2 = newArr1.reduce((a, b) => a * 1 + b * 1, 0);
        } else {
          sum2 = 0;
        }
        this.sumMinute = sum1 + sum2;
        // this.sortedItems = arr.slice().sort((a, b) => a.sortsNum - b.sortsNum);
      },
      immediate: true,
      deep: true
    },
    // },
    $route(to, from) {
      console.log(to, from, '路由变化了，重新加载数据');
      // if (to.name == 'vedioManage') {
      //   this.resetForm();
      //   sessionStorage.removeItem('videoData');
      // } else {
      //   this.videoForm = JSON.parse(sessionStorage.getItem('videoData'));
      // }
      // console.log(this.videoForm, 'this.videoForm');
      // this.curriculumId = this.videoForm.curriculumId;
      // if (this.videoForm && this.videoForm.id) {
      //   this.id = this.videoForm.id;
      //   this.getVideoDetail(this.videoForm.id);
      // }
    }
  },
  beforeDestroy(to, from, next) {
    sessionStorage.removeItem('courseData');
    sessionStorage.removeItem('courseNodeData');
  },
  methods: {
    changeCourseType() {
      this.courseForm.courseType = this.courseType;
    },
    confirmKonwLedge() {
      let data = this.$refs.knowledgePoint.multipleSelection;
      data.forEach((item) => {
        item.knowledgeId = item.id;
      });
      this.courseForm.courseKnowledgeCorrelationList = data;
      this.knowledgeIds = [...data];
      this.knowledgeDialogVisible = false;
    },
    dialogBeforeClose() {
      this.knowledgeDialogVisible = false;
    },
    handleKnowledgeScopeChange() {
      this.knowledgeDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.knowledgePoint) {
          // 重置选中状态
          this.$refs.knowledgePoint.multipleSelection = [];
          // 设置当前已选中的知识点
          this.$refs.knowledgePoint.multipleSelection = [...this.courseForm.courseKnowledgeCorrelationList];
        }
      });
    },
    // 排序校验
    validateIntegerInput() {
      // 移除非数字字符
      if (this.courseForm.courseSort !== '' && this.courseForm.courseSort !== null) {
        let numStr = this.courseForm.courseSort.toString().replace(/[^\d]/g, '');
        // 移除前导零，除非是单个0
        if (numStr.length > 1 && numStr.startsWith('0')) {
          numStr = numStr.substring(1);
        }
        if (numStr == '') {
          this.courseForm.courseSort = null;
        } else {
          let num = parseInt(numStr, 10);
          // 确保不超过9999999
          if (num > 9999999) {
            num = 9999999;
          }
          this.courseForm.courseSort = num;
        }
      }
    },
    // 取消
    handleCancel() {
      // 添加取消提示
      this.$confirm('确定取消吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.courseForm = {
          courseName: '',
          courseType: '',
          courseIsLockVideo: '0',
          courseSort: '', // 添加课程排序字段
          courseLockType: '0',
          // 流程配置数组
          courseProcessConfigCoList: []
        };
        this.knowledgeIds = [];
        // this.$router.push('/course/courseManagementList');
        this.$router.go(-1); // 返回上一页
      });
    },

    // 保存草稿
    handleSaveAsDraft() {
      // if (this.loading) return; // 如果正在加载中，直接返回
      this.loading = true;
      // 处理并拿到提交数据
      const formAllData = this.handleSubmitData();

      saveDraftAPI(formAllData)
        .then((res) => {
          this.$message.success('保存成功');
          this.$router.push('/_chinese_reader/superReaderCourseManagement');
        })
        .catch((error) => {
          console.log(222);
          console.error('保存失败', error);
        })
        .finally(() => {
          this.loading = false; // 无论成功失败都关闭loading
        });
    },
    // 校验所有流程组件
    async validateProcessComponents() {
      try {
        // 获取所有流程组件
        const processComponents = this.$children.filter((child) =>
          ['superReaderFixedImageComponent', 'superReaderFixedManyImageComponent', 'superReaderVideoComponent', 'superReaderBeforText', 'superReaderMiddleText', 'superReaderAfterText', 'superReaderLearningReport'].includes(child.$options.name)
        );

        // 校验所有组件
        for (const component of processComponents) {
          if (component.validate) {
            await component.validate();
          }
        }
        return true;
      } catch (error) {
        return false;
      }
    },
    // 处理提交数据
    handleSubmitData() {
      // 统一处理 深拷贝并处理ID
      const processItemList = (list, oldList, keyField) => {
        const processedList = JSON.parse(JSON.stringify(list));
        processedList.forEach((item) => {
          delete item.id;
          const oldItem = oldList.find((i) => i[keyField] === item[keyField]);
          if (oldItem) {
            item.id = oldItem.id;
          }
        });
        return processedList;
      };
      // 统一处理 获取被删除的项目
      const getDeletedItems = (oldList, newList, keyField) => {
        const deletedItems = oldList.filter((oldItem) => !newList.some((newItem) => newItem[keyField] === oldItem[keyField]));
        return deletedItems.map((item) => ({ ...item, isDeleted: 1 }));
      };
      // 开始执行！！！
      let { courseProcessConfigCoList, courseKnowledgeCorrelationList, courseProcessUnlockVideoCoList } = this.courseForm;
      // 深拷贝并处理ID
      //  -处理知识点关联列表
      const processedKnowledgeList = processItemList(courseKnowledgeCorrelationList, this.courseKnowledgeCorrelationListOld, 'knowledgeId');
      //  -处理视频解锁列表
      const processedVideoList = processItemList(courseProcessUnlockVideoCoList, this.courseProcessUnlockVideoCoListOld, 'videoId');

      // 获取被删除的项目
      //  -知识点关联列表
      const deletedKnowledgeList = getDeletedItems(this.courseKnowledgeCorrelationListOld, processedKnowledgeList, 'knowledgeId');
      //  -视频解锁列表
      const deletedVideoList = getDeletedItems(this.courseProcessUnlockVideoCoListOld, processedVideoList, 'videoId');

      // console.log('%cdeletedVideoList : ', 'color:#fff;background:#000', {
      //   courseKnowledgeCorrelationList: [...processedKnowledgeList, ...deletedKnowledgeList],
      //   courseProcessUnlockVideoCoList: [...processedVideoList, ...deletedVideoList],
      //   courseProcessConfigCoList: [...courseProcessConfigCoList, ...this.deleteList]
      // });

      // 课程基本信息
      const courseInfo = {
        curriculumId: this.courseNodeData.curriculumId,
        courseVersionNodeId: this.courseNodeData.courseVersionNodeId,
        coursePeriodNodeId: this.courseNodeData.coursePeriodNodeId,
        courseSubjectNodeId: this.courseNodeData.courseSubjectNodeId
      };
      // 组装最终数据
      return {
        ...this.courseForm,
        ...courseInfo,
        courseKnowledgeCorrelationList: [...processedKnowledgeList, ...deletedKnowledgeList],
        courseProcessUnlockVideoCoList: [...processedVideoList, ...deletedVideoList],
        // 将删除的流程节点恢复到 恢复回去// 后端需要处理
        courseProcessConfigCoList: [...courseProcessConfigCoList, ...this.deleteList]
      };
    },
    // 保存
    handleSave() {
      this.$refs.courseForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          try {
            // 校验所有流程组件
            const processComponentsValid = await this.validateProcessComponents();
            if (!processComponentsValid) {
              this.$message.error('请完善流程组件的信息');
              return;
            }
            if (this.courseForm.courseProcessConfigCoList.length == 0) {
              this.$message.error('请添加流程组件');
              return;
            }
            // 检查学情报告组件的依赖条件
            const hasLearningReport = this.courseForm.courseProcessConfigCoList.some(item => item.processType == 7);
            if (hasLearningReport) {
              const hasBeforeTest = this.courseForm.courseProcessConfigCoList.some(item => item.processType == 3);
              const hasAfterTest = this.courseForm.courseProcessConfigCoList.some(item => item.processType == 4);

              // 只有当同时存在学前测试和学后测试时才能添加学情报告
              if (!hasBeforeTest || !hasAfterTest) {
                this.$message.error('添加学情报告组件时，必须同时存在学前测试和学后测试组件');
                return;
              }
            }
            if(this.courseType == 1) {
              const hasLearningReport = this.courseForm.courseProcessConfigCoList.some(item => item.processType == 7);
              if (hasLearningReport) {
                this.$message.error('正课状态下不可有学情报告组件，请删除');
                return;
              }
            } else {
              // 对于试课，检查学情报告组件数量
              const learningReportCount = this.courseForm.courseProcessConfigCoList.filter(item => item.processType == 7).length;
              if (learningReportCount > 1) {
                this.$message.error('学情报告组件最多只能添加一个');
                return;
              }
            }
            // 检查sortsNum是否有重复值
            const sortsNums = this.courseForm.courseProcessConfigCoList.map(item => item.sortsNum);
            const hasDuplicate = sortsNums.some((item, index) => sortsNums.indexOf(item) !== index);
            if (hasDuplicate) {
              this.$message.error('课程组件流程排序重复请修改');
              return;
            }
            // 校验学前、学中、学后测试必须添加题目
            const testComponents = this.courseForm.courseProcessConfigCoList.filter(item =>
              item.processType == 3 || item.processType == 4 || item.processType == 5);

            for (let component of testComponents) {
              if (!component.courseProcessStudyTestCoList || component.courseProcessStudyTestCoList.length === 0) {
                let componentTypeName = '';
                switch(component.processType) {
                  case 3:
                    componentTypeName = '学前测试';
                    break;
                  case 4:
                    componentTypeName = '学后测试';
                    break;
                  case 5:
                    componentTypeName = '学中测试';
                    break;
                }
                this.$message.error(`请为${componentTypeName}组件添加题目`);
                return;
              }
            }
            // if (
            //   this.courseForm.courseProcessConfigCoList.some(
            //     (item) => (item.processType == 3 || item.processType == 4) && item.knowledgeScope && item.knowledgeScope.includes('1')
            //   )
            // ) {
            //   if (this.courseForm.courseKnowledgeCorrelationList.length == 0) {
            //     this.$message.error('请选择关联知识点');
            //     return;
            //   }
            // }
            // 校验学前测试和课后作业和学后测试的题型
            // const answerAndHomeworkComponents = this.courseForm.courseProcessConfigCoList.filter(
            //   (item) => item.processType == '3' || item.processType == '4' || item.processType == '5'
            // );
            // for (const component of answerAndHomeworkComponents) {
            //   if (!component.processQuestionTypeCoList || component.processQuestionTypeCoList.length === 0) {
            //     this.$message.error(`请为${component.processType == 3 ? '学前测试' : component.processType == 5 ? '学后测试' : '课后作业'}添加题型`);
            //     return;
            //   }
            //   for (const questionType of component.processQuestionTypeCoList) {
            //     if (!questionType.sortsNum) {
            //       this.$message.error('请填写题型排序');
            //       return;
            //     }
            //     if (!questionType.questionNum) {
            //       this.$message.error('请填写题型题目数量');
            //       return;
            //     }
            //     if (questionType.questionGrade === undefined || questionType.questionGrade === null || questionType.questionGrade === '') {
            //       this.$message.error('请填写题型每题分值');
            //       return;
            //     }
            //   }
            // }
            const hasType3 = this.courseForm.courseProcessConfigCoList.some((item) => item.processType == 3);
            console.log('1111111111111', this.courseForm.courseProcessConfigCoList);
            console.log('hasType3', hasType3); // false
            const hasType5 = this.courseForm.courseProcessConfigCoList.some((item) => item.processType == 4);
            console.log('hasType5', hasType5); // false
            if ((hasType3 && !hasType5) || (!hasType3 && hasType5)) {
              this.$message.warning('学前测试和学后测试必须同时存在');
              return;
            }
            // 处理并拿到提交数据
            const formAllData = this.handleSubmitData();
            console.log(formAllData, '表单数据');
            // return;
            await addCourse(formAllData);
            this.$message.success('保存成功');
            this.$router.push('/_chinese_reader/superReaderCourseManagement');
          } catch (error) {
            console.log(1111);
            this.$message.error('保存失败');
          } finally {
            this.loading = false;
          }
        } else {
          this.$message.error('请填写必填项');
          this.loading = false;
        }
      });
    },

    // 打开添加流程弹框
    handleAddProcess() {
      // 先验证课程名称和课程类型是否已填写
      this.$refs.courseForm.validateField(['courseName', 'courseType'], (errorMessage) => {
        if (errorMessage) {
          this.$message.warning('请先填写课程名称和课程类型');
          return;
        }

        // 额外检查：确保课程名称和课程类型都有实际值
        if (!this.courseForm.courseName || !this.courseForm.courseType) {
          this.$message.warning('请先填写课程名称和课程类型');
          return;
        }

        // 如果基本信息已填写，则继续执行原有逻辑
        if (this.$refs.processForm) {
          this.$refs.processForm.resetFields();
        }
        this.processForm.type = '';

        // 根据课程类型过滤流程选项
        if (this.courseForm.courseType === '1') {
          // 正式课不显示学情报告（value: 7）
          this.processTypeOptions = [
            { label: '固定图片', value: 1 },
            { label: '固定多图', value: 6 },
            { label: '视频播放', value: 2 },
            { label: '学前测试', value: 3 },
            { label: '学中测试', value: 5 },
            { label: '学后测试', value: 4 }
          ];
        } else {
          // 试课显示所有选项
          this.processTypeOptions = [
            { label: '固定图片', value: 1 },
            { label: '固定多图', value: 6 },
            { label: '视频播放', value: 2 },
            { label: '学前测试', value: 3 },
            { label: '学中测试', value: 5 },
            { label: '学后测试', value: 4 },
            { label: '学情报告', value: 7 }
          ];
        }
        this.processDialogVisible = true;
      });
    },
    handleVideoSearch() {
      this.videoPageParams.pageNum = 1;
      this.getVideoList();
    },
    // 将数据推入this.courseForm.courseProcessConfigCoList
    updateProcessData(formData, index) {
      console.log('formData', formData);
      console.log('index', index);
      Object.assign(this.courseForm.courseProcessConfigCoList[index], formData);
      // 当更新的是学前测组件(3)时
      if (formData.processType == 3) {
        // 提取知识点范围数据
        const knowledgeScope = formData.knowledgeScope;

        // 遍历所有组件，找到processType==5的组件
        this.courseForm.courseProcessConfigCoList.forEach((item, itemIndex) => {
          console.log('item', item);
          if (item.processType == 4) {
            // 仅更新knowledgeScope字段，保留其他数据不变
            this.courseForm.courseProcessConfigCoList[itemIndex].knowledgeScope = knowledgeScope;
          }
        });
      }
      if (this.courseForm.courseProcessConfigCoList.length > 0) {
        this.courseForm.courseProcessConfigCoList.forEach((item) => {
          if (item.processType == 3 && !this.isEdit) {
            if (item.courseProcessVideoCoList.length > 0) {
              item.courseProcessVideoCoList.forEach((videoItem) => {
                videoItem.fileId = videoItem.vid ? videoItem.vid : videoItem.id;
                videoItem.videoId = videoItem.videoId ? videoItem.videoId : videoItem.id;
                videoItem.videoName = videoItem.videoName ? videoItem.videoName : videoItem.fileName;
              });
            }
          }
        });
      }
      // console.log(this.$children, '=================');
      // formData = JSON.parse(JSON.stringify(this.courseForm.courseProcessConfigCoList[index]));
      // this.$refs[`component${index}`].formData = formData;
    },
    closeProcess() {
      // this.processTypeOptions = [];

      this.processDialogVisible = false;
    },
    // 保存流程
    saveProcess() {
      if (this.isEdit) {
        if (this.courseForm.courseProcessConfigCoList.length == 0) {
          this.processIdCounter = 1;
        } else {
          let index = this.courseForm.courseProcessConfigCoList[this.courseForm.courseProcessConfigCoList.length - 1].sortsNum;
          this.processIdCounter = index + 1;
        }
      } else {
        this.processIdCounter = 1;
      }
      this.$refs.processForm.validate((valid) => {
        if (valid) {
          const selectedType = parseInt(this.processForm.processType);

          //  判断是否已有该组件类型（只对 3 和 5 做唯一性限制）
          const alreadyExists = this.courseForm.courseProcessConfigCoList.some((item) => item.processType == selectedType);

          if (selectedType === 3 && alreadyExists) {
            this.$message.warning('只能添加一个学前测试阶段');
            return;
          }
          if (selectedType === 4 && alreadyExists) {
            this.$message.warning('只能添加一个学后测试阶段');
            return;
          }
          // 创建新的流程对象，保存数值类型
          let newProcess = {
            isDeleted: 0, // 新增流程时，默认未删除
            processType: parseInt(this.processForm.processType),
            sortsNum: `${this.processIdCounter++}`
          };
          if (newProcess.processType == 1) {
            newProcess = {
              ...newProcess,
              stageDuration: '',
              stageName: '',
              stageRemind: '1',
              imageCover: ''
            };
          } else if(newProcess.processType == 6){
            newProcess = {
              ...newProcess,
              stageDuration: '',
              stageName: '',
              stageRemind: '1',
              courseProcessFixedImgCoList:[]
            };
          } else if (newProcess.processType == 2) {
            newProcess = {
              ...newProcess,
              stageDuration: '',
              stageName: '',
              stageRemind: '1',
              knowledgeExpound: 0,
              courseProcessVideoCoList: []
            };
          } else if (newProcess.processType == 3) {
            newProcess = {
              ...newProcess,
              stageDuration: '',
              stageName: '',
              stageRemind: '1',
              isPk: '',
              knowledgeScope: '',
              solveStageDuration: '',
              solveStageName: '',
              solveStageRemind: '1',
              courseProcessStudyTestCoList: []
            };
          } else if(newProcess.processType == 5) {
            newProcess = {
              ...newProcess,
              stageDuration: '',
              stageName: '',
              stageRemind: '1',
              isPk: '',
              knowledgeScope: '',
              solveStageDuration: '',
              solveStageName: '',
              solveStageRemind: '1',
              courseProcessStudyTestCoList: []
            };
          } else if (newProcess.processType == 4) {
            newProcess = {
              ...newProcess,
              stageDuration: '',
              stageName: '',
              stageRemind: '1',
              isPk: '',
              knowledgeScope: '',
              solveStageDuration: '',
              solveStageName: '',
              solveStageRemind: '1',
              courseProcessStudyTestCoList: []
            };
          } else if (newProcess.processType == 7) {
            const beforeTest = this.courseForm.courseProcessConfigCoList.find((item) => item.processType == 3);
            const knowledgeScope = beforeTest ? (Array.isArray(beforeTest.knowledgeScope) ? [...beforeTest.knowledgeScope] : beforeTest.knowledgeScope || '') : '';
            newProcess = {
              ...newProcess,
              stageDuration: '',
              stageName: '学情报告',
              sortsNum: 9999,
              stageRemind: '1',
              knowledgeScope: knowledgeScope,
              processQuestionTypeCoList: []
            };
          }
          // 添加到流程列表中
          // 不要让后续的排序逻辑覆盖学情报告的特殊排序
          // 添加到流程列表中
          this.courseForm.courseProcessConfigCoList.push(newProcess);

          // 重新处理排序，但保持学情报告的9999排序
          this.courseForm.courseProcessConfigCoList.forEach((item, index) => {
            // 如果不是学情报告组件，则按顺序排序
            if (item.processType !== 7) {
              // 计算非学情报告组件的新排序号
              const nonReportItems = this.courseForm.courseProcessConfigCoList.filter(i => i.processType !== 7);
              const itemIndex = nonReportItems.findIndex(i => i === item);
              item.sortsNum = itemIndex + 1;
            }
          });
          // 关闭对话框并重置表单
          this.processDialogVisible = false;
          this.processForm.processType = '';
          this.$message.success('添加流程成功');
        } else {
          this.$message.error('请选择组件类型');
          return false;
        }
      });
    },
    // 处理删除流程
    handleDeleteProcess(index) {
      this.$confirm('确定要删除该流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          console.log('删除流程:', this.courseForm.courseProcessConfigCoList[index]);
          const item = this.courseForm.courseProcessConfigCoList[index];
          // 从数组中删除对应索引的流程
          if (item.id) {
            // 如果流程已保存到数据库中，则删除时需要标记为已删除
            this.deleteList.push({ ...item, isDeleted: 1 });
          }
          this.courseForm.courseProcessConfigCoList.splice(index, 1);
          this.$forceUpdate();
          console.log('删除后的流程列表:', JSON.parse(JSON.stringify(this.courseForm.courseProcessConfigCoList)));
          this.$message({
            type: 'success',
            message: '删除成功'
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },

    // 打开视频选择弹框
    openVideoDialog() {
      this.videoDialogVisible = true;
      this.videoPageParams.pageNum = 1;
      this.videoPageParams.videoName = '';
      // 初始化选中数据，使用深拷贝避免引用问题
      this.selectedVideos = this.courseForm.courseProcessUnlockVideoCoList ? JSON.parse(JSON.stringify(this.courseForm.courseProcessUnlockVideoCoList)) : [];
      this.getVideoList();
    },

    // 重置视频选择
    resetVideoSelection() {
      this.selectedVideos = [];
      this.isAllSelected = false;
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }
      this.videoPageParams = {
        pageNum: 1,
        pageSize: 10,
        videoName: ''
      };
      this.getVideoList();
    },

    // 处理选择变化
    handleSelectionChange(selection, row) {
      // 找到当前行在selectedVideos中的索引
      const index = this.selectedVideos.findIndex((item) => item.id === row.id || item.videoId === row.id || item.id === row.videoId);

      if (selection.includes(row)) {
        // 如果是选中操作且数据不存在，则添加
        if (index === -1) {
          this.selectedVideos.push(row);
        }
      } else {
        // 如果是取消选中操作，则移除
        if (index !== -1) {
          this.selectedVideos.splice(index, 1);
        }
      }
    },

    // 处理全选
    handleSelectAll(selection) {
      if (selection.length > 0) {
        // 全选时，将当前页面的所有数据添加到选中列表
        selection.forEach((item) => {
          const exists = this.selectedVideos.some((v) => v.id === item.id || v.videoId === item.id || v.id === item.videoId);
          if (!exists) {
            this.selectedVideos.push(item);
          }
        });
      } else {
        // 取消全选时，从选中列表中移除当前页面的所有数据
        this.selectedVideos = this.selectedVideos.filter((item) => !this.videoList.some((v) => v.id === item.id || v.videoId === item.id || v.id === item.videoId));
      }
    },

    // 获取视频列表
    getVideoList() {
      this.tableLoading = true;
      getVideoListAPI({
        pageNum: this.videoPageParams.pageNum,
        pageSize: this.videoPageParams.pageSize,
        courseSubjectNodeId: this.courseNodeData.courseSubjectNodeId,
        courseVersionNodeId: this.courseNodeData.courseVersionNodeId,
        videoName: this.videoPageParams.videoName
      })
        .then((res) => {
          this.videoList = res.data.data;
          this.videoTotal = +res.data.totalItems;
          this.tableLoading = false;
          this.$nextTick(() => {
            this.updateTableSelection();
          });
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 确认视频选择
    confirmVideoSelection() {
      // 深拷贝选中数据，避免引用问题
      this.courseForm.courseProcessUnlockVideoCoList = JSON.parse(JSON.stringify(this.selectedVideos));
      // 处理视频数据格式
      if (this.courseForm.courseProcessUnlockVideoCoList.length > 0) {
        this.courseForm.courseProcessUnlockVideoCoList.forEach((videoItem) => {
          videoItem.fileId = videoItem.vid || videoItem.fileId || '';
          videoItem.videoId = videoItem.videoId || videoItem.id;
          videoItem.videoName = videoItem.videoName || videoItem.fileName;
        });
      }
      this.videoDialogVisible = false;
    },

    // 处理分页大小变化
    handleSizeChange(val) {
      this.videoPageParams.pageNum = 1;
      this.videoPageParams.pageSize = val;
      this.getVideoList();
    },

    // 处理当前页变化
    async handleCurrentChange(val) {
      this.videoPageParams.pageNum = val;
      await this.getVideoList();
      this.$nextTick(() => {
        this.updateTableSelection();
      });
    },
    // 更新表格选择状态
    updateTableSelection() {
      if (!this.$refs.table) return;

      this.$refs.table.clearSelection();
      this.$nextTick(() => {
        this.videoList.forEach((row) => {
          const isSelected = this.selectedVideos.some((v) => v.id === row.id || v.videoId === row.id || v.id === row.videoId);
          if (isSelected) {
            this.$refs.table.toggleRowSelection(row, true);
          }
        });
      });
    },
    // 处理视频对话框关闭
    handleVideoDialogClose() {
      // 关闭时重置选中状态
      this.selectedVideos = [];
      this.videoDialogVisible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.form-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  // border-bottom: 1px solid #e8e8e8;
}
.search-container {
  padding: 0 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
}

.search-input {
  width: 220px;
}

.search-btns {
  display: flex;
  gap: 8px;
}

.el-form-item {
  margin-bottom: 22px;
}

.el-select {
  width: 100%;
}

.knowledge-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 240px;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  line-height: 32px;
  padding: 0 8px 0 15px;
  box-sizing: border-box;
  cursor: pointer;
  i {
    color: #c0c4cc;
    font-size: 14px;
  }
}

.process-form {
  padding: 0 20px;
}

.process-form /deep/ .el-select {
  width: 100%;
}

.dialog-footer {
  text-align: center;
  padding-bottom: 20px;
}

.video-selection-dialog .el-dialog__body {
  padding: 10px 20px;
}

.selected-count {
  display: inline-block;
  background-color: #fff;
  color: #409eff;
  border-radius: 10px;
  padding: 0 8px;
  font-size: 12px;
  margin-left: 5px;
}

.knowledge-item {
  cursor: pointer;
}
</style>
