import DxSelect from './DxSelect.vue'
import DxOption from './DxOption.vue'
import DxOptionGroup from './DxOptionGroup.vue'

// 为组件提供 install 安装方法，供按需引入
DxSelect.install = function (Vue) {
  Vue.component(DxSelect.name, DxSelect)
}

DxOption.install = function (Vue) {
  Vue.component(DxOption.name, DxOption)
}

DxOptionGroup.install = function (Vue) {
  Vue.component(DxOptionGroup.name, DxOptionGroup)
}

// 导出组件
export { DxSelect, DxOption, DxOptionGroup }

// 默认导出组件
export default {
  DxSelect,
  DxOption,
  DxOptionGroup,
  install(Vue) {
    Vue.component(DxSelect.name, DxSelect)
    Vue.component(DxOption.name, DxOption)
    Vue.component(DxOptionGroup.name, DxOptionGroup)
  }
}
