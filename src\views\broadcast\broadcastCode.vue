<template>
  <div class="app-container">
      <div class="demo-image__placeholder">
        <div class="block" align="center">
          <el-image style="width: 400px; height: 400px" :src="tableData" ></el-image>
        </div>
      </div>
  </div>
</template>

<script>
import broadcastApi from '@/api/broadcast'
import {
  pageParamNames
} from "@/utils/constants";
export default {
  data() {
    return {
      tableLoading: false,
      tableData: "" //表格数据
    }
  },
  created() {
    this.fetchData();
  },
  methods: {
    fetchData01() {
      this.fetchData();
    },
    // 查询+搜索课程列表
    fetchData() {
      const that = this
      that.tableLoading = true
      broadcastApi.getFunReviewCode().then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
      })
    },
  }
}
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.mt22 {
  margin-top: 22px;
}

@media (max-width:767px) {
  .el-message-box{
    width: 80%!important;
  }
}
</style>
