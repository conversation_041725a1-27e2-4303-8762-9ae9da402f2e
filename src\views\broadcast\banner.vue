<template>
  <div class="app-container">
    <!-- 搜索 -->
    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 10px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border >
        <el-table-column prop="id" label="编号" sortable/>
        <el-table-column prop="url" label="图片" align="center">
          <template slot-scope="scope">
            <img width="60px" :src="aliUrl+scope.row.url" alt />
          </template>
        </el-table-column>
        <el-table-column prop="id" label="操作" sortable >
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-edit-outline" @click="deleteData(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <!--    <el-col :span="20">-->
    <!--      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"-->
    <!--                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />-->
    <!--    </el-col>-->

    <!-- 编辑或者添加弹窗 -->
    <el-dialog :title="addOrUpdate?'添加banner图':'编辑banner图'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false"
               @close="close">
      <el-form :ref="addOrUpdate?'addBannerData':'updateActive'" :rules="rules" :model="addOrUpdate?addBannerData:updateActive"
               label-position="left" label-width="120px" style="width: 100%;">
        <el-form-item label="id" prop="categoryName" v-show="!addOrUpdate">
          <el-col :xs="24" :lg="18">
            <el-input v-if="!addOrUpdate" v-model="updateActive.id" />
          </el-col>
        </el-form-item>
        <el-form-item label="图片" prop="icon">
          <el-col :xs="24" :lg="18">
            <el-upload ref="clearupload" v-loading="uploadLoading" list-type="picture-card" action=""
                       element-loading-text="图片上传中" :limit="1" :on-exceed="justPictureNum" :file-list="!addOrUpdate? fileDetailList: fileDetailList.name"
                       :http-request="uploadDetailHttp" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetail">
              <i class="el-icon-plus" />
            </el-upload>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addBannerData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateActive')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt>
    </el-dialog>
  </div>
</template>

<script>
import broadcastApi from '@/api/broadcast'
import {
  pageParamNames
} from "@/utils/constants";
import {
  ossPrClient
} from '@/api/alibaba'

export default {
  data() {
    return {
      // 分页
      // tablePage: {
      //   currentPage: 1,
      //   size: 10,
      //   totalPage: null,
      //   totalItems: null
      // },
      tableLoading: false,
      dataQuery: {
        categoryCode: '',
        categoryName: ''
      },
      activeType: [], // 活动类型
      // 分页
      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      addBannerData: {}, // 新增Banner
      updateActive: {}, // 修改数据
      rules: { // 表单提交规则
        url: [{
          required: true,
          message: '图片不能为空',
          trigger: 'blur'
        }]
      },

      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表

      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表

      dialogUploadVisible: false,
      dialogImageUrl: '', // 上传图片预览

      uploadLoading: false, // 上传图片加载按钮
      fullscreenLoading: false, // 保存啥的加载

      content: '',
      isUploadSuccess: true, // 是否上传成功
      isShowRelevance: true, // 新增或修改是否展示关联产品

      radio: '0', //单选框状态 值必须是字符串
      gettime: '' //获取当前时间
    }

  },
  created() {
    this.fetchData01();
    ossPrClient();
  },
  methods: {
    fetchData01() {
      // this.tablePage = {
      //   currentPage: 1,
      //   size: 10,
      //   totalPage: null,
      //   totalItems: null
      // }
      this.fetchData()
    },
    // 查询+搜索课程列表
    fetchData() {
      const that = this
      that.tableLoading = true
      broadcastApi.bannerList().then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data.data[name])))
      })
    },
    //添加操作
    clickAdd() {
      this.addBannerData = {
        'id': '',
        'url': ''
      }
      this.dialogVisible = true
      this.addOrUpdate = true
      this.fileDetailListPending=[]
      if (this.fileDetailList.length !== 0) {
        this.$refs.clearupload.clearFiles()
      }
      this.$nextTick(() => this.$refs['addBannerData'].clearValidate())
    },
    // 新增banner
    addActiveFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '新增banner图',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          broadcastApi.addBanner(that.addBannerData.url).then(() => {
            that.dialogVisible = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('新增banner图成功')
          }, s => {
            if (s === 'error') {
              loading.close()
              that.$message.success('新增banner图失败')
            }
          }).catch(err => {
            loading.close()
          })
        } else {
          console.log('error submit!!')
          //loading.close();
          return false
        }
      })
    },
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate = false
      broadcastApi.bannerUpdate(id).then(res => {
        that.updateActive = res.data.data
        if (that.updateActive.url !== null && that.updateActive.url.length > 1) {
          that.fileDetailList = [{
            url: that.aliUrl + that.updateActive.url
          }]
        } else {
          that.fileDetailList = []
        }
      }).catch(err => {

      })
    },
    // 状态改变事件
    change(radio) {
      if (radio == '1') {
        this.addBannerData.isEnable = 1
      } else {
        this.addBannerData.isEnable = 0
      }
    },
    // 修改banner图提交
    updateActiveFun(ele) {
      const that = this
      if (that.updateActive.id == null || that.updateActive.id === '' || that.updateActive.id === '') {
        that.$message.info('id不能为空')
        return false
      }
      if (that.updateActive.url == null || that.updateActive.url === '' || that.updateActive.url === '') {
        that.$message.info('图片不能为空')
      }

      const update = {
        id: that.updateActive.id,
        url: that.updateActive.url
      }

      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '修改banner图提交',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          broadcastApi.editBanner(update.id,update.url).then(() => {
            that.dialogVisible = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('修改banner成功')
          }).catch(err => {
            // 关闭提示弹框
            loading.close()
          })
        } else {
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },

    //删除
    deleteData(id) {
      const that = this
      this.$confirm('确定操作吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        broadcastApi.deleteBanner(id).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    // 删除上传图片
    handleRemove(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7,17) == parseInt(file.uid/1000)){
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },

    // 删除上传图片
    handleRemoveDetail(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7,17) == parseInt(file.uid/1000)){
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogUploadVisible = true
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`)
    },
    // 上传图片请求
    uploadPrHttp({ file }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.fileList.push({
                'uid': file.uid,
                'url': url
              })
            } else { // 新增上传图片
              that.fileList.push({
                name
              })
              that.addBannerData.imageListFilePath = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
      })
    },

    uploadDetailHttp({
      file
    }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.fileDetailList.push({
                'uid': file.uid,
                'url': url
              })
              that.updateActive.url = name
              that.uploadLoading = false
            } else { // 新增上传图片
              that.fileDetailList.push({
                name
              })
              that.addBannerData.url = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
          that.updateActive.url = name
        })
      })
    },

    // 分页
    // handleSizeChange(val) {
    //   this.tablePage.size = val;
    //   this.fetchData();
    // },
    // handleCurrentChange(val) {
    //   this.tablePage.currentPage = val;
    //   this.fetchData();
    // },
    // 关闭弹窗
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('../../icons/stop.png') no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

@media screen and (max-width: 767px) {
  .el-upload-list--picture-card .el-upload-list__item,
  .el-upload--picture-card{
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  .el-message-box{
    width: 80%!important;
  }
}

</style>
