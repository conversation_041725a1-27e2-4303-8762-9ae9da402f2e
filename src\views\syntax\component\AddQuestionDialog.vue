<template>
  <div>
    <el-dialog
      v-loading="loading"
      element-loading-background="transparent"
      :title="title"
      :visible.sync="dialogVisible"
      width="1200px"
      :close-on-click-modal="false"
      @close="resetData"
      class="common-form"
    >
      <el-form :rules="rules" ref="dataDialogForm" :model="dataDialog" :inline="true" label-width="100px">
        <!-- 试题分类 -->
        <el-form-item label="试题分类:" prop="questionAroundType">
          <el-select v-model="dataDialog.questionAroundType" filterable clearable placeholder="请选择阶段" size="medium" style="width: 790px" value-key="value">
            <el-option v-for="item in testCategoryList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <!-- 排序 -->
        <el-form-item label="排序:" prop="sortNum">
          <el-input-number v-model="dataDialog.sortNum" :min="1" :max="1000" :step="1" :precision="0" controls-position="right" style="width: 790px" />
        </el-form-item>

        <!-- 阶段 -->
        <el-form-item label="阶段:" prop="phase">
          <el-select v-model="dataDialog.phase" filterable clearable placeholder="请选择阶段" size="medium" style="width: 790px" value-key="value">
            <el-option v-for="item in mapPhaseType" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <!-- 题目类型 -->
        <el-form-item label="题目类型：" prop="questionType">
          <el-select v-model="dataDialog.questionType" filterable clearable placeholder="请选择题型" size="medium" style="width: 790px" value-key="value">
            <el-option v-for="item in mapQuestionTypeType" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <!-- 题目内容 -->
        <el-form-item label="题目：" prop="questionName">
          <el-input type="textarea" v-model="dataDialog.questionName" clearable placeholder="请输入题目" size="medium" style="width: 790px" />
        </el-form-item>

        <!-- 答案/选项表格 -->
        <el-form-item v-if="dataDialog.questionType == 2" label="答案：" prop="optionList">
          <el-button type="primary" size="medium" icon="el-icon-plus" style="width: 130px" @click="confirmOrigin">添加选项</el-button>
          <el-table :data="tableData2" border style="width: 790px; margin-top: 10px">
            <el-table-column label="空题答案" align="center">
              <template #default="{ row }">
                <el-input v-if="row.tableEditing" v-model="row.optionContent" />
                <span v-else>{{ row.optionContent }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="{ $index, row }">
                <el-button v-if="row.tableEditing" type="text" @click="confirmEdit($index)">确定</el-button>
                <el-button v-else type="text" @click="editRow($index)">编辑</el-button>
                <el-button type="text" style="color: red" @click="deleteCheck($index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item v-if="dataDialog.questionType == 1" label="试题选项：" prop="optionList">
          <div class="groupStyle">
            <el-button type="primary" size="medium" icon="el-icon-plus" style="width: 130px" @click="confirmOrigin">添加选项</el-button>
            <el-radio-group v-model="status" @input="handleRadioChange">
              <el-table :data="tableData1" border style="width: 790px; margin-top: 10px">
                <el-table-column label="勾选正确答案" align="center">
                  <template #default="scope">
                    <el-radio :label="scope.$index" v-model="dataDialog.answerChoise">{{ String.fromCharCode(65 + scope.$index) }}</el-radio>
                  </template>
                </el-table-column>
                <el-table-column label="选项内容" align="center">
                  <template #default="{ row }">
                    <el-input v-if="row.tableEditing" v-model="row.optionContent" />
                    <span v-else>{{ row.optionContent }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                  <template #default="{ $index, row }">
                    <el-button v-if="row.tableEditing" type="text" @click="confirmEdit($index)">确定</el-button>
                    <el-button v-else type="text" @click="editRow($index)">编辑</el-button>
                    <el-button type="text" style="color: red" @click="deleteCheck($index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-radio-group>
          </div>
        </el-form-item>

        <!-- 解析 -->
        <el-form-item label="解析：" prop="questionDescription">
          <el-input type="textarea" v-model="dataDialog.questionDescription" clearable placeholder="请输入解析" size="medium" style="width: 790px" />
        </el-form-item>

        <!-- 知识点 -->
        <el-form-item label="知识点：" prop="knowledgeIdList">
          <el-select
            :disabled="dataDialog.phase == ''"
            v-model="dataDialog.knowledgeIdList"
            multiple
            filterable
            clearable
            @change="handleKnowledge"
            placeholder="请先选择阶段"
            size="medium"
            style="width: 790px"
            value-key="id"
          >
            <el-option v-for="item in localKnowTypeListG" :key="item.id" :label="item.name" :value="item" />
          </el-select>
        </el-form-item>

        <!-- 考点 -->
        <el-form-item label="考点:" prop="examPoints">
          <el-select
            :disabled="dataDialog.knowledgeIdList.length == 0"
            v-model="dataDialog.examPoints"
            multiple
            filterable
            clearable
            placeholder="请先选择知识点"
            size="medium"
            style="width: 790px"
            value-key="id"
          >
            <el-option v-for="item in localMapExamPoints" :key="item.id" :label="item.topic" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>

      <el-row style="text-align: right; margin-top: 30px">
        <el-button type="info" icon="el-icon-close" @click="resetData">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="submitClick">确定</el-button>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
  import { thirdNodeAPI, addOrUpdate, optionsAPI } from '@/api/grammar/prePostTest';

  export default {
    props: {
      dialogVisible: Boolean,
      title: String,
      dataDialog: Object,
      mapPhaseType: Array,
      mapQuestionTypeType: Array,
      grammarTypeListG: Array,
      testCategoryList: Array,
      mapExamPoints: Array
    },
    data() {
      //   /#+/g 匹配所有连续的 # 片段。
      // 只要有某个片段长度为奇数，直接校验失败。
      // 只要有单独的 #（即分散的#），也校验失败。
      // 只有所有连续的 # 都是偶数，才通过。
      // 题目内容自定义校验方法
      let validateQuestionName = (rule, value, callback) => {
        if (!value) {
          return callback();
        }

        // 匹配所有连续的#片段
        const matches = value.match(/#+/g);
        if (!matches && this.dataDialog.questionType == 2) {
          console.log('matches-----', matches);
          return callback(new Error('请至少输入一对##'));
        }
        // console.log(matches, '================');
        if (matches && this.dataDialog.questionType == 2) {
          for (let str of matches) {
            if (str.length % 2 !== 0) {
              return callback(new Error('题目中"#"必须连续偶数个出现，不能有奇数个#'));
            }
          }
        }
        // if (!matches) return callback();

        // 检查是否有分散的#
        if (value.includes('#')) {
          if (/(^|[^#])#([^#]|$)/.test(value)) {
            return callback(new Error('题目中"#"不能单独出现，必须连续偶数个'));
          }
        }

        // 新增：填空题时，##对数与选项数一致
        if (this.dataDialog.questionType == 2) {
          // 统计##对数
          const pairCount = (value.match(/(##)/g) || []).length;
          // 总空数 = ##对数 + 2*####对数
          const totalBlank = pairCount;
          // 选项数
          const optionCount = this.tableData2.length;

          if (totalBlank !== optionCount) {
            return callback(new Error(`填空题中有（${totalBlank}）对"##"，与答案里面的（${optionCount}）个选项数不一致`));
          }
        }

        callback();
      };
      return {
        tableData1: [{ id: '', optionIsAnswer: null }], // 单选题
        tableData2: [], // 填空题
        localMapExamPoints: [], // 本地考点
        localKnowTypeListG: [], //
        status: '',
        disabledShow: false,
        loading: false,
        rules: {
          questionAroundType: [{ required: true, message: '请选择试题分类', trigger: 'change' }],
          sortNum: [{ required: true, message: '请输入排序', trigger: 'blur' }],
          phase: [{ required: true, message: '请选择阶段', trigger: 'change' }],
          questionType: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
          questionName: [
            { required: true, message: '请输入题目内容', trigger: ['blur', 'change'] },
            { validator: validateQuestionName, trigger: ['blur', 'change'] }
          ],
          optionList: [{ required: true, message: '请填写选项或答案', trigger: 'change' }],
          questionDescription: [{ required: true, message: '请输入解析内容', trigger: 'blur' }],
          knowledgeIdList: [{ required: true, type: 'array', min: 1, message: '请选择至少一个知识点', trigger: 'change' }]
          // examPoints: [{ required: true, type: 'array', min: 1, message: '请选择至少一个考点', trigger: 'change' }]
        }
      };
    },
    mounted() {
      console.log('dataDialog弹框值', this.dataDialog);
    },
    created() {
      // 确保在组件创建时能获取到知识点数据
      this.ensureKnowledgeData();
      this.loadExamPoints(this.dataDialog.knowledgeIdList);
    },
    watch: {
      dataDialog: {
        handler(newVal) {
          console.log('🚀 ~ handler ~ newVal变化了:', newVal.examPoints);
          // newVal.examPoints = [];
          if (newVal.questionType == 1) {
            this.tableData1 = Array.isArray(newVal.optionList) ? newVal.optionList : [];
            this.status = newVal.optionList.findIndex((e) => e.optionIsAnswer == 1);
            // this.status = newVal.answerChoise;
          } else {
            this.tableData2 = Array.isArray(newVal.optionList) ? newVal.optionList : [];
          }

          // 确保知识点数据存在
          if (newVal.phase && this.localKnowTypeListG.length === 0) {
            // this.ensureKnowledgeData();
          }

          if (Array.isArray(newVal.knowledgeIdList) && newVal.knowledgeIdList.length > 0) {
            console.log(newVal.knowledgeIdList, 'newVal.knowledgeIdListnewVal.knowledgeIdListnewVal.knowledgeIdListnewVal.knowledgeIdListnewVal.knowledgeIdList');

            // this.loadExamPoints(newVal.knowledgeIdList);
          }
          console.log('🚀 ~ handler ~ tableData1:', this.tableData1);
          console.log('🚀 ~ handler ~ tableData2:', this.tableData2);
        },
        immediate: true,
        deep: true
      },
      // 监听阶段变化
      'dataDialog.phase'(newVal) {
        if (!newVal) {
          this.dataDialog.knowledgeIdList = [];
          this.localKnowTypeListG = []; // 这里直接修改了 prop
        } else {
          // 这里请求知识点
          console.log('🚀 ~ 这里请求知识点:');
          this.dataDialog.knowledgeIdList = [];
          this.localKnowTypeListG = [];
          this.loading = true;
          optionsAPI({ knowledgeFlag: true, phase: newVal })
            .then((res) => {
              this.localKnowTypeListG = res.data || []; // 这里也直接修改了 prop
            })
            .catch((err) => {
              console.log('请求知识点失败', err);
            })
            .finally(() => {
              this.loading = false;
            });
          console.log('🚀 ~ optionsAPI ~ this.localKnowTypeListG:', this.localKnowTypeListG);
        }
      },

      // 监听知识点变化
      'dataDialog.knowledgeIdList'(newVal) {
        if (!Array.isArray(newVal) || newVal.length === 0) {
          this.dataDialog.examPoints = [];
          // this.disabledShow = true;
        } else {
          this.loadExamPoints(newVal);
        }
      },

      // 监听题目类型变化
      'dataDialog.questionType'(newVal, oldVal) {
        if (newVal == 2 && oldVal == 1) {
          // 切换到填空题时清空选项
          console.log('🚀 ~ 切换到填空题时清空选项:');

          this.tableData2 = [];
          this.dataDialog.optionList = [];
          // this.dataDialog.knowledgeIdList = []; //知识点清空
          // this.dataDialog.examPoints = []; //考点清空
        }
        if (newVal == 1 && oldVal == 2) {
          // 切换到选择题时清空选项
          console.log('🚀 ~ 切换到选择题时清空选项:');
          this.tableData1 = [];
          this.dataDialog.optionList = [];
          // this.dataDialog.knowledgeIdList = []; //知识点清空
          // this.dataDialog.examPoints = []; //考点清空
        }
      }
    },
    methods: {
      // 确保知识点数据存在
      ensureKnowledgeData() {
        if (this.dataDialog.phase && this.localKnowTypeListG.length === 0) {
          this.loading = true;
          optionsAPI({ knowledgeFlag: true, phase: this.dataDialog.phase })
            .then((res) => {
              this.localKnowTypeListG = res.data || [];
            })
            .catch((err) => {
              console.log('请求知识点失败', err);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      },
      // 加载考点列表
      loadExamPoints(knowledgeIds) {
        const idStr = knowledgeIds.map((k) => k.id || k).join(',');
        if (idStr) {
          this.loading = true;
          thirdNodeAPI({ knowledgeIdList: idStr })
            .then((res) => {
              this.localMapExamPoints = res.data || [];
              console.log('🚀 ~ loadExamPoints ~ this.localMapExamPoints:', this.localMapExamPoints);
            })
            .catch((err) => {
              // cosnole.log('请求考点失败', err);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      },

      handleKnowledge() {
        this.loadExamPoints(this.dataDialog.knowledgeIdList);
      },

      // 添加选项
      confirmOrigin() {
        const list = this.dataDialog.questionType == 1 ? this.tableData1 : this.tableData2;
        list.push({
          optionContent: '',
          optionIsAnswer: this.dataDialog.questionType == 2 ? 1 : 0,
          tableEditing: true
        });
      },

      // 编辑 -> 确定
      confirmEdit(index) {
        const list = this.dataDialog.questionType == 1 ? this.tableData1 : this.tableData2;
        if (list[index]) {
          if (!list[index].optionContent || list[index].optionContent.trim() === '') {
            this.$message.warning('答案不能为空');
            return;
          }
          this.$set(list[index], 'tableEditing', false);
        }
      },

      // 编辑按钮
      editRow(index) {
        // tableData1选择 tableData2填空
        const list = this.dataDialog.questionType == 1 ? this.tableData1 : this.tableData2;
        if (list[index]) {
          this.$set(list[index], 'tableEditing', true);
        }
        console.log('🚀 ~ 编辑按钮editRow ~ list:', list);
      },

      // 删除
      deleteCheck(index) {
        const list = this.dataDialog.questionType == 1 ? this.tableData1 : this.tableData2;
        list.splice(index, 1);
      },

      // 重置并关闭弹窗
      resetData() {
        this.$refs.dataDialogForm?.resetFields();
        this.$emit('update:dialogVisible', false);
        this.$emit('resetDialogData');
      },
      // 单选选择正确答案
      handleRadioChange(e) {
        console.log('🚀 ~ hand单选选择正确答案leRadioChange ~ index:', e);

        this.tableData1.forEach((item, index) => {
          if (e !== index) {
            item.optionIsAnswer = 0;
          } else {
            item.optionIsAnswer = 1;
          }
        });
      },
      // 提交保存
      submitClick() {
        this.$refs.dataDialogForm.validate((valid) => {
          if (!valid) return;

          this.dataDialog.optionList = this.dataDialog.questionType == 1 ? this.tableData1 : this.tableData2;
          const hasCorrectAnswer = this.dataDialog.optionList.some((item) => item.optionIsAnswer == 1);
          const hasUnconfirmed = this.dataDialog.optionList.some((item) => item.tableEditing == true);
          if (!hasCorrectAnswer) {
            this.$message.warning('请勾选正确答案');
            return;
          }
          if (hasUnconfirmed) {
            this.$message.warning('请确定添加选项的内容');
            return;
          }
          if (Array.isArray(this.dataDialog.knowledgeIdList)) {
            this.dataDialog.knowledgeIdList = this.dataDialog.knowledgeIdList.map((item) => (typeof item === 'object' ? item.id : item));
          } else {
            this.dataDialog.knowledgeIdList = [];
          }

          console.log('🚀 ~ 提交保存 this.dataDialog:', this.dataDialog);
          addOrUpdate(this.dataDialog).then(() => {
            this.$message.success('保存成功');
            this.$emit('update:dialogVisible', false);
            this.$emit('submit');
          });
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .groupStyle {
    display: flex;
    flex-direction: column;
  }
</style>
