<template>
  <div class="spell_thesaurus_page">
    <h2>添加课时词库</h2>
    <div class="main_thesaurus" style="display: flex; justify-content: space-between; margin-bottom: 20px">
      <el-radio-group v-model="wordType" @change="onWordTypeChange">
        <el-radio-button class="radio_box" label="校内词库"></el-radio-button>
        <el-radio-button style="border-left: 1px solid #dcdfe6" label="考纲词库"></el-radio-button>
      </el-radio-group>
      <SearchBar :value="searchForm" @search="handleSearch" />
    </div>
    <div style="margin-bottom: 20px">
      <el-radio-group v-if="wordType == '考纲词库'" v-model="gradeType" @change="onGradeTypeChange">
        <el-radio-button label="小学考纲"></el-radio-button>
        <el-radio-button style="border-left: 1px solid #dcdfe6" label="初中考纲"></el-radio-button>
      </el-radio-group>
    </div>
    <div style="font-size: 32px">
      <!-- :on-remove="handlRemoveThesaurus" :before-upload="beforeThesaurusUpload" -->
      <el-upload action="#" :show-file-list="false" ref="uploadThesaurus" :file-list="fileListThesaurus" :http-request="uploadThesaurusHttp" accept=".xlsx,.xls">
        <div class="el-upload__text">
          <el-link icon="el-icon-upload2" :underline="false" class="upload_link">模板文件上传</el-link>
        </div>
      </el-upload>
      <div>
        <div v-if="fileListThesaurus.length > 0" class="custom-upload-list">
          <div v-for="file in fileListThesaurus" :key="file.id || file.name" class="custom-upload-list__item">
            <i class="el-icon-document"></i>
            <span class="file-name">{{ file.name }}</span>
            <i class="el-icon-close" @click="removeFile(file)" style="margin-left: 8px"></i>
          </div>
        </div>
        <el-progress v-if="showProgress" :percentage="percentage" :color="customColor"></el-progress>
      </div>
      <el-link class="download_link" :underline="false" icon="el-icon-download" :href="downloadHref">{{ wordType === '校内词库' ? '校内' : gradeType }}词库模板下载</el-link>
    </div>
    <div v-if="words.length === 0"></div>
    <ReusableTable
      ref="reusableTable"
      v-model="words"
      :columns="columns"
      :currentPage="listParmas.pageNum"
      @update:currentPage="handleCurrentPageUpdate"
      :pageSize="listParmas.pageSize"
      :total="total"
      @page-change="handlePageChange"
      :type="type"
      @update-row="handleUpdateRow"
      @delete-row="handleDeleteRow"
      @add-row="handleAddRow"
    />
  </div>
</template>

<script>
  import SearchBar from './components/SearchBar.vue';
  import ReusableTable from './components/ReusableTable.vue';
  import { ossPrClient } from '@/api/alibaba';
  import spellStrongThesaurusAPI from '@/api/course/spellStrongThesaurus';
  export default {
    name: 'SpellStrongThesaurus',
    components: {
      SearchBar,
      ReusableTable
    },
    computed: {
      type() {
        return this.wordType === '校内词库' ? 2 : this.gradeType === '小学考纲' ? 3 : 4;
      },

      downloadHref() {
        return this.wordType === '校内词库'
          ? 'https://document.dxznjy.com/applet/zhimi/config/%E6%A0%A1%E5%86%85%E8%AF%8D%E5%BA%93%E6%A8%A1%E6%9D%BF.xlsx'
          : this.gradeType === '小学考纲'
          ? 'https://document.dxznjy.com/applet/zhimi/config/%E5%B0%8F%E5%AD%A6%E8%80%83%E7%BA%B2%E6%A8%A1%E6%9D%BF.xlsx'
          : 'https://document.dxznjy.com/applet/zhimi/config/%E5%88%9D%E4%B8%AD%E8%80%83%E7%BA%B2%E6%A8%A1%E6%9D%BF.xlsx';
      },
      columns() {
        // 校内词库有年级，考纲词库没有
        const baseColumns = [
          { prop: 'word', label: '单词', type: 'input' },
          { prop: 'wordSplit', label: '单词拆分', type: 'input' },
          { prop: 'chinese', label: '意思', type: 'input' },
          { prop: 'group', label: '组别', type: 'input' },
          { prop: 'wordSpelling', label: '划拼音', type: 'textarea', width: 210 },
          { prop: 'wordAccent', label: '标重读', type: 'textarea', width: 210 },
          { prop: 'wordWeak', label: '标弱读', type: 'textarea', width: 210 },
          { prop: 'wordLength', label: '定长短', type: 'textarea', width: 210 },
          { prop: 'voiceSkill', label: '发音技巧', type: 'textarea', width: 210 }
        ];
        if (this.wordType === '校内词库') {
          // 年级只在校内词库显示
          baseColumns.splice(3, 0, {
            prop: 'grade',
            label: '年级',
            type: 'select',
            options: [
              { label: '三年级', value: 3 },
              { label: '四年级', value: 4 },
              { label: '五年级', value: 5 },
              { label: '六年级', value: 6 },
              { label: '七年级', value: 7 },
              { label: '八年级', value: 8 },
              { label: '九年级', value: 9 }
            ]
          });
        }
        return baseColumns;
      }
    },

    data() {
      return {
        wordType: '校内词库', // 默认选中校内词库
        gradeType: '小学考纲', // 默认选中小学考纲
        words: [],
        fileListThesaurus: [],
        searchForm: {
          word: '',
          chinese: ''
        },
        showProgress: false, // 是否显示进度条
        percentage: 0, // 进度条百分比
        customColor: '#409eff', // 进度条颜色
        listParmas: {
          pageNum: 1,
          pageSize: 10,
          type: 1,
          word: '',
          chinese: ''
        },
        totalPages: 0,
        total: 0
      };
    },
    created() {
      ossPrClient();
      this.uploadFileList();
      this.getWordsList();
      console.log('thesaurusFileList:', this.fileListThesaurus);
    },
    methods: {
      // 单选切换时初始化内容并重新查询
      onWordTypeChange() {
        this.gradeType = '小学考纲';
        this.words = [];
        this.fileListThesaurus = [];
        this.searchForm = { word: '', chinese: '' };
        this.uploadFileList();
        this.handleSearch(this.searchForm);
      },
      onGradeTypeChange() {
        this.words = [];
        this.fileListThesaurus = [];
        this.uploadFileList();
        this.handleSearch(this.searchForm);
      },
      // 查询单词
      getWordsList() {
        console.log('查询单词列表...');
        this.listParmas.type = this.type == 2 ? 1 : 2; // 1校内词库 2考纲词库
        // 小学考纲 3 初中考纲 4
        if (this.type == 3) {
          this.listParmas.categoryType = 0;
        } else if (this.type == 4) {
          this.listParmas.categoryType = 1;
        } else {
          delete this.listParmas.categoryType;
        }
        console.log('查询单词列表参数:', this.listParmas);
        spellStrongThesaurusAPI.thesaurusWordList(this.listParmas).then((response) => {
          console.log(`获取词库单词列表接口返回数据`, response);
          if (response && response.code === 20000) {
            let tempList = response.data?.data || [];
            tempList.forEach((item) => {
              item.grade = item.grade || null;
              item.group = item.group || null;
            });
            this.words = tempList;
            this.totalPages = response.data.totalPage || 0;
            this.total = Number(response.data.totalItems) || 0;
          } else {
            this.$message.error(response.message || '获取词库单词列表失败请检查网络或者刷新页面');
          }
        });
      },
      // 单词编辑
      handleUpdateRow(row, type) {
        console.log('编辑单词:', row);
        spellStrongThesaurusAPI
          .thesaurusWordEdit(row)
          .then((response) => {
            console.log(`编辑单词接口返回数据`, response);
            if (response.code === 20000) {
              this.$message.success(type === 'delete' ? '删除音频成功' : response.message || '修改成功');
              this.getWordsList(); // ⭐ 修改成功后重新查询数据
            } else {
              this.$message.error(response.message || '修改失败');
              this.getWordsList(); // ⭐ 保证数据回滚
            }
          })
          .catch((err) => {
            console.log(`编辑单词接口请求失败`, err);
            this.getWordsList(); // ⭐ 保证数据回滚
          });
      },
      // 单词删除
      handleDeleteRow(id) {
        console.log('删除单词ID:', id);
        this.$confirm('删除此单词后，则在KS端不进行呈现，请确定是否需要删除', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            spellStrongThesaurusAPI
              .thesaurusWordDelete({ id })
              .then((response) => {
                console.log(`删除单词接口返回数据`, response);
                if (response && response.code === 20000) {
                  this.$message.success(response.message || '删除单词成功');
                  // 判断是否在最后一页且当前页只剩一个数据
                  const isLastPage = this.listParmas.pageNum == this.totalPages;
                  const isLastItem = this.words.length === 1;
                  if (isLastPage && isLastItem && this.listParmas.pageNum > 1) {
                    this.listParmas.pageNum -= 1; // 页码减1
                  }
                  this.words = [];
                  this.getWordsList(); // ⭐ 删除成功后重新查询数据
                } else {
                  this.$message.error(response.message || '删除失败请检查网络或者刷新页面');
                }
              })
              .catch((err) => {
                console.log(`删除单词接口请求失败`, err);
              });
          })
          .catch(() => {
            this.$message.info('删除单词失败');
            console.log('取消删除单词');
          });
      },
      // 新增单词
      handleAddRow(row) {
        console.log('新增单词:', row);
        let type = this.type == 2 ? 1 : 2; // 1校内词库 2考纲词库

        let data = {
          ...row,
          type: type
        };
        // 小学考纲 3 初中考纲 4
        if (this.type == 3) {
          data.categoryType = 0;
        } else if (this.type == 4) {
          data.categoryType = 1;
        } else {
          delete this.listParmas.categoryType;
        }
        spellStrongThesaurusAPI
          .thesaurusWordSave(data)
          .then((response) => {
            console.log(`新增单词接口返回数据`, response);
            if (response.code === 20000) {
              this.$message.success(response.message || '新增单词成功');
              this.words = [];
              this.getWordsList(); // ⭐ 新增成功后重新查询数据
            } else {
              this.$message.error(response.message || '新增失败请检查网络或者刷新页面');
              this.listParmas.pageNum = 1;
              this.getWordsList(); // ⭐ 保证数据回滚
            }
          })
          .catch((err) => {
            console.log(`新增单词接口请求失败`, err);
            this.listParmas.pageNum = 1;
            this.getWordsList(); // ⭐ 保证数据回滚
          });
      },
      handleSearch(search) {
        console.log('搜索条件:', search);
        this.$refs.reusableTable.isAddRow = false;
        // 在这里处理搜索逻辑，例如过滤表格数据
        this.listParmas.pageNum = 1;
        this.listParmas.word = search.word || '';
        this.listParmas.chinese = search.chinese || '';
        this.words = [];
        this.getWordsList();
      },
      // 滚动分页加载
      // loadMoreData() {
      //   console.log('父组件：加载更多数据...');
      //   if (this.listParmas.pageNum >= this.totalPages) {
      //     return;
      //   }
      //   this.listParmas.pageNum += 1;
      //   this.getWordsList();
      // },
      // 文件上传
      uploadThesaurusHttp({ file }) {
        this.showProgress = true;
        this.percentage = 0;
        // 模拟上传文件
        console.log('上传词库文件:', file);
        // this.fileListThesaurus.push(file);
        const that = this;
        // const fileType = file.type.substring(file.type.lastIndexOf('/') + 1);
        // const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
        const fileName = `manage/${Date.parse(new Date())}/${file.name}`;
        console.log(`阿里云OSS上传词库文件名`, fileName);

        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传词库成功回调1`, file, res, url, name);
                let queryData = {
                  fileUrl: url,
                  type: that.type == 2 ? 1 : 2 // 1校内词库 2考纲词库
                };
                const formData = new FormData();
                formData.append('file', file);
                // 小学考纲 3 初中考纲 4
                if (that.type == 3) {
                  queryData.categoryType = 0;
                } else if (that.type == 4) {
                  queryData.categoryType = 1;
                }
                that.percentage = 99;
                spellStrongThesaurusAPI
                  .thesaurusFileUpload(queryData, formData)
                  .then((response) => {
                    console.log(`上传词库文件接口返回数据1`, response);
                    if (response && response.code === 20000) {
                      that.$message.success(response.message || '上传成功');
                      console.log(`上传词库文件接口返回数据`, response);
                      that.percentage = 100;
                      // 上传成功后处理逻辑
                      that.uploadFileList();
                      this.getWordsList();
                    } else {
                      that.$message.error(response.message || '上传失败请检查网络或者刷新页面');
                    }
                  })
                  .catch((err) => {
                    console.log(`上传词库文件接口请求失败`, err);
                  })
                  .finally(() => {
                    setTimeout(() => {
                      that.showProgress = false;
                      that.percentage = 0;
                    }, 200);
                  });
              }
            })
            .catch((err) => {
              that.$message.error('上传失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传失败回调`, err);
            });
        });
      },
      // 附件列表查询
      uploadFileList(file) {
        let queryData = {
          type: this.type == 2 ? 1 : 2 // 1校内词库 2考纲词库
        };
        // 小学考纲 3 初中考纲 4
        if (this.type == 3) {
          queryData.categoryType = 0;
        } else if (this.type == 4) {
          queryData.categoryType = 1;
        } else {
          delete this.listParmas.categoryType;
        }
        spellStrongThesaurusAPI.thesaurusFileList(queryData).then((response) => {
          console.log(`获取词库列表接口返回数据`, response);
          this.fileListThesaurus = [];
          if (response && response.code === 20000) {
            this.fileListThesaurus = response.data.map((item, index) => ({
              name: item.fileName,
              url: item.fileUrl,
              uid: index,
              id: item.id
            }));
            console.log('词库文件列表:', this.fileListThesaurus);
          } else {
            this.$message.error(response.message || '获取词库列表失败请检查网络或者刷新页面');
          }
        });
      },

      // 附件删除
      removeFile(file) {
        console.log('删除文件索引:', file);
        this.$confirm('确定是否需要删除已上传的表格！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            if (file && file.id) {
              spellStrongThesaurusAPI
                .thesaurusFileDelete({ id: file.id })
                .then((response) => {
                  console.log(`删除词库文件接口返回数据`, response);
                  if (response && response.code === 20000) {
                    this.$message.success(response.message || '表格删除成功');
                    this.uploadFileList();
                    // 删除成功后处理逻辑
                    this.listParmas.pageNum = 1;
                    this.getWordsList(); // ⭐ 删除成功后重新查询数据
                  } else {
                    this.$message.error(response.message || '表格删除失败');
                  }
                })
                .catch((err) => {
                  console.log(`删除词库文件接口请求失败`, err);
                });
            }
          })
          .catch(() => {
            this.$message.info('表格删除失败');
          });
      },
      handlePageChange({ page, pageSize }) {
        this.listParmas.pageNum = page;
        this.listParmas.pageSize = pageSize;
        this.getWordsList();
      },
      handleCurrentPageUpdate(val) {
        console.log('当前页码更新为:', val, this.totalPages);
        this.listParmas.pageNum = val;
        if (val != this.totalPages) {
          this.words = [];
          this.total += 1;
        } else {
          this.getWordsList();
        }
      }
    }
  };
</script>

<style lang="less" scoped>
  .spell_thesaurus_page {
    padding: 20px;
  }
  .el-radio-button {
    margin-right: 10px;
  }
  .main_thesaurus {
    .el-radio-button:first-child .el-radio-button__inner {
      width: 130px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .custom-upload-list {
    max-height: 88px;
    overflow-y: scroll;
    margin-top: 8px;
  }
  .custom-upload-list__item {
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 4px 12px;
    margin-bottom: 4px;
    font-size: 14px;
  }
  .custom-upload-list__item .el-icon-document {
    color: #909399;
    margin-right: 6px;
  }
  .file-name {
    // flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 38px;
  }
  .custom-upload-list__item .el-icon-close {
    display: none;
  }
  .custom-upload-list__item:hover {
    background: #f5f7fa;
  }
  .custom-upload-list__item:hover .el-icon-close {
    display: inline-block !important;
    cursor: pointer;
  }
</style>
