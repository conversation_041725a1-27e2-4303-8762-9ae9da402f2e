<template>
  <div>
    <el-row class="main-container">
      <el-col :span="24">
        <el-col :span="6" class="left-sidebar">
          <div style="margin: 20px 0 0 20px; line-height: 32px">条件筛选</div>
          <el-form :rules="rules" :model="form" ref="formName">
            <!-- <el-form-item label="题干" label-width="70px">
              <el-input placeholder="请输入题干名称" v-model="form.questionText" style="width: 65%" clearable></el-input>
            </el-form-item> -->
            <el-form-item label="题目ID" label-width="70px" prop="id">
              <el-input placeholder="请输入题目ID" v-model="form.id" style="width: 65%" clearable></el-input>
            </el-form-item>
            <el-form-item label="题目题干" label-width="70px">
              <el-input placeholder="请输入题干" v-model="form.questionText" style="width: 65%" clearable></el-input>
            </el-form-item>
            <el-form-item label="课程大类" label-width="70px">
              <el-select style="width: 65%" v-model="form.curriculumId" placeholder="请选择课程大类">
                <el-option v-for="item in curriculumList" :key="item.id" :value="item.id" :label="item.enName"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学科学段" label-width="70px">
              <el-cascader style="width: 65%" :options="subjectList" v-model="form.disciplineId" @change="handleChange" clearable></el-cascader>
            </el-form-item>
            <el-form-item label="知识小结" label-width="70px">
              <!-- collapse-tags -->
              <el-cascader
                :show-all-levels="false"
                collapse-tags
                style="width: 65%"
                :disabled="form.disciplineId.length > 0 ? false : true"
                :props="{ multiple: true }"
                :emitPath="true"
                :options="discipChildList"
                v-model="form.knowledgeIdList"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="题目类型" label-width="70px">
              <el-select style="width: 65%" v-model="form.questionType" placeholder="请选择题目类型" clearable>
                <el-option v-for="item in questionTypeList" :key="item.code" :label="item.value" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="生成类型" label-width="70px">
              <el-select style="width: 65%" v-model="form.buildType" placeholder="请选择生成类型" clearable>
                <el-option v-for="item in buildTypeList" :key="item.code" :label="item.value" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="关联状态" label-width="70px">
              <el-select style="width: 65%" v-model="form.linkKnowledge" placeholder="请选择关联状态" clearable>
                <el-option v-for="item in associationList" :key="item.code" :label="item.value" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div style="margin-top: 20px; text-align: center">
            <el-button type="primary" size="mini" @click="handleSearch()">搜索</el-button>
            <el-button type="primary" size="mini" @click="handleReset()">重置</el-button>
          </div>
        </el-col>
        <div class="right-content">
          <el-col :span="20" style="text-align: right; overflow: auto">
            <div class="filter-add">
              <span>共筛选出{{ tablePage.totalItems }}条数据</span>
              <el-button style="margin-right: 20px" type="primary" size="mini" v-if="checkPermission(['b:aaademo:topicManagementAdd'])" @click="handleAdd">新增题目</el-button>
            </div>
          </el-col>
          <el-col :span="20">
            <el-container>
              <el-col :span="24" v-if="topicList.length > 0">
                <el-card style="margin-bottom: 20px" v-for="(item, index) in topicList" :key="index">
                  <el-header style="font-size: 12px; margin-bottom: 20px" class="flex-around">
                    <span>题目ID：{{ item.id }}</span>
                    <span>学段：{{ item.gradeName }}</span>
                    <span>推荐答题时间：{{ item.answerTime }}分钟</span>
                    <span>试题难度： {{ item.questionDifficulty === 0 ? '低' : item.questionDifficulty === 1 ? '中' : item.questionDifficulty === 2 ? '高' : '未知' }}</span>
                    <span>最近编辑时间：{{ item.createTime }}</span>
                  </el-header>
                  <el-main style="text-align: center; padding: 0 160px">
                    <!-- 题目区域 -->
                    <!-- <span style="margin: 20px 0">
                    测试
                    {{ item.questionText }}
                  </span> -->
                    <!-- <mathematicalFormula :formulaText="item.questionText" /> -->
                    <math-test :formulaText="item.questionText" />
                    <div class="flex-around">
                      <el-image v-for="(url, indexUrl) in item.questionImg" :key="indexUrl" :preview-src-list="item.questionImg" style="height: 100px" :src="url"></el-image>
                    </div>
                    <div style="text-align: left; margin: 20px 0" v-for="(group, groupIndex) in groupByQuestionLocation(item.mathQuestionOptionVos)" :key="groupIndex">
                      <div v-if="item.questionType !== 0">
                        <div style="margin: 20px 0; font-weight: bold">
                          {{ item.questionType == 1 ? '填空' : '问题' }} {{ Number(groupIndex) + 1 }}：{{ group[groupIndex]?.questionSmallProblem }}
                        </div>
                      </div>
                      <div v-for="(optionItem, optionIndex) in group" :key="optionIndex" class="option">
                        <!-- <mathematicalFormula :formulaText="`${optionItem.choiceOption}. ${optionItem.content}`" /> -->
                        <math-test :formulaText="`${optionItem.choiceOption}. ${optionItem.content}`" />
                      </div>
                    </div>
                    <div class="flex-around">
                      <el-row>
                        <el-col :span="12" style="text-align: flex-start">
                          <el-button type="primary" size="mini" @click="handleReview(item.id)">查看解析</el-button>
                        </el-col>
                        <el-col :span="12" class="flex-around">
                          <el-button type="primary" size="mini" v-if="checkPermission(['b:aaademo:topicManagementEdit'])" @click="handleEdit(item.id)">编辑</el-button>
                          <el-button type="primary" size="mini" v-if="checkPermission(['b:aaademo:topicManagementdel'])" @click="handleDelect(item.id)">删除</el-button>
                        </el-col>
                        <span style="position: absolute; bottom: 10px; right: -230px; font-size: 12px">
                          题有问题？
                          <span style="color: #409eff; cursor: pointer" type="primary" size="mini" @click="handleFeedback(item)">点我反馈</span>
                        </span>
                      </el-row>
                    </div>
                  </el-main>
                  <div v-if="item.buildType == 1" style="display: flex; justify-content: flex-end">
                    <img style="width: 100px" src="../../../assets/AIPic.png" />
                  </div>
                </el-card>
              </el-col>
              <el-col :span="20" v-else style="height: 700px">
                <p style="text-align: center; margin-top: 300px">暂无数据</p>
              </el-col>
            </el-container>
          </el-col>
          <el-col :span="20" style="display: flex; justify-content: center">
            <el-pagination
              :page-size="tablePage.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tablePage.totalItems"
              :page-sizes="[10, 20, 30, 40, 50]"
              @size-change="handleSizeChange"
              :current-page.sync="tablePage.currentPage"
              @current-change="handleCurrentChange"
            />
          </el-col>
        </div>
      </el-col>
    </el-row>

    <!-- 查看解析 -->
    <el-dialog center title="查看解析" :visible.sync="dialogVisible" width="60%">
      <viewAnalysisDialog v-if="dialogVisible" :viewId="viewId"></viewAnalysisDialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 反馈弹窗 -->
    <feed-back-dialog @feedclose="feedclose" @childsubmit="childsubmit" :visible.sync="feedbackDialogVisible" />
  </div>
</template>

<script>
  import checkPermission from '@/utils/permission';
  import { pageParamNames } from '@/utils/constants';
  import viewAnalysisDialog from './components/viewAnalysisDialog.vue';
  import mathematicalFormula from './components/mathematicalFormula.vue';
  import feedBackDialog from './components/feedBackDialog.vue';
  import mathTest from './components/mathTest.vue';
  import forStudent from '@/api/testPaper/management';
  import { getCurriculumAPI, getSubjectAPI, listQuestionAPI, getBuildTypeAPI, getTypeAPI, deleteQuestionAPI, getRelationAPI, FeedbackAPI } from '@/api/mathApi/topicManagementAPI';
  export default {
    name: 'topicManagement',
    components: {
      viewAnalysisDialog,
      mathematicalFormula,
      feedBackDialog,
      mathTest
    },
    data() {
      return {
        rules: {
          id: [{ max: 19, message: '题目id最多只有18位', trigger: 'change' }]
        },
        viewId: '', // 查看解析id
        // 筛选数据
        form: {
          curriculumId: '',
          id: '',
          questionType: '',
          questionText: '',
          buildType: '',
          linkKnowledge: '',
          disciplineId: [],
          knowledgeIdList: []
        },
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        backitem: {},
        associationList: [],
        questionTypeList: [],
        dialogVisible: false,
        feedbackDialogVisible: false,
        url: '',
        srcList: [],
        // 题库列表
        topicList: [],
        curriculumList: [], // 课程大类列表
        subjectList: [], // 学科列表
        discipChildList: [], //知识小结列表
        subjectData: [], //学科源数据
        subjectValue: [], // 学科列表选中值
        buildTypeList: [] // 生成类型列表
      };
    },
    created() {
      this.initData();
      this.getType();
      this.getBuildType();
      this.getRelation();
    },
    watch: {
      // 监听课程大类
      'form.curriculumId': function (newVal) {
        if (newVal) {
          this.getSubjectList();
        }
      },
      '$route.query.refresh': function (newVal) {
        if (newVal) {
          this.initData(); // 调用分页数据刷新方法
          this.getType();
          this.getBuildType();
          this.getRelation();
        }
      }
    },

    methods: {
      checkPermission,
      async initData() {
        await this.getKcdlList();
        // this.form.curriculumId = '1223293140236390400';
        await this.getSubjectList(); // 获取学科学段

        await this.fetchData(); // 请求分页数据
      },
      groupByQuestionLocation(options) {
        console.log('🚀 ~ groupByQuestionLocation ~ options:', options);
        return options.reduce((groups, option) => {
          const location = option.questionLocation || 0; // 默认值为 0
          if (!groups[location]) {
            groups[location] = []; // 初始化分组
          }
          groups[location].push(option); // 将选项添加到对应分组
          return groups;
        }, {});
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      // 问题反馈接口
      childsubmit(value) {
        console.log('父组件接收到的值:', value);
        FeedbackAPI({
          feedbackType: 1,
          curriculumId: this.backitem.curriculumId,
          questionBankId: this.backitem.id,
          feedbackContent: value
        }).then((res) => {
          if (res.success) {
            this.$message.success('非常感谢你的反馈，数学超人将尽快安排完善');
          } else {
            this.$message.error('反馈失败，请稍后再试！');
          }
        });
        this.feedbackDialogVisible = false;
      },
      // 取消反馈弹框
      feedclose() {
        this.feedbackDialogVisible = false;
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 获取所有的课程大类
      getKcdlList() {
        return new Promise((resolve) => {
          forStudent.getKcdlForKnowledge().then((res) => {
            if (res.success) {
              this.curriculumList = res.data;
              this.form.curriculumId = res.data[0].id;
            }
            resolve();
          });
        });
      },
      // 获取题目类型列表
      async getType() {
        const res = await getTypeAPI();
        if (res.success) {
          this.questionTypeList = res.data;
        }
      },
      // 获取生成类型
      async getBuildType() {
        const res = await getBuildTypeAPI();
        if (res.success) {
          this.buildTypeList = res.data;
        }
      },
      // 是否关联知识点
      async getRelation() {
        const res = await getRelationAPI();
        if (res.success) {
          this.associationList = res.data;
        }
      },
      // 获取页面数据
      async fetchData() {
        // console.log(this.form.knowledgeIdList);
        let validate = await this.$refs.formName.validate();
        if (!validate) return;
        let knowledgeIdList = this.form.knowledgeIdList
          .map((i) => {
            return i.filter((item, index) => index !== 0);
          })
          .flat(1)
          .join(',');
        console.log(knowledgeIdList);

        listQuestionAPI({
          knowledgeIdList,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.size,
          curriculumId: this.form.curriculumId,
          disciplineId: this.form.disciplineId?.length ? this.form.disciplineId[0] : '',
          gradeId: this.form.disciplineId?.length ? this.form.disciplineId[1] : '',
          questionType: this.form.questionType,
          questionText: this.form.questionText,
          id: this.form.id,
          buildType: this.form.buildType,
          linkKnowledge: this.form.linkKnowledge
        })
          .then((res) => {
            if (res.success) {
              // this.topicList = res.data.data;
              this.$set(this, 'topicList', res.data.data);
              pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name] || 0)));
            }
          })
          .catch((err) => {
            this.$set(this, 'topicList', []);
          });
      },
      // 转换数据为 el-cascader 支持的格式
      transformToCascaderData(data, level = 1, maxLevel = 2) {
        // console.log(data);

        if (!data) return []; // 添加对 null 或 undefined 数据的处理
        return data
          .map((item) => {
            const node = {
              label: item.nodeName, // 使用 nodeName 作为显示的名称
              value: item.id // 使用 id 作为值
            };
            // 只在当前层级小于最大层级且存在子列表时才递归添加 children
            if (level < maxLevel && item.childList && item.childList.length > 0) {
              const children = this.transformToCascaderData(item.childList, level + 1, maxLevel);
              if (children.length > 0) {
                node.children = children; // 只有当 children 有数据时才保留
              }
            }
            return node;
          })
          .filter((node) => node.children || level === maxLevel); // 过滤掉没有 children 的节点，除非是最大层级
      },
      // 获取学科列表
      async getSubjectList() {
        this.form.disciplineId = [];
        const res = await getSubjectAPI({
          curriculumId: this.form.curriculumId,
          nodeLevel: 4 // 确保 API 请求获取到第三级数据
        });
        if (res.success && res.data) {
          this.subjectData = res.data;
          this.subjectList = this.transformToCascaderData(res.data, 1, 2);
          // 设置默认选中第一项
          if (this.subjectList.length > 0) {
            const firstSubject = this.subjectList[0];
            const firstGrade = firstSubject.children?.[0]?.value || null;
            this.form.disciplineId = [firstSubject.value, firstGrade]; // 默认选中第一项
            this.processorDiscipChildList();
          } else {
            this.form.disciplineId = []; // 如果没有数据，清空选中值
          }
        } else {
          this.subjectList = []; // 处理API调用失败或无数据的情况
          this.form.disciplineId = []; // 清空选中值
        }
      },
      processorDiscipChildList() {
        this.subjectData.forEach((i) => {
          if (i.id === this.form.disciplineId[0]) {
            i.childList.forEach((e) => {
              if (e.id === this.form.disciplineId[1]) {
                this.discipChildList = this.transformToCascaderData(e.childList, 1, 2);
                console.log(this.discipChildList, '351');
              }
            });
          }
        });
        console.log(this.form.knowledgeIdList);
      },
      handleChange(value) {
        console.log('🚀 ~ handleChange ~ value:', value);
        this.subjectValue = value; //
        this.form.disciplineId = value;
        this.form.knowledgeIdList = [];
        this.processorDiscipChildList();
      },
      // 新增题目
      handleAdd() {
        this.$router.push({
          path: './addQuestion',
          query: {
            editId: ''
          }
        });
        console.log('新增题目');
      },
      handleAdd1() {},
      // 搜索
      handleSearch() {
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      // 重置
      handleReset() {
        this.form.questionType = '';
        this.form.questionText = '';
        this.form.id = '';
        this.form.buildType = '';
        this.form.linkKnowledge = '';
        this.form.disciplineId = [];
        this.subjectValue = [];
        this.fetchData();
        this.form.knowledgeIdList = [];
        console.log('重置', this.form);
        this.tablePage.currentPage = 1;
      },
      // 反馈
      handleFeedback(item) {
        this.backitem = item;
        this.feedbackDialogVisible = true;
        console.log('反馈', item);
      },
      // 查看解析
      handleReview(id) {
        this.dialogVisible = true;
        this.viewId = id;
        console.log('查看解析', this.viewId);
      },
      // 编辑
      handleEdit(id) {
        console.log('编辑');
        this.$router.push({
          path: './addQuestion',
          query: {
            editId: id
          }
        });
      },
      // 删除
      handleDelect(id) {
        console.log('删除', id);
        this.$confirm(`您确定删除这条数据吗？`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            deleteQuestionAPI({ id: id }).then(() => {
              this.$message.success(`删除题目成功`);
              this.fetchData();
            });
          })
          .catch(() => {
            this.$message.info(`已取消删除`);
          });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .filter-add {
    display: flex;
    justify-content: space-between;
    line-height: 32px;
    margin: 20px 0;
  }
  .el-header {
    background-color: #b3c0d1;
    color: #333;
    line-height: 60px;
  }
  .flex-around {
    display: flex;
    justify-content: space-around;
  }
  .option {
    padding: 5px 0;
    margin: 5px 0;
  }
  .main-container {
    height: 100vh; /* 撑满整个视口高度 */
    display: flex;
    overflow: hidden;
  }

  .left-sidebar {
    height: 100%;
    overflow: auto; /* 如果你希望左边也可以滚动就保留 */
    // background: #f9f9f9;
    // border-right: 1px solid #eee;
  }

  .right-content {
    flex: 1;
    height: 100%;
    overflow-y: auto; /* 只让右边滚动 */
    padding: 16px;
  }
</style>
