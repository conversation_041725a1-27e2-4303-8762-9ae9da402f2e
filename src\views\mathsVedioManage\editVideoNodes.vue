<template>
  <div class="app-container process-card">
    <!-- <el-row class="video-node-row"> -->
    <el-breadcrumb class="process-card-title" separator-class="el-icon-arrow-right" v-if="videoDetail">
      <el-breadcrumb-item>{{ videoDetail.coursePeriodNodeName }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ videoDetail.courseSubjectNodeName }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ videoDetail.courseVersionNodeName }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ videoDetail.videoName }}</el-breadcrumb-item>
    </el-breadcrumb>
    <!-- 视频节点 -->
    <el-row class="video-node-row" type="flex" justify="space-between">
      <!-- 视频播放 -->
      <el-col :span="12">
        <div style="width: 100%; aspect-ratio: 16/9; height: auto; margin: 0 auto">
          <PlayVideo v-if="vid" ref="playVideo" :vid="vid" :videoKeyframes="videoKeyframes" :duration.sync="duration"></PlayVideo>
        </div>
      </el-col>
      <!-- 视频节点编辑 -->
      <el-col :span="11">
        <el-row>
          <el-table class="custom-video-table" :data="videoNodes" height="471" border empty-text="暂无节点" :row-class-name="tableRowClassName">
            <!-- 排序 -->
            <el-table-column label="排序" width="60">
              <template v-slot="{ row, column, $index }">
                <el-tag type="info" effect="plain">{{ $index + 1 }}</el-tag>
              </template>
            </el-table-column>
            <!-- 视频节点时间 -->
            <!-- selectableRange: `00:00:00 - ${duration}` -->
            <el-table-column label="视频节点时间" width="150">
              <template v-slot="{ row, column, $index }">
                <el-time-picker
                  :class="row.videoNodeTime ? '' : 'empty'"
                  :editable="false"
                  v-model="row.videoNodeTime"
                  :default-value="defaultTime"
                  value-format="HH:mm:ss"
                  @change="timeChange(row)"
                  :picker-options="{
                    selectableRange: `00:00:00 - ${duration}`
                  }"
                  placeholder="任意时间点"
                ></el-time-picker>
              </template>
            </el-table-column>
            <!-- 视频节点名称 -->
            <el-table-column label="视频节点名称">
              <template v-slot="{ row, column, $index }">
                <el-input
                  :class="row.videoNodePrompt ? '' : 'empty'"
                  v-model="row.videoNodePrompt"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 2 }"
                  placeholder="请输入视频节点名称"
                  maxlength="1000"
                  show-word-limit
                  resize="none"
                ></el-input>
              </template>
            </el-table-column>
            <!-- 删除视频节点图标 -->
            <el-table-column label="操作" width="100">
              <template v-slot="{ row, column, $index }">
                <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDeleteVideo(row, $index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button class="add-video-node-btn" type="primary" plain size="medium" icon="el-icon-plus" @click="handleAddVideoNode()">新增视频节点</el-button>
        </el-row>
      </el-col>
    </el-row>
    <!-- 保存按钮 -->
    <el-row class="btn-row" type="flex" justify="center">
      <el-col :span="3" :offset="1">
        <el-button class="add-video-node-btn" type="none" size="medium" icon="el-icon-close" @click="handleEditClose()">取消</el-button>
      </el-col>
      <el-col :span="3" :offset="1">
        <el-button class="add-video-node-btn" type="primary" size="medium" icon="el-icon-check" @click="handleSaveClick()">保存</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import PlayVideo from './components/PlayVideo.vue';
  import { deleteVideoNodesAPI, editVideoNodesAPI, listVideoNodesAPI } from '@/api/mathApi/editVideoNodesAPI';
  import { detailAPI } from '@/api/uploadVideoFile';

  export default {
    components: { PlayVideo },
    data() {
      return {
        emptyIndexList: [1], // 排序索引列表
        videoDetail: {}, // 视频详情
        vid: '', // 视频id
        duration: 0, // 视频时长
        defaultTime: new Date(0, 0, 0, 0, 0, 0), // 默认时间
        videoNodes: [], // 视频节点列表
        videoNodesOld: [], // 编辑前的视频节点列表
        deleteNodes: [], // 删除的节点列表
        videoKeyframes: [] // 视频播放节点列表
      };
    },
    mounted() {
      console.log('🚀 ~ mounted ~ this.route.query.row:', this.$route);
      // 初始化
      let row = this.$route.query.row;
      // 没有参数返回视频管理页面
      if (!row) {
        this.$message.warning('请先选择视频');
        this.goBack();
      }
      row = JSON.parse(decodeURIComponent(row));
      console.log('🚀 ~ mounted ~ row:', row);
      this.videoDetail = row;
      this.vid = this.videoDetail.vid;
      // 初始化视频节点
      this.init();
    },
    methods: {
      // 表格行样式
      tableRowClassName({ row, rowIndex }) {
        if (row && (!row.videoNodeTime || !row.videoNodePrompt)) {
          return 'warning-row';
        }
        return '';
      },
      // 返回上一页
      goBack() {
        this.$router.push({
          path: '/_aaa_demo/mathsVedioManage'
        });
      },
      // 初始化
      async init() {
        let row = this.videoDetail;
        const loading = this.$loading({
          lock: true,
          text: '时间节点加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        // // let res = await detailAPI(row.vid);
        // // console.log('🚀 ~ handleOpenEdit ~ res:', res);
        // // let url = res.data.videoUrl;
        // // if (!url || !url.includes(res.data.vid)) {
        // //   this.$message.info('视频审核中，无法预览');
        // //   url = '';
        // //   loading.close();
        // //   return;
        // // }
        let res = await detailAPI(this.videoDetail.vid);
        console.log('🚀 ~ handleOpenEdit ~ res:', res);
        let url = res.data.videoUrl;
        if (!url || !url.includes(res.data.vid)) {
          this.$message.info('视频审核中，无法预览');
          url = '';
          loading.close();
          this.goBack();
          return;
        }
        listVideoNodesAPI({ id: this.videoDetail.id })
          .then((res) => {
            console.log('🚀 ~ courseApi.getVideoNode ~ res:', res.data.data);
            const nodes = res.data.data || [];
            this.videoNodes = nodes
              .map((item, index) => {
                return {
                  id: item.id,
                  sortNum: item.sortNum,
                  videoNodeTime: item.videoNodeTime,
                  videoNodePrompt: item.videoNodePrompt,
                  isDeleted: item.isDeleted
                };
              })
              .reverse();
            this.videoNodesOld = JSON.parse(JSON.stringify(this.videoNodes)) || [];
            // this.videoKeyframes = this.videoNodes.map((item) => {
            //   return {
            //     // 打点出现时间
            //     keytime: item.videoNodeTime.split(':').reduce((acc, cur) => acc * 60 + +cur),
            //     // 打点提示内容
            //     keycontent: item.videoNodePrompt
            //   };
            // });
            loading.close();
          })
          .catch((err) => {
            loading.close();
            this.vid = this.videoDetail.vid;
          });
        // loading.close();
        // this.vid = this.videoDetail.vid;
      },

      // 保存按钮
      handleSaveClick() {
        // 是否有视频节点未填写内容
        console.log('🚀 ~ handleSaveClick ~ this.videoNodes:', undefined + 1);
        let isEmpty = false;
        let emptyIndexList = this.videoNodes
          .map((item, index) => {
            if (!item.videoNodePrompt || !item.videoNodeTime) {
              isEmpty = true;
              return index + 1;
            }
            return '';
          })
          .filter((item) => item);
        // console.log('🚀 ~ handleSaveClick ~ isEmpty:', isEmpty);
        if (isEmpty) {
          this.$alert(`视频节点 <strong style="color: red;font-size:1.5em;letter-spacing: 2px;">${emptyIndexList.join(',')}</strong> 未填写完整，请填写完整后再保存`, '提示', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '去填写',
            callback: () => {}
          });
          return;
          this.$confirm('有视频节点内容或时间未填写，继续将不会保存该节点，是否继续？', '提示', {
            confirmButtonText: '去填写',
            cancelButtonText: '继续保存',
            type: 'warning'
          })
            .then(() => {
              // 取消操作
            })
            .catch(() => {
              this.handleSaveVideoNode();
            });
          return;
        }
        this.handleSaveVideoNode();
      },
      // 保存视频节点
      handleSaveVideoNode() {
        let nodes = this.videoNodes
          .map((item, index) => {
            return {
              id: item.id,
              sortNum: index + 1,
              videoNodeTime: item.videoNodeTime,
              videoNodePrompt: item.videoNodePrompt,
              isDeleted: item.isDeleted
            };
          })
          .filter((item) => item.videoNodePrompt && item.videoNodeTime);
        console.log('🚀 ~ handleSaveVideoNode ~ nodes:', nodes, JSON.stringify(this.videoNodesOld));

        // 判断新旧视频节点数据是否一致
        if (JSON.stringify(nodes) === JSON.stringify(this.videoNodesOld)) {
          this.$message.success('视频节点保存成功');
          this.goBack();
          return;
        }
        console.log('🚀 ~ handleSaveVideoNode ~ nodes:', nodes);
        const loading = this.$loading({
          lock: true,
          text: '时间节点保存中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        nodes = [...nodes, ...this.deleteNodes];
        editVideoNodesAPI({ videoId: this.videoDetail.id, vid: this.vid, videoNodesList: nodes })
          .then((res) => {
            loading.close();

            if (res.success) {
              this.videoNodesOld = this.nodes;
              this.$message.success('视频节点保存成功');
              this.goBack();
            }
          })
          .catch((err) => {
            loading.close();
          });
      },
      // 时间修改
      timeChange(row) {
        // 时分秒转换成秒数
        if (row.videoNodeTime) {
          row.keytime = row.videoNodeTime.split(':').reduce((acc, cur) => acc * 60 + +cur);
          let isExist = this.handleCheckRepeat(row);
          if (isExist) {
            this.$message.warning('该时间节点已存在，请重新选择');
            row.videoNodeTime = '';
            row.keytime = '';
            return;
          }
        } else {
          row.keytime = '';
        }

        // console.log('🚀 ~ timeChange ~ row:', row);
      },
      // 删除视频节点
      handleDeleteVideo(row, index) {
        // 二次确认
        this.$confirm(`是否确认删除节点${index + 1}？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            if (row.id) {
              // 如果流程已保存到数据库中，则删除时需要标记为已删除
              this.deleteNodes.push({ ...row, isDeleted: 1 });
            }
            this.videoNodes.splice(index, 1);
          })
          .catch(() => {
            // 取消操作
          });
      },
      // 添加视频节点
      handleAddVideoNode() {
        if (this.videoNodes.length >= 10) {
          this.$message.warning('视频节点不能超过10个');
          return;
        }
        let { keytime, videoNodeTime } = this.$refs.playVideo.getCurrentTime();
        // 查看是否有相同的节点;
        let sortNum = (this.videoNodes[this.videoNodes.length - 1]?.sortNum || 0) + 1;
        let isExist = this.handleCheckRepeat({ keytime, videoNodeTime, sortNum });
        if (isExist) {
          this.$message.warning('该时间节点已存在，请重新选择时间');
          return;
        }
        this.videoNodes.push({
          keytime, // 秒数
          videoNodeTime, // 时分秒
          videoNodePrompt: '', // 节点内容
          sortNum, // 排序
          isDeleted: 0 // 是否删除
        });
        this.$nextTick(() => {
          // 获取正确的滚动容器
          const scrollWrapper = document.querySelector('.el-table__body-wrapper');

          if (scrollWrapper) {
            scrollWrapper.scrollTo({
              top: scrollWrapper.scrollHeight,
              behavior: 'smooth'
            });
          }
        });
      },
      // 判断是否有重复节点
      handleCheckRepeat({ keytime, videoNodeTime, sortNum }) {
        let isExist = this.videoNodes.some((item) => item.sortNum != sortNum && (item.keytime === keytime || item.videoNodeTime == videoNodeTime));
        return isExist;
      },
      // 编辑视频节点弹窗关闭
      handleEditClose() {
        const nodes = this.videoNodes
          .map((item, index) => {
            return {
              id: item.id,
              sortNum: index + 1,
              videoNodeTime: item.videoNodeTime,
              videoNodePrompt: item.videoNodePrompt,
              isDeleted: item.isDeleted
            };
          })
          .filter((item) => item.videoNodePrompt && item.videoNodeTime);
        console.log('🚀 ~ handleEditClose ~ nodes:', nodes, JSON.stringify(this.videoNodesOld));

        // 判断新旧视频节点数据是否一致
        if (JSON.stringify(nodes) != JSON.stringify(this.videoNodesOld)) {
          this.$confirm('是否放弃未保存的节点修改？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.$router.back();
            })
            .catch(() => {});
          return;
        } else {
          this.$router.back();
        }
        // this.$router.back();
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep(.custom-video-table) {
    .el-table__row {
      height: 100px; // 整行高度
    }

    .el-input__inner,
    .el-textarea__inner,
    .el-time-picker {
      height: 40px; // 输入框和时间选择器高度
      line-height: 30px;
    }
  }
  .video-node-row {
    margin-top: 30px;
  }
  .btn-row {
    margin-top: 50px;
  }
  .add-video-node-btn {
    width: 90%;
    margin: 10px 5% 0;
  }
  .process-card {
    margin: 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 20px;
    box-sizing: border-box;
  }
  .process-card-title {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    box-sizing: border-box;
  }

  ::v-deep {
    .el-breadcrumb {
      font-size: 18px;
    }
    .el-table .warning-row {
      background: #fef0f0;
      border: 1px solid #f56c6c !important;
      .empty {
        input {
          border-color: #f56c6c !important;
        }
        textarea {
          border-color: #f56c6c !important;
        }
      }
    }
  }
</style>
