// 用户权限-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/role/system',
  component: Layout,
  meta: {
    perm: 'm:sys',
    title: '权限管理',
    icon: 'roleSystem'
  },
  children: [
    {
      path: 'user_manage',
      name: 'user_manage',
      component: _import('_system/user/index'),
      meta: {
        perm: 'm:sys:user',
        title: '用户管理',
        noCache: true
      }
    },
    {
      path: 'role_manage',
      name: 'role_manage',
      component: _import('_system/role/index'),
      meta: {
        perm: 'm:sys:role',
        title: '角色管理',
        noCache: true
      }
    },
    {
      hidden: true,
      path: 'role_manage/:roleId/assign_perm',
      name: 'role_manage_assign_perm',
      component: _import('_system/role/assign_perm'),
      meta: {
        hiddenTag: true,
        title: '角色授权'
      }
    },
    {
      path: 'perm_manage',
      name: 'perm_manage',
      component: _import('_system/perm/index'),
      meta: {
        perm: 'm:sys:perm',
        title: '权限管理',
        noCache: true
      }
    },
    {
      path: 'userInfo',
      name: '用户信息',
      hidden: true,
      component: _import('_system/user/userInfo'),
      meta: {
        title: '用户信息',
        noCache: true
      }
    }
  ]
};
