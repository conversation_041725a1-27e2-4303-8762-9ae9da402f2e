<template>
  <div :class="['container', `grad${dialogWidth}`]">
    <!-- -->
    <div v-for="(item, index) in localList" :key="index" style="margin-bottom: 10px">
      <div style="display: flex; align-items: center">
        <el-input v-model.trim="item.value" placeholder="输入课程编号" @input="validateDate(index)" style="width: 140px; margin-right: 10px" />
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="addDate"
          size="mini"
          style="padding: 2px; border-radius: 50%"
          v-if="(index == 0 && localList.length == 1) || index == localList.length - 1"
        ></el-button>
        <el-button icon="el-icon-minus" type="danger" @click="removeDate(index)" size="mini" v-if="localList.length > 1" style="padding: 2px; border-radius: 50%"></el-button>
      </div>
      <div :style="{ color: item.valid ? 'green' : 'red', fontSize: '12px' }">
        {{ item.tip }}
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'CourseCodeInputList',

    data() {
      return {
        localList: []
      };
    },
    props: {
      value: {
        type: Array,
        required: true
      },
      dialogWidth: {
        type: String,
        default: '3'
      }
    },
    watch: {
      value: {
        immediate: true,
        handler(newVal) {
          if (this.syncing) return;
          const safeArray = Array.isArray(newVal) ? newVal : [];
          this.localList = safeArray.map((item) => ({
            value: item,
            valid: false,
            tip: ''
          }));
          this.localList.forEach((_, i) => this.validateDate(i));
        }
      },
      localList: {
        deep: true,
        handler() {
          this.syncing = true;
          const validValues = this.localList.map((item) => item.value);
          this.$emit('input', validValues);
          this.$nextTick(() => {
            this.syncing = false;
          });
        }
      }
    },
    mounted() {},

    methods: {
      syncToParent() {
        this.$emit(
          'input',
          this.localList.map((item) => item.value)
        );
      },
      addDate() {
        this.localList.push({ value: '', valid: false, tip: '' });
        this.syncToParent();
      },
      removeDate(index) {
        this.localList.splice(index, 1);
        this.syncToParent();
      },
      // 新方法：检查日期是否重复，排除自身
      isDuplicateDate(value, currentIndex) {
        for (let i = 0; i < this.localList.length; i++) {
          // 跳过自身
          if (i === currentIndex) continue;

          // 检查是否与其他项重复
          if (this.localList[i].value === value) {
            return true;
          }
        }
        return false;
      },
      validateDate(index) {
        const val = this.localList[index].value.trim();
        if (!val) {
          this.localList[index].valid = false;
          this.localList[index].tip = '';
          return;
        }
        if (this.isDuplicateDate(val, index)) {
          this.localList[index].valid = false;
          this.localList[index].tip = '❌ 编号重复，请检查';
          return;
        }
        this.localList[index].valid = true;
        this.localList[index].tip = '';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container {
    display: grid;
    // grid-template-columns: repeat(3, 1fr);
    gap: 4px;
  }
  .grad1 {
    grid-template-columns: repeat(1, 1fr);
  }
  .grad2 {
    grid-template-columns: repeat(2, 1fr);
  }
  .grad3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .grad4 {
    grid-template-columns: repeat(4, 1fr);
  }
</style>
