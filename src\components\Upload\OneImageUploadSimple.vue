<template>
  <div class="component-upload-image" :style="widthAndHeight">
    <!-- :http-request="upload" -->
    <el-upload
      action
      list-type="picture-card"
      :http-request="uploadHttp"
      :before-upload="handleBeforeUpload"
      :on-error="handleError"
      v-loading="loading"
      class="img-uploader"
      :show-file-list="false"
      :disabled="disabled"
      style="display: inline-block; vertical-align: top"
    >
      <el-image v-if="!modelValue" :src="modelValue">
        <div slot="error" class="image-slot">
          <i v-if="!disabled" class="el-icon-plus" />
          <b v-else>暂无</b>
        </div>
      </el-image>
      <div v-else class="image">
        <el-image :src="modelValue" class="pbImage" fit="fill" />
        <div class="mask">
          <div class="actions">
            <span @click.stop="dialogVisible = true" style="cursor: pointer">
              <i style="color: #fff; font-size: 20px" class="el-icon-zoom-in" />
            </span>
            <span v-if="!disabled" @click.stop="removeImage" style="cursor: pointer" :style="{ 'margin-left': width > 80 ? '15px' : '5px' }">
              <i style="color: #fff; font-size: 20px" class="el-icon-delete" />
            </span>
          </div>
        </div>
      </div>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img :src="modelValue" style="display: block; max-width: 100%; margin: 0 auto" />
    </el-dialog>
  </div>
</template>

<script>
  import { ossPrClient } from '@/api/alibaba';

  export default {
    name: 'OneImageUploadSimple',
    model: {
      prop: 'modelValue',
      event: 'update:modelValue'
    },
    props: {
      modelValue: { type: String, default: '' },
      disabled: { type: Boolean, default: false }, // 是否禁用
      width: { type: [Number, String], default: 100 }, // 上传框的宽
      height: { type: [Number, String], default: 100 }, // 上传框的高
      // 图片大小尺寸
      imgSize: {
        type: Number,
        default: 5 * 1024 * 1024 // 5M=>5*1024*1024 500KB=>500*1024
      },
      imgType: { type: Boolean, default: true } // 是否只上传jpg和png格式
    },
    computed: {
      // 动态显示MB或者KB
      isKbOrMb() {
        return this.imgSize / 1024 / 1024 >= 1 ? `${Number(this.imgSize / 1024 / 1024).toFixed(0)}MB` : `${Number(this.imgSize / 1024).toFixed(0)}KB`;
      },
      widthAndHeight() {
        return {
          '--height': this.height + 'px',
          '--width': this.width + 'px'
        };
      }
    },
    data() {
      return {
        loading: false,
        dialogVisible: false
      };
    },
    created() {
      ossPrClient();
    },
    methods: {
      uploadHttp({ file }) {
        console.log('file', file);
        this.loading = true;
        let suf = file.name.substring(file.name.lastIndexOf('.'));
        const fileName = 'manage/' + Date.parse(new Date()) + suf;
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              this.handleSuccess(this.aliUrl + name);
              this.loading = false;
            }
          })
          .catch((err) => {
            this.loading = false;
            this.$message.error('上传图片失败请检查网络或者刷新页面');
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      },

      handleBeforeUpload(file) {
        const isJPG = file.type === 'image/jpeg';
        const isPNG = file.type === 'image/png';
        const isGIF = file.type === 'image/gif';
        const isSize = file.size < this.imgSize; // 图片是否小于限定的尺寸
        if (this.imgType) {
          if (!isJPG && !isPNG) {
            this.$message.error('上传的图片只能是 JPG、JPEG、PNG格式');
            return false;
          }
        } else {
          if (!isJPG && !isPNG && !isGIF) {
            this.$message.error('上传的图片只能是 JPG、JPEG、PNG、GIF 格式');
            return false;
          }
        }

        // 图片是否小于限定的尺寸
        if (!isSize) {
          this.$message.error(`上传的图片大小不能超过 ${this.isKbOrMb}`);
          return false;
        }
        return (isJPG || isPNG || isGIF) && isSize;
      },
      handleSuccess(res) {
        this.$emit('update:modelValue', res || '');
      },

      removeImage() {
        this.$emit('update:modelValue', '');
      },
      // 图片上传失败
      handleError(err) {
        this.loading = false;
        this.$message.error('图片上传失败');
      }
    }
  };
</script>

<style scoped lang="scss">
  .image {
    position: relative;
    width: var(--width);
    height: var(--height);
    line-height: var(--height);
    .mask {
      background-color: rgba(0, 0, 0, 0);
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      cursor: default;
    }
    &:hover .mask {
      transition: all 0.3s;
      background-color: rgba(0, 0, 0, 0.5);
    }
    &:hover .actions {
      transition: all 0.3s;
      opacity: 1;
    }
  }
  .actions {
    width: var(--width);
    line-height: var(--height);
    opacity: 0;
  }

  .pbImage {
    width: var(--width);
    height: var(--height);
  }
  ::v-deep .el-upload--picture-card {
    width: var(--width) !important;
    height: var(--height) !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep .image-slot {
    display: flex !important;
  }
  ::v-deep .el-image__inner {
    object-fit: cover !important;
  }
</style>
