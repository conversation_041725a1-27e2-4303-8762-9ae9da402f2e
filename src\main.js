import Vue from 'vue';

import 'normalize.css/normalize.css'; // A modern alternative to CSS resets

import Element from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import 'element-ui/lib/theme-chalk/display.css';

import VideoPlayer from 'vue-video-player'; //视频播放
import 'video.js/dist/video-js.css'; //视频播放
import 'vue-video-player/src/custom-theme.css'; //视频播放

import '@/styles/index.scss'; // global css
import store from './store';
import App from './App';
import router from './router';
import print from './plugs/print'; // 打印机
import './icons'; // icon
import './errorLog'; // error log
import './permission'; // permission control
import * as filters from './filters'; // global filters
import VueAMap from 'vue-amap'; // 高德地图
import captcha from 'vue-social-captcha';
import { VuePlugin } from 'vuera';
import '@/components/business/index.js';
import '@/styles/icon/iconfont.css';
// 导入自定义表格组件
import DxTableComponents from '@/components/DxTable';
import '@/components/DxTable/dx-table.css';
// 导入自定义选择器组件
import DxSelectComponents from '@/components/DxSelect';
import '@/components/DxSelect/dx-select.css';
// 权限指令
import hasPerm from '@/directive/permission/hasPerm';
import perm from '@/directive/permission/perm';
import '@/filters/timeDown';
import '@/components/Dialog';
import DateRange from '@/components/DateRange';
import CustomDialog from '@/components/customDialog';
import getOperationWidth from './components/baseTable/getOperationWidth';

Vue.component('date-range', DateRange);
Vue.component('custom-dialog', CustomDialog);
//注册echarts
import * as echarts from 'echarts';
Vue.prototype.$echarts = echarts;

// 注册全局的权限判断方法和指令
Vue.prototype.$hasPerm = hasPerm;
Vue.directive('perm', perm);
//全局传参bus
const bus = new Vue();
Vue.prototype.$bus = bus;

let aggregationModule = JSON.parse(localStorage.getItem('aggregationModule'));

// 创建响应式全局变量-当前选中的聚合模块
Vue.prototype.$aggregationModule = Vue.observable(aggregationModule || { path: '', perm: '', name: '', redirect: '' });

// 注册全局方法-动态获取操作列宽度
Vue.prototype.$getOperationWidth = getOperationWidth;

Vue.use(Element, {
  size: 'small' // set element-ui default size
});
// 注册自定义表格组件
Vue.use(DxTableComponents);
// 注册自定义选择器组件
Vue.use(DxSelectComponents);
Vue.use(VideoPlayer); //视频播放
// Vue.use(VuePlugin)
// register global utility filters.
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});
if (process.env.NODE_ENV === 'production') {
  if (window) {
    window.console.log = function () { };
    window.console.log = function () { };
  }
}
// import htmlToPdf from '@/utils/htmlToPdf'
// Vue.use(htmlToPdf)
Vue.use(VuePlugin);
Vue.use(captcha);
Vue.use(VueAMap); //插件使用声明
//下面是vue-amap初始化，请将AMapKey换成你自己的key
VueAMap.initAMapApiLoader({
  key: 'dbd0d5d74d4fd5a36a6c43f56acc300f',
  plugin: [
    'AMap.Autocomplete',
    'AMap.PlaceSearch',
    'AMap.Scale',
    'AMap.OverView',
    'AMap.ToolBar',
    'AMap.MapType',
    'AMap.PolyEditor',
    'AMap.Geolocation',
    'AMap.Geocoder',
    'AMap.CircleEditor'
  ],
  v: '1.4.4'
});
Vue.config.productionTip = false;

Vue.prototype.aliUrl = 'https://document.dxznjy.com/'; //阿里上传图片后展示根目录

Vue.use(print);
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
});

// 思维导图
import jm from 'vue-jsmind';
Vue.use(jm);
if (window.jsMind) {
  Vue.prototype.jsMind = window.jsMind;
}

// 图片加载失败显示默认图片
import defaultImg from '@/utils/defaultImg';
Vue.directive('default-img', defaultImg);
// 图片懒加载
import lazyLoad from './directive/lazy';
Vue.directive('lazy', lazyLoad);

// 去除输入框首尾空格
import trim from './directive/trim';
Vue.directive('trim', trim);
// 禁止修改样式
import lockStyle from './directive/lock-style';
Vue.directive('lock-style', lockStyle);
