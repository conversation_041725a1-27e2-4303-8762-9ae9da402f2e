
import request from '@/utils/request'

export default {
  //题目列表
  questionList(data) {
    return request({
      url: '/calculation/web/question/list',
      method: 'GET',
      params: data
    })
  },
  //添加题目
  questionAdd(data) {
    return request({
      url: '/calculation/web/question',
      method: 'POST',
      data
    })
  },
  //编辑题目
  questionEdit(data){
    return request({
      url: '/calculation/web/question',
      method: 'PUT',
      data
    })
  },
  //题目详情
  questionDetail(id){
    return request({
      url: '/calculation/web/question',
      method: 'GET',
      params: {
        id:id
      }
    })
  },
  //删除题目
  questionDelete(id){
    return request({
      url: '/calculation/web/question',
      method: 'DELETE',
      params: {
        id:id
      }
    })
  },
  //获得题型 ：加减算、乘算 除算
  getCalculationType(){
    return request({
      url: '/calculation/web/question/calculationType',
      method: 'GET',
    })
  },
  //获取题目:对补数，音视频题....
  getQuestionType(type){
    return request({
      url: '/calculation/web/question/questionType',
      method: 'GET',
      params: {
        type:type
      }
    })
  },
  //更新题目排序
  updateOrderNum(params) {
    return request({
        url: '/calculation/web/course/updateOrderNum?'+params,
        method: 'POST'
    })
  },

  //课程列表
  courseList(data) {
    return request({
      url: '/calculation/web/course/list',
      method: 'GET',
      params: data
    })
  },
  //新增课程
  courseAdd(data) {
    return request({
      url: '/calculation/web/course',
      method: 'POST',
       data
    })
  },
  courseDetails(id) {
    return request({
      url: '/calculation/web/course',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
  //删除课程
  courseDelete(id){
    return request({
      url: '/calculation/web/course',
      method: 'DELETE',
      params: {
        id:id
      }
    })
  },

  //课程下个题目删除
  courseQuesDelete(id){
    console.log(id)
    return request({
      url: '/calculation/web/course/deleteQuestion',
      method: 'DELETE',
      params: {
        id:id
      }
    })
  },
  //课程下个题目新增
  courseQuesAdd(data){
    return request({
      url: '/calculation/web/course/addQuestion',
      method: 'POST',
      data
    })
  },
  //课程下题目列表
  courseQuesList(data){
    return request({
      url: '/calculation/web/course/questionList',
      method: 'GET',
      params: data
    })
  },



  //保利威 上传视频
  getSign() {
    return request({
      url: '/media/web/video/getUploadSign',
      method: 'get'
    })
  }
}

