import request from "@/utils/request";
// 视频管理接口

// 获取课程大类列表
export function getAllCourseTypeList(data) {
  return request({
    url: "znyy/curriculum/chinese",
    method: "GET",
    params: data
  });
}

// 获取课程版本列表
export function getCourseVersionList(data) {
  return request({
    url: '/dyw/web/chinese/basisConfig/selectVersionInfo',
    method: 'GET',
    params:data
  })
}

// 查询课程分类树及版本(需要版本ID)
export function getCourseTreeData(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/selectTreeVersion',
    method: 'GET',
    params:data
  })
}

// 视频分页数据查询
export function getVideoList(data) {
  return request({
    url: '/dyw/web/chinese/video/page',
    method: 'GET',
    params:data
  })
}

// 新增编辑视频
export function addEditVideo(data) {
  return request({
    url: '/dyw/web/chinese/video/addOrUpdate',
    method: 'POST',
    data
  })
}

// 视频vid 校验
export function videoVidCheck(data) {
  return request({
    url: '/dyw/web/chinese/video/vid',
    method: 'GET',
    params:data
  })
}

// 删除视频
export function deleteVideo(data) {
  return request({
    url: '/dyw/web/chinese/video/delete',
    method: 'DELETE',
    params: data
  })
}

// 获取视频详情
export function getVideoDetail(data) {
  return request({
    url: '/dyw/web/chinese/video/detail',
    method: 'GET',
    params: data
  })
}

// 添加修改视频节点
export function addEditVideoNodes(data) {
  return request({
    url: '/dyw/web/chinese/videoNodes/insertOrUpdate',
    method: 'POST',
    data
  })
}

// 视频节点分页查询
export function getVideoNodesList(data) {
  return request({
    url: '/dyw/web/chinese/videoNodes/pagelist',
    method: 'GET',
    params: data
  })
}

// 根据vid 查询视频详情
export function getVideodetailByVid(vid) {
  return request({
    url: `/media/web/video/details/${vid}`,
    method: 'get',
  });
}

// 视频模板导入
export function importVideo(data) {
  return request({
    url: '/dyw/web/chinese/video/import',
    method: 'POST',
    data
  })
}
