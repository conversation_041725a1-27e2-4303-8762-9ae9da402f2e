/**
 * 商品相关接口
 */
import request from '@/utils/request'

export default {

  // 录播新增
  addBroadcast(data) {
    return request({
      url: '/znyy/broadcast/addBroadcast',
      method: 'POST',
      data
    })
  },
  // banner图新增
  addBanner(url) {
    return request({
      url: '/znyy/broadcast/addBanner?url='+url,
      method: 'GET',
    })
  },
  // banner查看
  bannerUpdate(id) {
    return request({
      url: '/znyy/broadcast/getBanner/' + id,
      method: 'GET'
    })
  },
  // banner修改
  editBanner(id,url) {
    return request({
      url: '/znyy/broadcast/editBanner?id='+id+'&&url='+url,
      method: 'GET',
    })
  },
  // 录播修改
  updateBroadcastById(data) {
    return request({
      url: '/znyy/broadcast/editBroadcast',
      method: 'POST',
      data
    })
  },
  // 录播查询
  queryBroadcast(id) {
    return request({
      url: '/znyy/broadcast/getBroadcast/' + id,
      method: 'GET'
    })
  },
  // 商品删除
  deleteBroadcast(id) {
    return request({
      url: '/znyy/broadcast/deleteBroadcast/' + id,
      method: 'DELETE'
    })
  },
  //删除
  deleteBanner(id) {
    return request({
      url: '/znyy/broadcast/deleteBanner/'+id,
      method: 'DELETE'
    })
  },
  // 录播分页查询
  broadcastList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/broadcast/getBroadcastList/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  bannerList() {
    return request({
      url: '/znyy/broadcast/getBannerList',
      method: 'GET'
    })
  },
  getFunReviewCode() {
    return request({
      url: '/znyy/broadcast/getFunReviewCode',
      method: 'GET'
    })
  },
}
