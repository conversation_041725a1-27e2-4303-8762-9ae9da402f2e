<template>
  <div id="contractSigningDialog">
    <!-- 合同签署流程弹窗 -->
    <el-dialog
      v-if="canShow"
      id="contractSigningDialog1"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="canClose"
      :show-close="canClose"
      destroy-on-close
      width="45%"
    >
      <div v-if="showConfirmUserInfo" slot="title" class="title">
        <div class="title-row" style="">因合同签署需使用与用户信息一致的实名认证的微信，为方便您完成合同签署</div>
        <div class="title-row">
          请于
          <span class="title-row-days" style="font-size: 24px">{{ remainingDays }}</span>
          天内核对用户信息，并生成合同完成签署
        </div>
        <div class="title-row">如有信息错误，可修改后再进行合同生成，请尽快处理</div>
      </div>
      <div v-else slot="title" style="color: red; text-align: center; padding: 0 20px">
        <div v-if="isChangeRelationshipContract && isChangeRelationshipInitiator">
          <template v-if="isOperations">
            <div class="contract-reminder">
              请在{{ remainingDays }}天内完成以下的合同签署
              <br />
              当您与新{{ contractInfo[0].signName }}完成《{{ contractInfo[0].name }}》签署后，{{ contractInfo[0].signName }}才可变更成功
            </div>
          </template>
          <template v-else-if="isSchool">
            <div class="contract-reminder">
              请在{{ remainingDays }}天内完成以下的合同签署
              <br />
              当原归属俱乐部与新归属俱乐部完成《转让合同》签署、您与新{{ contractInfo[0].signName }}完成《{{ contractInfo[0].name }}》签署后，归属俱乐部和{{
                contractInfo[0].signName
              }}才可变更成功
            </div>
          </template>
        </div>
        <div v-else>我们将在{{ remainingDays }}天后关闭您的学习管理系统使用权限，请尽快完成以下全部合同签署，以免影响后续使用</div>
      </div>
      <div v-if="showConfirmUserInfo" class="content">
        <el-form class="user-info-form" ref="userInfoFormRef" :model="userInfoForm" :rules="userInfoRules" label-position="left" label-width="150px">
          <el-form-item label="负责人姓名" prop="contractHeadName" class="user-info-form-item">
            <div v-show="canEditUserInfo">
              <el-input v-model.trim="userInfoForm.contractHeadName" placeholder="请输入负责人姓名" />
            </div>
            <div v-show="!canEditUserInfo" class="user-info-form-item-text">
              {{ userInfoForm.contractHeadName }}
            </div>
          </el-form-item>
          <el-form-item label="手机号" prop="contractHeadPhone" class="user-info-form-item">
            <div v-show="canEditUserInfo">
              <el-input v-model.trim="userInfoForm.contractHeadPhone" placeholder="请输入手机号" oninput="value=value.replace(/[^\d]/g, '')" />
            </div>
            <div v-show="!canEditUserInfo" class="user-info-form-item-text">
              {{ userInfoForm.contractHeadPhone }}
            </div>
          </el-form-item>
          <el-form-item label="身份证号码" prop="contractHeadIcard" class="user-info-form-item">
            <div v-show="canEditUserInfo">
              <el-input v-model.trim="userInfoForm.contractHeadIcard" placeholder="请输入身份证号码" />
            </div>
            <div v-show="!canEditUserInfo" class="user-info-form-item-text">
              {{ userInfoForm.contractHeadIcard }}
            </div>
          </el-form-item>
          <el-form-item v-if="isOperations" label="企业名称" prop="businessName" class="user-info-form-item">
            <div v-show="canEditUserInfo">
              <el-input v-model.trim="userInfoForm.businessName" placeholder="请输入企业名称" />
            </div>
            <div v-show="!canEditUserInfo" class="user-info-form-item-text">
              {{ userInfoForm.businessName }}
            </div>
          </el-form-item>
          <div v-if="canEditUserInfo" style="text-align: center">
            <el-button size="large" style="margin-right: 55px; width: 98px" @click="handleCancelSaveClick" :disabled="userInfoFormLoading">取消</el-button>
            <el-button type="primary" size="large" style="width: 98px" :loading="userInfoFormLoading" @click="handleSaveClick(0)">保存</el-button>
          </div>
          <div v-else style="text-align: center">
            <el-button size="large" style="margin-right: 55px" @click="handleShowEditClick">修改信息</el-button>
            <el-button type="primary" size="large" @click="handleGenerateContractClick">合同生成</el-button>
          </div>
          <div style="color: red; font-size: 14px; text-align: center; margin-top: 16px">
            <el-link type="danger" href="https://minio-api.dxznjy.com/test1/如何查看微信实名.pdf" target="_blank">如何查看微信实名？</el-link>
          </div>
        </el-form>
      </div>
      <div v-else class="content">
        <el-steps v-if="!isChangeRelationshipContract" class="contract-signing-steps" :active="contractSigningActive" :space="370" finish-status="success" process-status="finish">
          <el-step
            :title="contractSigningActive === 0 ? '进行中的步骤' : contractSigningActive < 0 ? '未完成的步骤' : '已完成的步骤'"
            :description="'请完成《' + contractInfo[0].name + '》签署'"
          />
          <el-step
            :title="contractSigningActive === 1 ? '进行中的步骤' : contractSigningActive < 1 ? '未完成的步骤' : '已完成的步骤'"
            :description="'请完成《' + contractInfo[1].name + '》签署'"
          />
        </el-steps>
        <div v-if="contractInfo.length > contractSigningActive" class="contract-signing-content">
          <VerifyCode
            ref="childRef"
            :VerifyCodeData="contractInfo[contractSigningActive]"
            :verifyCodeDisabled="sendVerificationVodeStatus"
            :fackContactStatus="fackContactStatus"
            :isChangeRelationshipContract="isChangeRelationshipContract"
            :countdownSeconds="countdownSeconds"
            @handle-refresh="handleRefreshQrCodeClick"
            @send-code="handleSendCode"
            @input="handleCodeInput"
            v-if="contractInfo[contractSigningActive].isFakeFlow"
          />
          <div v-else>
            <div class="contract-signing-content-title">
              您将作为{{ contractInfo[contractSigningActive].signatory
              }}{{ contractInfo[contractSigningActive].signName ? '与' + contractInfo[contractSigningActive].signName : '' }}签署
            </div>
            <div class="contract-signing-content-name">《{{ contractInfo[contractSigningActive].name }}》</div>
            <div class="contract-signing-content-qrcode">
              <div v-if="contractInfo[contractSigningActive].showQrCodeTips">
                <div class="contract-signing-content-qrcode-refresh-icon" @click="handleRefreshQrCodeClick">
                  <i class="el-icon-refresh-right" style="color: #606266" />
                </div>
                <div class="contract-signing-content-qrcode-refresh-text">
                  {{ contractInfo[contractSigningActive].showQrCodeTipsText }}
                </div>
              </div>
              <div v-else class="contract-signing-content-qrcode-img" v-loading="contractQRLoading" @click="handleRefreshQrCodeClick">
                <el-image
                  :Loading="contractQRImageLoading"
                  @error.once="logErrorImage"
                  style="width: 100%; height: 100%"
                  fit="fill"
                  :src="contractInfo[contractSigningActive].qrUrl"
                ></el-image>
              </div>
            </div>
          </div>

          <div>
            <div>
              <el-tooltip popper-class="tips" content="Top Center" placement="top">
                <div slot="content">
                  修改用户信息后需
                  <br />
                  重新生成并签署
                </div>
                <el-button
                  v-if="!isChangeRelationshipContract"
                  size="small"
                  @click="
                    () => {
                      this.modifyUserVisible = true;
                      this.oldUserInfoForm = { ...this.userInfoForm };
                    }
                  "
                >
                  修改用户信息
                </el-button>
              </el-tooltip>
              <el-button type="primary" size="small" @click="handleNextContractClick(true)" v-if="isChangeRelationshipContract && contractInfo[contractSigningActive].isFakeFlow">
                提交
              </el-button>
              <el-button v-else type="primary" size="small" @click="handleNextContractClick(true)">
                {{
                  contractInfo[contractSigningActive].isFakeFlow
                    ? contractSigningActive == 1
                      ? '已完成'
                      : '下一步'
                    : contractSigningActive == 1 || isChangeRelationshipContract
                    ? '已签署'
                    : '下一步'
                }}
              </el-button>
            </div>
            <div style="font-size: 14px; text-align: center; margin-top: 16px">
              {{ showTips }}
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 合同签署流程-确认用户信息弹窗 -->
    <el-dialog
      v-if="canShow"
      :visible.sync="contractConfirmDialogVisible"
      width="40%"
      top="20vh"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      destroy-on-close
    >
      <div slot="title" style="font-size: 20px">请确认是否用以下信息发起合同并签署</div>
      <div class="contract-confirm-dialog">
        <div class="contract-confirm-dialog-row">
          <div class="contract-confirm-dialog-row-label">负责人姓名</div>
          <div>{{ userInfoForm.contractHeadName }}</div>
        </div>
        <div class="contract-confirm-dialog-row">
          <div class="contract-confirm-dialog-row-label">手机号</div>
          <div>{{ userInfoForm.contractHeadPhone }}</div>
        </div>
        <div class="contract-confirm-dialog-row">
          <div class="contract-confirm-dialog-row-label">身份证号码</div>
          <div>{{ userInfoForm.contractHeadIcard }}</div>
        </div>
        <div v-if="isOperations" class="contract-confirm-dialog-row">
          <div class="contract-confirm-dialog-row-label">企业名称</div>
          <div>{{ userInfoForm.businessName }}</div>
        </div>
        <template v-if="isOperations">
          <div class="contract-confirm-dialog-row">
            <div class="contract-confirm-dialog-row-label">归属品牌</div>
            <div v-if="userInfoForm.brandName">{{ userInfoForm.brandName }} 【{{ userInfoForm.brandRole }}】</div>
          </div>
          <div class="contract-confirm-dialog-row">
            <div class="contract-confirm-dialog-row-label">渠道合作伙伴</div>
            <div v-if="userInfoForm.operationsName">{{ userInfoForm.operationsName }} 【{{ userInfoForm.operationsRole }}】</div>
          </div>
        </template>
        <template v-if="isSchool">
          <div class="contract-confirm-dialog-row">
            <div class="contract-confirm-dialog-row-label">归属俱乐部</div>
            <div v-if="userInfoForm.clubName">{{ userInfoForm.clubName }} 【{{ userInfoForm.clubRole }}】</div>
          </div>
          <div class="contract-confirm-dialog-row">
            <div class="contract-confirm-dialog-row-label">课程推广大使</div>
            <div v-if="userInfoForm.coursePromotionName">{{ userInfoForm.coursePromotionName }} 【{{ userInfoForm.coursePromotionRole }}】</div>
          </div>
        </template>
        <div v-if="isOperations && !userInfoForm.businessName" style="color: red; font-size: 14px; width: 635px; margin: 16px auto 0">
          您未填写企业名称，将以个人身份进行合同生成，请在合同签署完成后的30天内，签署俱乐部转让协议
        </div>
        <div style="text-align: center; margin-top: 20px">
          <el-button size="large" style="margin-right: 55px" @click="closeContractConfirmDialogVisible()" :disabled="addContractOneLoading">取消</el-button>
          <el-button type="primary" size="large" :loading="addContractOneLoading" @click="handleGenerateContractConfirmClick">确认</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 上级变更通知确认弹窗 -->
    <el-dialog
      v-if="changeNoticeDialogVisible"
      id="changeNoticeConfirmDialog"
      :visible.sync="changeNoticeDialogVisible"
      width="35%"
      top="20vh"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      destroy-on-close
    >
      <div class="change-notice-dialog">
        <div class="content">
          鉴于您原属团队的经营规划发生变更，经
          <span class="superiorName">[{{ changeNoticeInfo.oldSuperiorName }}]</span>
          与
          <span class="superiorName">[{{ changeNoticeInfo.superiorName }}]</span>
          协商一致并签订《转让协议》，现将您调至
          <span class="superiorName">[{{ changeNoticeInfo.superiorName }}]</span>
          团队。希望您积极配合新团队的工作，共同保障业务的连续性与稳定性。
        </div>
        <div class="foot">
          <el-button type="primary" :loading="changeNoticeConfirmLoading" @click="handleChangeNoticeConfirmClick">我已知晓并同意</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 修改用户信息弹窗 -->
    <el-dialog
      v-if="canShow"
      :visible.sync="modifyUserVisible"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      destroy-on-close
      class="no-header-dialog"
    >
      <div class="modifyUserVisible_title">修改用户信息</div>
      <div class="modifyUserVisible_title text_colcor">用户信息修改后，所有合同需重新生成并签署并</div>
      <div class="modifyUserVisible_title text_colcor">需要由您支付重新生成的合同费用</div>
      <div class="modifyUserVisible_form">
        <el-form class="user-info-form" ref="userInfoFormRef" :model="userInfoForm" :rules="userInfoRules" label-position="left" label-width="150px">
          <el-form-item label="负责人姓名" prop="contractHeadName" :label-style="{ fontWeight: 'bold' }" class="user-info-form-item">
            <el-input v-model.trim="userInfoForm.contractHeadName" :label-style="{ fontWeight: 'bold' }" placeholder="请输入负责人姓名" />
          </el-form-item>
          <el-form-item label="手机号" prop="contractHeadPhone" :label-style="{ fontWeight: 'bold' }" class="user-info-form-item">
            <el-input v-model.trim="userInfoForm.contractHeadPhone" placeholder="请输入手机号" oninput="value=value.replace(/[^\d]/g, '')" />
          </el-form-item>
          <el-form-item label="身份证号码" prop="contractHeadIcard" :label-style="{ fontWeight: 'bold' }" class="user-info-form-item">
            <el-input v-model.trim="userInfoForm.contractHeadIcard" placeholder="请输入身份证号码" />
          </el-form-item>
          <el-form-item v-if="isOperations" :label-style="{ fontWeight: 'bold' }" label="企业名称" prop="businessName" class="user-info-form-item">
            <el-input v-model.trim="userInfoForm.businessName" placeholder="请输入企业名称" />
          </el-form-item>
          <div style="text-align: center">
            <el-button :disabled="userInfoFormLoading" @click="closeModifyUserInfoDialog()">取 消</el-button>
            <el-button type="primary" :loading="userInfoFormLoading" @click="modifyUserInfo()">确认修改</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <purchaseContract ref="contractSpurchaseDialog" style="z-index: 9999 !important"></purchaseContract>
  </div>
</template>
<script>
  import purchaseContract from '@/components/purchaseContract/index.vue';
  import VerifyCode from '@/components/OrderRFefund/VerifyCode.vue';
  import checkPermission from '@/utils/permission';
  import { sendContractVerifyCode, verifyContractCode } from '@/api/verifyCode';
  import {
    getChangeNoticeStatusData,
    setChangeNoticeStatus,
    getContractSigningDialogShowStatus,
    getNeedSigningContractStatusAndDays,
    getContractUserInfo,
    checkEnterpriseName,
    updateContractUserInfo,
    checkContractUserInfo,
    addContractOne,
    addContractTwo,
    addChangeRelationshipContract,
    getContractQRUrl,
    getContractSigingStatus,
    checkedVerificationVodeStatus
  } from '@/api/dialog/contractSigningDialog';

  let observer = null;
  export default {
    name: 'contractSigningDialog',
    data() {
      return {
        contractQRImageLoading: false,
        sendVerificationVodeStatus: false, // 是否发送验证码
        fackContactStatus: 2, // 0:未生成 1:生成失败 2:生成成功
        countdownSeconds: 60,
        originalData: {}, // 原始数据进行数据对比
        verify: '', // 验证码
        isOperations: false, // 是否是俱乐部(Operations)角色
        isSchool: false, // 是否是门店(School)角色
        canShow: false,
        visible: false,
        canClose: true,
        showConfirmUserInfo: true, //展示合同用户信息
        remainingDays: 7,
        // 用户信息表单显示状态
        canEditUserInfo: false,
        // 用户信息保存按钮加载状态
        userInfoFormLoading: false,
        // 用户信息修改前表单
        oldUserInfoForm: {},
        // 用户信息当前表单
        userInfoForm: {
          contractHeadName: '',
          contractHeadPhone: '',
          contractHeadIcard: '',
          businessName: ''
        },
        userInfoRules: {
          contractHeadName: [
            { required: true, message: '负责人姓名不可为空', trigger: 'blur' },
            { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
          ],
          contractHeadPhone: [
            { required: true, message: '手机号不可为空', trigger: 'blur' },
            { pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/, message: '手机号的格式错误', trigger: 'blur' },
            {
              len: 11,
              message: '手机号长度必须为11位',
              trigger: 'blur'
            }
          ],
          contractHeadIcard: [
            { required: true, message: '身份证号码不可为空', trigger: 'blur' },
            {
              len: 18,
              message: '身份证号码长度必须为18位',
              trigger: 'blur'
            },
            {
              pattern:
                /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/,
              message: '身份证号码的格式错误',
              trigger: 'blur'
            }
          ]
        },
        //修改用户弹窗
        modifyUserVisible: false,
        // 合同确认弹窗
        contractConfirmDialogVisible: false,
        // 二次确认弹窗确认按钮加载状态
        addContractOneLoading: false,
        // 合同签署相关数据
        contractSigningActive: 0, // 当前签署步骤
        // 轮询合同签署ID
        pollingStatusId: null,
        pollingStatusStopId: null,
        contractQRLoading: false,
        contractInfo: [],
        // 是否需要检查企业转让合同弹窗
        needCheckEnterpriseRransferContractDialog: false,
        // 变更上级关系合同弹窗相关数据
        changeRelationshipType: undefined, // 变更类型 0-无变更 1-变更上下级 2-变更推荐人
        changePeriodStatus: undefined, // 是否在变更期 0-变更失败 1-变更成功 2-在变更期 3-未在变更期 且非变更链
        isChangeRelationshipContract: true, // 是否为变更上级关系合同
        isChangeRelationshipInitiator: true, // 是否为变更上级关系发起方
        // 变更上级关系通知弹窗相关数据
        changeNoticeDialogVisible: false,
        changeNoticeConfirmLoading: false,
        changeNoticeInfo: {
          superiorName: '',
          oldSuperiorName: ''
        }
      };
    },
    components: {
      VerifyCode,
      purchaseContract
    },
    mounted() {
      // 可以在这里添加自定义样式，彻底隐藏标题栏
      const style = document.createElement('style');
      style.textContent = `
        .no-header-dialog .el-dialog__header {
          display: none;
        }
      `;
      document.head.appendChild(style);
    },
    watch: {
      visible(val) {
        if (!val) {
          this.canShow = false;
          this.$refs.contractSpurchaseDialog?.closeDialog(); // 关闭合同购买弹窗
          this.clearOneContractPolling();
          this.needCheckEnterpriseRransferContractDialog || localStorage.setItem('loginContractSigningFlag', 1);
          // 停止观察
          observer && observer.disconnect();
          this.$emit('signingEnd');
        }
      }
    },
    computed: {
      signType() {
        let signType = 0;
        if (this.isChangeRelationshipContract) {
          if (this.isOperations) {
            signType = 2;
          } else if (this.isSchool) {
            signType = 3;
          }
        } else {
          if (this.contractSigningActive == 0) {
            /**
             * 步骤1
             * 上下级/推荐人信息一致，上下级/推荐人信息不一致且未生成合同  type = 0
             * 推荐人信息不一致 生成合同 type = 1
             *
             * */
            signType = this.contractSigningActive;
            if (this.contractInfo[this.contractSigningActive].flowId) {
              signType = 1;
            }
          } else if (this.contractSigningActive == 1) {
            /**
             * 步骤2
             * 上下级/推荐人信息一致，上下级/推荐人信息不一致且未生成合同  type = 1
             * 推荐人信息不一致 生成合同 且为俱乐部 type = 2
             * 推荐人信息不一致 生成合同 且为门店 type =3
             * */
            signType = this.contractSigningActive;
            if (this.contractInfo[this.contractSigningActive].flowId) {
              if (this.isOperations) {
                signType = 2;
              } else if (this.isSchool) {
                signType = 3;
              }
            }
          }
        }
        return signType;
      },
      showTips() {
        let string = '';
        let isFakeFlow = this.contractInfo[this.contractSigningActive].isFakeFlow;
        if (!isFakeFlow && this.isChangeRelationshipContract) {
          string = '签署完成后，请点击该按钮';
        } else if (isFakeFlow && this.isChangeRelationshipContract) {
          string = '';
        } else {
          const action = isFakeFlow ? '验证' : '签署';
          let buttonText = '';
          if (this.contractSigningActive === 0) {
            buttonText = '下一步按钮';
          } else if (this.contractSigningActive === 1) {
            buttonText = '完成按钮';
          }
          string = `${action}完成后，请点击${buttonText}`;
        }
        return string;
      }
    },
    async created() {
      // 检查登录用户是否有俱乐部权限
      this.isOperations = checkPermission(['Operations']);
      // 检查登录用户是否有合伙人权限
      this.isSchool = checkPermission(['School']);
      if (this.isOperations || this.isSchool) {
        // 检查是否有合同变更通知
        this.changeNoticeDialogVisible = await this.checkChangeNotice();
        console.log('检查是否有合同变更通知', this.changeNoticeDialogVisible);
        if (this.changeNoticeDialogVisible) {
          this.initMutationObserver();
        } else {
          // 允许显示合同签署弹窗，且登录用户为俱乐部或门店角色时，请求后端
          this.checkContractSigningStatus();
        }
      }
    },
    beforeDestroy() {
      console.log('contractSigningDialog beforeDestroy');
      this.clearOneContractPolling();
      // 停止观察
      observer && observer.disconnect();
    },
    methods: {
      logErrorImage(e) {
        console.log('🚀 ~ logErrorImage ~ e:', e);
        // 先保存原始URL
        const originalUrl = this.contractInfo[this.contractSigningActive].qrUrl;
        if (originalUrl) {
          //二维码图片渲染失败后再次赋值
          this.$set(this.contractInfo[this.contractSigningActive], 'qrUrl', originalUrl);
          this.$forceUpdate();
        }
      },
      /**
       *  判断上下级是否签署
       * 变更合同默认可以发送验证码
       *  判断非变更合同能否发送验证码
       */
      checkedVerification() {
        if (this.isChangeRelationshipContract) {
          this.sendVerificationVodeStatus = true;
        } else {
          let data = {
            merchantCode: this.contractInfo[this.contractSigningActive].merchantCode
          };
          checkedVerificationVodeStatus(data).then((res) => {
            this.sendVerificationVodeStatus = res.data;
          });
        }
      },
      /**
       * 发送验证码
       */
      async handleSendCode() {
        let params = { merchantCode: this.contractInfo[this.contractSigningActive].merchantCode };
        try {
          await sendContractVerifyCode(params);
          this.$message.success('验证码发送成功');
        } catch (error) {
          this.$message.warning('验证码发送失败');
          throw new Error('发送失败');
        }
      },

      /**
       * 获取验证码
       */
      handleCodeInput(value) {
        this.verify = value;
      },

      /**
       * 检查上级关系变更状态
       */
      async checkChangeNotice() {
        try {
          const result = await getChangeNoticeStatusData();
          this.changeNoticeInfo = {
            superiorName: result.data?.newSuperiorName,
            oldSuperiorName: result.data?.originalSuperiorName
          };
          return this.changeNoticeInfo.superiorName && this.changeNoticeInfo.oldSuperiorName;
        } catch (error) {
          return false;
        }
      },
      /**
       * 处理知晓并同意上级关系变更按钮点击事件
       */
      handleChangeNoticeConfirmClick() {
        this.changeNoticeConfirmLoading = true;
        setChangeNoticeStatus()
          .then(() => {
            this.changeNoticeDialogVisible = false;
            this.checkContractSigningStatus();
          })
          .finally(() => {
            this.changeNoticeConfirmLoading = false;
          });
      },
      /**
       * 检查相关合同是否需要签署
       */
      async checkContractSigningStatus() {
        // 获取是否显示合同签署弹窗状态
        if (localStorage.getItem('loginContractSigningFlag')) return;
        try {
          // 获取是否显示合同签署弹窗开关状态
          const res = await getContractSigningDialogShowStatus();
          this.canShow = res.data.studyContractSwitch && res.data.brandWhiteListSwitch;
        } catch (error) {
          this.canShow = false;
        }
        if (!this.canShow) {
          this.$emit('signingEnd');
          return;
        }

        /**
         * 获取合同签署状态和剩余天数
         * 获取合同签署用户信息
         */
        const promisesArray = [getNeedSigningContractStatusAndDays(), this.handleContractUserInfo()];
        Promise.all(promisesArray)
          .then(([data, userInfo]) => {
            this.handleIsChangeRelationsData(data.data);
            if (this.isChangeRelationshipContract) {
              this.handleCheckChangeRelationshipContract(data.data, userInfo);
            } else {
              this.handleCheckStudyContract(data.data, userInfo);
            }
          })
          .catch(() => {
            this.init({});
          });
      },
      /**
       * 判断是否为变更上下级关系的合同
       * */
      handleIsChangeRelationsData(data) {
        this.changeRelationshipType = data.changeType;
        this.changePeriodStatus = data.isInChangePeriod;
        this.isChangeRelationshipContract = (this.changePeriodStatus == 1 && this.changeRelationshipType == 1) || this.changePeriodStatus == 2;
        this.isChangeRelationshipInitiator = this.changePeriodStatus == 2;
        console.log('changeType', this.changeRelationshipType);
        console.log('isInChangePeriod', this.changePeriodStatus);
        console.log('走变更合同流程', this.isChangeRelationshipContract);
        console.log('是变更发起方', this.isChangeRelationshipInitiator);
      },
      /**
       * 变更状态
       * 处理判断变更上下级相关合同状态
       */
      handleCheckChangeRelationshipContract(data, userInfo) {
        let needVisible = false;

        // 获取变更上下级需要签署的合同状态
        const changeRelationshipContractSign = this.isOperations ? data.channelContractSign : this.isSchool && data.promotionContractSign;
        console.log('处理判断变更上下级相关合同状态', data);

        // 判断变更上下级需要签署的合同是否已生成
        if (changeRelationshipContractSign) {
          // 判断变更上下级需要签署的合同是否未签署
          if (this.isOperations ? data.channelSignStatus !== 2 : this.isSchool && data.promotionSignStatus !== 2) {
            // 变更上下级相关合同已生成，未签署，需要弹窗展示合同二维码
            needVisible = true;
            this.showConfirmUserInfo = false;
            this.contractSigningActive = 0;
          }
        } else {
          // 变更上下级需要签署的合同未生成，需要确认用户信息
          needVisible = true;
          this.showConfirmUserInfo = true;
          this.contractSigningActive = 0;
        }
        this.init({ needVisible, data, userInfo });
      },
      /**
       * 非变更状态
       * 处理判断学习管理相关合同状态
       */
      async handleCheckStudyContract(data = {}, userInfo) {
        let needVisible = false;

        // 判断学习管理合同是否已生成
        if (data.studyContractSign) {
          // 判断学习管理合同是否已签署

          if (data.studySignStatus === 2) {
            // 学习管理合同已签署，检查第二份合同状态
            const secondContractNotSigned = this.isOperations
              ? !data.channelContractSign || data.channelSignStatus !== 2
              : this.isSchool && (!data.promotionContractSign || data.promotionSignStatus !== 2);
            if (secondContractNotSigned) {
              // 第二份合同未签署，需要弹窗，直接显示第二份合同签署内容
              needVisible = true;
              this.showConfirmUserInfo = false;
              this.contractSigningActive = 1;
            }
          } else {
            // 学习管理合同未签署，需要弹窗，直接显示第一份合同签署内容
            needVisible = true;
            this.showConfirmUserInfo = false;
            this.contractSigningActive = 0;
          }
        } else {
          // 学习管理合同未生成，需要弹窗
          needVisible = true;
          this.showConfirmUserInfo = true;
          this.contractSigningActive = 0;
        }
        this.init({ needVisible, data, userInfo });
      },
      async init({ needVisible = false, data = {}, userInfo = {} }) {
        let contractProp = '';
        // 初始化合同签署信息
        if (this.isOperations) {
          if (this.isChangeRelationshipContract) {
            this.contractInfo = [
              {
                signatory: '乙方',
                signName: '渠道合作伙伴',
                name: '新渠道合作伙伴合同',
                qrUrl: '',
                flowId: data.channelFlowId,
                isFakeFlow: data.channelIsFake, // 是否是假合同
                isFirstParty: 1,
                showQrCodeTips: false,
                showQrCodeTipsText: '',
                isUpdateContractInfo: userInfo.isUpdateContractInfo, //是否修改
                recommend: userInfo.sameRecommend, //推荐信息是否相同
                merchantCode: userInfo.operationsMerchantCode, // 归属俱乐部商户号
                phone: userInfo.operationsPhone, // 归属俱乐部手机号,
                parentPhoneLabel: '渠道合作伙伴手机号',
                title: this.isChangeRelationshipInitiator
                  ? data.channelSignPeopleType != 1
                    ? '系统已识别您的企业信息与渠道合作伙伴的企业信息一致，因此无需签署本合同，完成以下校验即可继续操作'
                    : '系统已识别您的用户信息与渠道合作伙伴的用户信息一致，因此无需签署本合同，完成以下校验即可继续操作'
                  : '系统已识别您的企业信息与所属品牌的企业信息一致，因此无需签署本合同，完成以下校验即可继续操作'
              }
            ];
            this.sendVerificationVodeStatus = true;
          } else {
            this.contractInfo = [
              {
                signatory: '乙方',
                signName: '',
                name: '超级俱乐部经销商销售合同',
                qrUrl: '',
                flowId: data.studyFlowId,
                isFakeFlow: data.studyIsFake,
                isFirstParty: 1,
                showQrCodeTips: false,
                showQrCodeTipsText: '',
                isUpdateContractInfo: userInfo.isUpdateContractInfo, //是否修改
                recommend: userInfo.sameParentAndChild, //上下级信息是否相同
                merchantCode: userInfo.brandMerchantCode, // 归属品牌商户号
                phone: userInfo.brandPhone, // 归属品牌手机号,
                parentPhoneLabel: '品牌手机号',
                title: '系统已识别您的企业信息与所属品牌的企业信息一致，因此无需签署本合同，完成以下校验即可继续操作'
              },
              {
                signatory: '乙方',
                signName: '渠道合作伙伴',
                name: '渠道合作伙伴合同',
                qrUrl: '',
                flowId: data.channelFlowId,
                isFakeFlow: data.channelIsFake,
                isFirstParty: 1,
                showQrCodeTips: false,
                showQrCodeTipsText: '',
                recommend: userInfo.sameRecommend, // 推荐信息是否相同
                merchantCode: userInfo.operationsMerchantCode, //渠道合作伙伴商户号
                phone: userInfo.operationsPhone, //课程推广大使手机号
                parentPhoneLabel: '渠道合作伙伴手机号',
                title:
                  data.channelSignPeopleType != 1
                    ? '系统已识别您的企业信息与渠道合作伙伴的企业信息一致，因此无需签署本合同，完成以下校验即可继续操作'
                    : '系统已识别您的用户信息与渠道合作伙伴的用户信息一致，因此无需签署本合同，完成以下校验即可继续操作',
                tooltip: `您的渠道合作伙伴俱乐部未签署《超级俱乐部经销商销售合同》<br>请等待其签署后重试`
              }
            ];
          }
          contractProp = 'channel';
        } else if (this.isSchool) {
          if (this.isChangeRelationshipContract) {
            this.contractInfo = [
              {
                signatory: '乙方',
                signName: '课程推广大使',
                name: '新课程推广大使合同',
                qrUrl: '',
                flowId: data.promotionFlowId,
                isFakeFlow: data.promotionIsFake,
                isFirstParty: 1,
                showQrCodeTips: false,
                showQrCodeTipsText: '',
                isUpdateContractInfo: userInfo.isUpdateContractInfo, //是否修改
                recommend: userInfo.sameRecommend, //推荐人信息是否相同
                merchantCode: userInfo.coursePromotionMerchantCode, //课程推广大使商户号
                phone: userInfo.coursePromotionPhone, //课程推广大使手机号
                parentPhoneLabel: '课程推广大使手机号',
                title: '系统已识别您的用户信息与课程推广大使的用户信息一致，因此无需签署本合同，完成以下校验即可继续操作'
              }
            ];
            this.sendVerificationVodeStatus = true;
          } else {
            this.contractInfo = [
              {
                signatory: '乙方',
                signName: '归属俱乐部',
                name: '分销商、零售商合同',
                qrUrl: '',
                flowId: data.studyFlowId,
                isFakeFlow: data.studyIsFake,
                isFirstParty: 1,
                showQrCodeTips: false,
                showQrCodeTipsText: '',
                isUpdateContractInfo: userInfo.isUpdateContractInfo, //是否修改
                recommend: userInfo.sameParentAndChild, //上下级信息是否相同
                merchantCode: userInfo.clubMerchantCode, //归属俱乐部商户号
                phone: userInfo.clubPhone, //归属俱乐部手机号
                parentPhoneLabel: '俱乐部手机号',
                title: '系统已识别您的用户信息与所属俱乐部的用户信息一致，因此无需签署本合同，完成以下校验即可继续操作',
                tooltip: `您的所属俱乐部未签署《超级俱乐部<br>经销商销售合同》，请等待俱乐部<br>签署后重试`
              },
              {
                signatory: '乙方',
                signName: '课程推广大使',
                name: '课程推广大使合同',
                qrUrl: '',
                flowId: data.promotionFlowId,
                isFakeFlow: data.promotionIsFake,
                isFirstParty: 1,
                showQrCodeTips: false,
                showQrCodeTipsText: '',
                isUpdateContractInfo: userInfo.isUpdateContractInfo,
                recommend: userInfo.sameRecommend, //推荐信息是否相同
                merchantCode: userInfo.coursePromotionMerchantCode, //课程推广大使商户号
                phone: userInfo.coursePromotionPhone, //课程推广大使手机号
                parentPhoneLabel: '课程推广大使手机号',
                title: '系统已识别您的用户信息与课程推广大使的用户信息一致，因此无需签署本合同，完成以下校验即可继续操作',
                tooltip: `您的课程推广大使未签署《分销商、零售商合同》<br>请等待其签署后重试`
              }
            ];
          }
          contractProp = 'promotion';
        }

        this.canShow = needVisible;
        this.visible = needVisible;
        if (needVisible) {
          // 设置天数倒计时
          this.remainingDays = data.remainingDays;
          if (this.remainingDays < 1) {
            this.canClose = false;
          }
          //合同已生产
          if (!this.showConfirmUserInfo) {
            if (this.isChangeRelationshipContract) {
              // 初始化变更关系相关合同二维码显示流程
              if (data[`${contractProp}SignStatus`] !== 2) {
                this.getContractQRUrlHandle();
              }
            } else {
              if (this.contractInfo[this.contractSigningActive].recommend) {
                this.checkedVerification();
              }
              // 初始化学习管理相关合同流程
              let needGetQRUrl = true;
              // 处理步骤一合同已签署但步骤二合同未生成的情况，自动生成步骤二合同
              if (data.studySignStatus === 2 && !data[`${contractProp}ContractSign`]) {
                try {
                  this.contractInfo[this.contractSigningActive].isFakeFlow = this.contractInfo[this.contractSigningActive].recommend;
                  this.fackContactStatus = 0;
                  this.contractQRLoading = true;
                  let usertype = this.isOperations ? 'Operations' : this.isSchool ? 'School' : '';
                  const res = await addContractTwo(usertype, this.contractInfo[this.contractSigningActive].recommend);

                  needGetQRUrl = !this.contractInfo[this.contractSigningActive].isFakeFlow;
                  this.contractInfo[this.contractSigningActive].showQrCodeTips = false;
                  this.contractInfo[this.contractSigningActive].flowId = res.data;
                  console.log(res.data, 'res.data');
                  this.fackContactStatus = 2;
                } catch (error) {
                  this.contractQRLoading = false;
                  needGetQRUrl = false;
                  this.fackContactStatus = 1;
                  this.handleAddContractError(error);
                }
              }

              if (needGetQRUrl) {
                this.getContractQRUrlHandle();
              }
            }
          }
          this.initMutationObserver();
        } else {
          this.$emit('signingEnd');
        }
      },
      /**
       * 获取签署用户信息
       */
      handleContractUserInfo() {
        return new Promise((resolve) => {
          getContractUserInfo().then((res) => {
            this.userInfoForm = res.data;
            console.log('🚀 ~ handleContractUserInfo ~ userInfoForm:', this.userInfoForm);
            this.oldUserInfoForm = { ...res.data };
            resolve(res.data);
          });
        });
      },
      /**
       * 处理取消保存用户信息按钮点击事件
       */
      handleCancelSaveClick() {
        this.userInfoForm = { ...this.oldUserInfoForm };
        this.userInfoFormLoading = false;
        this.canEditUserInfo = false;
        this.$nextTick(() => {
          this.$refs['userInfoFormRef']?.clearValidate();
        });
      },

      closeModifyUserInfoDialog() {
        this.userInfoForm = { ...this.oldUserInfoForm };
        this.modifyUserVisible = false;
        this.userInfoFormLoading = false;
        this.$nextTick(() => {
          this.$refs['userInfoFormRef']?.clearValidate();
        });
      },
      //检验数据是否做修改
      checkDataModified() {
        const originalStr = JSON.stringify(this.originalData);
        const currentStr = JSON.stringify(this.userInfoForm);
        if (originalStr === currentStr) {
          return false;
        }
        return true;
      },

      /**
       * 修改用户信息
       * 后端校验修改信息,校验通过后打开合同生成确认弹窗
       */

      async modifyUserInfo() {
        this.$refs['userInfoFormRef'].validate(async (valid) => {
          if (valid) {
            this.userInfoFormLoading = true;
            if (this.userInfoForm.businessName) {
              let params = {
                keyword: this.userInfoForm.businessName,
                pageNo: 1,
                pageSize: 10
              };
              try {
                const res = await checkEnterpriseName(params);
                let company = res.data?.data?.data?.some((item) => {
                  return item.companyName == this.userInfoForm.businessName;
                });
                if (!company) {
                  this.userInfoFormLoading = false;
                  this.$message.error('填写企业未注册');
                  return;
                }
              } catch (error) {
                this.userInfoFormLoading = false;
                return;
              }
            }
            this.userInfoForm.contractHeadIcard = this.userInfoForm.contractHeadIcard.toUpperCase();
            this.userInfoForm.contractType = this.signType;
            checkContractUserInfo(this.userInfoForm)
              .then(() => {
                this.contractConfirmDialogVisible = true;
              })
              .catch((e) => {
                this.userInfoFormLoading = false;
                if (typeof e === 'object' && e.code == '52000') {
                  this.$message.warning(e.message);
                }
              });
          }
        });
      },

      /**
       * 保存用户信息点击事件处理
       */
      handleSaveClick(type) {
        this.$refs['userInfoFormRef'].validate(async (valid) => {
          if (valid) {
            this.userInfoFormLoading = true;
            if (this.showConfirmUserInfo && this.userInfoForm.businessName) {
              let params = {
                keyword: this.userInfoForm.businessName,
                pageNo: 1,
                pageSize: 10
              };
              try {
                const res = await checkEnterpriseName(params);

                let company = res.data?.data?.data?.some((item) => {
                  return item.companyName == this.userInfoForm.businessName;
                });
                if (!company) {
                  this.userInfoFormLoading = false;
                  this.$message.error('填写企业未注册');
                  return;
                }
              } catch (error) {
                this.userInfoFormLoading = false;
                return;
              }
            }
            this.userInfoForm.contractHeadIcard = this.userInfoForm.contractHeadIcard.toUpperCase();
            this.userInfoForm.contractType = type;
            this.addContractOneLoading = true;
            if (this.contractSigningActive == 1 && !this.showConfirmUserInfo) {
              try {
                await this.handleEndNext(false, true);
              } catch (error) {
                if (error === true) {
                  this.$message.error('合同已全部签署完成，无法修改用户信息');
                }
                this.addContractOneLoading = false;
                this.userInfoFormLoading = false;
                return;
              }
            }
            checkContractUserInfo(this.userInfoForm)
              .then(() => {
                /**
                 * 更新用户信息成功后，撤销已生成的合同，重新生成合同
                 *
                 */
                updateContractUserInfo(this.userInfoForm)
                  .then(() => {
                    this.handleUpdateContractUserInfoAfter();
                  })
                  .catch((e) => {
                    this.addContractOneLoading = false;
                    this.userInfoFormLoading = false;
                    if (typeof e === 'object' && e.code == '50010') {
                      this.$confirm('剩余合同数不足，请购买补充后重试', '提示', { type: 'warning' }).then(() => {
                        this.goBuy();
                      });
                    }
                  })
                  .finally(() => {
                    this.$refs.childRef?.clearData(); // 清空子组件数据
                  });
              })
              .catch((e) => {
                this.addContractOneLoading = false;
                this.userInfoFormLoading = false;
                if (typeof e === 'object' && e.code == '52000') {
                  this.$message.warning(e.message);
                }
              });
          }
        });
      },
      goBuy() {
        this.$refs.contractSpurchaseDialog?.open();
      },
      /**
       * 修改修改用户信息后重新赋值
       * @param 更新recommend  merchantCode phone
       */

      handleUpdateContractUserInfoAfter() {
        console.log('重新赋值');
        this.clearOneContractPolling();
        this.handleContractUserInfo().then((userInfo) => {
          /**
           * 步骤一重新赋值
           */
          const handleOneProps = (item) => {
            if (this.isOperations) {
              item.recommend = userInfo.sameParentAndChild;
              item.merchantCode = userInfo.brandMerchantCode;
              item.phone = userInfo.brandPhone;
              item.titie = this.isChangeRelationshipInitiator
                ? userInfo.businessName
                  ? '系统已识别您的企业信息与渠道合作伙伴的企业信息一致，因此无需签署本合同，完成以下校验即可继续操作'
                  : '系统已识别您的用户信息与渠道合作伙伴的用户信息一致，因此无需签署本合同，完成以下校验即可继续操作'
                : '系统已识别您的企业信息与所属品牌的企业信息一致，因此无需签署本合同，完成以下校验即可继续操作';
            } else if (this.isSchool) {
              item.recommend = userInfo.sameParentAndChild;
              item.merchantCode = userInfo.clubMerchantCode;
              item.phone = userInfo.clubPhone;
            }
            return item;
          };
          /**
           * 步骤二重新赋值
           */
          const handleTwoProps = (item) => {
            if (this.isOperations) {
              item.recommend = userInfo.sameRecommend;
              item.merchantCode = userInfo.operationsMerchantCode;
              item.phone = userInfo.operationsPhone;
              item.title = userInfo.businessName
                ? '系统已识别您的企业信息与渠道合作伙伴的企业信息一致，因此无需签署本合同，完成以下校验即可继续操作'
                : '系统已识别您的用户信息与渠道合作伙伴的用户信息一致，因此无需签署本合同，完成以下校验即可继续操作';
            } else if (this.isSchool) {
              item.recommend = userInfo.sameRecommend;
              item.merchantCode = userInfo.coursePromotionMerchantCode;
              item.phone = userInfo.coursePromotionPhone;
            }
            return item;
          };
          this.contractInfo = this.contractInfo.map((item, index) => {
            item.flowId = '';
            item.isFakeFlow = null;
            item.qrUrl = '';
            item.showQrCodeTips = false;
            item.showQrCodeTipsText = '';
            item.isUpdateContractInfo = userInfo.isUpdateContractInfo;
            if (this.isChangeRelationshipContract) {
              item = handleTwoProps(item);
            } else if (index == 0) {
              item = handleOneProps(item);
            } else if (index == 1) {
              item = handleTwoProps(item);
            }
            return item;
          });
          this.contractSigningActive = 0;
          if (!this.showConfirmUserInfo) {
            this.handleAddContractOneQRLink();
          }
          console.log('this.contractInfo', this.contractInfo);
          this.closeModifyUserInfoDialog();
          this.addContractOneLoading = false;
          this.contractConfirmDialogVisible = false;
          this.canEditUserInfo = false;
          this.userInfoFormLoading = false;
        });
      },
      /**
       * 处理修改信息按钮点击事件
       */
      handleShowEditClick() {
        this.oldUserInfoForm = { ...this.userInfoForm };
        this.canEditUserInfo = true;
      },
      /**
       * 合同生成点击事件处理
       */
      handleGenerateContractClick() {
        this.$refs['userInfoFormRef'].validate((valid) => {
          if (valid) {
            this.contractConfirmDialogVisible = true;
          }
        });
      },

      /**
       * 关闭合同生成确认弹窗
       */
      closeContractConfirmDialogVisible() {
        this.addContractOneLoading = false;
        this.addContractOneLoading = false;
        this.userInfoFormLoading = false;
        this.contractConfirmDialogVisible = false;
      },
      /**
       * 合同生成确认弹窗确认点击事件处理
       */
      handleGenerateContractConfirmClick() {
        if (this.showConfirmUserInfo) {
          this.handleAddContractOneQRLink();
        } else {
          this.handleSaveClick(this.signType);
        }
      },
      handleAddContractOneQRLink() {
        this.addContractOneLoading = true;
        let usertype = this.isOperations ? 'Operations' : this.isSchool ? 'School' : '';
        const addContract = this.isChangeRelationshipContract ? addChangeRelationshipContract : addContractOne;
        console.log('合同生成确认弹窗确认点击事件处理 recommend', this.contractSigningActive, this.contractInfo[this.contractSigningActive]);
        const recommend = this.contractInfo[this.contractSigningActive].recommend;
        this.fackContactStatus = 0;
        this.contractInfo[this.contractSigningActive].isFakeFlow = recommend;
        this.contractQRLoading = true;
        if (recommend) {
          this.checkedVerification();
        }
        // 学习管理相关步骤一合同（或变更上下级相关合同）生成流程
        addContract(usertype, recommend)
          .then((res) => {
            // 通过合同流程id生成步骤一合同二维码链接
            this.contractInfo[this.contractSigningActive].showQrCodeTips = false;
            this.contractInfo[this.contractSigningActive].flowId = res.data;
            console.log('res.data', res.data);
            this.fackContactStatus = 2;
            this.showConfirmUserInfo = false;
            this.contractConfirmDialogVisible = false;
            this.addContractOneLoading = false;
            this.getContractQRUrlHandle();
          })
          .catch((error) => {
            this.fackContactStatus = 1;
            this.contractQRLoading = false;
            this.handleAddContractError(error);
            this.addContractOneLoading = false;
          })
          .finally(() => {
            this.addContractOneLoading = false;
          });
      },
      /**
       * 轮询步骤一合同(或变更上下级相关合同)签署状态，已签署自动跳转下一步
       */
      pollingContractStatus() {
        if (!this.pollingStatusId) {
          const that = this;
          this.pollingStatusId = setInterval(() => {
            console.log('轮询合同签署状态');
            that.handleNextContractClick();
          }, 30000);
          if (this.pollingStatusStopId) clearTimeout(this.pollingStatusStopId);
          this.pollingStatusStopId = setTimeout(() => {
            if (that.pollingStatusId) {
              console.log('已轮询10分钟, 停止轮询合同签署状态');
              clearInterval(that.pollingStatusId);
              that.pollingStatusId = null;
              this.pollingStatusStopId = null;
            }
          }, 10 * 60 * 1000);
        }
      },

      /**
       * 处理合同签署下一步点击事件
       */
      async handleNextContractClick(isClick) {
        if (this.contractInfo[this.contractSigningActive].isFakeFlow) {
          if (!this.verify) {
            this.$message.error('请输入验证码');
            return;
          }
          let params = {
            flowId: this.contractInfo[this.contractSigningActive].flowId,
            merchantCode: this.contractInfo[this.contractSigningActive].merchantCode,
            code: this.verify,
            contractType: this.signType
          };
          await verifyContractCode(params);
        }
        if (this.isChangeRelationshipContract) {
          this.handleEndNext(isClick);
        } else {
          if (this.contractSigningActive < 1) {
            this.$refs.childRef?.clearData();
            this.handleOneNext(isClick);
          } else if (this.contractSigningActive === 1) {
            this.handleEndNext(isClick);
          }
        }
      },
      /**
       * 学习管理相关步骤一合同下一步处理
       * @param isClick 是否用户点击行为
       */
      handleOneNext(isClick) {
        const { flowId, signatory: participantFlag } = this.contractInfo[this.contractSigningActive];
        if (flowId) {
          getContractSigingStatus({ flowId, participantFlag }).then((res) => {
            if (res.data && res.data?.signStatus == 2) {
              this.$message.success('合同签署成功');
              // 清除步骤一合同签署状态轮询定时器
              this.clearOneContractPolling();
              this.$refs.childRef?.clearData();
              this.contractSigningActive = 1;
              console.log('第一步假合同签署完成，点击下一步', this.contractInfo[this.contractSigningActive], this.contractSigningActive);
              // 生成步骤二合同并获取二维码链接
              this.handleAddContractTwoQrLink();
            } else if (isClick) {
              this.$message.error('请先完成当前合同签署');
            }
          });
        }
      },
      /**
       * 清除学习管理相关合同签署状态轮询定时器
       */
      clearOneContractPolling() {
        if (this.pollingStatusId) {
          console.log('提前停止轮询合同签署状态');
          clearInterval(this.pollingStatusId);
          this.pollingStatusId = null;
        }
        if (this.pollingStatusStopId) {
          clearTimeout(this.pollingStatusStopId);
          this.pollingStatusStopId = null;
        }
      },
      /**
       * 学习管理相关步骤二合同(或变更上下级相关合同)已签署处理
       */
      handleEndNext(isClick, isUpdata) {
        return new Promise((resolve, reject) => {
          const { flowId, signatory: participantFlag } = this.contractInfo[this.contractSigningActive];
          if (flowId) {
            getContractSigingStatus({ flowId, participantFlag })
              .then((res) => {
                if (res.data && res.data?.signStatus == 2) {
                  this.$message.success('合同签署成功');
                  this.$refs.childRef?.clearData();
                  // 步骤二(或变更上下级相关合同)已签署完成, 关闭弹窗
                  this.needCheckEnterpriseRransferContractDialog = true;
                  this.visible = false;
                  reject(true);
                } else {
                  isClick && this.$message.error('合同未签署');
                  resolve();
                }
              })
              .catch(() => {
                reject(false);
              });
          } else {
            isUpdata || this.$message.error('合同暂未生成，请生成合同后再试。');
            resolve();
          }
        });
      },

      /**
       * 刷新学习管理相关步骤一、二合同（或变更上级关系相关合同）二维码点击事件处理
       */
      handleRefreshQrCodeClick() {
        console.log(this.contractInfo[this.contractSigningActive].qrUrl, 'this.contractInfo[this.contractSigningActive].qrUrl');
        if (!this.contractInfo[this.contractSigningActive].qrUrl) {
          if (this.contractInfo[this.contractSigningActive].showQrCodeTips) {
            if (this.contractSigningActive == 0) {
              // 生成步骤一合同及获取二维码
              this.handleAddContractOneQRLink();
            } else if (this.contractSigningActive == 1) {
              // 生成步骤二合同及获取二维码
              this.handleAddContractTwoQrLink();
            }
          } else {
            this.getContractQRUrlHandle();
          }
        }
      },
      /**
       * 生成学习管理相关步骤二合同及获取二维码链接点击事件处理
       */
      handleAddContractTwoQrLink() {
        const usertype = this.isOperations ? 'Operations' : this.isSchool ? 'School' : '';
        const recommend = this.contractInfo[this.contractSigningActive].recommend;
        this.fackContactStatus = 0;
        console.log(this.contractSigningActive, 'this.contractSigningActive');
        this.contractInfo[this.contractSigningActive].isFakeFlow = recommend;
        this.contractQRLoading = true;
        console.log(recommend, '判断真假合同');

        addContractTwo(usertype, recommend)
          .then((res) => {
            recommend && this.checkedVerification();
            this.contractInfo[this.contractSigningActive].showQrCodeTips = false;
            this.contractInfo[this.contractSigningActive].flowId = res.data;
            this.fackContactStatus = 2;
            this.getContractQRUrlHandle(this.contractSigningActive, 3000);
          })
          .catch((error) => {
            this.fackContactStatus = 1;
            this.contractQRLoading = false;
            this.handleAddContractError(error);
          });
      },
      /**
       * 生成学习管理相关步骤二合同（或变更上级关系相关合同）后端请求错误处理
       * @param error 错误信息
       */
      handleAddContractError(error) {
        if (typeof error === 'object' && (error.code === 50010 || error.code === 50011)) {
          // 排除错误信息中多余的@eid相关后缀
          const index = error.message.indexOf('@');
          const message = index == -1 ? error.message : error.message.slice(0, index);

          // 与后端约定，以‘#’分隔错误信息，‘#’左边为需要提示的错误信息，‘#’右边为显示的二维码提示文案
          let [errorMsg, showQrCodeTipsText = ''] = message.split('#');

          this.contractInfo[this.contractSigningActive].showQrCodeTipsText = showQrCodeTipsText;
          if (error.code == '50011') {
            this.$confirm('剩余合同数不足，请购买补充后重试', '提示', { type: 'warning' }).then(() => {
              this.goBuy();
            });
          } else {
            this.$message.error(errorMsg);
          }
        } else {
          // 其他错误
          this.contractInfo[this.contractSigningActive].showQrCodeTipsText = '';
        }
        this.contractInfo[this.contractSigningActive].showQrCodeTips = true;
      },
      /**
       * 通过合同流程id生成合同二维码链接
       * @param index 合同步骤索引（不填默认取当前步骤索引值）
       */
      getContractQRUrlHandle(index = this.contractSigningActive, lazyTimes = 0) {
        this.contractQRImageLoading = true;
        if (this.isChangeRelationshipContract) {
          // 为变更关系合同时，index默认为0
          index = 0;
        } else if (![0, 1].includes(index)) {
          index = this.contractSigningActive;
        }
        const { flowId, isFirstParty, isFakeFlow } = this.contractInfo[index];
        if (flowId && !isFakeFlow) {
          // 通过合同流程id生成合同二维码链接
          this.contractQRLoading = true;
          setTimeout(() => {
            getContractQRUrl(flowId, isFirstParty)
              .then((contractQR) => {
                this.contractQRLoading = false;
                this.contractInfo[index].qrUrl = contractQR.qrUrl;
                this.contractQRImageLoading = false;
                if (contractQR.qrUrl) {
                  this.pollingContractStatus();
                }
              })
              .catch(() => {
                this.contractQRImageLoading = false;
                this.contractQRLoading = false;
              });
          }, lazyTimes);
        }
      },
      /**
       * 初始化节点监听器
       * 阻止用户手动删除合同签署弹窗元素节点，跳过合同签署流程
       */
      initMutationObserver() {
        this.$nextTick(() => {
          observer && observer.disconnect();
          // 观察器的配置（需要观察什么变动）
          // attributes: 观察受监视元素的属性值变更
          // childList: 监视目标节点（如果 subtree 为 true，则包含子孙节点）添加或删除新的子节点
          // subtree: 其他值也会作用于此子树下的所有节点，而不仅仅只作用于目标节点
          const config = { childList: true, subtree: true };
          // 创建一个观察器实例并传入回调函数
          observer = new MutationObserver(this.mutationObserverCallBack);
          const targetNode = document.getElementById('contractSigningDialog');
          // 以上述配置开始观察目标节点
          observer.observe(targetNode.parentNode, config);
        });
      },
      /**
       * 当观察到变动时执行的回调函数
       * @param mutationsList
       */
      mutationObserverCallBack(mutationsList) {
        for (let mutation of mutationsList) {
          // 这里可以获取mutation.target来获取想要监听的元素与属性
          if (
            mutation.type === 'childList' &&
            mutation.removedNodes.length &&
            ['contractSigningDialog', 'contractSigningDialog1', 'changeNoticeConfirmDialog'].includes(mutation.removedNodes[0].id)
          ) {
            (this.visible || this.changeNoticeDialogVisible) && location.reload();
          }
        }
      }
    }
  };
</script>

<style scoped>
  ::v-deep #changeNoticeConfirmDialog .el-dialog__header {
    padding: 0;
  }

  ::v-deep #contractSigningDialog1 .el-dialog__body {
    padding-top: 0px;
  }
</style>

<style lang="scss" scoped>
  .title {
    padding: 0 20px;
    font-size: 18px;
    .title-row {
      text-align: center;
      line-height: 24px;
      &-days {
        color: red;
        font-size: 24px;
      }
    }
  }
  .content {
    font-size: 18px;
    color: #000;
    .user-info-form {
      width: 60%;
      margin: 0 auto;
      ::v-deep .el-form-item__label {
        font-size: 16px;
        color: #000;
      }
      &-item {
        height: 33px;
        &-text {
          margin-left: 15px;
          font-size: 16px;
        }
      }
    }
    .contract-signing-steps {
      margin-left: 80px;
    }
    .contract-signing-content {
      margin-top: 24px;
      text-align: center;
      color: #303133;
      .contract-signing-content-title {
        font-size: 16px;
      }
      .contract-signing-content-name {
        margin: 16px 0 6px;
        font-size: 16px;
      }
      .contract-signing-content-qrcode {
        width: 270px;
        height: 270px;
        margin: 0 auto 6px;
        background-color: #f5f7fa;
        .contract-signing-content-qrcode-img {
          width: 270px;
          height: 270px;
        }
        .contract-signing-content-qrcode-refresh-icon {
          font-size: 60px;
          padding-top: 80px;
          width: 100px;
          margin: 0 auto;
        }
        .contract-signing-content-qrcode-refresh-text {
          color: #f56c6c;
          line-height: 30px;
          white-space: break-spaces;
        }
      }
    }
  }
  .contract-confirm-dialog {
    font-size: 18px;
    color: #000;
    .contract-confirm-dialog-row {
      display: flex;
      align-items: center;
      padding: 8px 0 8px 150px;
      &-label {
        width: 200px;
        flex-shrink: 0;
      }
    }
  }
  .change-notice-dialog {
    .content {
      font-size: 16px;
      margin-bottom: 20px;
      line-height: 25px;
      color: #000;
      .superiorName {
        font-weight: 600;
        text-decoration: underline;
      }
    }
    .foot {
      text-align: center;
    }
  }
  .modifyUserVisible_title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
  }
  .text_colcor {
    color: red;
  }
  .el-dialog__header {
    display: none !important;
  }
</style>
<style>
  .tips {
    background: #4f4f4f !important;
  }
  .contract-reminder {
    line-height: 30px;
    margin-top: 20px;
  }
</style>
