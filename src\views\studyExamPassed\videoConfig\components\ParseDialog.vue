<template>
  <el-dialog class="parse-dialog" :visible.sync="visible" :title="dialogText" width="70%" @close="handleClose" destroy-on-close>
    <ParseLook :question="queryParams" v-if="!flag" />
    <ParseLooks :question="queryParams" v-else />
  </el-dialog>
</template>

<script>
  import ParseLook from './ParseLook.vue';
  import ParseLooks from './ParseLooks.vue';

  export default {
    name: 'ParseDialog',
    components: { ParseLook, ParseLooks },
    data() {
      return {
        dialogText: '',
        visible: false,
        loading: false,
        flag: false,
        queryParams: {}
        // 模拟题目数据
        // mockQuestion: {
        //   stem: '函数 $f(x)=\\frac{1}{1+e^{-x}}$ 的下列说法中正确的是（ ）',
        //   imgs: [
        //     'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        //     'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
        //     'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
        //     'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
        //     'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg'
        //   ],
        //   options: [
        //     { letter: 'A', content: '函数值域为 $(0,1)$', value: 'A' },
        //     { letter: 'B', content: '存在 $x_0$ 使得 $f(x_0)=1$', value: 'B' },
        //     { letter: 'C', content: '对任意 $x\\in\\mathbb R$ ，有 $f(x)+f(-x)=1$', value: 'C' },
        //     { letter: 'D', content: '当 $x\\to +\\infty$ 时，$f(x)\\to 0$', value: 'D' }
        //   ],
        //   score: 5,
        //   answer: 'XX',
        //   analysis:
        //     '$f(x)=\\dfrac{1}{1+e^{-x}}\\in(0,1)$ 故 A 对；' +
        //     '$f(x)+f(-x)=\\dfrac{1}{1+e^{-x}}+\\dfrac{1}{1+e^{x}}=1$ 故 C 对；' +
        //     '$x\\to+\\infty$ 时 $f(x)\\to 1$ ，D 错；B 取不到 1。'
        // }
      };
    },
    computed: {},
    methods: {
      // 初始化方法
      init(dialogText, record) {
        this.dialogText = dialogText;
        let answer = '';
        if (record.length > 0) {
          this.flag = true;
          this.queryParams = record;
          console.log('record');
        } else {
          this.flag = false;
          if (record.questionType == 1 || record.questionType == 2) {
            answer = record.xktOptionVoList
              .filter((a) => {
                return a.optionIsAnswer == 1;
              })
              .map((a) => a.choiceOption)
              .join(',');
          }
          this.queryParams =
            {
              ...record,
              caption: record.caption ? record.caption.split(',') : [],
              analysisImage: record.analysisImage ? record.analysisImage.split(',') : [],
              answer
            } || {};
        }

        this.visible = true;
        console.log('this.queryParams', this.queryParams);
      },

      // 关闭对话框
      handleClose() {
        //   this.$refs['form'].resetFields(); //重置表单校验
        // this.loading = false
        this.visible = false;
      }
    }
  };
</script>

<style scoped lang="less">
  /* 加粗弹框标题字体 */
  /deep/ .el-dialog__title {
    font-weight: bold;
    font-size: 24px;
  }
</style>
