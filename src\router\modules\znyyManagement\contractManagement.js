// 合同管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/contractManagement',
  component: Layout,
  redirect: '/contractManagement/contractManagementList',
  meta: {
    perm: 'm:contractManagement:contractManagementList',
    title: '合同管理',
    icon: 'contractManagement'
  },
  children: [
    {
      path: 'contractManagementList',
      component: _import('merchantManagement/ContractManagement'),
      name: 'ContractManagement',
      meta: {
        perm: 'm:contractManagement:contractManagementList',
        title: '合同管理',
        icon: 'contractManagement'
      }
    }
  ]
};
