<template>
  <div class="app-container">
    <!-- 搜索表单区域 -->
    <el-form :inline="true" ref="SearchForm" :model="selectOperate" class="SearchForm">
      <el-row style="padding: 20px 20px 0 20px">
        <el-col :span="6" :xs="24">
          <el-form-item label="课程大类:">
            <el-select v-model="selectOperate.curriculumId" placeholder="请选择视频大类">
              <el-option v-for="item in courseTypeOptions" :key="item.id" :label="item.enName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item>
            <el-input v-model.trim="selectOperate.videoName" placeholder="请输入视频名称搜索" />
          </el-form-item>
        </el-col>
        <div style="text-align: right; padding-right: 20px">
          <el-button plain icon="el-icon-refresh" @click="reset()">重置</el-button>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch()">搜索</el-button>
        </div>
        <!-- <el-col :span="6">
                </el-col> -->
      </el-row>
    </el-form>
    <div style="padding: 20px">
      <div style="display: flex; align-items: center; justify-content: space-between">
        <div style="display: flex; align-items: center; gap: 10px">
          <h3 style="margin: 0">视频列表</h3>
          <span style="font-size: 12px; color: #6c6c6c; margin-top: 8px">
            没有版本、学科、学段，
            <el-button type="text" @click="goConfig">去配置></el-button>
            。
            <!-- 没有权限请联系平台运营 -->
          </span>
        </div>
        <div>
          <el-button type="primary" icon="el-icon-download" @click="importTemplate">下载模板</el-button>
          <el-button type="primary" icon="el-icon-document-add" @click="importQuestion" v-loading="importLoading">导入数据</el-button>
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        </div>
      </div>
    </div>
    <!-- conten内容 -->
    <el-card class="app-container">
      <div class="content-container">
        <div v-loading="versionLoading" style="border-right: 1px solid #dcdfe6; padding-right: 20px; height: calc(100vh - 280px); overflow-y: auto">
          <el-select v-model="versionId" placeholder="请选择" @change="exchangeVersionId">
            <el-option v-for="item in versionOptions" :key="item.id" :label="item.versionName" :value="item.id" />
          </el-select>
          <!-- 左侧树形结构 -->
          <div style="margin-top: 10px" v-if="this.versionOptions.length > 0">
            <Tree
              ref="tree"
              node-key="id"
              v-if="data.length"
              highlight-current
              :tree-data="data"
              :default-props="defaultProps"
              :default-expand-all="true"
              @node-click="handleNodeClick"
              :current-node-key="currentNodeKey"
              :expand-on-click-node="false"
            />
          </div>
          <div class="nomore" v-else>
            <el-image style="width: 100px; height: 100px" src="https://document.dxznjy.com/automation/1728442200000" />
            <div style="color: #999; margin-top: 20px">暂无数据</div>
          </div>
        </div>

        <!-- 右侧表格 -->
        <div class="table-container">
          <el-table v-loading="loading" :data="tableData" size="small" style="width: 100%" fit highlight-current-row :header-cell-style="getRowClass">
            <el-table-column type="index" label="序号" align="center"></el-table-column>
            <el-table-column prop="id" label="视频ID" align="center" />
            <el-table-column prop="videoName" label="视频名称" align="center" />
            <el-table-column prop="courseVersionNodeName" label="版本" align="center" />
            <el-table-column prop="courseSubjectNodeName" label="学科" align="center" width="60" />
            <el-table-column prop="coursePeriodNodeName" label="学段" align="center" />
            <el-table-column prop="knowledgeNum" label="关联知识点数" align="center" />
            <el-table-column prop="createTime" label="创建时间" align="center" />
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button @click="handleEdit(scope.row)" type="text" size="small">编辑</el-button>
                <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
                <el-button @click="handleEditVideoNode(scope.row)" type="text" size="small">编辑视频节点</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="pageParams.pageNum"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            />
          </div>
        </div>
      </div>
    </el-card>
    <el-dialog title="视频导入" :visible.sync="importOpen" width="70%" :close-on-click-modal="false" @close="close">
      <el-form ref="importFrom" :model="importFrom" label-position="left" label-width="120px" style="width: 100%">
        <el-form-item label="视频导入">
          <excel-upload :limit="1" :showTip="false" :fileList="fileList" @handleSuccess="handleSuccess" @handleRemove="handleRemove"></excel-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" :disabled="importLoading" @click="importSubmit()">确定</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="错误信息列表" :visible.sync="errorDiaOpen" width="50%" :close-on-click-modal="false">
      <el-table class="common-table" :data="errorData" max-height="300" border style="width: 100%">
        <el-table-column prop="sortsNum" label="序号"></el-table-column>
        <el-table-column prop="curriculumName" label="课程大类"></el-table-column>
        <el-table-column prop="courseVersion" label="版本"></el-table-column>
        <el-table-column prop="courseSubject" label="学科"></el-table-column>
        <el-table-column prop="coursePeriod" label="学段"></el-table-column>
        <el-table-column prop="videoName" label="视频名称"></el-table-column>
        <el-table-column prop="knowledgeList" label="关联知识点"></el-table-column>
        <el-table-column prop="vid" label="视频vid"></el-table-column>
        <el-table-column prop="failureReason" label="错误信息" width="auto"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="errorClose">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Tree from '../courseManagement/components/tree.vue';
import videoManageMentAPI from '@/api/mathApi/videoManageMentAPI';
import ExcelUpload from './components/upload.vue';
import checkPermission from '@/utils/permission';
import {
  deleteVideo,
  getAllCourseTypeList,
  getCourseTreeData,
  getCourseVersionList,
  getVideoList, importVideo
} from '@/api/superReaderAPI/videoManagement';

export default {
  name: 'videoManagment',
  components: {
    Tree,
    ExcelUpload
  },
  data() {
    return {
      importOpen: false,
      fileList: [],
      importFrom: {
        file: null
      },
      tempFile: null,
      currentNodeKey: '', // 当前点击节点的id
      currentNodeLevel: '1', // 当前点击节点的分类层级
      index: 1,
      // 查询条件
      selectOperate: {
        curriculumId: '',
        videoName: ''
      },
      // 视频大类选项
      courseTypeOptions: [],
      versionId: '',
      // 版本选项
      versionOptions: [],
      // 表格数据
      tableData: [],
      // 加载状态
      listLoading: false,
      // 树形数据
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      loading: false,
      // 分页参数
      pageParams: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      currentNodeId: 2, // 默认选中学科节点
      currentNodeType: '', // 当前选中节点的类型
      versionLoading: false,
      mostChildId: null,
      moreChildId: null,
      childrenId: null, // 子级id
      parentId: null, // 父级id
      moreParentId: null, // 更多父级id
      mostParentId: null, // 最上级id
      importLoading: false,
      errorDiaOpen: false,
      errorData: [],
      videoForm: {}
    };
  },
  mounted() {
    // 初始化时加载学科数据
    // this.getTableData();
    this.getCourseCategoriesAPI();
  },
  methods: {
    checkPermission,
    goConfig() {
      let flag = checkPermission(['b:aaademo:pzqxvideo']);
      if (flag) {
        this.$router.push({ path: '/_chinese_reader/superReaderCourseTypeConfig' });
      } else {
        return this.$message.warning('您没有权限');
      }
    },
    // 获取课程大类
    getCourseCategoriesAPI() {
      getAllCourseTypeList().then((res) => {
        if (res.success) {
          this.courseTypeOptions = res.data;
          if (this.courseTypeOptions && this.courseTypeOptions.length > 0) {
            // 设置默认选中的课程大类ID
            this.selectOperate.curriculumId = this.courseTypeOptions[0].id;
            // this.selectOperate.curriculumId = '1223293140236390400';
            // 获取版本数据
            this.getVersionId();
          }
        }
      });
    },
    // 获取版本id
    getVersionId() {
      getCourseVersionList({
          curriculumId: this.selectOperate.curriculumId
        })
        .then((res) => {
          if (res.success) {
            this.versionOptions = res.data;
            this.versionId = this.versionOptions[0].id;
            this.getVersionName(this.versionId);
            this.getTreeData();
          } else {
            this.versionOptions = [];
            this.versionId = '';
            // this.getTreeData();
          }
        });
    },
    exchangeVersionId(e) {
      console.log(e);
      this.getVersionName(e);
      this.getTableData();
      this.getTreeData();
    },
    getVersionName(e) {
      if (e) {
        let arr = this.versionOptions.filter((i) => i.id == e);
        if (arr && arr.length > 0) {
          this.videoForm.courseVersionNodeName = arr[0].versionName;
          console.log(arr[0].versionName);
        }
      }
    },
    // 规范树的数据
    formatTreeData(list) {
      return list.map((item) => ({
        id: item.id,
        label: item.nodeName,
        children: Array.isArray(item.childList) && item.childList.length > 0 ? this.formatTreeData(item.childList) : []
      }));
    },
    deepReplace(array) {
      if (array instanceof Array && array.length >= 1) {
        return array.map((el) => {
          return {
            id: el.id,
            label: el.nodeName,
            children: this.deepReplace(el.childList),
            ...el
          };
        });
      } else {
        return [];
      }
    },
    // 获取树形数据
    getTreeData() {
      this.versionLoading = true;
      if (!this.selectOperate.curriculumId) return;
      getCourseTreeData({
          curriculumId: this.selectOperate.curriculumId,
          versionId: this.versionId,
          nodeLevel: 2
        })
        .then((res) => {
          if (res.success) {
            // 转换数据结构
            this.data = this.deepReplace(res.data);
            this.currentNodeKey = this.data[0].id;
            this.childrenId = this.data[0].id;
            // 自动执行一次搜索
            this.handleSearch();
          }
        })
        .finally(() => {
          this.versionLoading = false;
        });
    },
    // 搜索按钮点击事件
    handleSearch() {
      // this.getVersionId()
      this.pageParams.pageNum = 1;
      // 获取表格数据
      this.getTableData();
    },
    // 去配置
    courseConfig() {
      // 判断是否有权限有--去视频分类配置页面
      // if (接口权限) {
      this.$router.push('/_aaa_demo/courseConfig');
      // }else{
      // this.$message({
      //   type: 'warning',
      //   message: '暂无权限，请联系平台运营'
      // });
      // }
    },
    // 重置
    reset() {
      this.selectOperate.videoName = '';

      this.handleSearch();
    },
    // 树节点点击事件
    handleNodeClick(data, node) {
      console.log(data, '点击的数据');
      console.log(node, '点击的节点');

      if (node.level == 1) {
        this.childrenId = node.data.id; // 当前节点的id
      } else if (node.level == 2) {
        this.childrenId = node.data.id; // 当前节点的id
        this.parentId = node.parent.data.id; // 子级
        console.log(node.data.nodeName, node.parent.data.nodeName, data.curriculumName, '==========');
        this.videoForm = {
          ...this.videoForm,
          courseSubjectNodeName: node.parent.data.nodeName,
          coursePeriodNodeName: node.data.nodeName,
          curriculumName: data.curriculumName
        };
      }

      this.currentNodeLevel = data.nodeLevel;
      this.currentNodeData = data;
      // 获取表格数据
      // this.getTableData();
      this.handleSearch();
    },

    handleSuccess(res) {
      console.log(res, 'res');
      this.fileList.push(res);
      this.tempFile = res;
    },
    handleRemove(index) {
      if (index != -1) {
        this.fileList.splice(index, 1);
        this.tempFile = null;
        console.log('后===', this.fileList);
      }
    },
    importSubmit() {
      if (this.fileList.length == 0) {
        this.$message({
          message: '请选择文件',
          type: 'warning'
        });
        return;
      }
      // 导入
      const that = this;
      that.importLoading = true;
      const formData = new FormData();
      formData.append('file', that.tempFile.raw);
      formData.append('curriculumId', this.selectOperate.curriculumId); // 额外参数
      console.log(formData, 'formData');
      // return
      importVideo(formData)
        .then((res) => {
          if (res.success && !res.data) {
            this.$message.success('导入成功');
            this.fileList = [];
            this.tempFile = null;
            this.importLoading = false;
            this.importOpen = false;
            this.getTableData();
          } else {
            this.fileList = [];
            this.tempFile = null;
            this.importLoading = false;
            this.errorData = res.data;
            this.errorDiaOpen = true;
          }
        })
        .catch((err) => {
          console.log(err, '-------------------------');
          that.$message.error('导入失败，请重试！');
          that.importLoading = false;
        });
    },
    errorClose() {
      this.importOpen = false;
      this.errorDiaOpen = false;
    },
    close() {
      this.importOpen = false;
      this.importFrom = {
        file: null
      };
      this.fileList = [];
    },
    importQuestion() {
      this.importOpen = true;
      // // 检查文件类型，只允许上传 Excel 文件
      // const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
      // if (!allowedTypes.includes(this.tempFile.type)) {
      //   this.$message.error('仅支持 Excel 文件上传');
      //   return;
      // }

      // // 开启加载状态
      // this.importLoading = true;
      // const formData = new FormData();
      // formData.append('file', this.tempFile);
      // console.log(formData, 'formData');

      // videoManageMentAPI
      //   .importVideo(formData)
      //   .then((res) => {
      //     if (res.success) {
      //       this.$message.success(res.message || '数据导入成功');
      //       // 清空文件选择
      //       this.tempFile = null;
      //       // 重新查询表格数据
      //       this.submitForm();
      //     } else {
      //       this.$message.error(res.message || '数据导入失败，请检查文件内容');
      //     }
      //   })
      //   .catch((err) => {
      //     console.error('导入数据时出错:', err);
      //     this.$message.error('网络错误，请稍后重试');
      //   })
      //   .finally(() => {
      //     // 关闭加载状态
      //     this.importLoading = false;
      //   });
    },
    // 下载模板
    importTemplate() {
      if (!this.selectOperate.curriculumId) {
        this.$message.error('请先选择课程大类');
        return;
      }
      const url = 'https://document.dxznjy.com/dyw/语文阅读超人视频导入模板.xlsx';
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', '语文阅读视频导入模板.xlsx');
      document.body.appendChild(link);
      try {
        link.click();
        this.$message.success('模板下载请求已发送，请查看浏览器下载列表');
      } catch (error) {
        this.$message.error('模板下载失败，请重试');
        console.error('文件下载失败:', error);
      }
      // 移除链接并释放内存
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
    // 新增按钮点击事件
    handleAdd() {
      // 检查是否是学段节点
      if (!this.currentNodeData || this.currentNodeData.nodeLevel !== 2) {
        this.$message({
          type: 'warning',
          message: '请选择学段节点后再进行新增操作'
        });
        return;
      }
      // 携带课程大类+id 版本名称+id 学科名称+id 学段名称+id ------新增页面需要回显
      let obj = {
        curriculumId: this.selectOperate.curriculumId,
        courseVersionNodeId: this.versionId,
        courseSubjectNodeId: this.parentId,
        coursePeriodNodeId: this.childrenId
      };
      console.log(this.videoForm, 'obj');
      this.videoForm = { ...obj, ...this.videoForm };
      sessionStorage.setItem('videoData', JSON.stringify(this.videoForm));
      this.$router.push({
        path: '/_chinese_reader/superReaderAddVideo',
        query: { parentId: this.parentId }
      });
    },
    // 获取表头样式
    getRowClass() {
      return {
        background: '#f5f7fa',
        color: '#606266',
        fontWeight: 'bold',
        textAlign: 'center'
      };
    },
    // 获取表格数据
    async getTableData() {
      this.loading = true;
      if (!this.selectOperate.curriculumId) return (this.loading = false);
      let tableData = {
        curriculumId: this.selectOperate.curriculumId,
        videoName: this.selectOperate.videoName,
        pageNum: this.pageParams.pageNum,
        pageSize: this.pageParams.pageSize
      };

      // 根据节点层级设置不同的参数
      if (this.currentNodeLevel == 1) {
        // 版本节点
        tableData.courseVersionNodeId = this.versionId;
        tableData.courseSubjectNodeId = this.childrenId;
      } else if (this.currentNodeLevel == 2) {
        // 学科节点
        tableData.courseVersionNodeId = this.versionId;
        tableData.courseSubjectNodeId = this.parentId;
        tableData.coursePeriodNodeId = this.childrenId;
        console.log(tableData.courseVersionNodeId, tableData.courseSubjectNodeId, tableData.coursePeriodNodeId);
      } else {
        console.log(this.versionId, '-----------------');

        tableData.courseVersionNodeId = this.versionId;
        tableData.courseSubjectNodeId = this.childrenId;
      }

      getVideoList(tableData)
        .then((res) => {
          if (res.success) {
            this.tableData = res.data.data;
            this.total = Number(res.data.totalItems);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 分页大小改变
    handleSizeChange(val) {
      this.pageParams.pageSize = val;
      this.getTableData();
    },
    // 页码改变
    handleCurrentChange(val) {
      this.pageParams.pageNum = val;
      this.getTableData();
    },
    // 打开视频节点
    handleEditVideoNode(row) {
      console.log('🚀 ~ handleEditVideoNode ~ row:', row);
      // 跳转到编辑页面
      this.$router.push({
        path: '/_chinese_reader/superReaderEditVideoNodes',
        query: { row: encodeURIComponent(JSON.stringify(row)) }
      });
    },
    // 编辑
    handleEdit(row) {
      console.log(row, 'row');
      // return;

      let obj = {
        id: row.id,
        curriculumId: this.selectOperate.curriculumId,
        courseVersionNodeId: row.courseVersionNodeId,
        courseSubjectNodeId: row.courseSubjectNodeId,
        coursePeriodNodeId: row.coursePeriodNodeId
      };
      this.videoForm = {
        ...this.videoForm,
        ...obj,
        courseVersionNodeName: row.courseVersionNodeName,
        courseSubjectNodeName: row.courseSubjectNodeName,
        coursePeriodNodeName: row.coursePeriodNodeName
      };
      sessionStorage.setItem('videoData', JSON.stringify(this.videoForm));
      this.$router.push('/_chinese_reader/superReaderAddVideo');
    },
    // 删除
    handleDelete(row) {
      this.$confirm('请确认是否删除视频！', '删除视频', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteVideo({
            id: row.id
          })
          .then((res) => {
            if (res.success) {
              this.$message.success(res.message);
              this.getTableData();
            }
          });
      });
    }
  }
};
</script>

<style scoped>
.app-container {
  background-color: #fff;
  border-radius: 4px;
}

.app-container :deep(.el-card__body) {
  padding: 0;
  height: 100%;
}

.SearchForm {
  background-color: #fff;
  border-radius: 4px;
}

.el-form-item {
  margin-bottom: 18px;
}

.table-operator {
  margin-bottom: 18px;
}
/* tree和table样式 */
.content-container {
  display: flex;
  min-height: calc(100vh - 180px);
}

.tree-container {
  width: 220px;
  border-right: 1px solid #dcdfe6;
  padding-right: 20px;
  flex-shrink: 0;
}

.table-container {
  flex: 1;
  padding: 20px 20px 0 20px;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.table-container :deep(.el-table) {
  width: 100% !important;
}

.table-container :deep(.el-table__body) {
  width: 100% !important;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 10px 0;
}

.no-spinners input::-webkit-outer-spin-button,
.no-spinners input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-spinners input[type='number'] {
  -moz-appearance: textfield;
}
.nomore {
  padding: 200px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>

