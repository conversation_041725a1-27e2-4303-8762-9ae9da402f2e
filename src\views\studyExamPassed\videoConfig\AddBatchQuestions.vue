<template>
  <div class="batch-upload-question-page">
    <!-- 页面标题 -->
    <div class="page-title">
      <h2 class="page-title__text">批量上传题目{{ priviewFlag ? '预览' : '' }}</h2>
      <div class="page-title__actions">
        <el-button v-if="!priviewFlag" plain @click="cancel">取消</el-button>
        <el-button v-if="showTable && !priviewFlag" type="primary" plain @click="priviewParse">预览</el-button>
        <el-button v-if="showTable && priviewFlag" plain @click="cancelPriview">关闭预览</el-button>
        <el-button v-if="showTable" type="primary" @click="postComfirm" :loading="importLoading">确定导入</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main">
      <!-- 顶部导航步骤 -->
      <div class="steps-container">
        <el-form ref="form" inline :model="SelectQueryParam" label-width="100px">
          <el-form-item label="课时阶段：" prop="lessonStage">
            <el-select v-model="SelectQueryParam.lessonStage" placeholder="请选择">
              <!-- <el-option label="课前练习" value="1"></el-option> -->
              <el-option label="课中习题" value="2"></el-option>
              <el-option label="课后习题" value="3"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="课程大类：" prop="course">
            <el-input v-model="SelectQueryParam.course" placeholder="" disabled></el-input>
          </el-form-item>

          <el-form-item label="选择视频：" prop="video">
            <el-input v-model="SelectQueryParam.video" placeholder="" disabled></el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- 中间内容区域 -->
      <div v-show="!showTable">
        <div class="content-container">
          <BatchTemplateUpload
            template-url="https://document.dxznjy.com/course/695c5bc497ff4242b3bc0ca0c2989b67.xlsx"
            file-name="批量上传题目模板.xlsx"
            :upload-fn="uploadDetailFile"
            @upload-success="handleUploadSuccess"
          />
        </div>

        <!-- 操作说明 -->
        <div class="instruction-section">
          <div style="width: 80%">
            <div class="instruction-title">整体说明：所上传的题目的题干及答案中均为文字的可选择批量上传，若涉及图片公式等请选择新建题目的形式进行添加</div>
            <div class="instruction-content">
              <P>1.文档大小限制在100M</P>
              <P>2.文档第一行为每列标题，请务必与模板保持一致</P>
              <P>3.简答题如有多个小问，请在小问文本结尾用【】标记小问分值，小问分值之和必须等于题目分值</P>
              <P>4.多选题的正确答案列中，请勿增加逗号、顿号等其他符号，简答题无需填写答案</P>
              <P>5.填空题存在多个答案的时候答案和答案之间通过顿号进行隔开</P>
              <P>6.答案解析和分值均可为空，分值可随后在编辑页面继续批量设置</P>
            </div>
          </div>
        </div>
      </div>
      <div v-show="showTable && !priviewFlag">
        <ReusableTable
          ref="ReusableTable"
          v-model="words"
          :columns="columns"
          :currentPage="listParmas.pageNum"
          @update:currentPage="handleCurrentPageUpdate"
          :pageSize="listParmas.pageSize"
          :total="total"
          @page-change="handlePageChange"
          :type="type"
          @update-row="handleUpdateRow"
        />
      </div>
    </div>

    <ParseLooks v-if="priviewFlag" :question="payloadList"></ParseLooks>
  </div>
</template>

<script>
  import BatchApi from '@/api/studyExamPassed/questionKnowledgeManage';
  import ParseLooks from './components/ParseLooks.vue';
  import { restoreMissingRouteQuery, persistRouteParams } from '@/utils/routeParamCache';
  import ReusableTable from './components/ReusableTable.vue';
  import BatchTemplateUpload from './components/BatchTemplateUpload.vue';
  export default {
    name: 'AddBatchQuestions',
    components: { ReusableTable, ParseLooks, BatchTemplateUpload },
    data() {
      return {
        type: 2, //2 视频批量上传 1课程批量上传
        words: [],
        columns: [
          {
            prop: 'questionTypeDesc',
            label: '题型',
            type: 'select',
            options: [
              { label: '单选题', value: 1 },
              { label: '多选题', value: 2 },
              { label: '填空题(系统作答)', value: 3 },
              { label: '单选题(拍照上传)', value: 4 },
              { label: '主观题', value: 5 }
            ]
          },
          { prop: 'questionText', label: '题干', type: 'input' },
          { prop: 'subTitle1', label: '简答题小问1', type: 'input' },
          { prop: 'subTitle2', label: '简答题小问2', type: 'input' },
          { prop: 'subTitle3', label: '简答题小问3', type: 'input' },
          { prop: 'option1', label: '选项1', type: 'input' },
          { prop: 'option2', label: '选项2', type: 'input' },
          { prop: 'option3', label: '选项3', type: 'input' },
          { prop: 'option4', label: '选项4', type: 'input' },
          { prop: 'option5', label: '选项5', type: 'input' },
          { prop: 'correctAnswer', label: '正确答案', type: 'textarea' },
          { prop: 'analysis', label: '答案解析', type: 'textarea' },
          { prop: 'score', label: '分值', type: 'textarea' },
          { prop: 'errMsg', label: '错误提示', width: 200, align: 'left' }
        ],
        payloadList: [],
        queryParams: {},
        listParmas: {
          pageNum: 1,
          pageSize: 10,
          type: 2, //关联视频
          curriculumId: '',
          lessonStage: '',
          videoName: '',
          videoId: ''
        },
        total: 0,
        SelectQueryParam: {
          lessonStage: '', // 课时阶段 (单选)
          courseType: '', // 课程大类
          video: '' // 视频
        },
        courses: [], // 课程列表
        // 禁用状态
        videoDisabled: true,
        priviewFlag: false, // 是否预览

        showTable: false, // 是否显示预览表格
        importLoading: false // 导入接口调用中
      };
    },
    created() {
      // 缓存路由参数
      restoreMissingRouteQuery(this.$router, this.$route, {
        ns: 'AddBatchQuestions',
        keys: ['id', 'videoName', 'curriculumId', 'curriculumName', 'videoName', 'data', 'subjectId', 'versionId', 'gradeLevel']
      });
      console.log('created-this.$route.query.category----', this.$route.query.data.id);
      this.queryParams.videoName = this.$route.query.data.videoName || '';
      this.queryParams.videoId = this.$route.query.data.id || '';
      this.queryParams.curriculumId = this.$route.query.data.curriculumId || '';
      this.queryParams.gradeLevel = this.$route.query.data.gradeLevel || '';
      this.queryParams.subjectId = this.$route.query.data.subjectId || '';
      this.queryParams.versionId = this.$route.query.data.versionId || '';
      this.SelectQueryParam.course = this.$route.query.data.curriculumName || '';
      this.SelectQueryParam.video = this.queryParams.videoName || '';
      persistRouteParams(this.$route, {
        ns: 'AddBatchQuestions',
        keys: ['id', 'videoName', 'curriculumId', 'curriculumName', 'videoName', 'data', 'subjectId', 'versionId', 'gradeLevel']
      });
    },

    activated() {
      restoreMissingRouteQuery(this.$router, this.$route, {
        ns: 'AddBatchQuestions',
        keys: ['id', 'videoName', 'curriculumId', 'curriculumName', 'videoName', 'data', 'subjectId', 'versionId', 'gradeLevel']
      });
    },
    computed: {},
    methods: {
      uploadDetailFile(formData, config) {
        return BatchApi.importDetailFile(formData, config).then((res) => res.data || []);
      },
      handleUploadSuccess(list) {
        this.words = Array.isArray(list) ? list : [];
        this.showTable = this.words.length > 0;
      },
      // ========== 表格分页 ==========
      handlePageChange({ page, pageSize }) {
        this.listParmas.pageNum = page;
        this.listParmas.pageSize = pageSize;
        // this.getWordsList && this.getWordsList();
      },
      // 单词编辑
      handleUpdateRow(updatedRow) {
        const index = this.words.findIndex((row) => row.id === updatedRow.id || row._index === updatedRow._index);
        if (index !== -1) {
          this.$set(this.words, index, { ...updatedRow, _editing: false });
        } else {
          this.words.push({ ...updatedRow, _editing: false });
        }
      },
      handleCurrentPageUpdate(val) {
        this.listParmas.pageNum = val;
      },
      // 初始化传参数据
      postAllParmas() {
        const typeReverseMap = new Map([
          ['单选题', 1],
          ['多选题', 2],
          ['填空题（系统作答）', 3],
          ['填空题（拍照上传）', 4],
          ['主观题', 5]
        ]);
        if (!Array.isArray(this.words)) return [];
        const payloadList = this.words.map((row) => {
          const rawOptionsArr = [row.option1, row.option2, row.option3, row.option4, row.option5];
          const rawSubTitlesArr = [row.subTitle1, row.subTitle2, row.subTitle3];
          const optionsArr = rawOptionsArr.map((v) => (v == null ? null : String(v).trim())).filter((v) => v);
          const subTitlesArr = rawSubTitlesArr.map((v) => (v == null ? null : String(v).trim())).filter((v) => v);
          const questionType = row.questionTypeDesc ? typeReverseMap.get(row.questionTypeDesc.trim()) || null : null;
          const options = optionsArr.map((content, i) => ({ choiceOption: String.fromCharCode(65 + i), content, optionImage: '' }));
          const subQuestions = subTitlesArr.map((content) => ({ subTitle: content }));
          return {
            taskNo: row.taskNo,
            questionTypeDesc: row.questionTypeDesc,
            questionType,
            questionText: row.questionText,
            analysis: row.analysis,
            caption: [],
            analysisImage: [],
            score: row.score,
            answer: row.correctAnswer,
            xktOptionVoList: options,
            xktSubQuestionVoList: subQuestions
          };
        });
        this.payloadList = payloadList;
      },
      // 取消操作
      cancel() {
        if (this.words.length > 0) {
          this.words = [];
          this.showTable = false;
          return;
        }
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.push({ path: '/studyExamPassed/videoConfig' });
      },
      // 预览
      priviewParse() {
        this.priviewFlag = true;
        this.postAllParmas();
      },
      cancelPriview() {
        this.priviewFlag = false;
      },
      // 导入
      postComfirm() {
        if (!this.SelectQueryParam.lessonStage) {
          this.$message.error('请先选择课时阶段');
          return;
        }
        if (this.importLoading) return;
        this.importLoading = true;
        const query = {
          curriculumId: this.queryParams.curriculumId,
          type: 2,
          lessonStage: this.SelectQueryParam.lessonStage,
          videoName: this.queryParams.videoName,
          videoId: this.queryParams.videoId,
          coList: this.words,
          subjectId: this.queryParams.subjectId,
          versionId: this.queryParams.versionId,
          gradeLevel: this.queryParams.gradeLevel
        };
        BatchApi.videoIdFile(query)
          .then((res) => {
            if (res.code === 20000) {
              this.$message.success('批量上传题目成功');
              this.$store.dispatch('delVisitedViews', this.$route);
              this.$router.push({ path: '/studyExamPassed/videoConfig' });
            } else if (res.code === 40034) {
              this.$message.error(res.message || '批量上传题目失败');
              if (res.data) {
                res.data.forEach((errorItem) => {
                  const targetRow = this.words.find((row) => row.taskNo === errorItem.taskNo);
                  if (targetRow) targetRow.errMsg = errorItem.errMsg;
                });
              }
              this.words = [...this.words];
            } else {
              this.$message.error(res.message || '批量上传题目失败');
            }
          })
          .catch((err) => {
            this.$message.error('导入请求失败：' + (err && err.message ? err.message : '请检查网络连接'));
          })
          .finally(() => {
            this.importLoading = false;
          });
      }
    }
  };
</script>

<style lang="less" scoped>
  .batch-upload-question-page {
    padding: 20px;
  }

  .page-title {
    position: relative;
    display: block;
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #dcdfe6;
    .cancelButton {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-70%);
    }
    &__text {
      font-size: 24px;
      color: #303133;
      font-weight: bold;
      margin: 0;
      padding: 10px 0 20px 0;
    }
    &__actions {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      gap: 12px;
      padding: 6px 0;
    }
  }
  .main {
    background-color: #ffffff;
    padding: 20px;
  }

  .steps-container {
    margin-bottom: 20px;
  }

  .content-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
    width: 100%;
  }

  .instruction-section {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    width: 100%;
    padding: 20px;
    border-radius: 8px;
    color: rgba(108, 108, 108, 1);
    font-size: 18px;
    text-align: left;
    font-family: PingFangSC-regular;
  }
</style>
