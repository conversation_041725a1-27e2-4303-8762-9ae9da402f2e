!function(i){function o(e,t){var i,n,r;if("string"==typeof e)return s(e);for(i=[],n=e.length,r=0;r<n;r++)i.push(s(e[r]));return t.apply(null,i)}var e,_,t,n,a={},r=function(e,t,i){var n,r={exports:t};"function"==typeof t&&(i.length||(i=[o,r.exports,r]),void 0!==(n=t.apply(null,i))&&(r.exports=n)),a[e]=r.exports},s=function(e){var t=a[e]||i[e];if(!t)throw new Error("`"+e+"` is undefined");return t},u=(_=i,n=o,(t=function(e,t,i){2===arguments.length&&(i=t,t=null),o(t||[],function(){r(e,i,arguments)})})("dollar-third",[],function(){return _.jQuery||_.<PERSON>}),t("dollar",["dollar-third"],function(e){return e}),t("promise-third",["dollar"],function(e){return{Deferred:e.Deferred,when:e.when,isPromise:function(e){return e&&"function"==typeof e.then}}}),t("promise",["promise-third"],function(e){return e}),t("base",["dollar","promise"],function(s,e){function t(){}var n,i,r,o,a,u,c,l,d,f,h,p,g,m,v=Function.call;function b(e,t){return function(){return e.apply(t,arguments)}}return{version:"0.1.2",$:s,Deferred:e.Deferred,isPromise:e.isPromise,when:e.when,browser:(c=navigator.userAgent,l={},d=c.match(/WebKit\/([\d.]+)/),f=c.match(/Chrome\/([\d.]+)/)||c.match(/CriOS\/([\d.]+)/),h=c.match(/MSIE\s([\d\.]+)/)||c.match(/(?:trident)(?:.*rv:([\w.]+))?/i),p=c.match(/Firefox\/([\d.]+)/),g=c.match(/Safari\/([\d.]+)/),m=c.match(/OPR\/([\d.]+)/),d&&(l.webkit=parseFloat(d[1])),f&&(l.chrome=parseFloat(f[1])),h&&(l.ie=parseFloat(h[1])),p&&(l.firefox=parseFloat(p[1])),g&&(l.safari=parseFloat(g[1])),m&&(l.opera=parseFloat(m[1])),l),os:(r=navigator.userAgent,o={},a=r.match(/(?:Android);?[\s\/]+([\d.]+)?/),u=r.match(/(?:iPad|iPod|iPhone).*OS\s([\d_]+)/),a&&(o.android=parseFloat(a[1])),u&&(o.ios=parseFloat(u[1].replace(/_/g,"."))),o),inherits:function(e,t,i){var n,r,o;return"function"==typeof t?(n=t,t=null):n=t&&t.hasOwnProperty("constructor")?t.constructor:function(){return e.apply(this,arguments)},s.extend(!0,n,e,i||{}),n.__super__=e.prototype,n.prototype=(r=e.prototype,Object.create?Object.create(r):((o=function(){}).prototype=r,new o)),t&&s.extend(!0,n.prototype,t),n},noop:t,bindFn:b,log:_.console?b(console.log,console):t,nextTick:function(e){setTimeout(e,1)},slice:(i=[].slice,function(){return v.apply(i,arguments)}),guid:(n=0,function(e){for(var t=(+new Date).toString(32),i=0;i<5;i++)t+=Math.floor(65535*Math.random()).toString(32);return(e||"wu_")+t+(n++).toString(32)}),formatSize:function(e,t,i){var n;for(i=i||["B","K","M","G","TB"];(n=i.shift())&&1024<e;)e/=1024;return("B"===n?e:e.toFixed(t||2))+n}}}),t("mediator",["base"],function(e){var t,r=e.$,o=[].slice,s=/\s+/;function a(e,t,i,n){return r.grep(e,function(e){return e&&(!t||e.e===t)&&(!i||e.cb===i||e.cb._cb===i)&&(!n||e.ctx===n)})}function u(e,i,n){r.each((e||"").split(s),function(e,t){n(t,i)})}function c(e,t){for(var i,n=!1,r=-1,o=e.length;++r<o;)if(!1===(i=e[r]).cb.apply(i.ctx2,t)){n=!0;break}return!n}return t={on:function(e,t,n){var r,o=this;return t&&(r=this._events||(this._events=[]),u(e,t,function(e,t){var i={e:e};i.cb=t,i.ctx=n,i.ctx2=n||o,i.id=r.length,r.push(i)})),this},once:function(e,t,n){var r=this;return t&&u(e,t,function(e,t){var i=function(){return r.off(e,i),t.apply(n||r,arguments)};i._cb=t,r.on(e,i,n)}),r},off:function(e,t,i){var n=this._events;return n&&(e||t||i?u(e,t,function(e,t){r.each(a(n,e,t,i),function(){delete n[this.id]})}):this._events=[]),this},trigger:function(e){var t,i,n;return this._events&&e?(t=o.call(arguments,1),i=a(this._events,e),n=a(this._events,"all"),c(i,t)&&c(n,arguments)):this}},r.extend({installTo:function(e){return r.extend(e,t)}},t)}),t("uploader",["base","mediator"],function(e,r){var o=e.$;function i(e){this.options=o.extend(!0,{},i.options,e),this._init(this.options)}return i.options={},r.installTo(i.prototype),o.each({upload:"start-upload",stop:"stop-upload",getFile:"get-file",getFiles:"get-files",addFile:"add-file",addFiles:"add-file",sort:"sort-files",removeFile:"remove-file",skipFile:"skip-file",retry:"retry",isInProgress:"is-in-progress",makeThumb:"make-thumb",getDimension:"get-dimension",addButton:"add-btn",getRuntimeType:"get-runtime-type",refresh:"refresh",disable:"disable",enable:"enable",reset:"reset"},function(e,t){i.prototype[e]=function(){return this.request(t,arguments)}}),o.extend(i.prototype,{state:"pending",_init:function(e){var t=this;t.request("init",e,function(){t.state="ready",t.trigger("ready")})},option:function(e,t){var i=this.options;if(!(1<arguments.length))return e?i[e]:i;o.isPlainObject(t)&&o.isPlainObject(i[e])?o.extend(i[e],t):i[e]=t},getStats:function(){var e=this.request("get-stats");return{successNum:e.numOfSuccess,cancelNum:e.numOfCancel,invalidNum:e.numOfInvalid,uploadFailNum:e.numOfUploadFailed,queueNum:e.numOfQueue}},trigger:function(e){var t=[].slice.call(arguments,1),i=this.options,n="on"+e.substring(0,1).toUpperCase()+e.substring(1);return!(!1===r.trigger.apply(this,arguments)||o.isFunction(i[n])&&!1===i[n].apply(this,t)||o.isFunction(this[n])&&!1===this[n].apply(this,t)||!1===r.trigger.apply(r,[this,e].concat(t)))},request:e.noop}),e.create=i.create=function(e){return new i(e)},e.Uploader=i}),t("runtime/runtime",["base","mediator"],function(t,e){function n(e){for(var t in e)if(e.hasOwnProperty(t))return t;return null}var r=t.$,o={};function s(e){this.options=r.extend({container:document.body},e),this.uid=t.guid("rt_")}return r.extend(s.prototype,{getContainer:function(){var e,t,i=this.options;return this._container?this._container:(e=r(i.container||document.body),(t=r(document.createElement("div"))).attr("id","rt_"+this.uid),t.css({position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.append(t),e.addClass("webuploader-container"),this._container=t)},init:t.noop,exec:t.noop,destroy:function(){this._container&&this._container.parentNode.removeChild(this.__container),this.off()}}),s.orders="html5,flash",s.addRuntime=function(e,t){o[e]=t},s.hasRuntime=function(e){return!!(e?o[e]:n(o))},s.create=function(e,t){var i;if(t=t||s.orders,r.each(t.split(/\s*,\s*/g),function(){if(o[this])return i=this,!1}),!(i=i||n(o)))throw new Error("Runtime Error");return new o[i](e)},e.installTo(s.prototype),s}),t("runtime/client",["base","mediator","runtime/runtime"],function(o,e,s){var a,n;function t(t,i){var n,e,r=o.Deferred();this.uid=o.guid("client_"),this.runtimeReady=function(e){return r.done(e)},this.connectRuntime=function(e,t){if(n)throw new Error("already connected!");return r.done(t),"string"==typeof e&&a.get(e)&&(n=a.get(e)),(n=n||a.get(null,i))?(o.$.extend(n.options,e),n.__promise.then(r.resolve),n.__client++):((n=s.create(e,e.runtimeOrder)).__promise=r.promise(),n.once("ready",r.resolve),n.init(),a.add(n),n.__client=1),i&&(n.__standalone=i),n},this.getRuntime=function(){return n},this.disconnectRuntime=function(){n&&(n.__client--,n.__client<=0&&(a.remove(n),delete n.__promise,n.destroy()),n=null)},this.exec=function(){if(n){var e=o.slice(arguments);return t&&e.unshift(t),n.exec.apply(this,e)}},this.getRuid=function(){return n&&n.uid},this.destroy=(e=this.destroy,function(){e&&e.apply(this,arguments),this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()})}return n={},a={add:function(e){n[e.uid]=e},get:function(e,t){var i;if(e)return n[e];for(i in n)if(!t||!n[i].__standalone)return n[i];return null},remove:function(e){delete n[e.uid]}},e.installTo(t.prototype),t}),t("lib/dnd",["base","mediator","runtime/client"],function(e,t,i){var n=e.$;function r(e){(e=this.options=n.extend({},r.options,e)).container=n(e.container),e.container.length&&i.call(this,"DragAndDrop")}return r.options={accept:null,disableGlobalDnd:!1},e.inherits(i,{constructor:r,init:function(){var e=this;e.connectRuntime(e.options,function(){e.exec("init"),e.trigger("ready")})},destroy:function(){this.disconnectRuntime()}}),t.installTo(r.prototype),r}),t("widgets/widget",["base","uploader"],function(d,e){var f=d.$,t=e.prototype._init,h={},r=[];function o(e){this.owner=e,this.options=e.options}return f.extend(o.prototype,{init:d.noop,invoke:function(e,t){var i=this.responseMap;return i&&e in i&&i[e]in this&&f.isFunction(this[i[e]])?this[i[e]].apply(this,t):h},request:function(){return this.owner.request.apply(this.owner,arguments)}}),f.extend(e.prototype,{_init:function(){var i=this,n=i._widgets=[];return f.each(r,function(e,t){n.push(new t(i))}),t.apply(i,arguments)},request:function(e,t,i){var n,r,o,s=0,a=this._widgets,u=a.length,c=[],l=[];for(t=function(e){if(e){var t=e.length,i=f.type(e);return 1===e.nodeType&&t||"array"===i||"function"!==i&&"string"!==i&&(0===t||"number"==typeof t&&0<t&&t-1 in e)}}(t)?t:[t];s<u;s++)(n=a[s].invoke(e,t))!==h&&(d.isPromise(n)?l.push(n):c.push(n));return i||l.length?(r=d.when.apply(d,l))[o=r.pipe?"pipe":"then"](function(){var e=d.Deferred(),t=arguments;return setTimeout(function(){e.resolve.apply(e,t)},1),e.promise()})[o](i||d.noop):c[0]}}),e.register=o.register=function(e,t){var i,n={init:"init"};return 1===arguments.length?(t=e).responseMap=n:t.responseMap=f.extend(n,e),i=d.inherits(o,t),r.push(i),i},o}),t("widgets/filednd",["base","uploader","lib/dnd","widgets/widget"],function(o,e,s){var a=o.$;return e.options.dnd="",e.register({init:function(e){if(e.dnd&&"html5"===this.request("predict-runtime-type")){var t,i=this,n=o.Deferred(),r=a.extend({},{disableGlobalDnd:e.disableGlobalDnd,container:e.dnd,accept:e.accept});return(t=new s(r)).once("ready",n.resolve),t.on("drop",function(e){i.request("add-file",[e])}),t.on("accept",function(e){return i.owner.trigger("dndAccept",e)}),t.init(),n.promise()}}})}),t("lib/filepaste",["base","mediator","runtime/client"],function(e,t,i){var n=e.$;function r(e){(e=this.options=n.extend({},e)).container=n(e.container||document.body),i.call(this,"FilePaste")}return e.inherits(i,{constructor:r,init:function(){var e=this;e.connectRuntime(e.options,function(){e.exec("init"),e.trigger("ready")})},destroy:function(){this.exec("destroy"),this.disconnectRuntime(),this.off()}}),t.installTo(r.prototype),r}),t("widgets/filepaste",["base","uploader","lib/filepaste","widgets/widget"],function(o,e,s){var a=o.$;return e.register({init:function(e){if(e.paste&&"html5"===this.request("predict-runtime-type")){var t,i=this,n=o.Deferred(),r=a.extend({},{container:e.paste,accept:e.accept});return(t=new s(r)).once("ready",n.resolve),t.on("paste",function(e){i.owner.request("add-file",[e])}),t.init(),n.promise()}}})}),t("lib/blob",["base","runtime/client"],function(e,i){function t(e,t){this.source=t,this.ruid=e,i.call(this,"Blob"),this.uid=t.uid||this.uid,this.type=t.type||"",this.size=t.size||0,e&&this.connectRuntime(e)}return e.inherits(i,{constructor:t,slice:function(e,t){return this.exec("slice",e,t)},getSource:function(){return this.source}}),t}),t("lib/file",["base","lib/blob"],function(e,n){var r=1,o=/\.([^.]+)$/;return e.inherits(n,function(e,t){var i;n.apply(this,arguments),this.name=t.name||"untitled"+r++,!(i=o.exec(t.name)?RegExp.$1.toLowerCase():"")&&this.type&&(i=/\/(jpg|jpeg|png|gif|bmp)$/i.exec(this.type)?RegExp.$1.toLowerCase():"",this.name+="."+i),!this.type&&~"jpg,jpeg,png,gif,bmp".indexOf(i)&&(this.type="image/"+("jpg"===i?"jpeg":i)),this.ext=i,this.lastModifiedDate=t.lastModifiedDate||(new Date).toLocaleString()})}),t("lib/filepicker",["base","runtime/client","lib/file"],function(e,t,o){var s=e.$;function i(e){if((e=this.options=s.extend({},i.options,e)).container=s(e.id),!e.container.length)throw new Error("按钮指定错误");e.innerHTML=e.innerHTML||e.label||e.container.html()||"",e.button=s(e.button||document.createElement("div")),e.button.html(e.innerHTML),e.container.html(e.button),t.call(this,"FilePicker",!0)}return i.options={button:null,container:null,label:null,innerHTML:null,multiple:!0,accept:null,name:"file"},e.inherits(t,{constructor:i,init:function(){var i=this,n=i.options,r=n.button;r.addClass("webuploader-pick"),i.on("all",function(e){var t;switch(e){case"mouseenter":r.addClass("webuploader-pick-hover");break;case"mouseleave":r.removeClass("webuploader-pick-hover");break;case"change":t=i.exec("getFiles"),i.trigger("select",s.map(t,function(e){return(e=new o(i.getRuid(),e))._refer=n.container,e}),n.container)}}),i.connectRuntime(n,function(){i.refresh(),i.exec("init",n),i.trigger("ready")}),s(_).on("resize",function(){i.refresh()})},refresh:function(){var e=this.getRuntime().getContainer(),t=this.options.button,i=t.outerWidth?t.outerWidth():t.width(),n=t.outerHeight?t.outerHeight():t.height(),r=t.offset();i&&n&&e.css({bottom:"auto",right:"auto",width:i+"px",height:n+"px"}).offset(r)},enable:function(){this.options.button.removeClass("webuploader-pick-disable"),this.refresh()},disable:function(){var e=this.options.button;this.getRuntime().getContainer().css({top:"-99999px"}),e.addClass("webuploader-pick-disable")},destroy:function(){this.runtime&&(this.exec("destroy"),this.disconnectRuntime())}}),i}),t("widgets/filepicker",["base","uploader","lib/filepicker","widgets/widget"],function(a,e,u){var c=a.$;return c.extend(e.options,{pick:null,accept:null}),e.register({"add-btn":"addButton",refresh:"refresh",disable:"disable",enable:"enable"},{init:function(e){return this.pickers=[],e.pick&&this.addButton(e.pick)},refresh:function(){c.each(this.pickers,function(){this.refresh()})},addButton:function(e){var t,i,n,r=this,o=r.options,s=o.accept;if(e)return n=a.Deferred(),c.isPlainObject(e)||(e={id:e}),t=c.extend({},e,{accept:c.isPlainObject(s)?[s]:s,swf:o.swf,runtimeOrder:o.runtimeOrder}),(i=new u(t)).once("ready",n.resolve),i.on("select",function(e){r.owner.request("add-file",[e])}),i.init(),this.pickers.push(i),n.promise()},disable:function(){c.each(this.pickers,function(){this.disable()})},enable:function(){c.each(this.pickers,function(){this.enable()})}})}),t("lib/image",["base","runtime/client","lib/blob"],function(t,i,n){var r=t.$;function o(e){this.options=r.extend({},o.options,e),i.call(this,"Image"),this.on("load",function(){this._info=this.exec("info"),this._meta=this.exec("meta")})}return o.options={quality:90,crop:!1,preserveHeaders:!0,allowMagnify:!0},t.inherits(i,{constructor:o,info:function(e){return e?(this._info=e,this):this._info},meta:function(e){return e?(this._meta=e,this):this._meta},loadFromBlob:function(e){var t=this,i=e.getRuid();this.connectRuntime(i,function(){t.exec("init",t.options),t.exec("loadFromBlob",e)})},resize:function(){var e=t.slice(arguments);return this.exec.apply(this,["resize"].concat(e))},getAsDataUrl:function(e){return this.exec("getAsDataUrl",e)},getAsBlob:function(e){var t=this.exec("getAsBlob",e);return new n(this.getRuid(),t)}}),o}),t("widgets/image",["base","uploader","lib/image","widgets/widget"],function(t,e,s){var a,n,r,u=t.$;function o(){for(var e;r.length&&n<5242880;)e=r.shift(),n+=e[0],e[1]()}return n=0,r=[],a=function(e,t,i){r.push([t,i]),e.once("destroy",function(){n-=t,setTimeout(o,1)}),setTimeout(o,1)},u.extend(e.options,{thumb:{width:110,height:110,quality:70,allowMagnify:!0,crop:!0,preserveHeaders:!1,type:"image/jpeg"},compress:{width:1600,height:1600,quality:90,allowMagnify:!1,crop:!1,preserveHeaders:!0}}),e.register({"make-thumb":"makeThumb","before-send-file":"compressImage"},{makeThumb:function(e,t,i,n){var r,o;(e=this.request("get-file",e)).type.match(/^image/)?(r=u.extend({},this.options.thumb),u.isPlainObject(i)&&(r=u.extend(r,i),i=null),i=i||r.width,n=n||r.height,(o=new s(r)).once("load",function(){e._info=e._info||o.info(),e._meta=e._meta||o.meta(),o.resize(i,n)}),o.once("complete",function(){t(!1,o.getAsDataUrl(r.type)),o.destroy()}),o.once("error",function(){t(!0),o.destroy()}),a(o,e.source.size,function(){e._info&&o.info(e._info),e._meta&&o.meta(e._meta),o.loadFromBlob(e.source)})):t(!0)},compressImage:function(i){var n,r,o=this.options.compress||this.options.resize,e=o&&o.compressSize||307200;if(i=this.request("get-file",i),o&&~"image/jpeg,image/jpg".indexOf(i.type)&&!(i.size<e)&&!i._compressed)return o=u.extend({},o),r=t.Deferred(),n=new s(o),r.always(function(){n.destroy(),n=null}),n.once("error",r.reject),n.once("load",function(){i._info=i._info||n.info(),i._meta=i._meta||n.meta(),n.resize(o.width,o.height)}),n.once("complete",function(){var e,t;try{e=n.getAsBlob(o.type),t=i.size,e.size<t&&(i.source=e,i.size=e.size,i.trigger("resize",e.size,t)),i._compressed=!0,r.resolve()}catch(e){r.resolve()}}),i._info&&n.info(i._info),i._meta&&n.meta(i._meta),n.loadFromBlob(i.source),r.promise()}})}),t("file",["base","mediator"],function(e,t){var i=e.$,n="WU_FILE_",r=0,o=/\.([^.]+)$/,s={};function a(e){this.name=e.name||"Untitled",this.size=e.size||0,this.type=e.type||"application",this.lastModifiedDate=e.lastModifiedDate||+new Date,this.id=n+r++,this.ext=o.exec(this.name)?RegExp.$1:"",this.statusText="",s[this.id]=a.Status.INITED,this.source=e,this.loaded=0,this.on("error",function(e){this.setStatus(a.Status.ERROR,e)})}return i.extend(a.prototype,{setStatus:function(e,t){var i=s[this.id];void 0!==t&&(this.statusText=t),e!==i&&(s[this.id]=e,this.trigger("statuschange",e,i))},getStatus:function(){return s[this.id]},getSource:function(){return this.source},destory:function(){delete s[this.id]}}),t.installTo(a.prototype),a.Status={INITED:"inited",QUEUED:"queued",PROGRESS:"progress",ERROR:"error",COMPLETE:"complete",CANCELLED:"cancelled",INTERRUPT:"interrupt",INVALID:"invalid"},a}),t("queue",["base","mediator","file"],function(e,t,i){var o=e.$,r=i.Status;function n(){this.stats={numOfQueue:0,numOfSuccess:0,numOfCancel:0,numOfProgress:0,numOfUploadFailed:0,numOfInvalid:0},this._queue=[],this._map={}}return o.extend(n.prototype,{append:function(e){return this._queue.push(e),this._fileAdded(e),this},prepend:function(e){return this._queue.unshift(e),this._fileAdded(e),this},getFile:function(e){return"string"!=typeof e?e:this._map[e]},fetch:function(e){var t,i,n=this._queue.length;for(e=e||r.QUEUED,t=0;t<n;t++)if(e===(i=this._queue[t]).getStatus())return i;return null},sort:function(e){"function"==typeof e&&this._queue.sort(e)},getFiles:function(){for(var e,t=[].slice.call(arguments,0),i=[],n=0,r=this._queue.length;n<r;n++)e=this._queue[n],t.length&&!~o.inArray(e.getStatus(),t)||i.push(e);return i},_fileAdded:function(e){var i=this;this._map[e.id]||(this._map[e.id]=e).on("statuschange",function(e,t){i._onFileStatusChange(e,t)}),e.setStatus(r.QUEUED)},_onFileStatusChange:function(e,t){var i=this.stats;switch(t){case r.PROGRESS:i.numOfProgress--;break;case r.QUEUED:i.numOfQueue--;break;case r.ERROR:i.numOfUploadFailed--;break;case r.INVALID:i.numOfInvalid--}switch(e){case r.QUEUED:i.numOfQueue++;break;case r.PROGRESS:i.numOfProgress++;break;case r.ERROR:i.numOfUploadFailed++;break;case r.COMPLETE:i.numOfSuccess++;break;case r.CANCELLED:i.numOfCancel++;break;case r.INVALID:i.numOfInvalid++}}}),t.installTo(n.prototype),n}),t("widgets/queue",["base","uploader","queue","file","lib/file","runtime/client","widgets/widget"],function(c,e,l,t,i,d){var f=c.$,n=/\.\w+$/,o=t.Status;return e.register({"sort-files":"sortFiles","add-file":"addFiles","get-file":"getFile","fetch-file":"fetchFile","get-stats":"getStats","get-files":"getFiles","remove-file":"removeFile",retry:"retry",reset:"reset","accept-file":"acceptFile"},{init:function(e){var t,i,n,r,o,s,a,u=this;if(f.isPlainObject(e.accept)&&(e.accept=[e.accept]),e.accept){for(o=[],n=0,i=e.accept.length;n<i;n++)(r=e.accept[n].extensions)&&o.push(r);o.length&&(s="\\."+o.join(",").replace(/,/g,"$|\\.").replace(/\*/g,".*")+"$"),u.accept=new RegExp(s,"i")}if(u.queue=new l,u.stats=u.queue.stats,"html5"===this.request("predict-runtime-type"))return t=c.Deferred(),(a=new d("Placeholder")).connectRuntime({runtimeOrder:"html5"},function(){u._ruid=a.getRuid(),t.resolve()}),t.promise()},_wrapFile:function(e){if(!(e instanceof t)){if(!(e instanceof i)){if(!this._ruid)throw new Error("Can't add external files.");e=new i(this._ruid,e)}e=new t(e)}return e},acceptFile:function(e){return!(!e||e.size<6||this.accept&&n.exec(e.name)&&!this.accept.test(e.name))},_addFile:function(e){var t=this;if(e=t._wrapFile(e),t.owner.trigger("beforeFileQueued",e)){if(t.acceptFile(e))return t.queue.append(e),t.owner.trigger("fileQueued",e),e;t.owner.trigger("error","Q_TYPE_DENIED",e)}},getFile:function(e){return this.queue.getFile(e)},addFiles:function(e){var t=this;e.length||(e=[e]),e=f.map(e,function(e){return t._addFile(e)}),t.owner.trigger("filesQueued",e),t.options.auto&&t.request("start-upload")},getStats:function(){return this.stats},removeFile:function(e){(e=e.id?e:this.queue.getFile(e)).setStatus(o.CANCELLED),this.owner.trigger("fileDequeued",e)},getFiles:function(){return this.queue.getFiles.apply(this.queue,arguments)},fetchFile:function(){return this.queue.fetch.apply(this.queue,arguments)},retry:function(e,t){var i,n,r;if(e)return(e=e.id?e:this.queue.getFile(e)).setStatus(o.QUEUED),void(t||this.request("start-upload"));for(n=0,r=(i=this.queue.getFiles(o.ERROR)).length;n<r;n++)(e=i[n]).setStatus(o.QUEUED);this.request("start-upload")},sortFiles:function(){return this.queue.sort.apply(this.queue,arguments)},reset:function(){this.queue=new l,this.stats=this.queue.stats}})}),t("widgets/runtime",["uploader","runtime/runtime","widgets/widget"],function(e,r){return e.support=function(){return r.hasRuntime.apply(r,arguments)},e.register({"predict-runtime-type":"predictRuntmeType"},{init:function(){if(!this.predictRuntmeType())throw Error("Runtime Error")},predictRuntmeType:function(){var e,t,i=this.options.runtimeOrder||r.orders,n=this.type;if(!n)for(e=0,t=(i=i.split(/\s*,\s*/g)).length;e<t;e++)if(r.hasRuntime(i[e])){this.type=n=i[e];break}return n}})}),t("lib/transport",["base","runtime/client","mediator"],function(e,i,t){var n=e.$;function r(e){var t=this;e=t.options=n.extend(!0,{},r.options,e||{}),i.call(this,"Transport"),this._blob=null,this._formData=e.formData||{},this._headers=e.headers||{},this.on("progress",this._timeout),this.on("load error",function(){t.trigger("progress",1),clearTimeout(t._timer)})}return r.options={server:"",method:"POST",withCredentials:!1,fileVal:"file",timeout:12e4,formData:{},headers:{},sendAsBinary:!1},n.extend(r.prototype,{appendBlob:function(e,t,i){var n=this,r=n.options;n.getRuid()&&n.disconnectRuntime(),n.connectRuntime(t.ruid,function(){n.exec("init")}),n._blob=t,r.fileVal=e||r.fileVal,r.filename=i||r.filename},append:function(e,t){"object"==typeof e?n.extend(this._formData,e):this._formData[e]=t},setRequestHeader:function(e,t){"object"==typeof e?n.extend(this._headers,e):this._headers[e]=t},send:function(e){this.exec("send",e),this._timeout()},abort:function(){return clearTimeout(this._timer),this.exec("abort")},destroy:function(){this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()},getResponse:function(){return this.exec("getResponse")},getResponseAsJson:function(){return this.exec("getResponseAsJson")},getStatus:function(){return this.exec("getStatus")},_timeout:function(){var e=this,t=e.options.timeout;t&&(clearTimeout(e._timer),e._timer=setTimeout(function(){e.abort(),e.trigger("error","timeout")},t))}}),t.installTo(r.prototype),r}),t("widgets/upload",["base","uploader","file","lib/transport","widgets/widget"],function(l,e,t,d){var f=l.$,o=l.isPromise,h=t.Status;f.extend(e.options,{prepareNextFile:!1,chunked:!1,chunkSize:5242880,chunkRetry:2,threads:3,formData:null}),e.register({"start-upload":"start","stop-upload":"stop","skip-file":"skipFile","is-in-progress":"isInProgress"},{init:function(){var e=this.owner;this.runing=!1,this.pool=[],this.pending=[],this.remaning=0,this.__tick=l.bindFn(this._tick,this),e.on("uploadComplete",function(e){e.blocks&&f.each(e.blocks,function(e,t){t.transport&&(t.transport.abort(),t.transport.destroy()),delete t.transport}),delete e.blocks,delete e.remaning})},start:function(){var n=this;f.each(n.request("get-files",h.INVALID),function(){n.request("remove-file",this)}),n.runing||(n.runing=!0,f.each(n.pool,function(e,t){var i=t.file;i.getStatus()===h.INTERRUPT&&(i.setStatus(h.PROGRESS),n._trigged=!1,t.transport&&t.transport.send())}),n._trigged=!1,n.owner.trigger("startUpload"),l.nextTick(n.__tick))},stop:function(e){!1!==this.runing&&(this.runing=!1,e&&f.each(this.pool,function(e,t){t.transport&&t.transport.abort(),t.file.setStatus(h.INTERRUPT)}),this.owner.trigger("stopUpload"))},isInProgress:function(){return!!this.runing},getStats:function(){return this.request("get-stats")},skipFile:function(e,t){(e=this.request("get-file",e)).setStatus(t||h.COMPLETE),e.skipped=!0,e.blocks&&f.each(e.blocks,function(e,t){var i=t.transport;i&&(i.abort(),i.destroy(),delete t.transport)}),this.owner.trigger("uploadSkip",e)},_tick:function(){var e,t,i=this,n=i.options;if(i._promise)return i._promise.always(i.__tick);i.pool.length<n.threads&&(t=i._nextBlock())?(i._trigged=!1,e=function(e){i._promise=null,e&&e.file&&i._startSend(e),l.nextTick(i.__tick)},i._promise=o(t)?t.always(e):e(t)):i.remaning||i.getStats().numOfQueue||(i.runing=!1,i._trigged||l.nextTick(function(){i.owner.trigger("uploadFinished")}),i._trigged=!0)},_nextBlock:function(){var e,t,i=this,n=i._act,r=i.options;return n&&n.has()&&n.file.getStatus()===h.PROGRESS?(r.prepareNextFile&&!i.pending.length&&i._prepareNextFile(),n.fetch()):i.runing?(!i.pending.length&&i.getStats().numOfQueue&&i._prepareNextFile(),e=i.pending.shift(),t=function(e){return e?(n=function(e,t){for(var i,n=[],r=e.source.size,o=t?Math.ceil(r/t):1,s=0,a=0;a<o;)i=Math.min(t,r-s),n.push({file:e,start:s,end:t?s+i:r,total:r,chunks:o,chunk:a++}),s+=i;return e.blocks=n.concat(),e.remaning=n.length,{file:e,has:function(){return!!n.length},fetch:function(){return n.shift()}}}(e,r.chunked?r.chunkSize:0),(i._act=n).fetch()):null},o(e)?e[e.pipe?"pipe":"then"](t):t(e)):void 0},_prepareNextFile:function(){var t,i=this,n=i.request("fetch-file"),r=i.pending;n&&((t=i.request("before-send-file",n,function(){return n.getStatus()===h.QUEUED?(i.owner.trigger("uploadStart",n),n.setStatus(h.PROGRESS),n):i._finishFile(n)})).done(function(){var e=f.inArray(t,r);~e&&r.splice(e,1,n)}),t.fail(function(e){n.setStatus(h.ERROR,e),i.owner.trigger("uploadError",n,e),i.owner.trigger("uploadComplete",n)}),r.push(t))},_popBlock:function(e){var t=f.inArray(e,this.pool);this.pool.splice(t,1),e.file.remaning--,this.remaning--},_startSend:function(e){var t=this,i=e.file;t.pool.push(e),t.remaning++,e.blob=1===e.chunks?i.source:i.source.slice(e.start,e.end),t.request("before-send",e,function(){i.getStatus()===h.PROGRESS?t._doSend(e):(t._popBlock(e),l.nextTick(t.__tick))}).fail(function(){1===i.remaning?t._finishFile(i).always(function(){e.percentage=1,t._popBlock(e),t.owner.trigger("uploadComplete",i),l.nextTick(t.__tick)}):(e.percentage=1,t._popBlock(e),l.nextTick(t.__tick))})},_doSend:function(n){var i,r,t=this,o=t.owner,s=t.options,a=n.file,u=new d(s),e=f.extend({},s.formData),c=f.extend({},s.headers);(n.transport=u).on("destroy",function(){delete n.transport,t._popBlock(n),l.nextTick(t.__tick)}),u.on("progress",function(e){var t=0,i=0;t=n.percentage=e,1<n.chunks&&(f.each(a.blocks,function(e,t){i+=(t.percentage||0)*(t.end-t.start)}),t=i/a.size),o.trigger("uploadProgress",a,t||0)}),i=function(t){var e;return(r=u.getResponseAsJson()||{})._raw=u.getResponse(),e=function(e){t=e},o.trigger("uploadAccept",n,r,e)||(t=t||"server"),t},u.on("error",function(e,t){n.retried=n.retried||0,1<n.chunks&&~"http,abort".indexOf(e)&&n.retried<s.chunkRetry?(n.retried++,u.send()):(t||"server"!==e||(e=i(e)),a.setStatus(h.ERROR,e),o.trigger("uploadError",a,e),o.trigger("uploadComplete",a))}),u.on("load",function(){var e;(e=i())?u.trigger("error",e,!0):1===a.remaning?t._finishFile(a,r):u.destroy()}),e=f.extend(e,{id:a.id,name:a.name,type:a.type,lastModifiedDate:a.lastModifiedDate,size:a.size}),1<n.chunks&&f.extend(e,{chunks:n.chunks,chunk:n.chunk}),o.trigger("uploadBeforeSend",n,e,c),u.appendBlob(s.fileVal,n.blob,a.name),u.append(e),u.setRequestHeader(c),u.send()},_finishFile:function(t,e,i){var n=this.owner;return n.request("after-send-file",arguments,function(){t.setStatus(h.COMPLETE),n.trigger("uploadSuccess",t,e,i)}).fail(function(e){t.getStatus()===h.PROGRESS&&t.setStatus(h.ERROR,e),n.trigger("uploadError",t,e)}).always(function(){n.trigger("uploadComplete",t)})}})}),t("widgets/validator",["base","uploader","file","widgets/widget"],function(e,t,i){var n,r=e.$,o={};return n={addValidator:function(e,t){o[e]=t},removeValidator:function(e){delete o[e]}},t.register({init:function(){var e=this;r.each(o,function(){this.call(e.owner)})}}),n.addValidator("fileNumLimit",function(){var e=this,t=e.options,i=0,n=t.fileNumLimit>>0,r=!0;n&&(e.on("beforeFileQueued",function(e){return n<=i&&r&&(r=!1,this.trigger("error","Q_EXCEED_NUM_LIMIT",n,e),setTimeout(function(){r=!0},1)),!(n<=i)}),e.on("fileQueued",function(){i++}),e.on("fileDequeued",function(){i--}),e.on("uploadFinished",function(){i=0}))}),n.addValidator("fileSizeLimit",function(){var e=this,t=e.options,i=0,n=t.fileSizeLimit>>0,r=!0;n&&(e.on("beforeFileQueued",function(e){var t=i+e.size>n;return t&&r&&(r=!1,this.trigger("error","Q_EXCEED_SIZE_LIMIT",n,e),setTimeout(function(){r=!0},1)),!t}),e.on("fileQueued",function(e){i+=e.size}),e.on("fileDequeued",function(e){i-=e.size}),e.on("uploadFinished",function(){i=0}))}),n.addValidator("fileSingleSizeLimit",function(){var t=this.options.fileSingleSizeLimit;t&&this.on("beforeFileQueued",function(e){if(e.size>t)return e.setStatus(i.Status.INVALID,"exceed_size"),this.trigger("error","F_EXCEED_SIZE",e),!1})}),n.addValidator("duplicate",function(){var e=this.options,i={};e.duplicate||(this.on("beforeFileQueued",function(e){var t=e.__hash||(e.__hash=function(e){for(var t=0,i=0,n=e.length;i<n;i++)t=e.charCodeAt(i)+(t<<6)+(t<<16)-t;return t}(e.name+e.size+e.lastModifiedDate));if(i[t])return this.trigger("error","F_DUPLICATE",e),!1}),this.on("fileQueued",function(e){var t=e.__hash;t&&(i[t]=!0)}),this.on("fileDequeued",function(e){var t=e.__hash;t&&delete i[t]}))}),n}),t("runtime/compbase",[],function(){return function(e,t){this.owner=e,this.options=e.options,this.getRuntime=function(){return t},this.getRuid=function(){return t.uid},this.trigger=function(){return e.trigger.apply(e,arguments)}}}),t("runtime/html5/runtime",["base","runtime/runtime","runtime/compbase"],function(a,t,i){var u={};function e(){var o={},s=this,e=this.destory;t.apply(s,arguments),s.type="html5",s.exec=function(e,t){var i,n=this.uid,r=a.slice(arguments,2);if(u[e]&&(i=o[n]=o[n]||new u[e](this,s))[t])return i[t].apply(i,r)},s.destory=function(){return e&&e.apply(this,arguments)}}return a.inherits(t,{constructor:e,init:function(){var e=this;setTimeout(function(){e.trigger("ready")},1)}}),e.register=function(e,t){return u[e]=a.inherits(i,t)},_.Blob&&_.FileReader&&_.DataView&&t.addRuntime("html5",e),e}),t("runtime/html5/blob",["runtime/html5/runtime","lib/blob"],function(e,n){return e.register("Blob",{slice:function(e,t){var i=this.owner.source;return i=(i.slice||i.webkitSlice||i.mozSlice).call(i,e,t),new n(this.getRuid(),i)}})}),t("runtime/html5/dnd",["base","runtime/html5/runtime","lib/file"],function(f,e,r){var o=f.$,s="webuploader-dnd-";return e.register("DragAndDrop",{init:function(){var e=this.elem=this.options.container;this.dragEnterHandler=f.bindFn(this._dragEnterHandler,this),this.dragOverHandler=f.bindFn(this._dragOverHandler,this),this.dragLeaveHandler=f.bindFn(this._dragLeaveHandler,this),this.dropHandler=f.bindFn(this._dropHandler,this),this.dndOver=!1,e.on("dragenter",this.dragEnterHandler),e.on("dragover",this.dragOverHandler),e.on("dragleave",this.dragLeaveHandler),e.on("drop",this.dropHandler),this.options.disableGlobalDnd&&(o(document).on("dragover",this.dragOverHandler),o(document).on("drop",this.dropHandler))},_dragEnterHandler:function(e){var t,i=this,n=i._denied||!1;return e=e.originalEvent||e,i.dndOver||(i.dndOver=!0,(t=e.dataTransfer.items)&&t.length&&(i._denied=n=!i.trigger("accept",t)),i.elem.addClass(s+"over"),i.elem[n?"addClass":"removeClass"](s+"denied")),e.dataTransfer.dropEffect=n?"none":"copy",!1},_dragOverHandler:function(e){var t=this.elem.parent().get(0);return t&&!o.contains(t,e.currentTarget)||(clearTimeout(this._leaveTimer),this._dragEnterHandler.call(this,e)),!1},_dragLeaveHandler:function(){var e,t=this;return e=function(){t.dndOver=!1,t.elem.removeClass(s+"over "+s+"denied")},clearTimeout(t._leaveTimer),t._leaveTimer=setTimeout(e,100),!1},_dropHandler:function(e){var t=this,i=t.getRuid(),n=t.elem.parent().get(0);return n&&!o.contains(n,e.currentTarget)||(t._getTansferFiles(e,function(e){t.trigger("drop",o.map(e,function(e){return new r(i,e)}))}),t.dndOver=!1,t.elem.removeClass(s+"over")),!1},_getTansferFiles:function(e,t){var i,n,r,o,s,a,u,c,l=[],d=[];for(i=(r=(e=e.originalEvent||e).dataTransfer).items,n=r.files,c=!(!i||!i[0].webkitGetAsEntry),a=0,u=n.length;a<u;a++)o=n[a],s=i&&i[a],c&&s.webkitGetAsEntry().isDirectory?d.push(this._traverseDirectoryTree(s.webkitGetAsEntry(),l)):l.push(o);f.when.apply(f,d).done(function(){l.length&&t(l)})},_traverseDirectoryTree:function(e,o){var s=f.Deferred(),a=this;return e.isFile?e.file(function(e){o.push(e),s.resolve()}):e.isDirectory&&e.createReader().readEntries(function(e){var t,i=e.length,n=[],r=[];for(t=0;t<i;t++)n.push(a._traverseDirectoryTree(e[t],r));f.when.apply(f,n).then(function(){o.push.apply(o,r),s.resolve()},s.reject)}),s.promise()},destroy:function(){var e=this.elem;e.off("dragenter",this.dragEnterHandler),e.off("dragover",this.dragEnterHandler),e.off("dragleave",this.dragLeaveHandler),e.off("drop",this.dropHandler),this.options.disableGlobalDnd&&(o(document).off("dragover",this.dragOverHandler),o(document).off("drop",this.dropHandler))}})}),t("runtime/html5/filepaste",["base","runtime/html5/runtime","lib/file"],function(a,e,u){return e.register("FilePaste",{init:function(){var e,t,i,n,r=this.options,o=this.elem=r.container,s=".*";if(r.accept){for(e=[],t=0,i=r.accept.length;t<i;t++)(n=r.accept[t].mimeTypes)&&e.push(n);e.length&&(s=(s=e.join(",")).replace(/,/g,"|").replace(/\*/g,".*"))}this.accept=s=new RegExp(s,"i"),this.hander=a.bindFn(this._pasteHander,this),o.on("paste",this.hander)},_pasteHander:function(e){var t,i,n,r,o,s=[],a=this.getRuid();for(r=0,o=(t=(e=e.originalEvent||e).clipboardData.items).length;r<o;r++)"file"===(i=t[r]).kind&&(n=i.getAsFile())&&s.push(new u(a,n));s.length&&(e.preventDefault(),e.stopPropagation(),this.trigger("paste",s))},destroy:function(){this.elem.off("paste",this.hander)}})}),t("runtime/html5/filepicker",["base","runtime/html5/runtime"],function(e,t){var l=e.$;return t.register("FilePicker",{init:function(){var e,t,i,n,r=this.getRuntime().getContainer(),o=this,s=o.owner,a=o.options,u=l(document.createElement("label")),c=l(document.createElement("input"));if(c.attr("type","file"),c.attr("name",a.name),c.addClass("webuploader-element-invisible"),u.on("click",function(){c.trigger("click")}),u.css({opacity:0,width:"100%",height:"100%",display:"block",cursor:"pointer",background:"#ffffff"}),a.multiple&&c.attr("multiple","multiple"),a.accept&&0<a.accept.length){for(e=[],t=0,i=a.accept.length;t<i;t++)e.push(a.accept[t].mimeTypes);c.attr("accept",e.join(","))}r.append(c),r.append(u),n=function(e){s.trigger(e.type)},c.on("change",function(e){var t,i=arguments.callee;o.files=e.target.files,t=this.cloneNode(!0),this.parentNode.replaceChild(t,this),c.off(),c=l(t).on("change",i).on("mouseenter mouseleave",n),s.trigger("change")}),u.on("mouseenter mouseleave",n)},getFiles:function(){return this.files},destroy:function(){}})}),t("runtime/html5/util",["base"],function(e){var t=_.createObjectURL&&_||_.URL&&URL.revokeObjectURL&&URL||_.webkitURL,i=e.noop,n=i;return t&&(i=function(){return t.createObjectURL.apply(t,arguments)},n=function(){return t.revokeObjectURL.apply(t,arguments)}),{createObjectURL:i,revokeObjectURL:n,dataURL2Blob:function(e){var t,i,n,r,o,s;for(t=(~(s=e.split(","))[0].indexOf("base64")?atob:decodeURIComponent)(s[1]),n=new ArrayBuffer(t.length),i=new Uint8Array(n),r=0;r<t.length;r++)i[r]=t.charCodeAt(r);return o=s[0].split(":")[1].split(";")[0],this.arrayBufferToBlob(n,o)},dataURL2ArrayBuffer:function(e){var t,i,n,r;for(t=(~(r=e.split(","))[0].indexOf("base64")?atob:decodeURIComponent)(r[1]),i=new Uint8Array(t.length),n=0;n<t.length;n++)i[n]=t.charCodeAt(n);return i.buffer},arrayBufferToBlob:function(e,t){var i,n=_.BlobBuilder||_.WebKitBlobBuilder;return n?((i=new n).append(e),i.getBlob(t)):new Blob([e],t?{type:t}:{})},canvasToDataUrl:function(e,t,i){return e.toDataURL(t,i/100)},parseMeta:function(e,t){t(!1,{})},updateImageHead:function(e){return e}}}),t("runtime/html5/imagemeta",["runtime/html5/util"],function(e){var d;return d={parsers:{65505:[]},maxMetaDataSize:262144,parse:function(e,t){var i=this,n=new FileReader;n.onload=function(){t(!1,i._parse(this.result)),n=n.onload=n.onerror=null},n.onerror=function(e){t(e.message),n=n.onload=n.onerror=null},e=e.slice(0,i.maxMetaDataSize),n.readAsArrayBuffer(e.getSource())},_parse:function(e,t){if(!(e.byteLength<6)){var i,n,r,o,s=new DataView(e),a=2,u=s.byteLength-4,c=a,l={};if(65496===s.getUint16(0)){for(;a<u&&(65504<=(i=s.getUint16(a))&&i<=65519||65534===i)&&!(a+(n=s.getUint16(a+2)+2)>s.byteLength);){if(r=d.parsers[i],!t&&r)for(o=0;o<r.length;o+=1)r[o].call(d,s,a,n,l);c=a+=n}6<c&&(e.slice?l.imageHead=e.slice(2,c):l.imageHead=new Uint8Array(e).subarray(2,c))}return l}},updateImageHead:function(e,t){var i,n,r,o=this._parse(e,!0);return r=2,o.imageHead&&(r=2+o.imageHead.byteLength),n=e.slice?e.slice(r):new Uint8Array(e).subarray(r),(i=new Uint8Array(t.byteLength+2+n.byteLength))[0]=255,i[1]=216,i.set(new Uint8Array(t),2),i.set(new Uint8Array(n),t.byteLength+2),i.buffer}},e.parseMeta=function(){return d.parse.apply(d,arguments)},e.updateImageHead=function(){return d.updateImageHead.apply(d,arguments)},d}),t("runtime/html5/imagemeta/exif",["base","runtime/html5/imagemeta"],function(h,e){var p={ExifMap:function(){return this}};return p.ExifMap.prototype.map={Orientation:274},p.ExifMap.prototype.get=function(e){return this[e]||this[this.map[e]]},p.exifTagTypes={1:{getValue:function(e,t){return e.getUint8(t)},size:1},2:{getValue:function(e,t){return String.fromCharCode(e.getUint8(t))},size:1,ascii:!0},3:{getValue:function(e,t,i){return e.getUint16(t,i)},size:2},4:{getValue:function(e,t,i){return e.getUint32(t,i)},size:4},5:{getValue:function(e,t,i){return e.getUint32(t,i)/e.getUint32(t+4,i)},size:8},9:{getValue:function(e,t,i){return e.getInt32(t,i)},size:4},10:{getValue:function(e,t,i){return e.getInt32(t,i)/e.getInt32(t+4,i)},size:8}},p.exifTagTypes[7]=p.exifTagTypes[1],p.getExifValue=function(e,t,i,n,r,o){var s,a,u,c,l,d,f=p.exifTagTypes[n];if(f){if(!((a=4<(s=f.size*r)?t+e.getUint32(i+8,o):i+8)+s>e.byteLength)){if(1===r)return f.getValue(e,a,o);for(u=[],c=0;c<r;c+=1)u[c]=f.getValue(e,a+c*f.size,o);if(f.ascii){for(l="",c=0;c<u.length&&"\0"!==(d=u[c]);c+=1)l+=d;return l}return u}h.log("Invalid Exif data: Invalid data offset.")}else h.log("Invalid Exif data: Invalid tag type.")},p.parseExifTag=function(e,t,i,n,r){var o=e.getUint16(i,n);r.exif[o]=p.getExifValue(e,t,i,e.getUint16(i+2,n),e.getUint32(i+4,n),n)},p.parseExifTags=function(e,t,i,n,r){var o,s,a;if(i+6>e.byteLength)h.log("Invalid Exif data: Invalid directory offset.");else{if(!((s=i+2+12*(o=e.getUint16(i,n)))+4>e.byteLength)){for(a=0;a<o;a+=1)this.parseExifTag(e,t,i+2+12*a,n,r);return e.getUint32(s,n)}h.log("Invalid Exif data: Invalid directory size.")}},p.parseExifData=function(e,t,i,n){var r,o,s=t+10;if(1165519206===e.getUint32(t+4))if(s+8>e.byteLength)h.log("Invalid Exif data: Invalid segment size.");else if(0===e.getUint16(t+8)){switch(e.getUint16(s)){case 18761:r=!0;break;case 19789:r=!1;break;default:return void h.log("Invalid Exif data: Invalid byte alignment marker.")}42===e.getUint16(s+2,r)?(o=e.getUint32(s+4,r),n.exif=new p.ExifMap,o=p.parseExifTags(e,s,s+o,r,n)):h.log("Invalid Exif data: Missing TIFF marker.")}else h.log("Invalid Exif data: Missing byte alignment offset.")},e.parsers[65505].push(p.parseExifData),p}),t("runtime/html5/image",["base","runtime/html5/runtime","runtime/html5/util"],function(e,t,r){return t.register("Image",{modified:!1,init:function(){var i=this,e=new Image;e.onload=function(){i._info={type:i.type,width:this.width,height:this.height},i._metas||"image/jpeg"!==i.type?i.owner.trigger("load"):r.parseMeta(i._blob,function(e,t){i._metas=t,i.owner.trigger("load")})},e.onerror=function(){i.owner.trigger("error")},i._img=e},loadFromBlob:function(e){var t=this._img;this._blob=e,this.type=e.type,t.src=r.createObjectURL(e.getSource()),this.owner.once("load",function(){r.revokeObjectURL(t.src)})},resize:function(e,t){var i=this._canvas||(this._canvas=document.createElement("canvas"));this._resize(this._img,i,e,t),this._blob=null,this.modified=!0,this.owner.trigger("complete")},getAsBlob:function(e){var t,i=this._blob,n=this.options;if(e=e||this.type,this.modified||this.type!==e){if(t=this._canvas,"image/jpeg"===e){if(i=r.canvasToDataUrl(t,"image/jpeg",n.quality),n.preserveHeaders&&this._metas&&this._metas.imageHead)return i=r.dataURL2ArrayBuffer(i),i=r.updateImageHead(i,this._metas.imageHead),i=r.arrayBufferToBlob(i,e)}else i=r.canvasToDataUrl(t,e);i=r.dataURL2Blob(i)}return i},getAsDataUrl:function(e){var t=this.options;return"image/jpeg"===(e=e||this.type)?r.canvasToDataUrl(this._canvas,e,t.quality):this._canvas.toDataURL(e)},getOrientation:function(){return this._metas&&this._metas.exif&&this._metas.exif.get("Orientation")||1},info:function(e){return e?(this._info=e,this):this._info},meta:function(e){return e?(this._meta=e,this):this._meta},destroy:function(){var e=this._canvas;this._img.onload=null,e&&(e.getContext("2d").clearRect(0,0,e.width,e.height),e.width=e.height=0,this._canvas=null),this._img.src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs%3D",this._img=this._blob=null},_resize:function(e,t,i,n){var r,o,s,a,u,c=this.options,l=e.width,d=e.height,f=this.getOrientation();~[5,6,7,8].indexOf(f)&&(i^=n,i^=n^=i),r=Math[c.crop?"max":"min"](i/l,n/d),c.allowMagnify||(r=Math.min(1,r)),o=l*r,s=d*r,c.crop?(t.width=i,t.height=n):(t.width=o,t.height=s),a=(t.width-o)/2,u=(t.height-s)/2,c.preserveHeaders||this._rotate2Orientaion(t,f),this._renderImageToCanvas(t,e,a,u,o,s)},_rotate2Orientaion:function(e,t){var i=e.width,n=e.height,r=e.getContext("2d");switch(t){case 5:case 6:case 7:case 8:e.width=n,e.height=i}switch(t){case 2:r.translate(i,0),r.scale(-1,1);break;case 3:r.translate(i,n),r.rotate(Math.PI);break;case 4:r.translate(0,n),r.scale(1,-1);break;case 5:r.rotate(.5*Math.PI),r.scale(1,-1);break;case 6:r.rotate(.5*Math.PI),r.translate(0,-n);break;case 7:r.rotate(.5*Math.PI),r.translate(i,-n),r.scale(-1,1);break;case 8:r.rotate(-.5*Math.PI),r.translate(-i,0)}},_renderImageToCanvas:function(){if(!e.os.ios)return function(e,t,i,n,r,o){e.getContext("2d").drawImage(t,i,n,r,o)};function k(e,t,i){var n,r,o=document.createElement("canvas"),s=o.getContext("2d"),a=0,u=i,c=i;for(o.width=1,o.height=i,s.drawImage(e,0,0),n=s.getImageData(0,0,1,i).data;a<c;)0===n[4*(c-1)+3]?u=c:a=c,c=u+a>>1;return 0==(r=c/i)?1:r}return 7<=e.os.ios?function(e,t,i,n,r,o){var s=t.naturalWidth,a=t.naturalHeight,u=k(t,0,a);return e.getContext("2d").drawImage(t,0,0,s*u,a*u,i,n,r,o)}:function(e,t,i,n,r,o){var s,a,u,c,l,d,f,h,p,g,m,v=t.naturalWidth,b=t.naturalHeight,_=e.getContext("2d"),y=1048576<(m=(h=t).naturalWidth)*h.naturalHeight&&((p=document.createElement("canvas")).width=p.height=1,(g=p.getContext("2d")).drawImage(h,1-m,0),0===g.getImageData(0,0,1,1).data[3]),w="image/jpeg"===this.type,x=1024,R=0,E=0;for(y&&(v/=2,b/=2),_.save(),(s=document.createElement("canvas")).width=s.height=x,a=s.getContext("2d"),u=w?k(t,0,b):1,c=Math.ceil(x*r/v),l=Math.ceil(x*o/b/u);R<b;){for(f=d=0;d<v;)a.clearRect(0,0,x,x),a.drawImage(t,-d,-R),_.drawImage(s,0,0,x,x,i+f,n+E,c,l),d+=x,f+=c;R+=x,E+=l}_.restore(),s=a=null}}()})}),t("runtime/html5/transport",["base","runtime/html5/runtime"],function(u,e){var t=u.noop,c=u.$;return e.register("Transport",{init:function(){this._status=0,this._response=null},send:function(){var i,e,t,n=this.owner,r=this.options,o=this._initAjax(),s=n._blob,a=r.server;r.sendAsBinary?(a+=(/\?/.test(a)?"&":"?")+c.param(n._formData),e=s.getSource()):(i=new FormData,c.each(n._formData,function(e,t){i.append(e,t)}),i.append(r.fileVal,s.getSource(),r.filename||n._formData.name||"")),r.withCredentials&&"withCredentials"in o?(o.open(r.method,a,!0),o.withCredentials=!0):o.open(r.method,a),this._setRequestHeader(o,r.headers),e?(o.overrideMimeType("application/octet-stream"),u.os.android?((t=new FileReader).onload=function(){o.send(this.result),t=t.onload=null},t.readAsArrayBuffer(e)):o.send(e)):o.send(i)},getResponse:function(){return this._response},getResponseAsJson:function(){return this._parseJson(this._response)},getStatus:function(){return this._status},abort:function(){var e=this._xhr;e&&(e.upload.onprogress=t,e.onreadystatechange=t,e.abort(),this._xhr=e=null)},destroy:function(){this.abort()},_initAjax:function(){var i=this,e=new XMLHttpRequest;return!this.options.withCredentials||"withCredentials"in e||"undefined"==typeof XDomainRequest||(e=new XDomainRequest),e.upload.onprogress=function(e){var t=0;return e.lengthComputable&&(t=e.loaded/e.total),i.trigger("progress",t)},e.onreadystatechange=function(){if(4===e.readyState)return e.upload.onprogress=t,e.onreadystatechange=t,i._xhr=null,i._status=e.status,200<=e.status&&e.status<300?(i._response=e.responseText,i.trigger("load")):500<=e.status&&e.status<600?(i._response=e.responseText,i.trigger("error","server")):i.trigger("error",i._status?"http":"abort")},i._xhr=e},_setRequestHeader:function(i,e){c.each(e,function(e,t){i.setRequestHeader(e,t)})},_parseJson:function(e){var t;try{t=JSON.parse(e)}catch(e){t={}}return t}})}),t("preset/html5only",["base","widgets/filednd","widgets/filepaste","widgets/filepicker","widgets/image","widgets/queue","widgets/runtime","widgets/upload","widgets/validator","runtime/html5/blob","runtime/html5/dnd","runtime/html5/filepaste","runtime/html5/filepicker","runtime/html5/imagemeta/exif","runtime/html5/image","runtime/html5/transport"],function(e){return e}),t("webuploader",["preset/html5only"],function(e){return e}),n("webuploader"));!function(e){var t,i,n,r,o,s;for(t in s=function(e){return e&&e.charAt(0).toUpperCase()+e.substr(1)},a)if(i=e,a.hasOwnProperty(t)){for(o=s((n=t.split("/")).pop());r=s(n.shift());)i[r]=i[r]||{},i=i[r];i[o]=a[t]}}(u),"object"==typeof module&&"object"==typeof module.exports?module.exports=u:"function"==typeof define&&define.amd?define([],u):(e=i.WebUploader,i.WebUploader=u,i.WebUploader.noConflict=function(){i.WebUploader=e})}(this);