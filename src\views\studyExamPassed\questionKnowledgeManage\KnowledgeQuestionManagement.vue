<template>
  <div class="question-management-page">
    <div class="main">
      <!-- 搜索区域 -->
      <div class="search-container">
        <el-form inline ref="form" :model="searchForm">
          <el-form-item label="题干搜索：">
            <el-input v-model="searchForm.questionText" placeholder="请输入题干关键词" :maxlength="50" show-word-limit style="width: 222px"></el-input>
          </el-form-item>

          <el-form-item label="题目类型：">
            <el-select v-model="searchForm.questionType" placeholder="请选择">
              <el-option label="全部" value=""></el-option>
              <el-option label="单选题" value="1"></el-option>
              <el-option label="多选题" value="2"></el-option>
              <el-option label="填空题(系统作答)" value="3"></el-option>
              <el-option label="填空题(拍照上传)" value="4"></el-option>
              <el-option label="主观题" value="5"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="创建时间段：">
            <el-date-picker
              style="width: 222px"
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>

          <el-form-item label="关联状态：">
            <el-select v-model="searchForm.isRelated" placeholder="请选择">
              <el-option label="全部" value="2"></el-option>
              <el-option label="已关联知识点" value="1"></el-option>
              <el-option label="未关联知识点" value="0"></el-option>
            </el-select>
          </el-form-item>

          <div class="button-group">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </div>
        </el-form>
      </div>

      <!-- 题目列表 -->
      <div
        class="question-list-outer"
        v-loading="pageLoading"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255,255,255,0.9)"
      >
        <div class="question-list-container">
          <div v-if="!pageLoading && questionList.length === 0" class="empty-wrapper">
            <div class="custom-empty">
              <i class="el-icon-folder-opened custom-empty__icon"></i>
              <div class="custom-empty__desc">{{ emptyDesc }}</div>
              <div class="custom-empty__tips" v-if="emptyTips">{{ emptyTips }}</div>
            </div>
          </div>

          <div class="question-item" v-for="(item, index) in questionList" :key="item.id">
            <div class="question-info">
              <div class="question-header">
                <span class="question-title" v-if="index == 0">共筛出{{ tableTotal }}条数据，共{{ totalScore }}分数</span>
                <table>
                  <tbody>
                    <tr>
                      <td>题目ID: {{ item.id }}</td>
                      <td>课程大类: {{ item.courseCategory }}</td>
                      <td>题目类型: {{ item.questionType | questionsTypeFilter }}</td>
                      <td>分值: {{ item.score }}分</td>
                      <td>最近修改时间: {{ item.updateTime }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div class="question-stem">
                <div class="stem-content" v-html="item.questionText ? index + 1 + '.' + renderFormulaCached(item.questionText) : '无'"></div>
                <div class="stem-image" v-if="item.caption.length == 1">
                  <el-image class="image" fit="cover" :src="item.caption[0]" :preview-src-list="item.caption" lazy></el-image>
                </div>

                <!-- 内容有多图时 -->
                <div class="stem-images" v-else-if="item.caption.length > 1">
                  <div class="image-wrapper" v-for="(img, idx) in item.caption" :key="idx">
                    <el-image class="images" fit="cover" :src="img" :preview-src-list="item.caption" lazy></el-image>
                  </div>
                </div>
              </div>

              <!-- 选项部分（纯文字，当所有选项都没有图片） -->
              <div class="options-container" v-if="(item.questionType == '1' || item.questionType == '2') && !hasAnyOptionImage(item)">
                <div v-for="(option, idx) in item.xktOptionVoList" :key="idx" class="option-item">
                  <span class="option-letter">{{ option.choiceOption }}.</span>
                  <span class="option-content" v-html="option.content ? renderFormulaCached(option.content) : '无'"></span>
                </div>
              </div>

              <!-- 选项有任意图片时，走图文栅格；单个缺图显示占位 -->
              <div class="option-images" v-if="(item.questionType == '1' || item.questionType == '2') && hasAnyOptionImage(item)">
                <div class="image-wrapper" v-for="(option, idx) in item.xktOptionVoList" :key="idx">
                  <el-tooltip effect="dark" placement="top" :disabled="!option.content" popper-class="option-tooltip">
                    <div slot="content" v-if="option.content" v-html="renderFormulaCached(option.choiceOption + '.' + option.content)"></div>
                    <div style="display: flex">
                      <span class="image-letter">{{ option.choiceOption }}.</span>
                      <span class="image-title" v-html="option.content ? renderFormulaCached(option.content) : ''"></span>
                    </div>
                  </el-tooltip>
                  <template v-if="option.optionImage">
                    <el-image class="images" fit="cover" :src="option.optionImage" :preview-src-list="[option.optionImage]" lazy>
                      <div slot="placeholder" class="img-fallback">加载中...</div>
                      <div slot="error" class="img-fallback">加载失败</div>
                    </el-image>
                  </template>
                  <!-- <template v-else>
                    <div class="images image-fallback">
                      <span class="fallback-text el-icon-picture-outline"></span>
                    </div>
                  </template> -->
                </div>
              </div>

              <!-- 填空问题(拍照)和主观 -->
              <div class="problem-item" v-if="(item.questionType == '4' || item.questionType == '5') && item.xktSubQuestionVoList.length > 0">
                <span v-for="(option, optIndex) in item.xktSubQuestionVoList" :key="optIndex">
                  <span class="problem-content">{{ '问题' + (optIndex + 1) }}：</span>
                  <span class="problem-content" v-html="option.subTitle ? renderFormulaCached(option.subTitle) + '（' + option.score + '）' : '无'"></span>
                </span>
              </div>

              <el-button plain :loading="parseLoadingId === item.id" @click="lookParse(item)">
                查看解析
                <i class="el-icon-arrow-right el-icon--right"></i>
              </el-button>
            </div>

            <div class="question-actions">
              <el-button plain @click="editQuestion(item)">编辑</el-button>
              <el-button plain type="warning" @click="deleteQuestion(item)">删除</el-button>
            </div>
          </div>
          <div class="table-pagination" v-if="questionList.length > 0">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :current-page.sync="tablePage"
              :page-size.sync="tablePageSize"
              :page-sizes="[10, 20, 30, 40, 50]"
              :total="tableTotal"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </div>
      </div>
    </div>

    <ParseDialog ref="ParseDialog"></ParseDialog>
  </div>
</template>

<script>
  import ParseDialog from './components/ParseDialog.vue';
  import { renderFormulaAuto } from '@/utils/formulaRenderer';
  import courseApi from '@/api/studyExamPassed/course';
  import videoConfigApi from '@/api/studyExamPassed/videoConfig';
  import { restoreMissingRouteQuery, persistRouteParams, clearRouteParamCache } from '@/utils/routeParamCache';

  export default {
    name: 'QuestionManagementPage',
    components: { ParseDialog },
    data() {
      return {
        tablePage: 1,
        tablePageSize: 10,
        tableTotal: 0,
        totalScore: 0,
        globalTotal: null, // 不带筛选条件的全量题目总数（当前视频）
        queryData: {},
        pageLoading: false, // 页面级加载遮罩
        searchForm: {
          questionText: '',
          questionType: '',
          dateRange: [],
          isRelated: '2'
        },
        questionList: [],
        courseCategoryList: [], // 课程大类
        gradeList: [], // 学段列表
        parseLoadingId: null // 当前加载解析的题目ID
      };
    },
    filters: {
      // 过滤器：格式化公式
      questionsTypeFilter(type) {
        const typeMap = {
          1: '单选题',
          2: '多选题',
          3: '填空题(系统作答)',
          4: '填空题(拍照上传)',
          5: '主观题'
        };
        return typeMap[type] || '';
      }
    },
    computed: {
      isAnyFilterApplied() {
        const { questionText, questionType, dateRange, isRelated } = this.searchForm;
        // isRelated 默认 '2' 表示全部
        return (
          (questionText && questionText.trim() !== '') ||
          questionType !== '' ||
          (Array.isArray(dateRange) && dateRange.length === 2 && dateRange[0] && dateRange[1]) ||
          isRelated !== '2'
        );
      },
      emptyDesc() {
        if (this.questionList.length > 0) return '';
        if (this.globalTotal === 0) return '当前视频暂无题目';
        if (this.isAnyFilterApplied) return '当前筛选条件下暂无题目';
        return '暂无题目数据';
      },
      emptyTips() {
        if (this.questionList.length > 0) return '';
        if (this.globalTotal === 0) return '可点击返回或在视频列表中新增题目';
        if (this.isAnyFilterApplied) return '可尝试修改/清空筛选条件';
        return '可尝试新增题目';
      }
    },
    created() {
      restoreMissingRouteQuery(this.$router, this.$route, { ns: 'KnowledgeQuestionManagement', keys: ['curriculumId'] });
      this.initPage(this.$route.query);
    },
    activated() {
      restoreMissingRouteQuery(this.$router, this.$route, { ns: 'KnowledgeQuestionManagement', keys: ['curriculumId'] });
      // 进入激活时禁止 body 滚动
      if (typeof document !== 'undefined') {
        document.body.classList.add('qm-no-scroll');
      }
    },
    mounted() {
      // 初次挂载也添加（切换进入第一次）
      if (typeof document !== 'undefined') {
        document.body.classList.add('qm-no-scroll');
      }
    },
    deactivated() {
      // keep-alive 切走时移除
      if (typeof document !== 'undefined') {
        document.body.classList.remove('qm-no-scroll');
      }
    },
    beforeDestroy() {
      // 组件销毁前移除，避免残留影响其它页面
      if (typeof document !== 'undefined') {
        document.body.classList.remove('qm-no-scroll');
      }
    },
    methods: {
      // 公式渲染缓存，降低重复解析成本
      renderFormulaCached(text) {
        if (!text) return '';
        if (!this._formulaCache) this._formulaCache = Object.create(null);
        const key = text;
        if (this._formulaCache[key]) return this._formulaCache[key];
        const html = renderFormulaAuto(text);
        this._formulaCache[key] = html;
        return html;
      },
      hasAnyOptionImage(question) {
        if (!question || !Array.isArray(question.xktOptionVoList) || !question.xktOptionVoList.length) return false;
        return question.xktOptionVoList.some((o) => o.optionImage && o.optionImage.trim() !== '');
      },
      // 初始化数据
      async initPage(routeData) {
        if (!routeData) return;
        this.queryData = {
          ...routeData
        };
        persistRouteParams(this.$route, { ns: 'KnowledgeQuestionManagement', keys: ['curriculumId'] });

        this.pageLoading = true;
        await Promise.all([this.initCourseCategoryList()]);
        await this.getList();
      },

      async getList() {
        let searchData = {
          questionText: this.searchForm.questionText,
          questionType: this.searchForm.questionType,
          isRelated: this.searchForm.isRelated,
          startTime: this.searchForm.dateRange ? this.searchForm.dateRange[0] : null,
          endTime: this.searchForm.dateRange ? this.searchForm.dateRange[1] : null
        };
        const params = {
          pageNum: this.tablePage,
          pageSize: this.tablePageSize,
          curriculumId: this.queryData.curriculumId,
          type: 1,
          // videoId: this.queryData.id,
          ...searchData
        };
        this.pageLoading = true;
        try {
          const res = await videoConfigApi.getQuestionPage(params);
          this.questionList = res.data.data.map((a) => {
            return {
              ...a,
              xktOptionVoList: a.xktOptionVoList || [],
              xktSubQuestionVoList: a.xktSubQuestionVoList || [],
              caption: a.caption ? a.caption.split(',') : [],
              analysisImage: a.analysisImage ? a.analysisImage.split(',') : [],
              courseCategory: this.courseCategoryList.find((c) => c.value === a.curriculumId)?.label || ''
            };
          });
          this.tableTotal = Number(res.data.totalItems);
          this.totalScore = Number(this.questionList[0]?.totalScore).toFixed(0);
          // 若当前不存在任何筛选条件，则本次 total 即为全量 total
          if (!this.isAnyFilterApplied) {
            this.globalTotal = this.tableTotal;
          }
          this.$bus.$off('updateEdit');
        } catch (e) {
        } finally {
          this.pageLoading = false;
        }
      },

      // 获取课程大类
      initCourseCategoryList() {
        return courseApi.getCourseCategory().then((res) => {
          this.courseCategoryList = res.data.map((item) => ({ label: item.enName, value: item.id }));
        });
      },

      // 格式化公式
      renderFormula(text) {
        return renderFormulaAuto(text);
      },
      async handleSearch() {
        this.tablePage = 1;
        await this.getList();
      },
      async resetSearch() {
        this.searchForm = {
          questionText: '',
          questionType: '',
          dateRange: [],
          isRelated: '2'
        };
        this.tablePage = 1;
        await this.getList();
      },
      async lookParse(item) {
        if (this.parseLoadingId) return;
        this.parseLoadingId = item.id;
        try {
          const res = await videoConfigApi.getQuestionDetail({ id: item.id });
          this.$refs.ParseDialog.init('查看解析', res.data);
        } catch (e) {
          this.$message.error('加载解析失败，请稍后重试');
        } finally {
          this.parseLoadingId = null;
        }
      },
      editQuestion(item) {
        const target = {
          path: '/studyExamPassed/addKnowledge',
          name: 'addKnowledge',
          meta: {}
        };
        this.$store.dispatch('delVisitedViews', target);
        clearRouteParamCache('knowledgeQueryInfo');
        this.$router.push({
          path: '/studyExamPassed/editKnowledge',
          query: { title: '编辑题目', type: 'edit', id: item.id, curriculumId: item.curriculumId }
        });
      },

      deleteQuestion(item) {
        this.$confirm('此操作将删除该题目, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            // type: 1, curriculumId: item.curriculumId
            await videoConfigApi.getQuestionDelete({ id: item.id });
            this.$message.success('删除成功');
            // 如果当前页仅此一条被删除，先尝试回退一页再拉取（除非是第一页）
            if (this.questionList.length === 1 && this.tablePage > 1) {
              this.tablePage = this.tablePage - 1;
            }
            const wasFilterApplied = this.isAnyFilterApplied;
            await this.getList();
            // 如果删除时存在筛选，则全量总数需要手动递减（因为 getList 在有筛选时不会更新 globalTotal）
            if (wasFilterApplied && this.globalTotal != null && this.globalTotal > 0) {
              this.globalTotal = this.globalTotal - 1;
            }
            // 仅当全量（不带筛选）也为空才自动返回
            if (this.globalTotal === 0) {
              this.$message.info('当前视频暂无题目，返回视频列表');
              this.$bus.$emit('updateKnowledgeTable');
              setTimeout(() => {
                this.$store.dispatch('delVisitedViews', this.$route);
                this.$router.push({ path: '/studyExamPassed/questionKnowledgeManage' });
              }, 600);
            }
          } catch (e) {
            this.$message.error('删除失败，请稍后重试');
          }
        });
      },

      handlePageChange(p) {
        this.tablePage = p;
        this.pageLoading = true;
        this.getList();
      },
      handleSizeChange(size) {
        this.tablePageSize = size;
        this.tablePage = 1;
        this.pageLoading = true;
        this.getList();
      }
    }
  };
</script>

<style scoped lang="less">
  @normal-text: PingFangSC-regular;
  @normal-color: rgba(16, 16, 16, 1);
  .question-management-page {
    padding: 20px;
  }

  .main {
    display: flex;
    gap: 20px;
  }

  .search-container {
    flex: 1;
    padding: 20px;
    border-right: 2px solid #dcdfe6;
    .button-group {
      display: flex;
      justify-content: center;
    }
  }

  .question-list-container {
    height: 100%;
    overflow: auto;
    .empty-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
    .custom-empty {
      text-align: center;
      padding: 40px 0;
      .custom-empty__icon {
        font-size: 56px;
        color: #dcdfe6;
      }
      .custom-empty__desc {
        margin-top: 12px;
        font-size: 16px;
        color: #606266;
        font-weight: 500;
      }
      .custom-empty__tips {
        margin-top: 4px;
        font-size: 13px;
        color: #909399;
        margin-bottom: 12px;
      }
    }
    .table-pagination {
      margin-top: 10px;
      text-align: right;
      width: 100%;
    }
    .question-item {
      padding: 10px 20px;
      &:last-child {
        border-bottom: none;
      }

      .question-info {
        margin-bottom: 15px;

        .question-header {
          margin-bottom: 15px;
          .question-title {
            color: rgba(108, 108, 108, 1);
            font-size: 16px;
            text-align: left;
            font-family: @normal-text;
          }

          table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 20px;
          }
          th,
          td {
            border: 1px solid #c1c1c1;
            padding: 15px;
            text-align: center;
            font-size: 14px;
          }
        }

        .question-stem {
          margin-bottom: 15px;

          .stem-content {
            line-height: 23px;
            color: @normal-color;
            font-size: 16px;
            text-align: left;
            font-family: @normal-text;
          }

          .stem-image {
            .image {
              width: auto;
              height: 180px;
              margin-top: 9px;
            }
          }

          .stem-images {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            margin-top: 15px;
            .image-wrapper {
              padding: 5px;
              .images {
                width: auto;
                height: 170px;
                display: block; /* 预留高度避免图片加载回流 */
              }
            }
          }
        }

        .problem-item {
          display: flex;
          flex-direction: column;
          margin-bottom: 8px;

          .problem-content {
            line-height: 30px;
            color: @normal-color;
            font-size: 16px;
            font-family: @normal-text;
          }
        }

        .options-container {
          .option-item {
            padding: 10px;
            margin-bottom: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            .option-letter {
              font-size: 16px;
              margin-right: 8px;
            }
            .option-content {
              flex: 1;
              font-size: 16px;
              color: #303133;
              font-family: @normal-text;
            }
          }

          margin: 20px 0;
        }

        .option-images {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          margin-top: 10px;

          .image-wrapper {
            width: 25%; // 一行最多四个
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            padding: 5px 10px 15px 10px;
            .image-letter {
              font-size: 16px;
              margin-right: 8px;
            }
            .image-title {
              color: @normal-color;
              font-size: 14px;
              font-family: @normal-text;
              // max-height: 2.8em; // 2 行 * 1.4 line-height
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2; // 兼容性补充
              -webkit-box-orient: vertical;
              word-break: break-all;
            }
            .images {
              width: 100%;
              height: 170px;
              margin-top: 8px;
              object-fit: cover;
            }
            .image-fallback {
              background: #f5f7fa;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #909399;
              font-size: 12px;
            }
            .img-fallback {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #909399;
              font-size: 12px;
            }
            .fallback-text {
              font-size: 18px;
            }
          }
        }
      }

      .question-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
      }
    }
  }
  .question-list-outer {
    flex: 3;
    padding: 20px;
    height: calc(100vh - 124px);
    position: relative;
    box-sizing: border-box;
  }

  /deep/.katex {
    white-space: normal !important;
    word-break: break-word;
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
    font-weight: inherit !important;
  }
</style>
