/**
 * 品牌列表相关接口
 */
import request from '@/utils/request';
const vocabularyApi = {
  // 上传词库文件
  uploadWordFile(formData) {
    return request({
      url: '/api/word/upload',
      method: 'post',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  // 获取词库列表（可选）
  getWordList(params) {
    return request({
      url: '/api/word/list',
      method: 'get',
      params
    });
  }
};

export default vocabularyApi;