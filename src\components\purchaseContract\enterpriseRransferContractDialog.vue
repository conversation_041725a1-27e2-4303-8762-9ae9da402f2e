<template>
  <div v-if="canShow" id="enterpriseRransferContractDialog">
    <el-dialog
      id="enterpriseRransferContractDialog1"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="canClose"
      :show-close="canClose"
      destroy-on-close
      width="45%"
    >
      <div slot="title" style="text-align: center; padding: 0 20px; white-space: break-spaces; line-height: 1.5">
        请于
        <span style="color: red; font-size: 25px">{{ remainingDays }}</span>
        天内{{ showConfirmUserInfo ? '补充企业名称，并生成合同完成签署\n' : '完成甲乙双方的转让合同签署，' }}若逾期不签署合同，将无法开展后续业务
      </div>
      <div v-if="showConfirmUserInfo" style="margin: 0 30px">
        <el-form ref="userInfoFormRef" :model="userInfoForm" :rules="userInfoRules" label-position="left" label-width="110px">
          <el-form-item label="企业信用代码" prop="enterpriseCreditCode">
            <el-input
              v-model="userInfoForm.enterpriseCreditCode"
              size="medium"
              placeholder="请输入已注册的企业信用代码，支持个体工商户"
              oninput="value=value.replace(/[\u4E00-\u9FA5]/g, '')"
              @input="handleEnterpriseCreditCodeInput"
              @change="handleEnterpriseCreditCodeChange"
            />
          </el-form-item>
          <el-form-item label="企业名称" prop="enterpriseName">
            <el-input v-model="userInfoForm.enterpriseName" size="medium" placeholder="请输入企业信用代码，自动查询企业名称" disabled />
          </el-form-item>
          <el-form-item label="负责人" prop="headName">
            <el-input v-model="userInfoForm.headName" size="medium" placeholder="请输入负责人姓名" />
          </el-form-item>
          <el-form-item label="手机号" prop="headPhone">
            <el-input v-model="userInfoForm.headPhone" size="medium" placeholder="请输入手机号" oninput="value=value.replace(/[^\d]/g, '')" />
          </el-form-item>
          <el-form-item label="身份证号码" prop="headIdcrad">
            <el-input v-model="userInfoForm.headIdcrad" size="medium" placeholder="请输入身份证号码" />
          </el-form-item>
        </el-form>
        <div style="text-align: center">
          <el-button type="primary" size="large" :loading="saveUserInfoAndaddContractOneLoading" @click="handleSaveAndGenerateContractClick">保存并生成合同</el-button>
        </div>
        <div style="color: red; font-size: 14px; text-align: center; margin-top: 16px">
          <el-link type="danger" href="https://minio-api.dxznjy.com/test1/如何查看微信实名.pdf" target="_blank">如何查看微信实名？</el-link>
        </div>
      </div>
      <div v-else class="content">
        <el-steps class="contract-signing-steps" :active="contractSigningActive" :space="370" finish-status="success" process-status="finish">
          <el-step
            :title="contractSigningActive === 0 ? '进行中的步骤' : contractSigningActive < 0 ? '未完成的步骤' : '已完成的步骤'"
            :description="'请作为' + contractInfo[0].signatory + '签署《' + contractInfo[0].name + '》'"
          ></el-step>
          <el-step
            :title="contractSigningActive === 1 ? '进行中的步骤' : contractSigningActive < 1 ? '未完成的步骤' : '已完成的步骤'"
            :description="'请作为' + contractInfo[1].signatory + '签署《' + contractInfo[1].name + '》'"
          ></el-step>
        </el-steps>
        <div v-if="contractInfo.length > contractSigningActive" class="contract-signing-content">
          <div class="contract-signing-content-title">{{ contractInfo[contractSigningActive].qrMessage }}</div>
          <div class="contract-signing-content-qrcode">
            <div class="contract-signing-content-qrcode-img" v-loading="contractQRLoading" @click="handleRefreshQrCodeClick">
              <el-image style="width: 100%; height: 100%" fit="fill" :src="contractInfo[contractSigningActive].qrUrl" />
            </div>
          </div>
          <div>
            <!-- v-if="contractSigningActive != 1" -->
            <el-tooltip class="item" effect="dark" content="Top Center" placement="top">
              <div slot="content">
                修改企业信息后需
                <br />
                重新生成并签署
              </div>
              <el-button size="small" :loading="modifyEnterpriseInfoLoading" @click="modifyEnterpriseInfo()">修改企业信息</el-button>
            </el-tooltip>

            <el-button type="primary" size="small" @click="handleNextContractClick()">{{ contractSigningActive == 1 ? '已签署' : '下一步' }}</el-button>
          </div>
          <div style="font-size: 14px; text-align: center; margin-top: 16px">签署完成后，请点击{{ contractSigningActive == 1 ? '已签署' : '下一步' }}按钮</div>
        </div>
      </div>
    </el-dialog>
    <purchaseContract ref="rransferContractSpurchaseDialog" style="z-index: 9999 !important"></purchaseContract>
    <el-dialog :visible.sync="modifyEnterpriseVisible" width="30%" :show-close="false" :title="null" class="no-header-dialog">
      <div class="modifyEnterprise_title">修改企业信息</div>
      <div class="text_colcor modifyEnterprise_title">企业信息修改后，所有合同需重新生成并签署并</div>
      <div class="text_colcor modifyEnterprise_title">需要由您支付重新生成的合同费用</div>
      <div style="margin: 0 30px">
        <el-form ref="userInfoFormRef" :model="userInfoForm" :rules="userInfoRules" label-position="left" label-width="110px">
          <el-form-item label="企业信用代码" prop="enterpriseCreditCode">
            <el-input
              v-model="userInfoForm.enterpriseCreditCode"
              size="medium"
              placeholder="请输入已注册的企业信用代码，支持个体工商户"
              oninput="value=value.replace(/[\u4E00-\u9FA5]/g, '')"
              @input="handleEnterpriseCreditCodeInput"
              @change="handleEnterpriseCreditCodeChange"
            />
          </el-form-item>
          <el-form-item label="企业名称" prop="enterpriseName">
            <el-input v-model="userInfoForm.enterpriseName" size="medium" placeholder="请输入企业信用代码，自动查询企业名称" disabled />
          </el-form-item>
          <el-form-item label="负责人" prop="headName">
            <el-input v-model="userInfoForm.headName" size="medium" placeholder="请输入负责人姓名" />
          </el-form-item>
          <el-form-item label="手机号" prop="headPhone">
            <el-input v-model="userInfoForm.headPhone" size="medium" placeholder="请输入手机号" oninput="value=value.replace(/[^\d]/g, '')" />
          </el-form-item>
          <el-form-item label="身份证号码" prop="headIdcrad">
            <el-input v-model="userInfoForm.headIdcrad" size="medium" placeholder="请输入身份证号码" />
          </el-form-item>
        </el-form>
        <div style="display: flex; justify-content: center; align-items: center">
          <el-button :disabled="modifyEnterpriseLoading" @click="closeModifyEnterpriseVisible()">取 消</el-button>
          <el-button type="primary" :loading="modifyEnterpriseLoading" @click="modifyEnterprise()">确认修改</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import purchaseContract from '@/components/purchaseContract/index.vue';
  import checkPermission from '@/utils/permission';
  import {
    getContractSigningDialogShowStatus,
    getNeedSigningRransferContractStatusAndDays,
    getContractUserInfo,
    checkEnterpriseName,
    saveUserAndGenerateContract,
    getContractQRUrl,
    getContractSigingStatus,
    updateClubPersonInfo,
    getClubPersonContractInfo
  } from '@/api/dialog/enterpriseRransferContractDialog';
  let observer = null;
  export default {
    name: 'enterpriseRransferContractDialog',
    components: { purchaseContract },
    props: {
      initFlag: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        modifyEnterpriseLoading: false,
        modifyEnterpriseInfoLoading: false,
        originalData: {},
        modifyEnterpriseVisible: false, // 修改企业信息弹窗
        isOperations: false, // 是否是俱乐部(Operations)角色
        canShow: false,
        visible: false,
        canClose: true,
        remainingDays: 30,
        showConfirmUserInfo: true,
        originalUserInfo: {},
        userInfoForm: {},
        userInfoRules: {
          enterpriseCreditCode: [{ required: true, message: '请输入企业信用代码', trigger: 'blur' }],
          enterpriseName: [{ required: true, message: '请输入企业信用代码，自动查询企业名称', trigger: 'blur' }],
          headName: [
            { required: true, message: '负责人姓名不可为空', trigger: 'blur' },
            { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
          ],
          headPhone: [
            { required: true, message: '手机号不可为空', trigger: 'blur' },
            { pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/, message: '手机号的格式错误', trigger: 'blur' }
          ],
          headIdcrad: [
            { required: true, message: '身份证号码不可为空', trigger: 'blur' },
            {
              pattern:
                /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/,
              message: '身份证号码的格式错误',
              trigger: 'blur'
            }
          ]
        },
        // 保存用户信息并生成合同按钮加载状态
        saveUserInfoAndaddContractOneLoading: false,

        // 合同签署相关数据
        contractSigningActive: 0, // 当前签署步骤
        contractInfo: [],
        contractQRLoading: false,
        pollingStatusId: null,
        pollingStatusStopId: null
      };
    },
    mounted() {
      // 可以在这里添加自定义样式，彻底隐藏标题栏
      const style = document.createElement('style');
      style.textContent = `
      .no-header-dialog .el-dialog__header {
        display: none;
      }
    `;
      document.head.appendChild(style);
    },
    watch: {
      initFlag(val) {
        val && this.initCreated();
      },
      visible(val) {
        if (!val) {
          this.canShow = false;
          localStorage.setItem('loginContractSigningFlag', 1);
          this.clearPollingContract();

          // 停止观察
          this.disconnectMutationObserver();
        }
      }
    },
    beforeDestroy() {
      console.log('enterpriseRransferContractDialog beforeDestroy');
      this.clearPollingContract();
      // 停止观察
      this.disconnectMutationObserver();
    },
    methods: {
      async initCreated() {
        // 检查登录用户是否有俱乐部权限
        this.isOperations = checkPermission(['Operations']);

        // 获取是否显示合同签署弹窗状态
        let canShow = !localStorage.getItem('loginContractSigningFlag');

        // 可以显示合同签署弹窗，且登录用户为俱乐部时，请求后端
        if (canShow && this.isOperations) {
          try {
            // 获取是否显示合同签署弹窗开关状态
            const res = await getContractSigningDialogShowStatus();
            if (!res.data.transferContractSwitch) {
              this.canShow = false;
              return;
            }
          } catch (error) {
            this.canShow = false;
            return;
          }
          getNeedSigningRransferContractStatusAndDays().then((res) => {
            let needVisible = false;
            if (res.data.channelSignPeopleType != 1 || (res.data.studySignStatus !== 2 && !this.handleIsChangeRelationsData(res.data)) || res.data.channelSignStatus !== 2) {
              // 前置条件不满足， 不显示弹窗
              needVisible = false;
            } else if (res.data.transferSignStatus || res.data.transferContractSign) {
              this.showConfirmUserInfo = false;
              if (res.data.transferSignStatus == 2) {
                // 合同已生成，步骤一已签署
                this.contractSigningActive = 1;
                needVisible = res.data.transferContractStatus != 2;
              } else {
                // 合同已生成，步骤一未签署，获取合同二维码
                this.contractSigningActive = 0;
                needVisible = true;
              }
            } else {
              // 合同未生成，获取用户信息
              this.showConfirmUserInfo = true;
              this.handleContractUserInfo();
              needVisible = true;
            }
            this.init(needVisible, res.data);
          });
        }
        this.$emit('update:initFlag', false);
      },
      // 判断是否为变更上下级关系的合同
      handleIsChangeRelationsData(data) {
        return (data.isInChangePeriod == 1 && data.changeType == 1) || data.isInChangePeriod == 2;
      },
      init(needVisible = false, data = {}) {
        this.contractInfo = [
          {
            signatory: '甲方',
            name: '俱乐部权益转让合同',
            qrUrl: '',
            qrMessage: '',
            flowId: data.transferFlowId,
            isFirstParty: 0
          },
          {
            signatory: '乙方',
            name: '俱乐部权益转让合同',
            qrUrl: '',
            qrMessage: '',
            flowId: data.transferFlowId,
            isFirstParty: 1
          }
        ];
        this.canShow = needVisible;
        this.visible = needVisible;

        if (needVisible) {
          // 设置天数倒计时
          this.remainingDays = data.transferContractRemainingDay;
          if (this.remainingDays < 1) {
            this.canClose = false;
          }
          if ((data.transferSignStatus && data.transferSignStatus != 2) || (data.transferContractSign && data.transferContractStatus != 2)) {
            const { flowId, signatory } = this.contractInfo[this.contractSigningActive];
            this.getContractQRUrlHandle(flowId, signatory);
          }
          this.initMutationObserver();
        }
      },
      /**
       * 获取签署用户信息
       */
      handleContractUserInfo() {
        if (this.showConfirmUserInfo) {
          getClubPersonContractInfo().then((res) => {
            this.userInfoForm = {
              enterpriseCreditCode: res.data.enterpriseCreditCode,
              enterpriseName: res.data.enterpriseName,
              headName: res.data.headName,
              headPhone: res.data.headPhone,
              headIdcrad: res.data.headIdcrad,
              bvMerchantId: res.data.bvMerchantId
            };
          });
        } else {
          getContractUserInfo().then((res) => {
            this.userInfoForm = {
              enterpriseCreditCode: '',
              enterpriseName: '',
              headName: res.data.contractHeadName,
              headPhone: res.data.contractHeadPhone,
              headIdcrad: res.data.contractHeadIcard
            };
          });
        }
      },
      handleEnterpriseCreditCodeInput() {
        if (this.userInfoForm.enterpriseName) {
          this.$set(this.userInfoForm, 'enterpriseName', '');
        }
      },
      /**
       * 处理企业信用代码改变查询对应企业名称
       */
      async handleEnterpriseCreditCodeChange(enterpriseCreditCode) {
        if (!enterpriseCreditCode) return;
        this.$set(this.userInfoForm, 'enterpriseName', '');
        await checkEnterpriseName(enterpriseCreditCode).then((res) => {
          let findFlag = false;
          if (res.data.data.data) {
            const companyItem = res.data.data.data.find((item) => item.creditNo == enterpriseCreditCode);
            if (companyItem?.companyName) {
              findFlag = true;
              this.$set(this.userInfoForm, 'enterpriseName', companyItem.companyName);
              this.$refs.userInfoFormRef.clearValidate('enterpriseName');
            }
          }
          findFlag || this.$message.error('请输入正确的企业信用代码');
        });
      },
      /**
       * 处理保存并生成合同点击事件
       */
      async handleSaveAndGenerateContractClick() {
        if (this.userInfoForm.enterpriseCreditCode && !this.userInfoForm.enterpriseName) {
          this.saveUserInfoAndaddContractOneLoading = true;
          await this.handleEnterpriseCreditCodeChange(this.userInfoForm.enterpriseCreditCode);
          if (!this.userInfoForm.enterpriseName) {
            this.saveUserInfoAndaddContractOneLoading = false;
          }
        }
        this.$refs.userInfoFormRef.validate((valid) => {
          if (valid) {
            this.userInfoForm.headIdcrad = this.userInfoForm.headIdcrad.toUpperCase();
            this.saveUserInfoAndaddContractOneLoading = true;
            console.log('saveUserAndGenerateContract', this.userInfoForm);
            saveUserAndGenerateContract(this.userInfoForm)
              .then((res) => {
                this.contractSigningActive = 0;
                this.contractInfo[0].flowId = res.data;
                this.contractInfo[1].flowId = res.data;
                //通过合同流程ID请求获取合同二维码链接
                this.getContractQRUrlHandle(res.data, this.contractInfo[this.contractSigningActive].signatory);
                this.showConfirmUserInfo = false;
                this.modifyEnterpriseLoading = false;
                this.modifyEnterpriseVisible = false;
              })
              .catch((e) => {
                if (typeof e === 'object' && e.code == '50010') {
                  //合同数不足，走购买合同流程
                  this.$confirm('剩余合同数不足，请购买补充后重试', '提示', { type: 'warning' }).then(() => {
                    this.goBuy();
                  });
                }
              })
              .finally(() => {
                this.saveUserInfoAndaddContractOneLoading = false;
              });
          } else {
            this.saveUserInfoAndaddContractOneLoading = false;
          }
        });
      },
      goBuy() {
        this.$refs.rransferContractSpurchaseDialog?.open();
      },
      /**
       * 通过合同流程ID获取合同二维码链接
       */
      getContractQRUrlHandle(flowId, participantFlag) {
        this.contractQRLoading = true;
        getContractQRUrl(flowId, participantFlag)
          .then((res) => {
            this.pollingContractStatus();
            let qrProp = this.contractSigningActive == 0 ? 'qrUrl' : 'orgQrUrl';
            this.contractInfo[this.contractSigningActive].qrMessage = res.data.qrMessage;
            this.contractInfo[this.contractSigningActive].qrUrl = res.data[qrProp];
          })
          .finally(() => {
            this.contractQRLoading = false;
          });
      },
      /**
       * 处理刷新二维码点击
       */
      handleRefreshQrCodeClick() {
        if (!this.contractInfo[this.contractSigningActive].qrUrl) {
          const { flowId, signatory } = this.contractInfo[this.contractSigningActive];
          this.getContractQRUrlHandle(flowId, signatory);
        }
      },
      /**
       * 轮询合同签署状态，已签署自动跳转下一步
       */
      pollingContractStatus() {
        const that = this;
        if (!this.pollingStatusId) {
          this.pollingStatusId = setInterval(() => {
            console.log('轮询合同签署状态');
            that.handleNextContractClick(true);
          }, 30000);
          if (this.pollingStatusStopId) clearTimeout(this.pollingStatusStopId);
          this.pollingStatusStopId = setTimeout(() => {
            console.log('已轮询10分钟, 停止轮询合同签署状态');
            that.clearPollingContract();
          }, 10 * 60 * 1000);
        }
      },
      clearPollingContract() {
        if (this.pollingStatusId) {
          clearInterval(this.pollingStatusId);
          this.pollingStatusId = null;
        }
        if (this.pollingStatusStopId) {
          clearTimeout(this.pollingStatusStopId);
          this.pollingStatusStopId = null;
        }
      },
      //检验数据是否做修改
      checkDataModified() {
        const originalStr = JSON.stringify(this.originalData);
        const currentStr = JSON.stringify(this.userInfoForm);

        if (originalStr === currentStr) {
          return false;
        }
        return true;
      },
      /**
       * 主动修改企业信息
       */
      modifyEnterpriseInfo() {
        this.modifyEnterpriseInfoLoading = true;
        getClubPersonContractInfo().then((res) => {
          this.userInfoForm = {
            enterpriseCreditCode: res.data.enterpriseCreditCode,
            enterpriseName: res.data.enterpriseName,
            headName: res.data.headName,
            headPhone: res.data.headPhone,
            headIdcrad: res.data.headIdcrad,
            bvMerchantId: res.data.bvMerchantId
          };
          this.originalData = JSON.parse(JSON.stringify(this.userInfoForm));
          this.modifyEnterpriseInfoLoading = false;
          this.modifyEnterpriseVisible = true;
        });
      },

      closeModifyEnterpriseVisible() {
        this.modifyEnterpriseVisible = false;
        this.$nextTick(() => {
          this.$refs['userInfoFormRef']?.clearValidate();
        });
      },
      // 修改用户信息
      async modifyEnterprise() {
        this.modifyEnterpriseLoading = true;
        if (!this.checkDataModified()) {
          this.modifyEnterpriseLoading = false;
          return this.$message.warning('修改的信息与原信息一致，无需修改哦～');
        } else if (this.userInfoForm.enterpriseCreditCode && !this.userInfoForm.enterpriseName) {
          this.saveUserInfoAndaddContractOneLoading = true;
          await this.handleEnterpriseCreditCodeChange(this.userInfoForm.enterpriseCreditCode);
          if (!this.userInfoForm.enterpriseName) {
            this.saveUserInfoAndaddContractOneLoading = false;
          }
        }

        this.$refs.userInfoFormRef.validate((valid) => {
          if (valid) {
            this.userInfoForm.headIdcrad = this.userInfoForm.headIdcrad.toUpperCase();
            this.userInfoForm.contractType = 4;
            this.saveUserInfoAndaddContractOneLoading = true;
            console.log('saveUserAndGenerateContract', this.userInfoForm);
            updateClubPersonInfo(this.userInfoForm)
              .then(() => {
                this.handleSaveAndGenerateContractClick();
              })
              .catch((e) => {
                this.modifyEnterpriseLoading = false;
                if (typeof e === 'object' && e.code == '50010') {
                  this.$confirm('剩余合同数不足，请购买补充后重试', '提示', { type: 'warning' }).then(() => {
                    this.goBuy();
                  });
                }
              });
          } else {
            this.modifyEnterpriseLoading = false;
          }
        });
      },
      /**
       * 处理下一步点击
       */
      handleNextContractClick(isPolling) {
        let { flowId, signatory: participantFlag } = this.contractInfo[this.contractSigningActive];
        getContractSigingStatus({ flowId, participantFlag }).then((res) => {
          if (res.data && res.data?.signStatus == 2) {
            this.clearPollingContract();
            this.$message.success('合同签署成功！');
            if (this.contractSigningActive == 0) {
              this.contractSigningActive++;
              this.handleRefreshQrCodeClick();
            } else if (this.contractSigningActive == 1) {
              this.visible = false;
            }
          } else if (!isPolling) {
            this.$message.error('合同暂未签署');
          }
        });
      },
      /**
       * 初始化节点监听器
       * 阻止用户手动删除合同签署弹窗元素节点，跳过合同签署流程
       */
      initMutationObserver() {
        this.$nextTick(() => {
          this.disconnectMutationObserver();
          // 观察器的配置（需要观察什么变动）
          // attributes: 观察受监视元素的属性值变更
          // childList: 监视目标节点（如果 subtree 为 true，则包含子孙节点）添加或删除新的子节点
          // subtree: 其他值也会作用于此子树下的所有节点，而不仅仅只作用于目标节点
          const config = { childList: true, subtree: true };
          // 创建一个观察器实例并传入回调函数
          observer = new MutationObserver(this.mutationObserverCallBack);
          const targetNode = document.getElementById('enterpriseRransferContractDialog');
          // 以上述配置开始观察目标节点
          observer.observe(targetNode.parentNode, config);
        });
      },
      /**
       * 当观察到变动时执行的回调函数
       * @param mutationsList
       */
      mutationObserverCallBack(mutationsList) {
        for (let mutation of mutationsList) {
          // 这里可以获取mutation.target来获取想要监听的元素与属性
          if (
            mutation.type === 'childList' &&
            mutation.removedNodes.length &&
            ['enterpriseRransferContractDialog', 'enterpriseRransferContractDialog1'].includes(mutation.removedNodes[0].id)
          ) {
            this.visible && location.reload();
          }
        }
      },
      disconnectMutationObserver() {
        if (observer) {
          observer.disconnect();
          observer = null;
        }
      }
    }
  };
</script>

<style lang="less" scoped>
  .title {
    padding: 0 20px;
    font-size: 18px;
    .title-row {
      text-align: center;
      line-height: 24px;
      &-days {
        color: red;
        font-size: 24px;
      }
    }
  }
  .content {
    font-size: 18px;
    color: #000;
    .user-info-form {
      width: 60%;
      margin: 0 auto;
      ::v-deep .el-form-item__label {
        font-size: 16px;
        color: #000;
      }
      &-item {
        height: 33px;
        &-text {
          margin-left: 15px;
          font-size: 16px;
        }
      }
    }
    .contract-signing-steps {
      margin-left: 80px;
    }
    .contract-signing-content {
      margin-top: 24px;
      text-align: center;
      color: #303133;
      .contract-signing-content-title {
        min-height: 60px;
        margin-bottom: 6px;
        line-height: 30px;
        font-size: 16px;
        white-space: break-spaces;
      }
      .contract-signing-content-qrcode {
        width: 270px;
        height: 270px;
        margin: 0 auto 6px;
        background-color: #f5f7fa;
        .contract-signing-content-qrcode-img {
          width: 270px;
          height: 270px;
        }
        .contract-signing-content-qrcode-refresh-icon {
          font-size: 60px;
          padding-top: 80px;
          width: 100px;
          margin: 0 auto;
        }
        .contract-signing-content-qrcode-refresh-text {
          color: #f56c6c;
          line-height: 30px;
          white-space: break-spaces;
        }
      }
    }
  }
  .modifyEnterprise_title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
  }
  .text_colcor {
    color: red;
  }
</style>
