<template>
  <div class="app-container">
    <el-card style="margin-bottom: 15px">
      <el-form :model="queryParams" ref="queryForm" :inline="false" label-width="88px">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="分类名称：">
              <el-input v-model="queryParams.name" :clearable="true" placeholder="流程分类名称" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="分类编码：">
              <el-input v-model="queryParams.code" :clearable="true" placeholder="分类编码" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-button type="success" icon="el-icon-search" size="mini" @click="getPageList">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-row :gutter="10" style="margin-bottom: 30px">
      <el-col :span="1.5">
        <el-button type="primary" size="mini" @click="onAddFlowCategoryClick()">新建</el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table ref="flowCategory" :data="tableList" size="mini" header-cell-class-name="table-header-gray">
          <el-table-column label="序号" header-align="center" align="center" type="index" width="55px" :index="tableList.getTableIndex" />
          <el-table-column label="流程分类名称" prop="name"></el-table-column>
          <el-table-column label="分类编码" prop="code"></el-table-column>
          <el-table-column label="显示顺序" prop="showOrder"></el-table-column>
          <el-table-column label="创建时间" prop="updateTime"></el-table-column>
          <el-table-column label="操作" fixed="right">
            <template v-solt="{ row }">
              <baseOperationBtn :btnList="operationBtnList" :row="row"></baseOperationBtn>
            </template>
            <template slot="header">
              <baseOperationHeader></baseOperationHeader>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-col :span="20">
          <el-pagination
            :current-page="tablePage.currentPage"
            :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tablePage.totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-col>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import BaseOperationBtn from '@/components/baseTable/baseOperationBtn';
  import baseOperationHeader from '@/components/baseTable/baseOperationHeader';
  import formEditFlowCategory from '@/views/activiti/workFlow/flowCategory/formEditFlowCategory';
  import flowCategoryApi from '@/api/activiti/flowCategory';
  import { pageParamNames } from '@/utils/constants';

  export default {
    name: 'formFlowCategory',
    components: { BaseOperationBtn, baseOperationHeader },
    data() {
      return {
        queryParams: {
          name: undefined,
          code: undefined
        },
        operationBtnList: [
          { name: '编辑', key: 'edit', handleClick: (row) => this.onEditFlowCategoryClick(row) },
          { name: '删除', key: 'delete', handleClick: (row) => this.onDeleteFlowCategoryClick(row) }
        ],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableList: []
      };
    },

    created() {
      this.getPageList();
    },

    methods: {
      /** 重置按钮操作 */
      resetQuery() {
        (this.queryParams = {
          name: null,
          code: null
        }),
          this.getPageList();
      },
      /**
       * FlowCategory数据获取函数，返回Promise
       */
      getPageList() {
        this.queryParams.pageNum = this.tablePage.currentPage;
        this.queryParams.pageSize = this.tablePage.size;
        flowCategoryApi.pageList(this.queryParams).then((res) => {
          this.tableList = res.data.data;
          this.loading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name])));
        });
      },
      /**
       * FlowCategory数据获取检测函数，返回true正常获取数据，返回false停止获取数据
       */
      loadFlowCategoryVerify() {
        this.formFlowCategory.formFilterCopy.name = this.formFlowCategory.formFilter.name;
        this.formFlowCategory.formFilterCopy.code = this.formFlowCategory.formFilter.code;
        return true;
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.getPageList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getPageList();
      },

      /**
       * 新建
       */
      onAddFlowCategoryClick() {
        let params = {};

        this.$dialog
          .show(
            '新建',
            formEditFlowCategory,
            {
              area: '500px'
            },
            params
          )
          .catch((e) => {});
      },
      /**
       * 编辑
       */
      onEditFlowCategoryClick(row) {
        let params = {
          categoryId: row.categoryId
        };

        this.$dialog
          .show(
            '编辑',
            formEditFlowCategory,
            {
              area: '500px'
            },
            params
          )
          .catch((e) => {});
      },
      /**
       * 删除
       */
      onDeleteFlowCategoryClick(row) {
        if (row.categoryId == null) {
          this.$message.error('请求失败，发现必填参数为空！');
          return;
        }
        let params = {
          categoryId: row.categoryId
        };

        this.$confirm('是否删除此流程分类？')
          .then((res) => {
            flowCategoryApi
              .delete(params)
              .then((res) => {
                this.$message.success('删除成功');
                this.getPageList();
              })
              .catch((e) => {});
          })
          .catch((e) => {});
      },
      onResume() {
        this.refreshFormFlowCategory();
      },
      initFormData() {},
      formInit() {
        this.refreshFormFlowCategory();
      }
    },
    watch: {}
  };
</script>
