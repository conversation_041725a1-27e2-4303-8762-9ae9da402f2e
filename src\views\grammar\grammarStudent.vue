<template>
  <div class="app-container">
    <el-card style="margin-bottom: 16px">
      <el-form label-width="80px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6" :xs="24">
            <el-form-item label="学员编号：" label-width="100px">
              <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="姓名：" label-width="60px">
              <el-input v-model="dataQuery.studentName" placeholder="请输入姓名" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label-width="0">
              <el-button type="primary" @click="fetchData01()">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="showTooltip">
          <el-alert :title="tooltip" center type="warning"></el-alert>
        </template>
      </el-form>
    </el-card>

    <el-card v-if="tablePage.totalItems > 0">
      <el-table
        class="common-table"
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ list: 'children', hasChildren: 'true' }"
      >
        <el-table-column prop="phaseName" label="学段"></el-table-column>
        <el-table-column prop="studentCode" label="学员编号"></el-table-column>
        <el-table-column prop="withdrawnBonus" label="操作">
          <template slot-scope="scope">
            <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.openStatus === 0" @click="editOpenStatus(scope.row.id, scope.row.openStatus)">
              开通
            </el-button>
            <el-button type="danger" size="mini" icon="el-icon-video-pause" v-else @click="editOpenStatus(scope.row.id, scope.row.openStatus)">暂停</el-button>
            <el-button
              :type="scope.row.openStatus == true ? 'primary' : 'default'"
              size="mini"
              icon="el-icon-edit-outline"
              :disabled="scope.row.openStatus == true ? false : true"
              @click="jumpOpenMessage(scope.row.studentCode)"
            >
              查看寄语列表
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="studentName" label="姓名"></el-table-column>
        <el-table-column prop="gradeName" label="年级"></el-table-column>
        <el-table-column prop="school" label="学校"></el-table-column>
        <el-table-column prop="openStatus" label="状态" sortable>
          <template slot-scope="scope">
            <span class="green" v-if="scope.row.openStatus === 1">开通</span>
            <span class="red" v-else>暂停</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <el-row type="flex" justify="end" style="height: 28px; margin-top: 36px; line-height: 28px">
        <el-pagination
          :current-page="tablePage.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tablePage.totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
    </el-card>
    <el-card v-else>
      <NoMore></NoMore>
    </el-card>
  </div>
</template>

<script>
  import grammarApi from '@/api/grammar';
  import Tinymce from '@/components/Tinymce';
  import { pageParamNames } from '@/utils/constants';
  import merchantAccountFlowApi from '@/api/merchantAccountFlow';
  import ls from '@/api/sessionStorage';
  import NoMore from '@/components/NoMore/index.vue';

  export default {
    components: { NoMore },
    data() {
      return {
        recharge: {
          course: 0,
          toAccountCourse: 0,
          sumCoursePrice: 0
        },
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          studentCode: '',
          studentName: ''
        },
        tooltip: '',
        showTooltip: false,
        updateData: {
          id: '',
          openStatus: ''
        }
      };
    },
    created() {
      this.fetchData01();
    },
    methods: {
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchData() {
        const that = this;
        that.tableLoading = true;
        that.showTooltip = false;
        that.tooltip = '';
        grammarApi.getStudentList(that.tablePage.currentPage, that.tablePage.size, this.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          for (let i = 0; i < that.tableData.length; i++) {
            if (that.tableData[i].hasPhaseMessage != 1) {
              that.tooltip = that.tooltip + that.tableData[i].studentName + ' ';
            }
          }
          if (that.tooltip.length > 0) {
            that.tooltip = that.tooltip + '需配置结业寄语';
            that.showTooltip = true;
          }
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      jumpOpenMessage(studentCode) {
        window.localStorage.setItem('grammarMessageStudentCode', studentCode);
        const that = this;
        that.$router.push({
          path: '/grammar/grammarMessage',
          query: {
            studentCode: studentCode
          }
        });
      },
      editOpenStatus(id, openStatus) {
        if (openStatus == 0) {
          openStatus = 1;
        } else {
          openStatus = 0;
        }
        const that = this;
        that.updateData.id = id;
        that.updateData.openStatus = openStatus;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            grammarApi
              .updateStatus(that.updateData)
              .then((res) => {
                that.$nextTick(() => that.fetchData());
                that.$message.success('修改成功!');
              })
              .catch((err) => {});
          })
          .catch((err) => {});
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      }
    }
  };
</script>

<style>
  .period-table td,
  .period-table th {
    text-align: center;
  }

  .mt20 {
    margin-top: 20px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  @media screen and (max-width: 767px) {
    .recharge-dialog .el-dialog {
      width: 90% !important;
    }

    .el-message-box {
      width: 80% !important;
    }
  }
</style>
