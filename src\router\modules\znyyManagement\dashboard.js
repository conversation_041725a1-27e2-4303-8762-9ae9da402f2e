// 渠道管理-我的桌面-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/dashboard',
  component: Layout,
  name: 'dashboard',
  meta: {
    title: '我的桌面',
    icon: 'dashboard',
    noCache: true
  },
  children: [
    {
      path: 'dashboard',
      component: _import('dashboard/index'),
      name: 'dashboard',
      meta: {
        title: '我的桌面',
        icon: 'dashboard',
        noCache: true
      }
    }
  ]
};
