<template>
  <el-dialog :visible.sync="visible" title="上传课件" width="50%" @close="handleCancel">
    <el-form :model="form" label-width="120px" :rules="rules" ref="formRef" v-loading="loadingBase || loadingDetail" element-loading-text="加载中...">
      <el-form-item label="课程大类：" prop="curriculumId">
        <!-- 多课程大类时 -->
        <template v-if="multipleCurriculums">
          <el-select v-model="form.curriculumIds" multiple disabled placeholder="请选择">
            <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </template>
        <!-- 单课程大类 -->
        <template v-else>
          <el-select v-model="form.curriculumId" disabled placeholder="请选择">
            <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </template>
      </el-form-item>
      <el-form-item label="学段/学科：" prop="subject">
        <el-cascader v-model="form.subject" :options="dataQueryDict.gradeList" disabled></el-cascader>
      </el-form-item>
      <el-form-item label="关联视频名称：" prop="videoName">
        <el-input v-model="form.videoName" placeholder="请输入视频名称" disabled style="width: 222px" />
      </el-form-item>
      <el-form-item label="上传课件：" prop="file" required>
        <BaseUploadMSoft ref="baseUpload" @upload-success="uploadFile" @delete-file="deleteFile" @uploading-change="onUploadingChange" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="submitUpload" :loading="saveUploading" :disabled="disableAction">保存</el-button>
      <el-button type="primary" @click="handleSaveAndContinue" :loading="saveAlluploading" :disabled="disableContinue">保存后上传新课件</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import BaseUploadMSoft from './BaseUploadMSoft.vue';
  import videoConfig from '@/api/studyExamPassed/videoConfig';
  import courseApi from '@/api/studyExamPassed/course';
  import { mapGetters } from 'vuex';

  export default {
    name: 'CourseWareUploadDialog',
    components: { BaseUploadMSoft },
    data() {
      return {
        visible: false,
        queryParams: {},
        loadingBase: false, // 基础数据加载（课程大类/学段）
        loadingDetail: false, // 视频详情加载
        list: [], // 批量迭代的视频列表
        currentIndex: -1, // 当前处理的视频在列表中的索引
        form: {
          curriculumId: '', // 单个课程大类 ID
          curriculumIds: [], // 多个课程大类 ID 列表（父组件可能传入）
          subject: [],
          videoName: '',
          file: ''
        },
        courseCategoryList: [],
        dataQueryDict: {
          gradeList: []
        },
        saveUploading: false,
        saveAlluploading: false,
        innerUploading: false,
        rules: {
          videoName: [{ required: true, message: '请输入视频名称', trigger: 'blur' }],
          file: [{ required: true, message: '请上传课件', trigger: 'change' }]
        }
      };
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat']),
      multipleCurriculums() {
        return Array.isArray(this.form.curriculumIds) && this.form.curriculumIds.length > 1;
      },
      curriculumLabels() {
        const map = {};
        this.courseCategoryList.forEach((it) => (map[it.value] = it.label));
        return map;
      },
      // 是否还能继续上传：存在下一个未上传课件的视频
      canSaveAndContinue() {
        return this.getNextPendingIndex() !== -1;
      },
      disableAction() {
        return this.innerUploading || !this.form.file || this.saveUploading;
      },
      disableContinue() {
        return this.innerUploading || !this.form.file || this.saveAlluploading || !this.canSaveAndContinue;
      }
    },
    methods: {
      // 初始化弹窗
      async init(row, list = [], startIndex = null) {
        this.queryParams = row || {};
        this.list = Array.isArray(list) ? list : [];
        this.currentIndex = startIndex !== null ? startIndex : this.list.length ? this.list.findIndex((i) => i.id === row?.id) : -1;
        this.resetState();
        this.visible = true;
        this.loadBaseData();
        this.loadDetail(row);
      },
      loadBaseData() {
        this.loadingBase = true;
        Promise.all([this.initCourseCategoryList(), this.initGradeList()])
          .catch(() => {})
          .finally(() => {
            this.loadingBase = false;
          });
      },
      loadDetail(row) {
        if (!row || !row.id) return;
        this.loadingDetail = true;
        this.getDetail(row)
          .catch(() => {})
          .finally(() => {
            this.loadingDetail = false;
          });
      },
      // 获取视频详情并填充表单
      async getDetail(record) {
        if (!record || !record.id) return;
        try {
          const res = await videoConfig.getVideoDetail({ id: record.id });
          const data = res.data || {};
          // 兼容后端可能返回 curriculumIds（数组）或单个 curriculumId
          const multiIds = Array.isArray(data.curriculumIds) ? data.curriculumIds : [];
          this.form.curriculumIds = multiIds.length ? multiIds : Array.isArray(record.curriculumIds) ? record.curriculumIds : [];
          this.form.curriculumId = data.curriculumId || this.form.curriculumIds[0] || '';
          this.form.subject = data.gradeLevel && data.subjectId ? [data.gradeLevel, data.subjectId] : [];
          this.form.videoName = data.videoName || '';
          this.form.file = '';
          Object.keys(data).forEach((k) => {
            if (!(k in this.form)) this.$set(this.form, k, data[k]);
          });
        } catch (e) {
          this.$message.error('获取视频详情失败');
        }
      },
      // 获取课程大类
      initCourseCategoryList() {
        courseApi.getCourseCategory().then((res) => {
          this.courseCategoryList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },
      // 获取学段
      initGradeList() {
        courseApi.getGradeAndSubjectList().then((res) => {
          this.dataQueryDict.gradeList = res.data;
        });
      },
      // 处理上传成功回调
      uploadFile(file) {
        this.form.file = file.url;
      },
      onUploadingChange(flag) {
        this.innerUploading = flag;
      },
      // 处理删除文件回调
      deleteFile() {
        this.form.file = '';
      },
      // 重置内部状态
      resetState() {
        this.form = { curriculumId: '', curriculumIds: [], subject: [], videoName: '', file: '' };
        this.$refs?.baseUpload?.clearAll && this.$refs.baseUpload.clearAll();
        this.uploading = false;
      },

      handleCancel() {
        // 触发子组件清理
        const child = this.$refs?.baseUpload;
        if (child && typeof child.handleCancel === 'function') {
          try {
            child.handleCancel();
          } catch (e) {}
        }
        this.resetState();
        this.$nextTick(() => this.$refs.formRef?.resetFields?.());
        this.visible = false;
      },

      // 提交上传（保存）
      submitUpload() {
        this.$refs.formRef.validate((valid) => {
          if (!valid) return;
          this.saveUploading = true;
          videoConfig
            .addCourseware({
              id: this.form.id,
              coursewareUrl: this.form.file
            })
            .then(() => {
              this.$message.success('上传课件成功');
              this.handleCancel();
              this.$emit('refresh-videoList');
              this.saveUploading = false;
            })
            .catch(() => {
              this.saveUploading = false;
            });
        });
      },
      // 保存后继续
      handleSaveAndContinue() {
        this.$refs.formRef.validate((valid) => {
          if (!valid) return;
          if (!this.form.file) return this.$message.warning('请先选择文件');
          this.saveAlluploading = true;
          videoConfig
            .addCourseware({ id: this.form.id, coursewareUrl: this.form.file })
            .then(() => {
              this.$message.success('上传课件成功');
              this.$emit('refresh-videoList');
              // 标记当前条目已上传，便于跳过
              if (this.list[this.currentIndex]) {
                this.$set(this.list[this.currentIndex], 'coursewareUrl', this.form.file);
              }
              // 进入下一个
              this.advanceToNext();
            })
            .catch(() => {
              this.saveAlluploading = false;
            });
        });
      },
      // 获取下一个未上传课件的索引（仅向下查找）
      getNextPendingIndex() {
        if (!Array.isArray(this.list) || this.currentIndex === -1) return -1;
        for (let i = this.currentIndex + 1; i < this.list.length; i++) {
          const item = this.list[i];
          if (!this.isUploaded(item)) return i; // 未上传
        }
        return -1;
      },
      // 判断是否已上传课件
      isUploaded(item) {
        if (!item) return false;
        const val = item.coursewareUrl;
        return typeof val === 'string' ? val.trim() !== '' : !!val;
      },
      // 前进到下一个未上传视频并重新加载详情
      async advanceToNext() {
        const nextIndex = this.getNextPendingIndex();
        if (nextIndex === -1) {
          this.$message.success('该页视频已全部上传课件');
          this.saveAlluploading = false;
          this.handleCancel();
          return;
        }
        this.currentIndex = nextIndex;
        const nextItem = this.list[this.currentIndex];
        this.resetState();
        try {
          await this.loadDetail(nextItem);
          this.saveAlluploading = false;
          this.$message.info(`继续上传：${nextItem.videoName || nextItem.id}`);
        } catch (e) {
          this.$message.error('加载下一个视频失败');
          this.saveAlluploading = false;
          this.handleCancel();
        }
      }
    }
  };
</script>

<style scoped lang="less">
  .upload-container {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 10px;
  }

  .upload-area {
    text-align: center;
    padding: 30px 0;
  }

  .upload-area .el-icon-upload {
    font-size: 48px;
    color: #c0c4cc;
    margin-bottom: 10px;
  }

  .upload-text {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
  }

  .upload-tip {
    font-size: 12px;
    color: #999;
  }

  .progress-container {
    margin: 10px 0;
  }

  .file-info-container {
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
  }

  .file-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .file-name {
    flex: 1;
    font-weight: bold;
    margin-right: 10px;
  }

  .file-size,
  .file-type {
    color: #999;
    margin-right: 10px;
  }

  .file-type {
    font-size: 12px;
  }
</style>
