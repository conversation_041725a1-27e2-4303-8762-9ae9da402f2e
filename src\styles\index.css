:export {
  menuText: #bfcbd9;
  menuActiveText: #409EFF;
  subMenuActiveText: #f4f4f5;
  menuBg: #304156;
  menuHover: #263445;
  subMenuBg: #1f2d3d;
  subMenuHover: #001528;
  sideBarWidth: 210px;
}

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  position: absolute;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload input[type="file"] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.el-upload-list {
  width: auto !important;
}

.cell .el-tag {
  margin-right: 0px;
}

.small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width .el-button--mini {
  padding: 7px 10px;
  min-width: 60px;
}

.status-col .cell {
  padding: 0 10px;
  text-align: center;
}

.status-col .cell .el-tag {
  margin-right: 0px;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container .el-upload {
  width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu a {
  display: block;
}

.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-range-separator {
  box-sizing: content-box;
}

#app .main-container {
  min-height: 100%;
  transition: margin-left .28s;
  margin-left: 210px;
  position: relative;
}

#app .sidebar-container {
  transition: width 0.28s;
  width: 210px !important;
  background-color: #304156;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
}

#app .sidebar-container .horizontal-collapse-transition {
  transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}

#app .sidebar-container .scrollbar-wrapper {
  overflow-x: hidden !important;
}

#app .sidebar-container .el-scrollbar__bar.is-vertical {
  right: 0px;
}

#app .sidebar-container .el-scrollbar {
  height: 100%;
}

#app .sidebar-container.has-logo .el-scrollbar {
  height: calc(100% - 50px);
}

#app .sidebar-container .is-horizontal {
  display: none;
}

#app .sidebar-container a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}

#app .sidebar-container .svg-icon {
  margin-right: 16px;
}

#app .sidebar-container .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}

#app .sidebar-container .el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}

#app .sidebar-container .submenu-title-noDropdown:hover,
#app .sidebar-container .el-submenu__title:hover {
  background-color: #263445 !important;
}

#app .sidebar-container .is-active > .el-submenu__title {
  color: #f4f4f5 !important;
}

#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title,
#app .sidebar-container .el-submenu .el-menu-item {
  min-width: 210px !important;
  background-color: #1f2d3d !important;
}

#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title:hover,
#app .sidebar-container .el-submenu .el-menu-item:hover {
  background-color: #001528 !important;
}

#app .hideSidebar .sidebar-container {
  width: 54px !important;
}

#app .hideSidebar .main-container {
  margin-left: 54px;
}

#app .hideSidebar .submenu-title-noDropdown {
  padding: 0 !important;
  position: relative;
}

#app .hideSidebar .submenu-title-noDropdown .el-tooltip {
  padding: 0 !important;
}

#app .hideSidebar .submenu-title-noDropdown .el-tooltip .svg-icon {
  margin-left: 20px;
}

#app .hideSidebar .submenu-title-noDropdown .el-tooltip .sub-el-icon {
  margin-left: 19px;
}

#app .hideSidebar .el-submenu {
  overflow: hidden;
}

#app .hideSidebar .el-submenu > .el-submenu__title {
  padding: 0 !important;
}

#app .hideSidebar .el-submenu > .el-submenu__title .svg-icon {
  margin-left: 20px;
}

#app .hideSidebar .el-submenu > .el-submenu__title .sub-el-icon {
  margin-left: 19px;
}

#app .hideSidebar .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

#app .hideSidebar .el-menu--collapse .el-submenu > .el-submenu__title > span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

#app .hideMenu .sidebar-container {
  width: 0px !important;
}

#app .hideMenu .main-container {
  margin-left: 54px;
}

#app .hideMenu .submenu-title-noDropdown {
  padding: 0 !important;
  position: relative;
}

#app .hideMenu .submenu-title-noDropdown .el-tooltip {
  padding: 0 !important;
}

#app .hideMenu .submenu-title-noDropdown .el-tooltip .svg-icon {
  margin-left: 20px;
}

#app .hideMenu .submenu-title-noDropdown .el-tooltip .sub-el-icon {
  margin-left: 19px;
}

#app .hideMenu .el-submenu {
  overflow: hidden;
}

#app .hideMenu .el-submenu > .el-submenu__title {
  padding: 0 !important;
}

#app .hideMenu .el-submenu > .el-submenu__title .svg-icon {
  margin-left: 20px;
}

#app .hideMenu .el-submenu > .el-submenu__title .sub-el-icon {
  margin-left: 19px;
}

#app .hideMenu .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

#app .hideMenu .el-menu--collapse .el-submenu > .el-submenu__title > span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

#app .el-menu--collapse .el-menu .el-submenu {
  min-width: 210px !important;
}

#app .mobile .main-container {
  margin-left: 0px;
}

#app .mobile .sidebar-container {
  transition: transform .28s;
  width: 210px !important;
}

#app .mobile.hideSidebar .sidebar-container {
  pointer-events: none;
  transition-duration: 0.3s;
  transform: translate3d(-210px, 0, 0);
}

#app .withoutAnimation .main-container,
#app .withoutAnimation .sidebar-container {
  transition: none;
}

.el-menu--vertical > .el-menu .svg-icon {
  margin-right: 16px;
}

.el-menu--vertical > .el-menu .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}

.el-menu--vertical .nest-menu .el-submenu > .el-submenu__title:hover,
.el-menu--vertical .el-menu-item:hover {
  background-color: #263445 !important;
}

.el-menu--vertical > .el-menu--popup {
  max-height: 100vh;
  overflow-y: auto;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar {
  width: 6px;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}

:export {
  menuText: #bfcbd9;
  menuActiveText: #409EFF;
  subMenuActiveText: #f4f4f5;
  menuBg: #304156;
  menuHover: #263445;
  subMenuBg: #1f2d3d;
  subMenuHover: #001528;
  sideBarWidth: 210px;
}

.blue-btn {
  background: #324157;
}

.blue-btn:hover {
  color: #324157;
}

.blue-btn:hover:before, .blue-btn:hover:after {
  background: #324157;
}

.light-blue-btn {
  background: #3A71A8;
}

.light-blue-btn:hover {
  color: #3A71A8;
}

.light-blue-btn:hover:before, .light-blue-btn:hover:after {
  background: #3A71A8;
}

.red-btn {
  background: #C03639;
}

.red-btn:hover {
  color: #C03639;
}

.red-btn:hover:before, .red-btn:hover:after {
  background: #C03639;
}

.pink-btn {
  background: #E65D6E;
}

.pink-btn:hover {
  color: #E65D6E;
}

.pink-btn:hover:before, .pink-btn:hover:after {
  background: #E65D6E;
}

.green-btn {
  background: #30B08F;
}

.green-btn:hover {
  color: #30B08F;
}

.green-btn:hover:before, .green-btn:hover:after {
  background: #30B08F;
}

.tiffany-btn {
  background: #4AB7BD;
}

.tiffany-btn:hover {
  color: #4AB7BD;
}

.tiffany-btn:hover:before, .tiffany-btn:hover:after {
  background: #4AB7BD;
}

.yellow-btn {
  background: #FEC171;
}

.yellow-btn:hover {
  color: #FEC171;
}

.yellow-btn:hover:before, .yellow-btn:hover:after {
  background: #FEC171;
}

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;
}

.pan-btn:hover {
  background: #fff;
}

.pan-btn:hover:before, .pan-btn:hover:after {
  width: 100%;
  transition: 600ms ease all;
}

.pan-btn:before, .pan-btn:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 2px;
  width: 0;
  transition: 400ms ease all;
}

.pan-btn::after {
  right: inherit;
  top: inherit;
  left: 0;
  bottom: 0;
}

.custom-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  color: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: ' ';
  clear: both;
  height: 0;
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

aside a {
  color: #337ab7;
  cursor: pointer;
}

aside a:hover {
  color: #20a0ff;
}

.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, #20b6f9 0%, #20b6f9 0%, #2178f1 100%, #2178f1 100%);
}

.sub-navbar .subtitle {
  font-size: 20px;
  color: #fff;
}

.sub-navbar.draft {
  background: #d0d0d0;
}

.sub-navbar.deleted {
  background: #d0d0d0;
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
}

.link-type:hover,
.link-type:focus:hover {
  color: #20a0ff;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}

.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.form-single-fragment {
  padding-left: 10px;
  padding-top: 10px;
  padding-bottom: 20px;
}
