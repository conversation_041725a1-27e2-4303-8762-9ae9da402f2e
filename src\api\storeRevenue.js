import request from '@/utils/request';

export const merchantEarnings = (data) => {
  return request({
    url: 'znyy/V2/merchant/merchantEarnings',
    method: 'GET',
    params: data
  });
};

export const merchantBalance = (data) => {
  return request({
    url: 'znyy/V2/merchant/merchantBalance',
    method: 'GET',
    params: data
  });
};
export function businessTag() {
  return request({
    url: '/mps/order/queryBusinessTagAll',
    method: 'GET'
  });
}

export function orderStatus() {
  return request({
    url: '/mps/order/queryOrderStatusAll',
    method: 'GET'
  });
}
export function getMerchantBalance() {
  return request({
    url: '/znyy/V2/merchant/merchantBalance',
    method: 'GET'
  });
}
export function checkedSign(data) {
  return request({
    url: 'flexpay/user/sign-status',
    method: 'GET',
    params: data
  });
}
export function signTime(data) {
  return request({
    url: 'flexpay/user/sign-time?userCode=' + data.userCode,
    method: 'PUT'
  });
}
export function cashWithdrawal(data) {
  return request({
    url: 'flexpay/order/withdraw/unified ',
    method: 'post',
    data
  });
}
