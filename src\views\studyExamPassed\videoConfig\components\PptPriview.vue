<template>
  <div v-if="visible" class="full-screen-preview">
    <div class="preview-overlay" @click="closePreview"></div>
    <div class="preview-container">
      <div class="preview-header" :class="{ 'single-right': mode !== 'slides' }">
        <div class="header-left" v-if="mode === 'slides' && totalPages > 1">
          <el-button type="text" @click="goToFirstPage" :disabled="isFirstPage">首页</el-button>
          <el-button type="text" @click="goToLastPage" :disabled="isLastPage">最后一页</el-button>
        </div>
        <div class="header-right">
          <el-button type="text" @click="closePreview">退出预览</el-button>
        </div>
      </div>
      <div class="preview-main">
        <div class="slide-control left" @click="prevPage" v-if="mode === 'slides' && !isFirstPage">
          <i class="el-icon-arrow-left"></i>
        </div>
        <div class="slide-content" ref="slideContent">
          <template v-if="mode === 'slides'">
            <div class="slide-wrapper">
              <img :src="currentSlide" alt="课件内容" />
            </div>
          </template>
          <template v-else>
            <div class="embed-wrapper">
              <iframe :src="embedUrl" frameborder="0" allowfullscreen></iframe>
            </div>
          </template>
        </div>
        <div class="slide-control right" @click="nextPage" v-if="mode === 'slides' && !isLastPage">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
      <div class="preview-footer">
        <template v-if="mode === 'slides'">
          <span>{{ currentPage }} / {{ totalPages }}</span>
        </template>
        <template v-else-if="mode === 'pdf'">
          <span>PDF 预览</span>
        </template>
        <template v-else-if="mode === 'office'">
          <span>PPT 在线预览</span>
        </template>
        <template v-else>
          <span>文件预览</span>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'FullScreenCoursewarePreview',
    props: {
      visible: { type: Boolean, default: false },
      coursewareUrl: { type: String, required: true }
    },
    data() {
      return {
        currentPage: 1,
        totalPages: 1,
        currentSlide: '',
        slides: [],
        loading: false,
        mode: 'slides', // slides | pdf | office | other
        embedUrl: ''
      };
    },
    computed: {
      isFirstPage() {
        return this.currentPage === 1;
      },
      isLastPage() {
        return this.currentPage === this.totalPages;
      }
    },
    watch: {
      visible(val) {
        if (val) this.preparePreview();
      },
      coursewareUrl() {
        if (this.visible) this.preparePreview();
      }
    },
    methods: {
      getExt(url) {
        if (!url) return '';
        const i = url.lastIndexOf('.');
        return i !== -1 ? url.slice(i + 1).toLowerCase() : '';
      },
      preparePreview() {
        const ext = this.getExt(this.coursewareUrl);
        if (!ext) {
          this.mode = 'other';
          this.embedUrl = this.coursewareUrl;
          return;
        }
        if (ext === 'pdf') {
          this.mode = 'pdf';
          this.embedUrl = this.coursewareUrl;
          return;
        }
        if (ext === 'ppt' || ext === 'pptx') {
          const encoded = encodeURIComponent(this.coursewareUrl);
          this.mode = 'office';
          this.embedUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encoded}`;
          return;
        }
        if (['png', 'jpg', 'jpeg', 'gif', 'webp'].includes(ext)) {
          this.mode = 'slides';
          this.slides = [this.coursewareUrl];
          this.totalPages = 1;
          this.currentSlide = this.coursewareUrl;
          return;
        }
        this.mode = 'other';
        this.embedUrl = this.coursewareUrl;
      },

      // 跳转到首页
      goToFirstPage() {
        if (!this.isFirstPage) {
          this.currentPage = 1;
          this.currentSlide = this.slides[0];
        }
      },

      // 跳转到最后一页
      goToLastPage() {
        if (!this.isLastPage) {
          this.currentPage = this.totalPages;
          this.currentSlide = this.slides[this.totalPages - 1];
        }
      },

      // 上一页
      prevPage() {
        if (!this.isFirstPage) {
          this.currentPage--;
          this.currentSlide = this.slides[this.currentPage - 1];
        }
      },

      // 下一页
      nextPage() {
        if (!this.isLastPage) {
          this.currentPage++;
          this.currentSlide = this.slides[this.currentPage - 1];
        }
      },

      // 关闭预览
      closePreview() {
        this.$emit('close');
      },

      // 重新加载
      reload() {
        this.preparePreview();
      }
    },
    mounted() {
      if (this.visible) this.preparePreview();
    }
  };
</script>

<style scoped lang="less">
  // ========== LESS 变量 ========== //
  @primary: #409eff;
  @overlay-bg: rgba(0, 0, 0, 0.5);
  @overlay-bg-hover: rgba(0, 0, 0, 0.7);
  @panel-bg: #fff;
  @panel-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  @border-color: #e4e7ed;
  @header-bg: #f5f7fa;
  @text-gray: #666;
  @text-light: #999;
  @header-height: 60px;

  .full-screen-preview {
    position: fixed;
    inset: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;

    .preview-overlay {
      position: absolute;
      inset: 0;
      background-color: @overlay-bg;
      z-index: 1;
    }

    .preview-container {
      position: relative;
      width: 80%;
      max-width: 1200px;
      height: 80%;
      max-height: 800px;
      background: @panel-bg;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: @panel-shadow;
      z-index: 2;
      display: flex;
      flex-direction: column;

      .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: @header-bg;
        border-bottom: 1px solid @border-color;
        height: @header-height;

        .header-left,
        .header-right {
          display: flex;
          gap: 10px;

          .el-button {
            padding: 0;
            margin: 0;
            min-width: auto;
            width: auto;
            font-size: 14px;
            color: @text-gray;
            &:hover {
              color: @primary;
            }
          }
        }
      }
      .preview-header.single-right {
        justify-content: flex-end;
      }

      .preview-main {
        position: relative;
        flex: 1; // 占满剩余高度
        height: calc(100% - @header-height); // 兼容旧逻辑
        overflow: hidden;

        .slide-control {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 40px;
          height: 40px;
          background: @overlay-bg;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          z-index: 10;
          color: #fff;
          &:hover {
            background: @overlay-bg-hover;
          }
          &.left {
            left: 10px;
          }
          &.right {
            right: 10px;
          }
        }

        .slide-content {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;
          .slide-wrapper {
            width: 100%;
            height: 100%;
            position: relative;
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              display: block;
            }
          }
          .embed-wrapper {
            width: 100%;
            height: 100%;
            iframe {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .preview-footer {
        padding: 15px 20px;
        background: @header-bg;
        border-top: 1px solid @border-color;
        text-align: center;
        color: @text-light;
        font-size: 14px;
      }
    }

    // 响应式
    @media (max-width: 800px) {
      .preview-container {
        width: 95%;
        height: 90%;
        max-width: none;
        max-height: none;

        .preview-main {
          .slide-control {
            width: 30px;
            height: 30px;
          }
          .slide-content .slide-wrapper .slide-text {
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
