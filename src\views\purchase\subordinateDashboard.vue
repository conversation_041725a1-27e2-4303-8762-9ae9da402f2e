<!--采购管理-下级采购看板-->
<template>
  <div class="app-container">
    <el-card style="margin-bottom: 16px">
      <el-tabs v-model="activeName" @tab-click="handleClick" v-if="isAdmin">
        <el-tab-pane label="品牌采购" name="0"></el-tab-pane>
        <el-tab-pane label="俱乐部采购" name="1"></el-tab-pane>
      </el-tabs>
      <el-form label-width="80px" label-position="left" ref="dataQuery" :model="dataQuery">
        <el-row type="flex" :gutter="20">
          <el-col :span="6" :xs="24" v-if="isAdmin">
            <el-form-item :label="activeName === '0' ? '品牌名称:' : '俱乐部名称:'" :prop="activeName === '0' ? 'merchantName' : 'merchantName'" label-width="110px">
              <el-input v-model="dataQuery.merchantName" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24" v-if="isZxBrand">
            <el-form-item label="俱乐部名称" prop="merchantName" label-width="110px">
              <el-input v-model="dataQuery.merchantName" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="负责人:" prop="chargeName">
              <el-input v-model="dataQuery.chargeName" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="20">
            <el-form-item label-width="0">
              <el-button
                type="primary"
                @click="
                  () => {
                    tablePage.currentPage = 1;
                    fetchData();
                  }
                "
              >
                搜索
              </el-button>
              <el-button @click="rest()">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- ----------------------------- -->
      </el-form>
    </el-card>

    <el-card v-if="tablePage.totalItems > 0">
      <el-row style="margin-bottom: 20px" v-if="isZxBrand">
        <p class="card">
          <i class="el-icon-s-tools"></i>
          &nbsp;剩余合伙人智能学习管理系统数量：
          <el-tag type="info" effect="plain">{{ systemNumber || 0 }}</el-tag>
        </p>
      </el-row>
      <el-table
        class="common-table"
        v-loading="tableLoading"
        :data="tableData"
        max-height="600px"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        stripe
        default-expand-all
        :row-style="{ height: '50px' }"
      >
        <el-table-column v-if="isAdmin" :prop="activeName === '0' ? 'merchantName' : 'merchantName'" :label="activeName === '0' ? '品牌名称' : '俱乐部名称'"></el-table-column>
        <el-table-column v-if="isZxBrand" prop="merchantName" label="俱乐部名称"></el-table-column>
        <el-table-column v-for="(item, index) in tableHeader" :key="index" :prop="item.prop" :label="item.label" :width="item.width"></el-table-column>
        <el-table-column prop="orderId" label="操作" width="400px" fixed="right">
          <template v-slot="{ row }">
            <el-button icon="el-icon-tickets" type="primary" size="mini" @click="openDetails(row)" plain>查看明细</el-button>
            <!-- <el-button icon="el-icon-edit-outline" type="primary" size="mini" @click="submitSend(row)" v-if="isZxBrand" plain>分配数量</el-button> -->
            <el-button
              icon="el-icon-edit-outline"
              type="primary"
              size="mini"
              @click="submitSend(row)"
              v-if="(isAdmin && activeName === '0') || (isZxBrand && activeName === '0')"
              plain
            >
              发货
            </el-button>
            <!-- && activeName == 0 -->
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <el-row type="flex" justify="end" style="height: 28px; margin-top: 36px; line-height: 28px" v-if="tablePage.totalItems > 0">
        <el-pagination
          :current-page="tablePage.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tablePage.totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
    </el-card>
    <el-card v-else>
      <NoMore></NoMore>
    </el-card>
    <!-- 查看凭证 -->
    <CustomDialog v-if="dialogVisible" :value.sync="dialogVisible" @close="dialogVisible = false">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </CustomDialog>
    <!-- 分配弹窗 -->
    <CustomDialog v-if="addDialogVisible2" :value.sync="addDialogVisible2" width="35%" title="发货" @close="cancelSend2" :close-on-click-modal="false">
      <el-form label-width="110px" label-position="left" ref="allocationForm" :model="allocationForm" style="text-align: left">
        <!-- <el-form-item v-if="isAdmin" :label="activeName === '0' ? '品牌名称' : '俱乐部名称'">
          <el-input v-model="allocationForm['merchantName']" disabled></el-input>
        </el-form-item> -->
        <el-form-item label="俱乐部名称">
          <el-input v-model="allocationForm['merchantName']" disabled></el-input>
        </el-form-item>
        <el-form-item label="发货内容">
          <el-input v-model="shippingContent" disabled></el-input>
        </el-form-item>
        <el-form-item label="负责人">
          <el-input v-model="allocationForm.chargeName" disabled></el-input>
        </el-form-item>
        <el-form-item label="发货数量" prop="count">
          <el-slider v-model="allocationForm.count" show-input :min="1" :max="systemNumber"></el-slider>
          <div style="margin-top: -10px">
            分配最大数量不可超过自身剩余数量：
            <span class="red">{{ systemNumber }}</span>
          </div>
        </el-form-item>
        <el-form-item label="付款凭证">
          <el-upload
            list-type="picture-card"
            element-loading-text="图片上传中"
            v-loading="uploadLoading"
            action=""
            :limit="1"
            :on-exceed="justPictureNum"
            :file-list="imageList"
            :before-upload="beforeAvatarUpload"
            :http-request="uploadDetailHttpContract"
            :on-preview="handlePictureCardPreviewContract"
            :on-remove="handleRemoveDetailContract"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div style="margin-top: -10px">上传最大限制10MB</div>
        </el-form-item>
        <!-- 提示 -->
        <el-form-item label="">
          <span class="red">发货数量后将无法撤销，请核对发货确认单</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="cancelSend2">取消</el-button>
          <el-button type="primary" @click="submitSend()">提交</el-button>
        </el-form-item>
      </el-form>
    </CustomDialog>
    <CustomDialog v-if="addDialogVisible" :value="addDialogVisible" width="35%" title="发货" @close="cancelSend" :close-on-click-modal="false">
      <el-form label-width="70px" label-position="left" ref="allocationForm" :model="allocationForm" style="text-align: left">
        <el-form-item v-if="isAdmin" :label="activeName === '0' ? '品牌名称' : '俱乐部名称'">
          <el-input v-model="allocationForm['merchantName']" disabled></el-input>
        </el-form-item>
        <el-form-item v-if="isZxBrand" label="俱乐部名称">
          <el-input v-model="allocationForm['merchantName']" disabled></el-input>
        </el-form-item>
        <!-- <el-form-item v-if="isZxBrand" label="发货内容">
        <el-input v-model="allocationForm.shippingContent" disabled></el-input>
      </el-form-item> -->

        <el-form-item label="负责人">
          <el-input v-model="allocationForm.chargeName" disabled></el-input>
        </el-form-item>
        <el-form-item label="发货内容">
          <el-radio-group v-model="allocationForm.purchaseType" class="radio-container" @change="changeRadio">
            <el-radio label="2">全款俱乐部智能学习管理系统</el-radio>
            <el-radio label="1">合伙人智能学习管理系统</el-radio>
            <el-radio label="3">定金俱乐部智能学习管理系统</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="类型" prop="type" v-if="showMOnneyType">
          <div class="type-container" v-for="item in typeList" :key="item.id">
            <span class="type-description">{{ item.deposit }}</span>
            <el-input-number size="mini" :min="0" v-model="item.num" @change="handleChange(item.id)"></el-input-number>
          </div>
        </el-form-item>
        <el-form-item label="发货数量" prop="count" v-else>
          <el-slider v-model="allocationForm.count" show-input :min="1" :max="systemNumber"></el-slider>
          <div style="margin-top: -10px">
            分配最大数量不可超过自身剩余数量：
            <span class="red">{{ systemNumber }}</span>
          </div>
        </el-form-item>

        <el-form-item label="付款凭证">
          <el-upload
            list-type="picture-card"
            element-loading-text="图片上传中"
            v-loading="uploadLoading"
            action=""
            :limit="1"
            :on-exceed="justPictureNum"
            :file-list="imageList"
            :before-upload="beforeAvatarUpload"
            :http-request="uploadDetailHttpContract"
            :on-preview="handlePictureCardPreviewContract"
            :on-remove="handleRemoveDetailContract"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div style="margin-top: -10px">上传最大限制10MB</div>
        </el-form-item>
        <!-- 提示 -->
        <el-form-item label="">
          <span class="red">分配数量后将无法撤销，请核对发货确认单</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="cancelSend">取消</el-button>
          <el-button type="primary" @click="submitSend()">提交</el-button>
        </el-form-item>
      </el-form>
    </CustomDialog>
    <!-- 采购明细 -->
    <CustomDialog v-if="detailDialogVisible" :value.sync="detailDialogVisible" width="40%" title="发货" @close="detailDialogVisible = false" :close-on-click-modal="false">
      <div v-if="detailsDataPage.totalItems > 0">
        <el-table
          class="common-table"
          max-height="385px"
          v-loading="detailTableLoading"
          :data="detailsData"
          style="width: 100%; margin-bottom: 20px"
          row-key="id"
          border
          default-expand-all
          :header-cell-style="{ color: '#666', height: '60px', background: '#f5f5f5' }"
          :row-style="{ height: '50px' }"
        >
          <el-table-column v-if="isAdmin" :prop="activeName === '0' ? 'merchantName' : 'merchantName'" :label="activeName === '0' ? '品牌名称' : '俱乐部名称'"></el-table-column>
          <el-table-column v-if="isZxBrand" prop="merchantName" label="俱乐部名称"></el-table-column>
          <el-table-column prop="purchaseType" label="采购内容"></el-table-column>
          <el-table-column prop="purchaseCount" label="数量"></el-table-column>
          <el-table-column prop="status" label="采购状态"></el-table-column>
          <el-table-column prop="purchaseTime" label="采购时间"></el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-row>
          <el-col :span="24" style="overflow-x: auto" :xs="24">
            <el-pagination
              :current-page="detailsDataPage.currentPage"
              :page-sizes="[10, 20, 30, 40, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="detailsDataPage.totalItems"
              @size-change="handleDetailsSizeChange"
              @current-change="handleDetailsCurrentChange"
            />
          </el-col>
        </el-row>
      </div>
      <NoMore v-else></NoMore>
    </CustomDialog>

    <purchaseContract ref="spurchaseDialogVisible"></purchaseContract>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { ossPrClient } from '@/api/alibaba';
  import orderApi from '@/api/purchase/subordinateDashboard';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import checkPermission from '@/utils/permission';
  import purchaseContract from '@/components/purchaseContract/dialog.vue';
  import store from '@/store';
  import CustomDialog from '@/components/customDialog/index.vue';

  import dayjs from 'dayjs';
  import json from 'highlight.js/lib/languages/json';
  import NoMore from '@/components/NoMore/index.vue';
  export default {
    name: 'subordinateDashboard',
    components: { NoMore, purchaseContract, CustomDialog },
    data() {
      return {
        typeNum: 0,
        type: {},
        // systemNumber: 0, // 剩余管理系统数量
        activeName: '0', // 0:品牌 1:俱乐部
        // 查询条件
        dataQuery: {
          merchantName: '',
          chargeName: ''
        },
        tableHeader: [
          { label: '负责人', prop: 'chargeName' },
          { label: '全款俱乐部智能学习管理系统采购总数', prop: 'fullTotalPurchase' },
          { label: '全款俱乐部智能学习管理系统消耗总数', prop: 'fullTotalConsumption' },
          { label: '全款俱乐部智能学习管理系统剩余总数', prop: 'fullResidualTotal' },
          { label: '合伙人智能学习管理系统采购总数', prop: 'totalPurchase' },
          { label: '合伙人智能学习管理系统消耗总数', prop: 'totalConsumption' },
          { label: '合伙人智能学习管理系统剩余总数', prop: 'residualTotal' },
          { label: '最新采购时间', prop: 'latestPurchaseTime' }
        ],
        regTime: [],
        allocationForm: {
          count: 1,
          amount: '',
          payment: '',
          imageUrl: '',
          SystemType: 1,
          purchaseType: 2
        },
        shippingContent: '合伙人智能学习管理系统',
        imageList: [],
        addDialogVisible: false, // 新增采购单
        addDialogVisible2: false, // 新增采购单
        dialogVisible: false, // 查看凭证
        detailDialogVisible: false, // 采购明细
        tableLoading: false,
        uploadLoading: false, // 上传图片
        detailTableLoading: false,
        dialogImageUrl: '/static/img/avator.c61856d8.png',
        // 采购申请列表
        tableData: [],
        // 采购明细列表
        detailsData: [
          {
            orderId: '1234567890',
            name: '学习管理系统',
            startTime: '2020-01-01 00:00:00',
            endTime: '2020-01-01 00:00:00',
            status: 1,
            operationsName: '666俱乐部',
            chargeName: '申请人a',
            amount: '5000',
            payment: '2',
            updateNum: 3,
            imageUrl: '/static/img/avator.c61856d8.png'
          },
          {
            orderId: '1234567890',
            name: '学习管理系统',
            startTime: '2020-01-01 00:00:00',
            endTime: '2020-01-01 00:00:00',
            status: 2,
            operationsName: '666俱乐部',
            chargeName: '申请人a',
            amount: '5000',
            payment: '2',
            updateNum: 2,
            imageUrl: 'http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1741229815000'
          }
        ],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        //采购明细-分页
        detailsDataPage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        isAdmin: null, // 是否是管理员
        isZxBrand: null, // 是否是品牌
        currentItem: null, // 当前操作行
        showMOnneyType: false,
        typeList: [],
        chooseType: {}
      };
    },
    computed: {
      ...mapGetters(['setpayUrl', 'systemNumber'])
    },
    activated() {
      this.fetchData();
    },
    mounted() {
      // 管理员(admin) 还是 品牌(zxBrand)
      this.isAdmin = checkPermission(['admin']);
      this.isZxBrand = checkPermission(['zxBrand']);

      if (this.isZxBrand) {
        this.tableHeader = [
          { label: '负责人', prop: 'chargeName' },
          { label: '合伙人智能学习管理系统采购总数', prop: 'totalPurchase', width: 160 },
          { label: '合伙人智能学习管理系统消耗总数', prop: 'totalConsumption', width: 160 },
          { label: '合伙人智能学习管理系统剩余总数', prop: 'residualTotal', width: 160 },
          { label: '最新采购时间', prop: 'latestPurchaseTime', width: 160 }
        ];
      }
      // 品牌
      if (this.isZxBrand) {
        this.activeName = '0';
      }
      if (!this.isAdmin && !this.isZxBrand) {
        // 只有 管理员 和 品牌 可以访问
        this.$message.warning('权限不足，无法访问');
        this.tableHeader = [];
        return;
      }
      this.fetchData();
      ossPrClient();
      // this.addDialogVisible = true;
    },
    methods: {
      handleChange() {
        console.log('ppppppppppppppp');
      },
      checkPermission, // 权限判断
      // 打开采购明细
      openDetails(currentItem = false) {
        this.detailDialogVisible = true;
        if (currentItem) this.currentItem = currentItem;
        this.detailTableLoading = true;
        orderApi.queryPurchaseDetail(this.detailsDataPage.currentPage, this.detailsDataPage.size, this.currentItem.merchantCode).then((res) => {
          this.detailsData = res.data.data;
          this.detailTableLoading = false; // 关闭加载中

          pageParamNames.forEach((name) => this.$set(this.detailsDataPage, name, Number(res.data[name])));
        });
      },
      // 提交分配/打开分配
      submitSend(row = false) {
        let that = this;

        // 有参数是 打开分配弹窗
        if (row) {
          this.changeRadio(2);
          console.log(that.allocationForm.purchaseType, 'this.allocationForm.purchaseTyp');

          if (this.systemNumber <= 0) {
            return this.$message.warning('数量不足，请先采购数量');
          }
          if (that.isAdmin) {
            that.uploadLoading = false; // 关闭上传loading
            that.addDialogVisible = true;
          }

          if (that.isZxBrand) {
            console.log('品牌');
            that.uploadLoading = false; // 关闭上传loading
            that.addDialogVisible2 = true;
          }
          this.allocationForm = JSON.parse(JSON.stringify(row));
        } else {
          // 没参数是分配
          if (this.allocationForm.count == 0 || !this.allocationForm.count) {
            return this.$message.warning('请输入数量');
          }
          if (this.imageList.length <= 0 || !this.imageList[0].url) {
            return this.$message.warning('请上传凭证');
          }
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          /**
           * 提交分配
           * @param {Object} data
           * @param {String} data.merchantCode 商户编码
           * @param {String} data.payDocument 支付凭证
           * @param {Number} data.count 数量
           */

          if (that.isAdmin) {
            let data = {
              merchantName: this.allocationForm.merchantName,
              purchaseType: this.allocationForm.purchaseType,
              merchantCode: this.allocationForm.merchantCode,
              paymentDocument: that.imageList[0].url,
              number: this.allocationForm.count,
              type: this.typeList
            };

            orderApi
              .transfer(data)
              .then((res) => {
                // console.log(res.data);
                that.cancelSend(); // 关闭分配弹窗
                that.cancelSend2(); // 关闭分配弹窗
                that.fetchData(); // 刷新数据
                loading.close(); //关闭加载中
                this.typeList = [];
                this.allocationForm.purchaseType = 1;
              })
              .catch((err) => {
                this.imageList = []; // 清空图片
                this.typeList = [];
                loading.close();
              });
          } else {
            orderApi
              .distribution({ merchantCode: this.allocationForm.merchantCode, payDocument: that.imageList[0].url, count: this.allocationForm.count })
              .then((res) => {
                // console.log(res.data);
                that.cancelSend(); // 关闭分配弹窗
                that.cancelSend2(); // 关闭分配弹窗
                that.fetchData(); // 刷新数据
                loading.close(); //关闭加载中
              })
              .catch((err) => {
                this.imageList = []; // 清空图片
                loading.close();
              });
          }
        }
      },
      // 取消分配
      cancelSend() {
        let that = this;
        this.imageList = []; // 清空图片
        this.$refs.allocationForm.resetFields(); // 重置表单
        that.addDialogVisible = false; // 关闭弹窗
        this.allocationForm.purchaseType = 2;
      },
      cancelSend2() {
        let that = this;
        this.typeList = [];
        this.allocationForm.purchaseType = 2;
        this.imageList = []; // 清空图片
        this.$refs.allocationForm.resetFields(); // 重置表单
        that.addDialogVisible2 = false; // 关闭弹窗
      },
      // // 打开分配
      // submitSend(row) {
      //   if (this.systemNumber <= 0) {
      //     return this.$message.warning('数量不足，请先采购数量');
      //   }
      //   this.addDialogVisible = true;
      //   this.allocationForm = JSON.parse(JSON.stringify(row));
      // },
      // 上传图片数量超限
      justPictureNum(file, fileList) {
        this.$message.warning(`当前限制选择1个文件`);
      },
      // 删除图片
      handleRemoveDetailContract(file, fileList) {
        const that = this;
        that.imageList = fileList;
      },
      // 上传图片预览
      handlePictureCardPreviewContract(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },
      // 上传图片
      uploadDetailHttpContract({ file }) {
        this.uploadLoading = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                that.imageList.push({
                  uid: file.uid,
                  url: url
                });
                that.$nextTick(() => {
                  that.uploadLoading = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
              that.uploadLoading = false;
            });
        });
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/png' || file.type === 'image/jpeg';
        const isLt2M = file.size / 1024 / 1024 < 10;

        if (!isJPG) {
          this.$message.error('上传图片只能是 jpg/jpeg/png 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 10MB!');
        }
        return isJPG && isLt2M;
      },
      // 查看图片
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.imageUrl;
        this.dialogVisible = true;
      },
      // 获取数据
      fetchData() {
        const that = this;
        // 设置查询参数
        this.dataQuery.queryRoleTag = this.activeName == 1 ? 'Operations' : 'zxBrand';
        that.tableLoading = true;

        orderApi.queryList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          this.tableData = res.data.data;
          this.tableLoading = false;
          // 设置分页
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, Number(res.data[name])));
        });
        // 品牌需要查询剩余学习系统数量
        if (this.isZxBrand) {
          // 查询剩余学习系统数量
          store.dispatch('getSystem');
        }
      },
      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        this.regTime = '';
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      handleDetailsSizeChange(val) {
        this.detailsDataPage.size = val;
        this.detailsDataPage.currentPage = 1;
        this.openDetails();
      },
      handleDetailsCurrentChange(val) {
        this.detailsDataPage.currentPage = val;
        this.openDetails();
      },
      // 切换tab
      handleClick(tab, event) {
        console.log(tab, event);
        console.log(this.activeName);
        this.rest();
        if (this.activeName == 1) {
          this.tableHeader = [
            { label: '负责人', prop: 'chargeName' },
            { label: '合伙人智能学习管理系统采购总数', prop: 'totalPurchase' },
            { label: '合伙人智能学习管理系统消耗总数', prop: 'totalConsumption' },
            { label: '合伙人智能学习管理系统剩余总数', prop: 'residualTotal' },
            { label: '最新采购时间', prop: 'latestPurchaseTime' }
          ];
        } else {
          this.tableHeader = [
            { label: '负责人', prop: 'chargeName' },
            { label: '全款俱乐部智能学习管理系统采购总数', prop: 'fullTotalPurchase' },
            { label: '全款俱乐部智能学习管理系统消耗总数', prop: 'fullTotalConsumption' },
            { label: '全款俱乐部智能学习管理系统剩余总数', prop: 'fullResidualTotal' },
            { label: '合伙人智能学习管理系统采购总数', prop: 'totalPurchase' },
            { label: '合伙人智能学习管理系统消耗总数', prop: 'totalConsumption' },
            { label: '合伙人智能学习管理系统剩余总数', prop: 'residualTotal' },
            { label: '最新采购时间', prop: 'latestPurchaseTime' }
          ];
        }
      },
      changeRadio(value) {
        this.allocationForm.purchaseType = value;
        console.log(`selected ${value}`);
        if (value == 3) {
          orderApi.depositLearningSysType().then((res) => {
            if (res.success) {
              this.showMOnneyType = true;
              // this.typeList = res.data.map((item, index) => ({
              //   id: index + 1,      // id 从 1 开始
              //   num: 0,         // num 从 0 开始
              //   deposit: item       // 保留原字符串
              // }));
              this.typeList = res.data.map((item, index) => {
                const deposit = parseInt(item.match(/定金: (\d+)元/)[1]);
                const systems = parseInt(item.match(/含(\d+)套/)[1]);

                return {
                  id: systems, // id 从 1 开始
                  num: 0, // num 从 0 开始
                  deposit: item
                };
              });
            }
          });
        } else {
          this.showMOnneyType = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .radio-container {
    display: flex;
    flex-flow: column nowrap;
    align-items: flex-start;
  }
  .type-container {
    padding-left: 5px;
    width: 100%;
    height: 40px;
    border: 1px solid #000;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 40px;
    margin-top: 10px;
  }
  // :deep.el-input-number {
  //   width: 80px;
  //   margin-top: 5px;
  //   margin-right: 5px;
  // }
  :deep(.el-upload--picture-card) {
    margin-bottom: 20px;
  }
  ::v-deep.el-tabs__item {
    font-size: 16px;
  }
  ::v-deep.el-dialog__wrapper {
    text-align: center;
    white-space: nowrap;
    overflow: auto;
    &:after {
      content: '';
      display: inline-block;
      vertical-align: middle;
      height: 100%;
    }
    .el-dialog {
      margin: 30px auto !important;
      display: inline-block;
      vertical-align: middle;
      text-align: center;
      white-space: normal;
    }
  }

  .card {
    background-color: #f4f4f5;
    color: #666;
    // background-color: #f4f4f5;
    // color: #909399;
    padding: 10px 15px;
    border-radius: 5px;
  }

  :deep(.el-form-item--small .el-form-item__label) {
    line-height: 32px;
    flex-shrink: 0;
  }
  ::v-deep .el-radio {
    display: block;
    margin: 10px 0;
  }
</style>
