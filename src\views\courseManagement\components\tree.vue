<template>
    <el-tree
      ref="elTree"
      :data="treeData"
      :props="defaultProps"
      :node-key="nodeKey"
      :expand-on-click-node="expandOnClickNode"
      :default-expand-all="defaultExpandAll"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <i class="el-icon-folder-opened"></i>
          <span>{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </template>
  
  <script>
  export default {
    name: 'TreeWithIcon',
    props: {
      treeData: {
        type: Array,
        required: true
      },
      defaultProps: {
        type: Object,
        default: () => ({
          children: 'children',
          label: 'label'
        })
      },
      nodeKey: {
        type: String,
        default: 'id'
      },
      expandOnClickNode: {
        type: Boolean,
        default: true
      },
      defaultExpandAll: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      handleNodeClick(data, node) {
        this.$emit('node-click', data, node)
      }
    }
  }
  </script>
  
  <style scoped>
  .custom-tree-node {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-right: 8px;
  }
  
  .custom-tree-node i {
    margin-right: 5px;
    font-size: 16px;
    color: #909399;
  }
  </style> 