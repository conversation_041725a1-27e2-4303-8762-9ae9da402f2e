// 语文超人-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/_chinese_reader',
  component: Layout,
  redirect: '/_chinese_reader',
  meta: {
    perm: 'm:chineseReader',
    title: '语文超人色彩阅读',
    icon: 'reader'
  },
  children: [
    {
      path: 'superReaderCourseManagement',
      component: () => import('@/views/chineseCourseManagement/superReaderCourseManagement.vue'),
      name: 'superReaderCourseManagement',
      meta: {
        perm: 'm:chineseReader:superReaderCourseManagement',
        title: '课程管理'
      }
    },
    {
      path: 'superReaderVideoManagement',
      component: () => import('@/views/chineseCourseManagement/superReaderVideoManagement.vue'),
      name: 'superReaderVideoManagement',
      meta: {
        perm: 'm:chineseReader:superReaderVideoManagement',
        title: '视频管理'
      }
    },
    {
      path: 'superReaderTestBaseManagement',
      component: () => import('@/views/chineseCourseManagement/superReaderTestBaseManagement.vue'),
      name: 'superReaderTestBaseManagement',
      meta: {
        perm: 'm:chineseReader:superReaderTestBaseManagement',
        title: '题库管理',
        noCache: true
      }
    },
    {
      path: 'questionStemMaterialManagement',
      component: () => import('@/views/chineseCourseManagement/components/questionStemMaterialManagement.vue'),
      name: 'questionStemMaterialManagement',
      meta: {
        perm: 'm:chineseReader:questionStemMaterialManagement',
        title: '题干材料管理'
      }
    },
    {
      path: 'addSuperReaderQuestion',
      hidden: true,
      component: () => import('@/views/chineseCourseManagement/components/addSuperReaderQuestion.vue'),
      name: 'addSuperReaderQuestion',
      meta: {
        perm: 'm:chineseReader:addSuperReaderQuestion',
        title: '新增/编辑题目'
      }
    },
    {
      path: 'superReaderKnowledgeManagement',
      hidden: false,
      component: _import('_chinese_reader/superReaderKnowledgeManagement/index'),
      name: 'superReaderKnowledgeManagement',
      meta: {
        perm: 'm:chineseReader:superReaderKnowledgeManagement',
        title: '知识点管理'
      }
    },
    {
      path: 'superReaderCourseTypeConfig',
      hidden: false,
      component: _import('_chinese_reader/superReaderCourseTypeConfig/index'),
      name: 'superReaderCourseTypeConfig',
      meta: {
        perm: 'm:chineseReader:superReaderCourseTypeConfig',
        title: '课程分类配置'
      }
    },
    {
      path: 'superReaderSpeech',
      hidden: true,
      component: () => import('@/views/chineseCourseManagement/components/superReaderSpeech.vue'),
      name: 'speech',
      meta: {
        perm: 'm:chineseReader:superReaderSpeech',
        title: '话术',
        icon: 'course'
      }
    },
    {
      path: 'addSuperReaderCourse',
      hidden: true,
      component: () => import('@/views/chineseCourseManagement/addSuperReaderCourse.vue'),
      name: 'addCourse',
      meta: {
        perm: 'm:chineseReader:addSuperReaderCourse',
        title: '新增/编辑课程',
        icon: 'course'
      }
    },
    {
      path: 'superReaderEditVideoNodes',
      component: () => import('@/views/chineseCourseManagement/editVideoNodes.vue'),
      name: 'editVideoNodes',
      hidden: true,
      meta: {
        perm: 'm:chineseReader:editVideoNodes',
        title: '编辑视频节点',
        icon: 'course'
      }
    },
    {
      path: 'superReaderAddVideo',
      hidden: true,
      component: () => import('@/views/chineseCourseManagement/addVideo.vue'),
      name: 'addVideo',
      meta: {
        perm: 'm:chineseReader:addVideo',
        title: '新增/编辑视频',
        icon: 'course'
      }
    }
  ]
};
