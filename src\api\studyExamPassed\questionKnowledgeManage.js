import request from '@/utils/request';

export default {
  /**
   * 获取课程大类（知识点体系 - 课程大类）
   */
  getCurriculumList(params) {
    return request({
      url: '/dyf/web/xkt/knowledge/curriculum',
      method: 'GET',
      params
    });
  },

  /**
   * 获取已绑定的课程大类（可能用于回显）
   */
  getBindCurriculumList(params) {
    return request({
      url: '/dyf/web/xkt/knowledge/bind/curriculum',
      method: 'GET',
      params
    });
  },

  /**
   * 根据课程大类获取树状知识点信息
   */
  getKnowledgeTreeList(params) {
    return request({
      url: '/dyf/web/xkt/knowledge/tree-list',
      method: 'GET',
      params
    });
  },

  /**
   * 根据题目id获取树状知识点信息
   */
  getQuestionTree(params) {
    return request({
      url: '/dyf/web/xkt/knowledge/question/tree',
      method: 'GET',
      params
    });
  },

  /**
   * 根据课程大类获取知识点扁平列表
   */
  getKnowledgeList(params) {
    return request({
      url: '/dyf/web/xkt/knowledge/list',
      method: 'GET',
      params
    });
  },

  /**
   * 新增知识点
   */
  addOrUpdateKnowledge(data) {
    return request({
      url: '/dyf/web/xkt/knowledge/addOrUpdate',
      method: 'POST',
      data
    });
  },

  // 批量更新知识点
  addAllOrUpdateKnowledge(data) {
    return request({
      url: '/dyf/web/xkt/knowledge/addAll',
      method: 'POST',
      data
    });
  },

  /**
   * 修改知识点排序
   */
  updateKnowledgeSort(data) {
    return request({
      url: '/dyf/web/xkt/knowledge/updateSort',
      method: 'POST',
      data
    });
  },

  /**
   * 删除知识点
   */
  deleteKnowledge(params) {
    return request({
      url: '/dyf/web/xkt/knowledge/delete',
      method: 'GET',
      params
    });
  },
  /**
   * 文件导入知识点
   */
  importKnowledgeFile(data, config = {}) {
    return request({
      url: '/dyf/web/xkt/knowledge/importFile',
      method: 'POST',
      data,
      ...config
    });
  },

  // 通过课程大类获取视频信息
  getXktVideoOptions(params) {
    return request({
      url: '/dyf/web/xktVideo/options',
      method: 'GET',
      params
    });
  },
  // 批量上传 文件导入展示明细
  importDetailFile(data, config = {}) {
    return request({
      url: '/dyf/web/xkt/question/import/detail',
      method: 'POST',
      data,
      ...config
    });
  },
  // 批量上传 文件导入-关联视频
  videoIdFile(data, config = {}) {
    return request({
      url: '/dyf/web/xkt/question/import/videoId',
      method: 'POST',
      data,
      ...config
    });
  },
  // 批量上传 文件导入-关联课程大类
  lessonFile(data, config = {}) {
    return request({
      url: '/dyf/web/xkt/question/import/curriculumId',
      method: 'POST',
      data,
      ...config
    });
  },
  //根据课程大类获取所有课程和视频
  getLVesson(params) {
    return request({
      url: '/dyf/web/xktCourse/all/video/curriculumId',
      method: 'GET',
      params
    });
  }
};
