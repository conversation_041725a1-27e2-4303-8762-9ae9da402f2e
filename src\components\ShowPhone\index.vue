<template>
  <div>
    <CustomDialog
      v-if="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :value.sync="dialogVisible"
      width="700px"
      :modal-append-to-body="false"
      title="解锁验证"
      align="center"
      style="font-weight: 700"
    >
      <div class="dialog-Show-title">为保障数据安全，解锁查看需要进行验证，请确认是否向您的登录账号({{ this.temp.mobile }})发送手机验证码？</div>
      <el-form :rules="rules" ref="dataForm" :model="temp">
        <el-form-item prop="smsCode" style="width: 50%; text-align: center">
          <div style="display: flex; justify-content: center">
            <el-input placeholder="请输入验证码" v-model="temp.smsCode" style="width: 85%"></el-input>
            <el-button @click.stop="sendSmg()" :disabled="disabledSmsClick" type="primary">{{ count }}</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-tip">温馨提示:如果您的登录号码收不到验证码，请联系渠道经理修改登录手机号。</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="phoneUnclock" size="medium">解锁</el-button>
        <el-button @click="closeDialog" size="medium" class="closeButton">取消</el-button>
      </div>
    </CustomDialog>
  </div>
</template>
<script>
  import auth from '@/api/auth';
  import Verify from '@/components/verifition/Verify';
  import forgotApi from '@/api/forgot';
  import studentApi from '@/api/studentList';
  import userApi from '@/api/user';
  import CustomDialog from '@/components/customDialog';
  export default {
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      },
      phoneShowStatus: {
        type: String,
        default: false
      }
    },
    components: {
      Verify,
      CustomDialog
    },
    watch: {
      dialogVisible(val) {
        if (val) {
          this.temp.smsCode = '';
          clearInterval(this.timer);
          this.timer = null;
          this.disabledSmsClick = false;
          this.count = '获取验证码';
          this.$nextTick(() => {
            this.$refs['dataForm'].clearValidate();
          });
          if (!this.temp.mobile) {
            this.getPhoneNum(); // 如果需要，每次打开重新获取手机号
          }
        }
      }
    },

    data() {
      return {
        temp: {
          mobile: '',
          smsCode: '',
          source: 'admin'
        },
        rules: {
          smsCode: [
            { required: true, message: '请输入验证码', trigger: 'blur' },
            { message: '验证码不得小于6位', trigger: 'blur' }
          ]
        },
        count: '获取验证码',
        timer: null,
        disabledSmsClick: false
      };
    },
    created() {
      this.getPhoneNum();
    },
    methods: {
      // 获取当前登录用户手机号
      getPhoneNum() {
        userApi.getPhone().then((res) => {
          this.temp.mobile = res.data.phone;
          // this.temp.mobile = '15256228190';
        });
      },

      sendSmg() {
        forgotApi.sendPhoneSmg(this.temp.mobile).then((res) => {
          this.$message.success('短信验证码发送成功，请注意查收');
          var num = 60;
          this.disabledSmsClick = true;
          if (!this.timer) {
            this.timer = setInterval(() => {
              if (num > 0) {
                num--;
                this.count = num + 's';
              } else {
                clearInterval(this.timer);
                this.timer = null;
                this.count = '重新获取验证码';
                this.disabledSmsClick = false;
              }
            }, 1000);
          }
        });
      },
      phoneUnclock() {
        var patrn = /^\d{6}$/;
        if (!patrn.test(this.temp.smsCode)) {
          this.$message.error('验证码为6位数字');
          return false;
        }
        studentApi.getShowPhone(this.temp.smsCode, this.temp.mobile).then((res) => {
          console.log(res);
          if (res.code == 20000) {
            this.$message.success('操作成功!列表下所有学员手机号数据均完整展示');
            this.$emit('update:dialogVisible', false);
            this.$emit('update:phoneShowStatus', '1');
            localStorage.setItem('phoneShowStatus', '1');
            this.$emit('fetchData', '1'); // 触发自定义事件
          } else {
            this.$message.error(res.msg);
          }
        });
      },
      closeDialog() {
        this.$emit('update:dialogVisible', false);
      }
    }
  };
</script>
<style lang="scss" scoped>
  .dialog-Show-title {
    color: #000;
    margin-bottom: 40px;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    text-align: right;
    padding-top: 10px;
    border-top: 1px solid #e8e8e8;
  }
  .dialog-tip {
    text-align: center;
    color: red;
    font-size: 12px;
    margin-top: 40px;
  }
  ::v-deep .el-icon-close:before {
    content: ' ';
  }
</style>
