// 渠道管理员列表-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/channel',
  component: Layout,
  redirect: '/channel',
  meta: {
    perm: 'm:channel',
    title: '渠道管理',
    icon: 'channel'
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/channel/channelManager'),
      name: 'channelManager',
      meta: {
        perm: 'm:channel:channelManager',
        title: '渠道管理员列表',
        icon: 'channel'
      }
    }
  ]
};
