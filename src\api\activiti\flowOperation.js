import request from '@/utils/request'

export default {
  //分页
  list(data) {
    return request({
      url: '/activiti/flowOperation/list',
      method: 'GET',
      params: data
    })
  },
  stopProcessInstance(data) {
    return request({
      url: '/activiti/flowOperation/stopProcessInstance',
      method: 'POST',
      params: data
    })
  },
  deleteProcessInstance(data) {
    return request({
      url: '/activiti/flowOperation/deleteProcessInstance',
      method: 'DELETE',
      params: data
    })
  },
  viewProcessBpmn(data) {
    return request({
      url: '/activiti/flowOperation/viewProcessBpmn',
      method: 'GET',
      params: data
    })
  },
  viewHighlightFlowData(data) {
    return request({
      url: '/activiti/flowOperation/viewHighlightFlowData',
      method: 'GET',
      params: data
    })
  },
  viewInitialTaskInfo(data) {
    return request({
      url: '/activiti/flowOperation/viewInitialTaskInfo',
      method: 'GET',
      params: data
    })
  },
  startOnly(data) {
    return request({
      url: '/activiti/flowOperation/startOnly',
      method: 'POST',
      params: data
    })
  },
  listRuntimeTask(data) {
    return request({
      url: '/activiti/flowOperation/listRuntimeTask',
      method: 'POST',
      params: data
    })
  },
  viewRuntimeTaskInfo(data) {
    return request({
      url: '/activiti/flowOperation/viewRuntimeTaskInfo',
      method: 'GET',
      params: data
    })
  },
  listHistoricProcessInstance(data) {
    return request({
      url: '/activiti/flowOperation/listHistoricProcessInstance',
      method: 'POST',
      params: data
    })
  },
  viewInitialHistoricTaskInfo(data) {
    return request({
      url: '/activiti/flowOperation/viewInitialHistoricTaskInfo',
      method: 'GET',
      params: data
    })
  },
  listHistoricTask(data) {
    return request({
      url: '/activiti/flowOperation/listHistoricTask',
      method: 'POST',
      params: data
    })
  },
  viewHistoricTaskInfo(data) {
    return request({
      url: '/activiti/flowOperation/viewHistoricTaskInfo',
      method: 'GET',
      params: data
    })
  },
  listFlowTaskComment(data){
    return request({
      url: '/activiti/flowOperation/listFlowTaskComment',
      method: 'GET',
      params: data
    })
  }

}
