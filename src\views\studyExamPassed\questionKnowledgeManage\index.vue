<template>
  <div class="question-knowledge-manage">
    <el-form :inline="true" :model="query" class="filter-form">
      <el-form-item label="课程大类：">
        <el-select v-model="query.curriculumId" placeholder="请选择" clearable style="width: 220px">
          <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchList">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border v-loading="tableLoading" style="width: 100%" :header-cell-style="{ fontWeight: '600' }">
      <el-table-column label="序号" width="80" align="center">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column prop="name" label="课程大类" align="center" min-width="260" />
      <el-table-column label="操作" min-width="300" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-setting" @click="handleKnowledge(scope.row)">知识点管理</el-button>
          <el-button type="success" size="mini" icon="el-icon-upload" @click="handleUploadPre(scope.row)">上传学前题库</el-button>
          <el-button type="warning" size="mini" icon="el-icon-upload" @click="handleBatchUploadPre(scope.row)">批量上传学前题库</el-button>
          <el-button type="info" size="mini" icon="el-icon-setting" v-if="scope.row.isMark == 1" @click="handleQuestionManage(scope.row)">题目管理</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pager">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pager.total"
        :page-size="pager.pageSize"
        :current-page="pager.pageNum"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
  import knowledgeApi from '@/api/studyExamPassed/questionKnowledgeManage';
  import { clearRouteParamCache } from '@/utils/routeParamCache';

  export default {
    name: 'questionKnowledgeManage',
    data() {
      return {
        query: { curriculumId: '' },
        categoryOptions: [],
        tableData: [],
        allData: [],
        tableLoading: false,
        pager: { pageNum: 1, pageSize: 10, total: 0 }
      };
    },
    created() {
      this.fetchList();
      this.initCourseCategoryList();
      this.$bus.$on('updateKnowledgeTable', () => {
        this.fetchList();
      });
    },
    mounted() {},
    methods: {
      // 获取课程大类
      async initCourseCategoryList() {
        try {
          const res = await knowledgeApi.getCurriculumList({});
          const list = Array.isArray(res.data) ? res.data : [];
          this.categoryOptions = list.map((item) => ({ label: item.name || item.enName || '-', value: item.id }));
        } catch (e) {
          console.error(e);
        }
      },

      // 获取已绑定课程列表（表格数据）
      async fetchList() {
        this.tableLoading = true;
        try {
          const params = { curriculumId: this.query.curriculumId };
          const res = await knowledgeApi.getCurriculumList(this.query.curriculumId ? params : {});
          const dataList = Array.isArray(res.data) ? res.data : res.data?.list || [];
          this.allData = dataList.map((i) => ({
            id: i.id,
            name: i.name || i.enName,
            value: i.id,
            uploadPreLoading: false,
            ...i
          }));
          this.pager.total = this.allData.length;
          this.sliceTable();
        } catch (e) {
        } finally {
          this.tableLoading = false;
        }
      },
      // 本地分页切片
      sliceTable() {
        const start = (this.pager.pageNum - 1) * this.pager.pageSize;
        this.tableData = this.allData.slice(start, start + this.pager.pageSize);
      },
      resetQuery() {
        this.query = { curriculumId: '' };
        this.pager.pageNum = 1;
        this.fetchList();
      },
      // 知识点管理
      handleKnowledge(row) {
        this.$router.push({ path: '/studyExamPassed/knowledgeManage', query: { curriculumId: row.id } });
      },
      // 上传学前题库
      async handleUploadPre(row) {
        console.log('tableData', this.tableData);
        try {
          await sessionStorage.setItem(
            'KnowledgeAddQuestionContext',
            JSON.stringify({
              list: this.tableData,
              currentId: row.id,
              ts: Date.now()
            })
          );
        } catch (e) {
        } finally {
          const target = {
            path: '/studyExamPassed/editKnowledge',
            name: 'editKnowledge',
            meta: {}
          };
          this.$store.dispatch('delVisitedViews', target);
          clearRouteParamCache('knowledgeQueryInfo');
          this.$router.push({ path: '/studyExamPassed/addKnowledge', query: { title: '新增题目', type: 'add', id: row.id } });
        }
      },
      // 批量上传学前题库
      handleBatchUploadPre(row) {
        this.$router.push({ path: '/studyExamPassed/AddBatchKnowledge', query: { category: row.value } });
      },
      // 题目管理
      handleQuestionManage(row) {
        this.$router.push({ path: '/studyExamPassed/knowledgeQuestionManagement', query: { curriculumId: row.id } });
      },
      handleSizeChange(val) {
        this.pager.pageSize = val;
        this.sliceTable();
      },
      handleCurrentChange(val) {
        this.pager.pageNum = val;
        this.sliceTable();
      }
    }
  };
</script>

<style lang="less" scoped>
  .question-knowledge-manage {
    padding: 20px;
    .filter-form {
      margin-bottom: 10px;
    }
    .pager {
      text-align: right;
      margin-top: 15px;
    }
    .tips-list {
      padding-left: 18px;
      margin: 0;
      li {
        line-height: 18px;
        font-size: 12px;
        margin-bottom: 6px;
      }
    }
  }
</style>
