import { shallowMount, mount } from '@vue/test-utils';
import EditClubAndBrandDialog from '@/views/merchantManagement/components/EditClubAndBrandDialog.vue';
import dealerListApi from '@/api/operationsList';
import schoolApi from '@/api/schoolList';

// Mock API调用
jest.mock('@/api/operationsList');
jest.mock('@/api/schoolList');

// Mock Element UI组件
const mockElementComponents = {
  'el-dialog': {
    template: '<div><slot></slot></div>',
    props: ['title', 'center', 'visible', 'width', 'close-on-press-escape', 'close-on-click-modal']
  },
  'el-form': {
    template: '<div><slot></slot></div>',
    props: ['model', 'label-position', 'label-width', 'rules']
  },
  'el-form-item': {
    template: '<div><slot></slot></div>',
    props: ['label', 'prop']
  },
  'el-input': {
    template: '<input />',
    props: ['disabled', 'value', 'placeholder', 'maxlength']
  },
  'el-button': {
    template: '<button><slot></slot></button>',
    props: ['type', 'loading']
  }
};

describe('EditClubAndBrandDialog.vue', () => {
  let wrapper;

  const mockFormData = {
    merchantName: '测试门店',
    brandName: '原品牌',
    operationsName: '原俱乐部',
    refereeName: '原推广大使',
    refereeCode: '001',
    newChannelCode: '002',
    newRefereeName: '新推广大使',
    newOperationsName: '新俱乐部',
    newBrandCode: '003'
  };

  beforeEach(() => {
    wrapper = shallowMount(EditClubAndBrandDialog, {
      propsData: {
        isShowDialog: true
      },
      stubs: mockElementComponents
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  it('renders correctly', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('computes dialogVisible correctly', () => {
    expect(wrapper.vm.dialogVisible).toBe(true);
  });

  it('sets data correctly', () => {
    wrapper.vm.setData(mockFormData);
    expect(wrapper.vm.form.merchantName).toBe('测试门店');
    expect(wrapper.vm.form.newBrandCode).toBe('003');
  });

  it('resets form data', () => {
    wrapper.vm.setData(mockFormData);
    wrapper.vm.reset();
    expect(wrapper.vm.form.merchantName).toBe('');
    expect(wrapper.vm.form.newBrandCode).toBe('');
  });

  describe('API calls', () => {
    it('handles brand code blur with successful response', async () => {
      const mockResponse = { code: 20000, data: '新品牌名称' };
      schoolApi.getMerchantNameApi.mockResolvedValue(mockResponse);

      wrapper.vm.form.newBrandCode = '003';
      await wrapper.vm.handleRefereeCodeBlur();

      expect(wrapper.vm.form.newOperationsName).toBe('新品牌名称');
    });

    it('handles brand code blur with error response', async () => {
      schoolApi.getMerchantNameApi.mockRejectedValue(new Error('API Error'));

      wrapper.vm.form.newBrandCode = '003';
      await wrapper.vm.handleRefereeCodeBlur();

      expect(wrapper.vm.form.newOperationsName).toBe('');
    });

    it('handles channel code blur with successful response', async () => {
      const mockResponse = { code: 20000, data: '新渠道名称' };
      schoolApi.getMerchantNameApi.mockResolvedValue(mockResponse);

      wrapper.vm.form.newChannelCode = '002';
      await wrapper.vm.handleChannelCodeBlur();

      expect(wrapper.vm.form.newRefereeName).toBe('新渠道名称');
    });

    it('handles channel code blur with error response', async () => {
      schoolApi.getMerchantNameApi.mockRejectedValue(new Error('API Error'));

      wrapper.vm.form.newChannelCode = '002';
      await wrapper.vm.handleChannelCodeBlur();

      expect(wrapper.vm.form.newRefereeName).toBe('');
    });
  });

  describe('Form submission', () => {
    it('submits form successfully', async () => {
      const mockResponse = { code: 20000 };
      dealerListApi.confirmChangeBrand.mockResolvedValue(mockResponse);

      wrapper.vm.setData(mockFormData);
      await wrapper.vm.handleSubmit();

      expect(wrapper.vm.isSubmitLoading).toBe(true);
      expect(wrapper.vm.dialogReasonVisible).toBe(false);
    });

    it('handles submit error', async () => {
      dealerListApi.confirmChangeBrand.mockRejectedValue(new Error('Submit Error'));

      await wrapper.vm.handleSubmit();

      expect(wrapper.vm.isSubmitLoading).toBe(false);
      expect(wrapper.vm.dialogReasonVisible).toBe(false);
    });
  });

  describe('Form validation', () => {
    let fullWrapper;

    beforeEach(() => {
      fullWrapper = mount(EditClubAndBrandDialog, {
        propsData: {
          isShowDialog: true
        },
        stubs: mockElementComponents
      });
    });

    afterEach(() => {
      fullWrapper.destroy();
    });

    it('validates form and shows reason dialog when cannot change', async () => {
      const mockResponse = {
        data: {
          canChange: false,
          reasons: ['原因1', '原因2'],
          step: 2
        }
      };
      dealerListApi.checkNewBrandAndChanel.mockResolvedValue(mockResponse);

      fullWrapper.vm.setData({
        ...mockFormData,
        merchantCode: 'M001'
      });

      // Mock form validation
      fullWrapper.vm.$refs.channerManagerRef = {
        validate: (callback) => callback(true)
      };

      await fullWrapper.vm.handleConfirm();

      expect(fullWrapper.vm.dialogReasonVisible).toBe(true);
      expect(fullWrapper.vm.reasonContent).toEqual(['原因1', '原因2']);
      expect(fullWrapper.vm.workStep).toBe(2);
      expect(fullWrapper.vm.showTitleStatus).toBe(3);
    });

    it('validates form and shows success dialog when can change', async () => {
      const mockResponse = {
        data: {
          canChange: true
        }
      };
      dealerListApi.checkNewBrandAndChanel.mockResolvedValue(mockResponse);

      fullWrapper.vm.setData({
        ...mockFormData,
        merchantCode: 'M001'
      });

      // Mock form validation
      fullWrapper.vm.$refs.channerManagerRef = {
        validate: (callback) => callback(true)
      };

      await fullWrapper.vm.handleConfirm();

      expect(fullWrapper.vm.dialogReasonVisible).toBe(true);
      expect(fullWrapper.vm.workStep).toBe(4);
      expect(fullWrapper.vm.reasonType).toBe(2);
    });

    it('prevents submission when loading', async () => {
      fullWrapper.setData({ loading: true });

      await fullWrapper.vm.handleConfirm();

      expect(dealerListApi.checkNewBrandAndChanel).not.toHaveBeenCalled();
    });
  });

  describe('Dialog closing', () => {
    it('handles outer close with closeDialog value', () => {
      const spy = jest.spyOn(wrapper.vm, 'reset');
      wrapper.vm.handleOuterClose('closeDialog');

      expect(wrapper.vm.dialogReasonVisible).toBe(false);
      expect(spy).not.toHaveBeenCalled();
    });

    it('handles outer close with other values', () => {
      const spy = jest.spyOn(wrapper.vm, 'reset');
      wrapper.vm.handleOuterClose();

      expect(spy).toHaveBeenCalled();
    });
  });
});
