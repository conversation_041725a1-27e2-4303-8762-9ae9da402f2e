<template>
  <el-dialog :width="dialogWidth" :visible.sync="dialogVisible" :show-close="showClose" :close-on-click-modal="closeOnClickModal" @close="handleClose">
    <template slot="title">
      <div
        class="custom-title"
        :style="{
          'background-color': titleBackgroundColor,
          'font-size': titleFontSize,
          color: titleColor,
          height: title ? '68px' : '54px'
        }"
      >
        <span class="title-text">{{ title }}</span>
        <span
          class="title-close"
          @click="handleClose"
          :style="{
            color: closeButtonColor
          }"
        >
          <i class="el-icon-close"></i>
        </span>
      </div>
    </template>

    <slot></slot>

    <div class="custom-footer" slot="footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    name: 'CustomDialog',
    props: {
      // 控制弹窗显示
      value: {
        type: Boolean,
        default: false
      },
      // 标题内容
      title: {
        type: String,
        default: ''
      },
      // 标题背景颜色
      titleBackgroundColor: {
        type: String,
        default: '#fff'
      },
      // 标题字体大小
      titleFontSize: {
        type: String,
        default: '20px'
      },
      // 标题文字颜色
      titleColor: {
        type: String,
        default: '#333'
      },
      // 关闭按钮颜色
      closeButtonColor: {
        type: String,
        default: '#333'
      },
      // 是否显示关闭图标
      showClose: {
        type: Boolean,
        default: false
      },
      // 是否可以通过点击 modal 关闭 Dialog
      closeOnClickModal: {
        type: Boolean,
        default: false
      },

      // 固定宽度，如果设置则忽略响应式宽度
      width: {
        type: String,
        default: ''
      },
      borderRadius: {
        type: String,
        default: '8px'
      }
    },
    data() {
      return {
        screenWidth: window.innerWidth
      };
    },
    computed: {
      // 对话框显示状态
      dialogVisible: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      // 根据屏幕宽度计算对话框宽度
      dialogWidth() {
        if (this.width) {
          return this.width;
        }

        if (this.screenWidth > 1300) {
          return '60%';
        } else if (this.screenWidth > 768) {
          return '70%';
        } else {
          return '90%';
        }
      }
    },
    mounted() {
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
      // 移除事件监听
      window.removeEventListener('resize', this.handleResize);
    },
    methods: {
      // 处理窗口大小变化
      handleResize() {
        this.screenWidth = window.innerWidth;
      },
      // 处理关闭事件
      handleClose() {
        this.dialogVisible = false;
        this.$emit('close');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .custom-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
    font-weight: 500;
    height: 68px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
  .title-close {
    cursor: pointer;
    font-weight: bold;
    font-size: 30px;
    transition: opacity 0.3s;

    &:hover {
      opacity: 0.7;
    }
  }

  ::v-deep .el-dialog {
    border-radius: 8px;
  }
  ::v-deep .el-dialog__header {
    padding: 0;
  }
  ::v-deep .el-dialog__body {
    padding: 10px 30px;
  }
  ::v-deep .el-dialog__footer {
    border-top: 1px solid #e8e8e8;
    padding: 20px 20px;
    margin: 0 auto;
    width: 94%;
  }
</style>
