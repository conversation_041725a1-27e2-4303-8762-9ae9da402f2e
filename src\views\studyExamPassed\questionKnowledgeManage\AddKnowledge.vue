<template>
  <div
    class="add-question-page"
    v-loading="pageLoading"
    element-loading-text="加载中..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255,255,255,0.9)"
  >
    <!-- 页面标题 -->
    <div class="page-title">
      <h2>{{ title }}</h2>
    </div>

    <!-- 主要内容区域 -->
    <div class="main">
      <!-- 左侧表单区域 -->
      <div class="left-form-container">
        <el-form ref="selectForm" :rules="rules" :model="queryParam" label-width="120px">
          <el-form-item label="课程大类：" prop="curriculumId">
            <template v-if="multipleCurriculums">
              <el-select v-model="queryParam.curriculumIds" multiple disabled placeholder="请选择">
                <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
            <template v-else>
              <el-select v-model="queryParam.curriculumId" disabled placeholder="请选择">
                <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-form-item>
          <el-form-item label="检测类型：" prop="detectionType">
            <el-select v-model="queryParam.detectionType" disabled placeholder="请选择">
              <el-option label="学前检测" value="1"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="学科：" prop="subjectId">
            <el-select v-model="queryParam.subjectId" disabled placeholder="请选择">
              <el-option label="高中/物理" value="1"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="题目类型：" prop="questionType">
            <el-select v-model="queryParam.questionType" :disabled="type === 'edit'" @change="questionTypeChange" placeholder="请选择">
              <el-option label="单选题" value="1"></el-option>
              <el-option label="多选题" value="2"></el-option>
              <el-option label="填空题(系统作答)" value="3"></el-option>
              <el-option label="填空题(拍照上传)" value="4"></el-option>
              <el-option label="主观题" value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关联知识点：" prop="knowledgePoint">
            <div style="width: 222px" @click="handlePointClick">
              <el-tag v-if="!selectedCount" type="info" class="tag-class">未关联知识点</el-tag>
              <el-tag v-else class="tag-class">已关联{{ selectedCount }}个知识点</el-tag>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content-container">
        <el-form ref="tableForm" :rules="rules" :model="queryParam" label-width="120px">
          <el-form-item label="题干：" prop="questionText">
            <el-input
              v-if="queryParam.questionType === '3'"
              type="textarea"
              :maxlength="5000"
              :rows="6"
              show-word-limit
              v-model="queryParam.questionText"
              placeholder="请输入题干内容，题目填空内容用（）表示"
            ></el-input>
            <el-input v-else type="textarea" :maxlength="1000" :rows="6" show-word-limit v-model="queryParam.questionText" placeholder="请输入题干内容"></el-input>
          </el-form-item>
          <el-form-item label="题目分值：" prop="score">
            <el-input-number v-model="queryParam.score" :precision="0" :min="1" :max="100" :controls="false" placeholder="请输入数字整数" style="width: 30%"></el-input-number>
          </el-form-item>
          <!-- 单选题 -->
          <el-form-item label="选项：" prop="questionOptions" v-if="queryParam.questionType === '1' || queryParam.questionType === '2'">
            <div v-if="queryParam.questionType === '1' && optionsRadioTableData.length > 0">
              <el-table :data="optionsRadioTableData" border style="width: 100%">
                <el-table-column v-for="(item, index) in optionsTableProp1" :key="item.prop" :prop="item.prop" :label="item.label" :width="item.width" align="center">
                  <template slot-scope="scope">
                    <span v-if="item.prop == 'choiceOption'">
                      <el-radio v-model="selectedOption" :label="scope.row.checkId">{{ scope.row.choiceOption }}</el-radio>
                    </span>

                    <span v-else-if="item.prop == 'content'">
                      <el-input type="textarea" :rows="1" autosize v-model="scope.row.content" show-word-limit placeholder="请输入选项内容" maxlength="500"></el-input>
                    </span>
                    <span v-else-if="item.prop == 'optionImage'">
                      <OneImageUploadSimple v-model="scope.row.optionImage" :imgSize="20 * 1024 * 1024" :imgType="true" />
                    </span>
                  </template>
                </el-table-column>
                <el-table-column align="center" width="100" label="操作">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="removeOption(scope.$index, scope.row)" type="text" style="color: #ff4949">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 多选题 -->
            <div v-else-if="queryParam.questionType === '2' && optionsChecksTableData.length > 0">
              <el-checkbox-group v-model="checkBoxOptions">
                <el-table :data="optionsChecksTableData" border style="width: 100%">
                  <el-table-column v-for="(item, index) in optionsTableProp2" :key="item.prop" :prop="item.prop" :label="item.label" :width="item.width" align="center">
                    <template slot-scope="scope">
                      <span v-if="item.prop == 'choiceOption'">
                        <el-checkbox :label="scope.row.choiceOption"></el-checkbox>
                      </span>
                      <span v-else-if="item.prop == 'content'">
                        <el-input type="textarea" :rows="1" autosize v-model="scope.row.content" show-word-limit placeholder="请输入选项内容" maxlength="500"></el-input>
                      </span>
                      <span v-else-if="item.prop == 'optionImage'">
                        <OneImageUploadSimple v-model="scope.row.optionImage" :imgSize="20 * 1024 * 1024" :imgType="true" />
                      </span>
                    </template>
                  </el-table-column>

                  <el-table-column align="center" width="100" label="操作">
                    <template slot-scope="scope">
                      <el-button size="mini" @click="removeOption(scope.$index, scope.row)" type="text" style="color: #ff4949">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-checkbox-group>
            </div>
            <div
              :class="
                (queryParam.questionType === '1' && optionsRadioTableData.length > 0) || (queryParam.questionType === '2' && optionsChecksTableData.length > 0)
                  ? 'add-option-btn'
                  : ''
              "
            >
              <el-button type="primary" plain @click="addOption" :disabled="optionDisabled" style="width: 30%">+ 添加选项</el-button>
            </div>
          </el-form-item>
          <el-form-item v-if="queryParam.questionType === '1' || queryParam.questionType === '2'" label="" prop="redTitle">
            <div class="option-hint">选项内容必填文字或图片，选项图片支持格式jpg、jpeg、png，大小限制20M以内，图片的最佳长宽比1:1或者1:1.5</div>
          </el-form-item>

          <!-- 填空or主观题 -->
          <el-form-item label="填空：" prop="questionFill" v-if="queryParam.questionType === '3'">
            <el-table :data="optionsFillTableData" border style="width: 100%" v-if="optionsFillTableData.length > 0">
              <el-table-column v-for="(item, index) in optionsTableProp3" :key="item.prop" :prop="item.prop" :label="item.label" :width="item.width" align="center">
                <template slot-scope="scope">
                  <span v-if="item.prop == 'questionLocation'">
                    {{ scope.$index + 1 }}
                  </span>
                  <span v-else-if="item.prop == 'content'">
                    <el-input type="textarea" :rows="1" autosize v-model="scope.row.content" show-word-limit placeholder="请输入填空答案" maxlength="100"></el-input>
                  </span>
                  <span v-else-if="item.prop == 'score'">
                    <el-input v-model="scope.row.score" placeholder="请输入分值"></el-input>
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="center" width="100" label="操作">
                <template slot-scope="scope">
                  <el-button size="mini" @click="removeOption(scope.$index, scope.row)" type="text" style="color: #ff4949">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <div :class="optionsFillTableData.length > 0 ? 'add-option-btn' : ''">
              <el-button type="primary" plain @click="addOption" style="width: 30%">+ 添加填空</el-button>
            </div>
          </el-form-item>

          <el-form-item label="题目小问：" prop="questionProblem" v-if="queryParam.questionType === '4' || queryParam.questionType === '5'">
            <div class="problem-main" v-if="optionsProblemTableData.length > 0">
              <div v-for="(item, index) in optionsProblemTableData" :key="index" style="margin-bottom: 10px">
                <div class="problem-title">
                  <div>
                    <span style="color: #ff4949">*</span>
                    <span>问题{{ index + 1 }}</span>
                  </div>
                  <i @click="removeOption(index, item)" class="el-icon-delete"></i>
                </div>
                <el-input type="textarea" :maxlength="500" :rows="5" show-word-limit v-model="item.subTitle" placeholder="请输入题目小问内容"></el-input>
                <el-input
                  v-model="item.score"
                  show-word-limit
                  placeholder="请输入小题分值，须填数字整数，所有小题分数之和须等于题目总分"
                  maxlength="10"
                  style="margin-top: 10px"
                ></el-input>
              </div>
            </div>

            <div :class="optionsProblemTableData.length > 0 ? 'add-option-btn' : ''">
              <el-button type="primary" plain @click="addOption" :disabled="problemDisabled" style="width: 30%">+ 添加小问</el-button>
            </div>
          </el-form-item>
          <el-form-item label="配图：" prop="caption">
            <MyUpload
              @handleSuccess="handleMoreSuccess"
              @handleRemove="handleMoreRemove"
              :limit="5"
              :imgSize="20 * 1024 * 1024"
              :fullUrl="true"
              :file-list="supportFileList"
              :showTip="false"
              :tipText="'jpg、jpeg、png'"
            />
            <div class="option-hint">仅支持上传5张图，支持格式jpg、jpeg、png，大小限制20M以内，图片的最佳长宽比1:1或者1:1.5</div>
          </el-form-item>
          <el-form-item label="解析：" prop="analysis">
            <el-input type="textarea" :maxlength="1000" :rows="6" show-word-limit v-model="queryParam.analysis" placeholder="请输入解析内容"></el-input>
          </el-form-item>
          <el-form-item label="解析图片：" prop="analysisImage">
            <MyUpload
              @handleSuccess="handleOneSuccess"
              @handleRemove="handleOneRemove"
              :limit="5"
              :imgSize="20 * 1024 * 1024"
              :fullUrl="true"
              :file-list="parsedFileList"
              :showTip="false"
              :tipText="'jpg、jpeg、png'"
            />
            <div class="option-hint">仅支持上传5张图，jpg、jpeg、png，大小限制20M以内，图片的最佳长宽比1:1或者1:1.5</div>
          </el-form-item>

          <el-form-item label="">
            <div class="option-hint" v-show="submitAllFormFlag">您有未填的必填项，无法保存</div>
            <el-button @click="cancel">取消</el-button>
            <el-button v-if="type === 'edit'" type="primary" :loading="saving" :disabled="saving" @click="submitEditForm">保存修改</el-button>
            <template v-else>
              <el-button type="primary" plain @click="priviewParse">预览</el-button>
              <el-button type="primary" :loading="saving && saveMode === 'add'" :disabled="saving" @click="submitForm">保存</el-button>
              <el-button type="primary" :loading="saving && saveMode === 'addContinue'" :disabled="saving" @click="submitFormContinue">保存并继续添加</el-button>
            </template>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <ParseDialog ref="parseDialog"></ParseDialog>
    <PointDialog ref="pointDialog" @confirm="confirmPoint"></PointDialog>
  </div>
</template>

<script>
  import ParseDialog from './components/ParseDialog.vue';
  import PointDialog from './components/PointDialog.vue';
  import MyUpload from '@/components/Upload/MyUpload.vue';
  import OneImageUploadSimple from '@/components/Upload/OneImageUploadSimple';
  import courseApi from '@/api/studyExamPassed/course';
  import knowledgeApi from '@/api/studyExamPassed/questionKnowledgeManage';
  import videoConfigApi from '@/api/studyExamPassed/videoConfig';
  import { mapGetters } from 'vuex';
  import { restoreMissingRouteQuery, persistRouteParams } from '@/utils/routeParamCache';

  export default {
    name: 'AddQuestionPage',
    components: { ParseDialog, PointDialog, OneImageUploadSimple, MyUpload },
    watch: {
      optionsProblemTableData: {
        handler(newVal) {
          this.problemDisabled = newVal.length >= 10 ? true : false;
        }
      },
      selectedOption(newVal) {
        // 单选题选中值变化后进行校验
        if (this.queryParam.questionType === '1' && this.$refs.tableForm) {
          this.$nextTick(() => this.$refs.tableForm.validateField('questionOptions'));
        }
      },
      checkBoxOptions(newVal) {
        // 多选题正确答案集合变化后进行校验
        if (this.queryParam.questionType === '2' && this.$refs.tableForm) {
          this.$nextTick(() => this.$refs.tableForm.validateField('questionOptions'));
        }
      },
      optionsRadioTableData(newVal) {
        if (this.queryParam.questionType === '1') {
          this.optionDisabled = newVal.length >= 10;
        }
      },
      optionsChecksTableData(newVal) {
        if (this.queryParam.questionType === '2') {
          this.optionDisabled = newVal.length >= 10;
        }
      }
    },
    data() {
      var checkQuestionOptions = (rule, value, callback) => {
        const isSingle = this.queryParam.questionType === '1';
        const isMultiple = this.queryParam.questionType === '2';
        // 新需求：选项文字与图片至少有一个非空；只有二者都为空才算未填写。
        const optionFilled = (item) => {
          const textOk = item.content && item.content.trim() !== '';
          const imgOk = !!item.optionImage;
          return textOk || imgOk;
        };
        const singleContentOk = this.optionsRadioTableData.every(optionFilled);
        const multipleContentOk = this.optionsChecksTableData.every(optionFilled);

        if (isSingle) {
          if (!singleContentOk) return callback(new Error('选项内容未填写完整'));
          if (this.optionsRadioTableData.length < 2) return callback(new Error('请添加至少两个选项'));
          if (this.selectedOption === '' || this.selectedOption === null || this.selectedOption === undefined) {
            return callback(new Error('请选择至少一个作为正确答案'));
          }
        } else if (isMultiple) {
          if (!multipleContentOk) return callback(new Error('选项内容未填写完整'));
          if (this.optionsChecksTableData.length < 2) return callback(new Error('请添加至少两个选项'));
          if (!Array.isArray(this.checkBoxOptions) || this.checkBoxOptions.length < 2) return callback(new Error('请至少选择2个作为正确答案'));
        }
        callback();
      };

      var checkQuestionFill = (rule, value, callback) => {
        if (this.optionsFillTableData.length === 0) return callback(new Error('请至少保留一个填空'));
        let allFilled = this.optionsFillTableData.every((i) => i.content && i.content.trim() !== '');
        if (!allFilled) return callback(new Error('填空答案未填写完整'));
        // 校验每个填空分值为正整数
        let validScores = this.optionsFillTableData.every((item) => {
          const raw = item.score;
          // 允许字符串数字，转换后判断整数性与范围
          if (raw === '' || raw === null || raw === undefined) return false;
          const num = Number(raw);
          return Number.isInteger(num) && num > 0;
        });
        if (!validScores) return callback(new Error('填空分值必须为正整数'));
        let totalScore = this.optionsFillTableData.reduce((sum, item) => {
          return sum + Number(item.score);
        }, 0);
        // 总分需与题目分值一致
        if (!Number.isInteger(Number(this.queryParam.score)) || totalScore !== Number(this.queryParam.score)) {
          return callback(new Error('题目分值设置有误，请修改'));
        }

        callback();
      };

      var checkQuestionProblem = (rule, value, callback) => {
        let contentFlag = this.optionsProblemTableData.every((item) => item.subTitle && item.subTitle.trim() !== '');
        if (this.optionsProblemTableData.length < 1) {
          callback();
        } else if (!contentFlag) {
          callback(new Error('题目小问未填写完整'));
        } else {
          // 校验每个小题分值必须为正整数
          const validScores = this.optionsProblemTableData.every((item) => {
            const raw = item.score;
            if (raw === '' || raw === null || raw === undefined) return false;
            const num = Number(raw);
            return Number.isInteger(num) && num > 0;
          });
          if (!validScores) return callback(new Error('小题分值必须为正整数'));
          const totalScore = this.optionsProblemTableData.reduce((sum, item) => sum + Number(item.score), 0);
          if (!Number.isInteger(Number(this.queryParam.score)) || totalScore !== Number(this.queryParam.score)) {
            return callback(new Error('题目分值设置有误，请修改'));
          }
          callback();
        }
      };
      return {
        rules: {
          lessonStage: [{ required: true, message: '请选择课时阶段', trigger: 'change' }],
          questionType: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
          questionText: [{ required: true, message: '请输入题干', trigger: 'blur' }],
          score: [{ required: true, message: '请输入题目分值', trigger: 'blur' }],
          questionOptions: [{ validator: checkQuestionOptions, required: true, trigger: 'blur' }],
          questionFill: [{ validator: checkQuestionFill, required: true, trigger: 'blur' }],
          questionProblem: [{ validator: checkQuestionProblem, trigger: 'blur' }]
        },
        title: this.$route.query.title,
        type: this.$route.query.type,

        courseCategoryList: [],
        queryData: {},

        supportFileList: [],
        parsedFileList: [],
        // 表单数据
        queryParam: {
          lessonStage: '1',
          questionType: '1',
          detectionType: '1'
        },
        selectedCount: 0, // 已选知识点数量
        dataQueryDict: {
          gradeList: []
        },
        pendingRecord: null, // 知识点
        // 列表数据
        optionsRadioTableData: [],
        optionsChecksTableData: [],
        optionsFillTableData: [],
        optionsProblemTableData: [],
        checkId: 1,
        fillNum: 1,
        problemNum: 1,
        selectedOption: '',
        checkBoxOptions: [],
        problemDisabled: false,
        submitAllFormFlag: false,
        optionDisabled: false, // 单/多选添加按钮禁用
        // 保存状态
        saving: false,
        saveMode: '', // 当前触发的保存模式:add | addContinue | edit

        optionsTableProp1: [
          { prop: 'choiceOption', width: '140', label: '勾选正确答案' },
          { prop: 'content', width: '', label: '选项内容' },
          { prop: 'optionImage', width: '180', label: '选项内容（图片）' }
        ],
        optionsTableProp2: [
          { prop: 'choiceOption', width: '140', label: '勾选正确答案(多选)' },
          { prop: 'content', width: '', label: '选项内容' },
          { prop: 'optionImage', width: '140', label: '选项内容（图片）' }
        ],
        optionsTableProp3: [
          { prop: 'questionLocation', width: '80', label: '填空位置' },
          { prop: 'content', label: '填空答案' },
          { prop: 'score', width: '120', label: '填空分值' }
        ],
        // 页面加载（编辑模式下数据回显用）
        pageLoading: false
      };
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat']),
      multipleCurriculums() {
        return Array.isArray(this.queryParam.curriculumIds) && this.queryParam.curriculumIds.length > 1;
      }
    },
    async created() {
      restoreMissingRouteQuery(this.$router, this.$route, { ns: 'knowledgeQueryInfo', keys: ['id', 'curriculumId', 'title', 'type'] });
      this.title = this.$route.query.title;
      this.type = this.$route.query.type;
      const isEdit = this.$route.query.type === 'edit';
      if (isEdit) {
        this.pageLoading = true;
        try {
          const res = await videoConfigApi.getQuestionDetail({ id: this.$route.query.id });
          await this.initPage(res.data);
          // 编辑模式：预取题目专用知识点树，并根据 isMark 预选
          try {
            await this.fetchKnowledgeTreeList(this.queryParam.id || this.$route.query.id, this.queryParam.curriculumId || this.$route.query.curriculumId, false);
          } catch (e2) {}
        } catch (e) {
          this.$message.error('加载详情失败，请稍后重试');
        } finally {
          this.pageLoading = false;
        }
      } else {
        await this.initPage(this.$route.query);
      }
    },
    activated() {
      restoreMissingRouteQuery(this.$router, this.$route, { ns: 'knowledgeQueryInfo', keys: ['title', 'type', 'id', 'curriculumId'] });
    },
    methods: {
      // 初始化数据
      async initPage(routeData) {
        if (!routeData) return;
        this.queryData = {
          ...routeData
        };
        persistRouteParams(this.$route, { ns: 'knowledgeQueryInfo', keys: ['id', 'curriculumId', 'title', 'type'] });
        this.setBaseQuery(this.queryData);
        const [courseRes, gradeRes] = await Promise.all([this.initCourseCategoryList(), this.initGradeList()]);
      },
      setBaseQuery(d) {
        if (this.$route.query.type == 'edit') {
          this.queryParam = {
            ...d,
            detectionType: d.detectionType ? String(d.detectionType) : '1',
            grade: [d.gradeLevel, d.subjectId],
            lessonStage: d.lessonStage ? String(d.lessonStage) : '1',
            questionType: d.questionType ? String(d.questionType) : '1',
            caption: d.caption
              ? d.caption.split(',').map((a) => {
                  return {
                    url: a
                  };
                })
              : [],
            analysisImage: d.analysisImage
              ? d.analysisImage.split(',').map((a) => {
                  return {
                    url: a
                  };
                })
              : []
          };
          // 回显知识点数量
          if (d.knowledgeId) {
            const ids = d.knowledgeId.split(',').filter(Boolean);
            this.selectedCount = ids.length;
            this.queryParam.knowledgeId = d.knowledgeId;
          } else if (Array.isArray(d.knowledgeList) && d.knowledgeList.length) {
            const ids = d.knowledgeList.map((k) => k.id).filter(Boolean);
            this.selectedCount = ids.length;
            this.queryParam.knowledgeId = ids.join(',');
          } else {
            this.selectedCount = '';
          }

          this.queryParam.caption.map((item, index) => {
            this.supportFileList.push({ url: item.url });
          });

          this.queryParam.analysisImage.map((item, index) => {
            this.parsedFileList.push({ url: item.url });
          });

          if (d.questionType == 1 || d.questionType == 2 || d.questionType == 3) {
            this.selectedOption = d.xktOptionVoList
              .map((a) => {
                if (a.optionIsAnswer == 1) {
                  return a.sortsNum; // 对应上面赋的 checkId: item.sortsNum
                }
              })
              .filter((item) => item !== undefined)[0];
            this.checkBoxOptions = d.xktOptionVoList
              .filter((a) => a.optionIsAnswer == 1)
              .map((a) => a.choiceOption)
              .filter((c) => c !== undefined && c !== null && c !== '');
            this.optionsRadioTableData =
              d.xktOptionVoList.map((item) => {
                return {
                  ...item,
                  checkId: item.sortsNum
                };
              }) || [];
            this.optionsChecksTableData =
              d.xktOptionVoList.map((item) => {
                return {
                  ...item,
                  checkId: item.sortsNum
                };
              }) || [];
            this.optionsFillTableData = d.xktOptionVoList || [];
            this.checkId =
              d.questionType == 1
                ? this.optionsRadioTableData[this.optionsRadioTableData.length - 1].checkId + 1
                : this.optionsChecksTableData[this.optionsChecksTableData.length - 1].checkId + 1;

            this.fillNum = this.optionsFillTableData[this.optionsFillTableData.length - 1].sortsNum + 1;
          } else if (d.questionType == 4 || d.questionType == 5) {
            this.optionsProblemTableData = d.xktSubQuestionVoList || [];
            if (this.optionsProblemTableData.length > 0) {
              this.problemNum = this.optionsProblemTableData[this.optionsProblemTableData.length - 1].sortsNum + 1;
            }
          }
          console.log('////', this.queryParam);
        } else {
          this.selectedCount = 0;
          this.queryParam = {
            ...this.queryParam,
            // grade: [d.gradeLevel, d.subjectId],
            curriculumId: d.id || '',
            versionName: d.versionName,
            videoName: d.videoName,
            questionText: '',
            score: undefined,
            analysis: '',
            caption: [],
            analysisImage: [],
            versionId: d.versionId || '',
            videoId: '',
            // 重置知识点关联
            knowledgeId: null
          };
        }
      },
      // 获取课程大类
      initCourseCategoryList() {
        courseApi.getCourseCategory().then((res) => {
          this.courseCategoryList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },

      // 获取学段
      initGradeList() {
        courseApi.getGradeAndSubjectList().then((res) => {
          this.dataQueryDict.gradeList = res.data;
        });
      },

      // 清空右侧填写的数据
      resetRightSideData() {
        // 清空基本表单项
        this.queryParam.questionText = '';
        this.queryParam.score = undefined;
        this.queryParam.analysis = '';
        this.queryParam.caption = [];
        this.queryParam.analysisImage = [];

        // 清空上传列表
        this.supportFileList = [];
        this.parsedFileList = [];

        // 清空题型相关的可变列表与选择状态
        this.optionsRadioTableData = [];
        this.optionsChecksTableData = [];
        this.optionsFillTableData = [];
        this.optionsProblemTableData = [];
        this.selectedOption = '';
        this.checkBoxOptions = [];

        // 重置辅助状态
        this.checkId = 1;
        this.fillNum = 1;
        this.problemNum = 1;

        this.problemDisabled = false;
        this.submitAllFormFlag = false;

        // 清理校验提示
        setTimeout(() => {
          if (this.$refs && this.$refs.tableForm && this.$refs.tableForm.clearValidate) {
            this.$refs.tableForm.clearValidate();
          }
        }, 0);
      },
      // 添加选项
      addOption() {
        // 单双选
        const newOption = {
          choiceOption: String.fromCharCode(65 + (this.queryParam.questionType === '1' ? this.optionsRadioTableData.length : this.optionsChecksTableData.length)),
          content: '',
          optionImage: '',
          checkId: this.checkId++
        };
        // 填空
        const newFill = {
          sortsNum: this.fillNum++,
          content: '',
          score: ''
        };
        // 小问题
        const newProblem = {
          sortsNum: this.problemNum++,
          subTitle: '',
          score: ''
        };
        if (this.queryParam.questionType === '1') {
          this.optionsRadioTableData.push(newOption);
        } else if (this.queryParam.questionType === '2') {
          this.optionsChecksTableData.push(newOption);
        } else if (this.queryParam.questionType === '3') {
          this.optionsFillTableData.push(newFill);
        } else if (this.queryParam.questionType === '4' || this.queryParam.questionType === '5') {
          this.optionsProblemTableData.push(newProblem);
        }
      },

      // 删除选项
      removeOption(index, row) {
        if (this.queryParam.questionType === '1') {
          const removed = this.optionsRadioTableData[index];
          const removedIsSelected = removed && removed.checkId === this.selectedOption;
          this.optionsRadioTableData.splice(index, 1);
          // 重排字母显示
          this.optionsRadioTableData.forEach((item, i) => {
            item.choiceOption = String.fromCharCode(65 + i);
          });
          if (removedIsSelected) {
            this.selectedOption = '';
          }
        } else if (this.queryParam.questionType === '2') {
          const removed = this.optionsChecksTableData[index];
          this.optionsChecksTableData.splice(index, 1);
          // 重排字母显示
          this.optionsChecksTableData.forEach((item, i) => {
            item.choiceOption = String.fromCharCode(65 + i);
          });
          if (removed && this.checkBoxOptions.includes(removed.choiceOption)) {
            this.checkBoxOptions = this.checkBoxOptions.filter((c) => c !== removed.choiceOption);
          }
          this.checkBoxOptions = this.checkBoxOptions.filter((c) => c !== undefined && c !== null && c !== '');
        } else if (this.queryParam.questionType === '3') {
          this.optionsFillTableData.splice(index, 1);
        } else if (this.queryParam.questionType === '4' || this.queryParam.questionType === '5') {
          this.optionsProblemTableData.splice(index, 1);
        }
      },

      questionTypeChange() {
        // 先清空右侧数据，再重置校验
        this.resetRightSideData();
        // 若切换为填空题，默认生成3个空白填空
        if (this.queryParam.questionType === '3') {
          // 默认生成 3 个填空，并补充 sortsNum 字段
          this.optionsFillTableData = Array.from({ length: 3 }, (_v, i) => ({ sortsNum: i + 1, content: '', score: '' }));
          // 更新计数器
          this.fillNum = 3;
        }
        if (this.$refs && this.$refs.form && this.$refs.form.resetFields) {
          this.$nextTick(() => this.$refs.form.resetFields());
        }
      },

      // 关联知识点接口
      async fetchKnowledgeTreeList(questionId, curriculumId, openDialog = true) {
        if (!curriculumId) return;
        this.pageLoading = true;
        try {
          let res;
          if (this.$route.query.type === 'edit') {
            res = await knowledgeApi.getQuestionTree({ id: questionId, curriculumId });
          } else {
            res = await knowledgeApi.getKnowledgeTreeList({ curriculumId });
          }
          const list = Array.isArray(res.data) ? res.data : [];
          const transform = (nodes) =>
            nodes.map((n) => ({
              id: String(n.id),
              topic: n.knowledgeName || '未命名',
              isMark: Number(n.isMark) === 1 ? 1 : 0,
              origin: n,
              children: Array.isArray(n.children) && n.children.length ? transform(n.children) : []
            }));
          const root = { id: 'root', topic: '知识点', children: transform(list) };
          // 选中集合：优先 isMark=1 的节点；若 knowledgeId 存在也合并
          const collectMarked = (nodes, set = new Set()) => {
            nodes.forEach((n) => {
              if (n.isMark === 1) set.add(String(n.id));
              if (Array.isArray(n.children) && n.children.length) collectMarked(n.children, set);
            });
            return set;
          };
          const markedSet = collectMarked(list);
          if (this.queryParam.knowledgeId) {
            this.queryParam.knowledgeId
              .split(',')
              .filter(Boolean)
              .forEach((id) => markedSet.add(String(id)));
          }
          const selectedIds = Array.from(markedSet);
          // 如果数量未设置，通过 markedSet 回显
          if (!this.selectedCount) {
            this.selectedCount = selectedIds.length || '';
          }
          this.pendingRecord = { data: root, selectedIds };
          if (openDialog && this.$refs.pointDialog) {
            this.$refs.pointDialog.init(this.pendingRecord);
          }
        } catch (e) {
          if (openDialog) this.pendingRecord = null;
        } finally {
          this.pageLoading = false;
        }
      },

      // 知识点反参回显
      confirmPoint(data) {
        // 优先使用弹框直接传出的 ids（已过滤 root & 去重）
        let ids = Array.isArray(data.ids) ? data.ids.slice() : [];
        if (!ids.length) {
          // 兼容旧结构: 扁平化 nodes
          const flattenIds = (nodes, set = new Set()) => {
            if (!Array.isArray(nodes)) return set;
            nodes.forEach((n) => {
              if (!n || !n.id) return;
              set.add(n.id);
              if (Array.isArray(n.children) && n.children.length) flattenIds(n.children, set);
            });
            return set;
          };
          ids = Array.from(flattenIds(data.nodes));
        }
        this.queryParam.knowledgeId = ids.join(',');
        this.selectedCount = data.count;
        // 缓存已选，方便再次打开未重新请求时回显
        if (this.pendingRecord) {
          this.pendingRecord.selectedIds = ids;
        }
      },

      // 关联知识点
      async handlePointClick() {
        const curriculumId = this.queryParam && this.queryParam.curriculumId ? this.queryParam.curriculumId : this.queryParam;
        const qid = this.queryParam && (this.queryParam.id || this.queryParam.videoId) ? this.queryParam.id || this.queryParam.videoId : this.queryParam;
        if (this.pendingRecord) {
          // 打开前同步最新 knowledgeId
          const extra = (this.queryParam.knowledgeId ? this.queryParam.knowledgeId.split(',').filter(Boolean) : []).map((i) => String(i));
          const existSet = new Set(this.pendingRecord.selectedIds || []);
          extra.forEach((i) => existSet.add(i));
          this.pendingRecord.selectedIds = Array.from(existSet);
          this.$refs.pointDialog.init(this.pendingRecord);
        } else {
          await this.fetchKnowledgeTreeList(qid, curriculumId, true);
        }
      },

      // 预览
      priviewParse() {
        // 预览前校验必填项
        this._validateAll().then((valid) => {
          if (!valid) {
            this.$message.error('您有未填的必填项，无法预览！');
            return;
          }
          this.$refs.parseDialog.init('题目预览', this.postAllParmas());
        });
      },

      // 配图上传
      handleMoreSuccess(url) {
        this.queryParam.caption.push({ url: url, num: '' });
      },
      handleMoreRemove(data) {
        this.removeImgNum(data.url, this.queryParam.caption);
      },

      // 解析图片上传
      handleOneSuccess(url) {
        this.queryParam.analysisImage.push({ url: url, num: '' });
      },
      handleOneRemove(data) {
        this.removeImgNum(data.url, this.queryParam.analysisImage);
      },

      removeImgNum(url, arr) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].url === url) {
            arr.splice(i, 1);
            break;
          }
        }
      },

      // 取消
      cancel() {
        console.log('cancel');
        // this.$refs['form'].resetFields(); //重置表单校验
        this.$store.dispatch('delVisitedViews', this.$route);
        if (this.type === 'edit') {
          this.$router.push({ path: '/studyExamPassed/knowledgeQuestionManagement' });
        } else {
          this.$router.push({ path: '/studyExamPassed/questionKnowledgeManage' });
        }
      },
      // 单表单 Promise 化校验
      _validate(refName) {
        return new Promise((resolve) => {
          const form = this.$refs[refName];
          if (!form) return resolve(false);
          form.validate((valid) => resolve(valid));
        });
      },
      // 并行校验左右两侧表单
      async _validateAll() {
        const [left, right] = await Promise.all([this._validate('selectForm'), this._validate('tableForm')]);
        return left && right;
      },
      // 统一提交流程
      async _submit(mode) {
        if (this.saving) return; // 防重复
        this.saveMode = mode;
        const valid = await this._validateAll();
        if (!valid) {
          this.submitAllFormFlag = true;
          this.$message.error('您有未填的必填项，无法保存！');
          return;
        }
        this.submitAllFormFlag = false;
        this.saving = true;
        const isEdit = mode === 'edit';
        const postApi = mode == 'edit' ? videoConfigApi.AddQuestionUpdate : videoConfigApi.AddQuestionCurriculum;
        await postApi(this.postAllParmas())
          .then((res) => {
            if (mode === 'addContinue') {
              this.$message.success('题目保存成功，可继续录入新题目');
              this.$bus.$emit('updateKnowledgeTable');
              this.resetRightSideData();
            } else if (mode === 'add') {
              this.$message.success('题目保存成功');
              this.$bus.$emit('updateKnowledgeTable');
              this.$store.dispatch('delVisitedViews', this.$route);
              this.$router.push({ path: '/studyExamPassed/questionKnowledgeManage' });
            } else if (mode === 'edit') {
              this.$message.success('编辑题目成功');
              this.$store.dispatch('delVisitedViews', this.$route);
              this.$router.push({ path: '/studyExamPassed/knowledgeQuestionManagement', query: { curriculumId: this.queryParam.curriculumId } });
            }
          })
          .catch((e) => {
            this.$message.error(isEdit ? '编辑题目失败' : '题目保存失败');
          })
          .finally(() => {
            this.saving = false;
            this.saveMode = '';
          });
      },

      // 整理提交参数
      postAllParmas() {
        let xktOptionVoList = [
          ...(this.queryParam.questionType === '1'
            ? this.optionsRadioTableData.map((item) => ({
                ...item,
                sortsNum: item.checkId,
                optionIsAnswer: item.checkId === this.selectedOption ? 1 : 0
              }))
            : []),
          ...(this.queryParam.questionType === '2'
            ? this.optionsChecksTableData.map((item) => ({
                ...item,
                sortsNum: item.checkId,
                optionIsAnswer: this.checkBoxOptions.find((a) => {
                  return item.choiceOption === a;
                })
                  ? 1
                  : 0
              }))
            : []),
          ...(this.queryParam.questionType === '3'
            ? this.optionsFillTableData.map((item) => ({
                ...item,
                sortsNum: item.sortsNum,
                optionIsAnswer: 1
              }))
            : [])
        ];
        let xktSubQuestionVoList = [
          ...(this.queryParam.questionType === '4' || this.queryParam.questionType === '5'
            ? this.optionsProblemTableData.map((item) => {
                return {
                  ...item,
                  sortsNum: item.sortsNum
                };
              })
            : [])
        ];
        let confirmParams = {
          ...this.queryParam,
          caption: this.queryParam.caption.map((item) => item.url).join(','),
          analysisImage: this.queryParam.analysisImage.map((item) => item.url).join(','),
          isSubQuestion: (this.queryParam.questionType === '4' || this.queryParam.questionType === '5') && this.optionsProblemTableData.length > 0 ? 1 : 0,
          // detectionType: 1,
          questionNumber: this.queryParam.questionNumber || 1,
          xktOptionVoList,
          xktSubQuestionVoList
        };
        return confirmParams;
      },
      // 提交新增
      submitForm() {
        this._submit('add');
      },
      // 提交新增并继续添加
      submitFormContinue() {
        this._submit('addContinue');
      },
      // 提交修改
      submitEditForm() {
        this._submit('edit');
      }
    }
  };
</script>

<style scoped lang="less">
  @normal-text: PingFangSC-regular;
  @normal-color: #ff4949;
  .add-question-page {
    height: 100%;
    padding: 20px;
  }

  .page-title {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #dcdfe6;
  }

  .page-title h2 {
    font-size: 24px;
    color: #303133;
    font-weight: bold;
  }

  .main {
    display: flex;
    gap: 20px;
  }

  .left-form-container {
    flex: 1;
    padding: 20px;
    border-right: 2px solid #dcdfe6;
    .tag-class {
      width: 100%;
      text-align: center;
      cursor: pointer;
    }
  }

  .right-content-container {
    flex: 3;
    padding: 20px;
    height: calc(100vh - 240px);
    overflow: auto;
    .el-form-item__label {
      font-weight: 600 !important;
      color: #303133 !important;
    }

    .add-option-btn {
      margin-top: 20px;
    }

    .option-hint {
      color: @normal-color;
      font-family: @normal-text;
      font-size: 12px;
    }

    .problem-main {
      background-color: #efefef;
      padding: 10px 10px 20px 10px;
      border-radius: 8px;
      .problem-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        span {
          font-weight: 600;
          font-size: 14px;
          color: #303133;
        }
        i {
          font-size: 20px;
          color: @normal-color;
          @normal-text: PingFangSC-regular;
          cursor: pointer;
        }
      }
    }
  }

  ::v-deep .el-textarea .el-input__count {
    bottom: 4px !important;
  }
</style>
