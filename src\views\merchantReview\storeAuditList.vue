<template>
  <!-- 门店 -->
  <div class="app-container">
    <el-card style="margin-bottom: 15px">
      <el-form label-width="100px" label-position="left">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="门店名称：" prop="merchantName">
              <el-input v-model="dataQuery.merchantName" placeholder="请输入门店名称" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="负责人姓名：" prop="realName">
              <el-input v-model="dataQuery.realName" placeholder="请输入负责人姓名" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="登录账号：" prop="name">
              <el-input v-model="dataQuery.name" placeholder="请输入登录账号" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24" v-if="isAdmin">
            <el-form-item label="所属俱乐部：" prop="operationsName">
              <el-input v-model="dataQuery.operationsName" placeholder="请输入所属俱乐部" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="课程推广大使：" prop="refereeName" label-width="110px">
              <el-input v-model="dataQuery.refereeName" placeholder="请输入课程推广大使" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="业务开展地：" prop="selectCity">
              <el-cascader placeholder="请选择业务开展地" :options="areaData" v-model="selectCity" :props="{ value: 'label' }" clearable style="width: 100%"></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="审核状态：" prop="currentAuditStatus">
              <el-select v-model="dataQuery.currentAuditStatus" placeholder="请选择审核状态" clearable style="width: 100%">
                <el-option label="审核中" value="1"></el-option>
                <el-option label="已通过" value="2"></el-option>
                <el-option label="已驳回" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24" v-if="isAdmin">
            <el-form-item label="业务开展地是否一致：" label-width="160px" prop="isBusinessArea">
              <el-select v-model="dataQuery.isBusinessArea" style="width: 100%" placeholder="请选择业务开展地是否一致" clearable>
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-button type="primary" @click="fetchData01" class="searchStyle">搜索</el-button>
            <el-button @click="reset" class="restStyle">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card>
      <div v-if="tableData.length > 0">
        <el-table
          class="custom-table"
          stripe
          v-loading="tableLoading"
          :data="tableData"
          style="width: 100%; margin-bottom: 20px"
          row-key="id"
          default-expand-all
          :tree-props="{ list: 'children', hasChildren: 'true' }"
        >
          <el-table-column prop="auditCode" label="编号" width="100" align="center"></el-table-column>
          <el-table-column prop="merchantName" label="门店名称" width="150" align="center"></el-table-column>
          <el-table-column prop="realName" label="负责人姓名" width="120" align="center"></el-table-column>
          <el-table-column prop="name" label="登录账号" align="center"></el-table-column>
          <el-table-column prop="operationsName" label="所属俱乐部" align="center" v-if="isAdmin"></el-table-column>
          <el-table-column prop="refereeName" label="课程推广大使" width="120" align="center"></el-table-column>
          <el-table-column prop="businessArea" label="业务开展地" align="center"></el-table-column>
          <el-table-column prop="isBusinessArea" label="业务开展地是否一致" width="150" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isBusinessArea === 0">否</span>
              <span v-if="scope.row.isBusinessArea === 1">是</span>
            </template>
          </el-table-column>
          <el-table-column prop="currentAuditStatus" label="审核状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.currentAuditStatus === 1" style="color: #46a6ff">审核中</span>
              <span v-if="scope.row.currentAuditStatus === 2" style="color: green">已通过</span>
              <span v-if="scope.row.currentAuditStatus === 3" style="color: red">已驳回</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="150" align="center"></el-table-column>
          <el-table-column label="操作" width="150px" align="center">
            <template slot-scope="scope">
              <div
                size="mini"
                style="color: #73bf8a"
                class="operateCommonStyles"
                @click="handleView(scope.row.id, '审批', '门店')"
                v-if="!isAdmin && scope.row.currentAuditStatus == 1"
              >
                审核
              </div>
              <div
                size="mini"
                style="color: #73bf8a"
                class="operateCommonStyles"
                @click="handleView(scope.row.id, '查看', '门店')"
                v-if="isAdmin || scope.row.currentAuditStatus != 1"
              >
                查看
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-row>
          <el-col :span="24" style="text-align: right; overflow-x: auto">
            <el-pagination
              :current-page="tablePage.currentPage"
              :page-sizes="[10, 20, 30, 40, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tablePage.totalItems"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <NoMore></NoMore>
      </div>
    </el-card>
    <!-- 审核弹框 -->
    <ReviewInforDialog ref="ReviewInforDialog" @refresh-list="handleRefreshList" />
  </div>
</template>

<script>
  import registrationCodeApi from '@/api/registrationCodeApi/registrationCodeApi';
  import { pageParamNames } from '@/utils/constants';
  import areaData from '@/utils/newProvinceCityTree.json';
  import ReviewInforDialog from './dialogs/reviewInforDialog.vue';
  import checkPermission from '@/utils/permission';
  import NoMore from '@/components/NoMore/index.vue';
  export default {
    name: 'merchantFlowList',
    components: {
      ReviewInforDialog,
      NoMore
    },
    data() {
      return {
        areaData,
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {},
        reviewDialogVisible: false,
        currentReviewData: {},
        isAdmin: false,
        selectCity: []
      };
    },
    created() {
      this.fetchData();
      this.isAdmin = checkPermission(['admin']);
    },
    methods: {
      fetchData01() {
        this.tablePage.currentPage = 1;
        const that = this;
        that.dataQuery.province = that.selectCity[0];
        if (that.selectCity && that.selectCity[1] == '市辖区') {
          that.dataQuery.city = that.selectCity[0];
        } else {
          that.dataQuery.city = that.selectCity[1];
        }
        that.dataQuery.area = that.selectCity[2];
        this.fetchData();
      },
      // 查询俱乐部审核列表
      fetchData() {
        this.tableLoading = true;
        const params = {
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.size,
          ...this.dataQuery
        };
        registrationCodeApi
          .StoreListData(params)
          .then((res) => {
            this.tableData = res.data.data;
            this.tableLoading = false;
            pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name])));
          })
          .catch(() => {
            this.tableLoading = false;
          });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      reset() {
        this.tablePage.currentPage = 1;
        this.dataQuery = {};
        this.selectCity = [];
        this.fetchData();
      },
      handleView(id, title, storeClubType) {
        this.$refs.ReviewInforDialog.open(id, title, storeClubType);
      },
      handleRefreshList() {
        this.fetchData01();
      }
    }
  };
</script>

<style scoped lang="less">
  ::v-deep .el-image-viewer__close {
    color: #ffffff;
  }
  .operateCommonStyles {
    display: inline-flex;
    margin-right: 24px;
    margin-bottom: 8px;
    cursor: pointer;
  }
</style>
