!function(){var _p={r:function(t){if(_p[t].inited)return _p[t].value;if("function"!=typeof _p[t].value)return _p[t].inited=!0,_p[t].value;var e={exports:{}},n=_p[t].value(null,e.exports,e);if(_p[t].inited=!0,void 0!==(_p[t].value=n))return n;for(var r in e.exports)if(e.exports.hasOwnProperty(r))return _p[t].inited=!0,_p[t].value=e.exports,e.exports}};_p[0]={value:function(t){function a(t){var e=parseFloat(t,10);return/ms/.test(t)?e:/s/.test(t)?1e3*e:/min/.test(t)?60*e*1e3:e}var s=_p.r(8),o=_p.r(1),u=_p.r(11).createClass("Animator",{constructor:function(t,e,n){if(1==arguments.length){var r=t;this.beginValue=r.beginValue,this.finishValue=r.finishValue,this.setter=r.setter}else this.beginValue=t,this.finishValue=e,this.setter=n},start:function(t,e,n,r,i){2===arguments.length&&"object"==typeof e&&(n=e.easing,r=e.delay,i=e.callback,e=e.duration),4===arguments.length&&"function"==typeof r&&(i=r,r=0);var s=this.create(t,e,n,i);return 0<(r=a(r))?setTimeout(function(){s.play()},r):s.play(),s},create:function(t,e,n,r){var i;return e=e&&a(e)||u.DEFAULT_DURATION,"string"==typeof(n=n||u.DEFAULT_EASING)&&(n=o[n]),i=new s(this,t,e,n),"function"==typeof r&&i.on("finish",r),i},reverse:function(){return new u(this.finishValue,this.beginValue,this.setter)}});u.DEFAULT_DURATION=300,u.DEFAULT_EASING="linear";var e=_p.r(60);return _p.r(11).extendClass(e,{animate:function(t,e,n,r,i){var s=this._KityAnimateQueue=this._KityAnimateQueue||[],a=t.create(this,e,n,i);return a.on("finish",function(){s.shift(),s.length&&setTimeout(s[0].t.play.bind(s[0].t),s[0].d)}),s.push({t:a,d:r}),1==s.length&&setTimeout(a.play.bind(a),r),this},timeline:function(){return this._KityAnimateQueue[0].t},stop:function(){var t=this._KityAnimateQueue;if(t)for(;t.length;)t.shift().t.stop();return this}}),u}},_p[1]={value:function(t,e,n){var i={linear:function(t,e,n,r){return n*(t/r)+e},swing:function(t,e,n,r){return i.easeOutQuad(t,e,n,r)},ease:function(t,e,n,r){return i.easeInOutCubic(t,e,n,r)},easeInQuad:function(t,e,n,r){return n*(t/=r)*t+e},easeOutQuad:function(t,e,n,r){return-n*(t/=r)*(t-2)+e},easeInOutQuad:function(t,e,n,r){return(t/=r/2)<1?n/2*t*t+e:-n/2*(--t*(t-2)-1)+e},easeInCubic:function(t,e,n,r){return n*(t/=r)*t*t+e},easeOutCubic:function(t,e,n,r){return n*((t=t/r-1)*t*t+1)+e},easeInOutCubic:function(t,e,n,r){return(t/=r/2)<1?n/2*t*t*t+e:n/2*((t-=2)*t*t+2)+e},easeInQuart:function(t,e,n,r){return n*(t/=r)*t*t*t+e},easeOutQuart:function(t,e,n,r){return-n*((t=t/r-1)*t*t*t-1)+e},easeInOutQuart:function(t,e,n,r){return(t/=r/2)<1?n/2*t*t*t*t+e:-n/2*((t-=2)*t*t*t-2)+e},easeInQuint:function(t,e,n,r){return n*(t/=r)*t*t*t*t+e},easeOutQuint:function(t,e,n,r){return n*((t=t/r-1)*t*t*t*t+1)+e},easeInOutQuint:function(t,e,n,r){return(t/=r/2)<1?n/2*t*t*t*t*t+e:n/2*((t-=2)*t*t*t*t+2)+e},easeInSine:function(t,e,n,r){return-n*Math.cos(t/r*(Math.PI/2))+n+e},easeOutSine:function(t,e,n,r){return n*Math.sin(t/r*(Math.PI/2))+e},easeInOutSine:function(t,e,n,r){return-n/2*(Math.cos(Math.PI*t/r)-1)+e},easeInExpo:function(t,e,n,r){return 0===t?e:n*Math.pow(2,10*(t/r-1))+e},easeOutExpo:function(t,e,n,r){return t==r?e+n:n*(1-Math.pow(2,-10*t/r))+e},easeInOutExpo:function(t,e,n,r){return 0===t?e:t==r?e+n:(t/=r/2)<1?n/2*Math.pow(2,10*(t-1))+e:n/2*(2-Math.pow(2,-10*--t))+e},easeInCirc:function(t,e,n,r){return-n*(Math.sqrt(1-(t/=r)*t)-1)+e},easeOutCirc:function(t,e,n,r){return n*Math.sqrt(1-(t=t/r-1)*t)+e},easeInOutCirc:function(t,e,n,r){return(t/=r/2)<1?-n/2*(Math.sqrt(1-t*t)-1)+e:n/2*(Math.sqrt(1-(t-=2)*t)+1)+e},easeInElastic:function(t,e,n,r){var i=1.70158,s=0,a=n;return 0===t?e:1==(t/=r)?e+n:(s=s||.3*r,i=a<Math.abs(n)?(a=n,s/4):s/(2*Math.PI)*Math.asin(n/a),-(a*Math.pow(2,10*--t)*Math.sin((t*r-i)*(2*Math.PI)/s))+e)},easeOutElastic:function(t,e,n,r){var i=1.70158,s=0,a=n;return 0===t?e:1==(t/=r)?e+n:(s=s||.3*r,i=a<Math.abs(n)?(a=n,s/4):s/(2*Math.PI)*Math.asin(n/a),a*Math.pow(2,-10*t)*Math.sin((t*r-i)*(2*Math.PI)/s)+n+e)},easeInOutElastic:function(t,e,n,r){var i=1.70158,s=0,a=n;if(0===t)return e;if(2==(t/=r/2))return e+n;if(s=s||r*(.3*1.5),a<Math.abs(n)){a=n;i=s/4}else i=s/(2*Math.PI)*Math.asin(n/a);return t<1?a*Math.pow(2,10*--t)*Math.sin((t*r-i)*(2*Math.PI)/s)*-.5+e:a*Math.pow(2,-10*--t)*Math.sin((t*r-i)*(2*Math.PI)/s)*.5+n+e},easeInBack:function(t,e,n,r,i){return null==i&&(i=1.70158),n*(t/=r)*t*((i+1)*t-i)+e},easeOutBack:function(t,e,n,r,i){return null==i&&(i=1.70158),n*((t=t/r-1)*t*((i+1)*t+i)+1)+e},easeInOutBack:function(t,e,n,r,i){return null==i&&(i=1.70158),(t/=r/2)<1?n/2*(t*t*((1+(i*=1.525))*t-i))+e:n/2*((t-=2)*t*((1+(i*=1.525))*t+i)+2)+e},easeInBounce:function(t,e,n,r){return n-i.easeOutBounce(r-t,0,n,r)+e},easeOutBounce:function(t,e,n,r){return(t/=r)<1/2.75?n*(7.5625*t*t)+e:t<2/2.75?n*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?n*(7.5625*(t-=2.25/2.75)*t+.9375)+e:n*(7.5625*(t-=2.625/2.75)*t+.984375)+e},easeInOutBounce:function(t,e,n,r){return t<r/2?.5*i.easeInBounce(2*t,0,n,r)+e:.5*i.easeOutBounce(2*t-r,0,n,r)+.5*n+e}};return i}},_p[2]={value:function(t,e){var n,r=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||function(t){return setTimeout(t,1e3/60)},i=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.webkitCancelAnimationFrame||window.msCancelAnimationFrame||window.clearTimeout,s=[];function a(t){1===s.push(t)&&(n=r(o))}function o(){var t=s;for(s=[];t.length;)u(t.pop());n=0}function u(t){var e=+new Date,n=e-t.time;200<n&&(n=1e3/60),t.dur=n,t.elapsed+=n,t.time=e,t.action.call(null,t),t.index++}e.requestFrame=function(t){var e,n,r=(e=t,n={index:0,time:+new Date,elapsed:0,action:e,next:function(){a(n)}});return a(r),r},e.releaseFrame=function(t){var e=s.indexOf(t);~e&&s.splice(e,1),0===s.length&&i(n)}}},_p[3]={value:function(t){var e=_p.r(0),s=_p.r(34),a=_p.r(46),n=_p.r(60),o=_p.r(11).createClass("MotionAnimator",{base:e,constructor:function(t,e){var i=this;this.callBase({beginValue:0,finishValue:1,setter:function(t,e){var n=i.motionPath instanceof a?i.motionPath.getPathData():i.motionPath,r=s.pointAtPath(n,e);t.setTranslate(r.x,r.y),this.doRotate&&t.setRotate(r.tan.getAngle())}}),this.doRotate=e,this.motionPath=t}});return _p.r(11).extendClass(n,{motion:function(t,e,n,r,i){return this.animate(new o(t),e,n,r,i)}}),o}},_p[4]={value:function(t){var e=_p.r(0),s=_p.r(11).createClass("OpacityAnimator",{base:e,constructor:function(t){this.callBase({beginValue:function(t){return t.getOpacity()},finishValue:t,setter:function(t,e){t.setOpacity(e)}})}}),n=_p.r(60);return _p.r(11).extendClass(n,{fxOpacity:function(t,e,n,r,i){return this.animate(new s(t),e,n,r,i)},fadeTo:function(){return this.fxOpacity.apply(this,arguments)},fadeIn:function(){return this.fxOpacity.apply(this,[1].concat([].slice.call(arguments)))},fadeOut:function(){return this.fxOpacity.apply(this,[0].concat([].slice.call(arguments)))}}),s}},_p[5]={value:function(t){var e=_p.r(0),r=_p.r(34),s=_p.r(11).createClass("OpacityAnimator",{base:e,constructor:function(n){this.callBase({beginValue:function(t){return this.beginPath=t.getPathData(),0},finishValue:1,setter:function(t,e){t.setPathData(r.pathTween(this.beginPath,n,e))}})}}),n=_p.r(46);return _p.r(11).extendClass(n,{fxPath:function(t,e,n,r,i){return this.animate(new s(t),e,n,r,i)}}),s}},_p[6]={value:function(t){var e=_p.r(0),s=_p.r(11).createClass("RotateAnimator",{base:e,constructor:function(t){this.callBase({beginValue:0,finishValue:t,setter:function(t,e,n){var r=n.getDelta();t.rotate(r,ax,ay)}})}}),n=_p.r(60);return _p.r(11).extendClass(n,{fxRotate:function(t,e,n,r,i){return this.animate(new s(t),e,n,r,i)}}),s}},_p[7]={value:function(t){var e=_p.r(0),a=_p.r(11).createClass("ScaleAnimator",{base:e,constructor:function(a,o){this.callBase({beginValue:0,finishValue:1,setter:function(t,e,n){var r=n.getDelta(),i=Math.pow(a,r),s=Math.pow(o,r);t.scale(s,i)}})}}),n=_p.r(60);return _p.r(11).extendClass(n,{fxScale:function(t,e,n,r,i,s){return this.animate(new a(t,e),n,r,i,s)}}),a}},_p[8]={value:function(t){var e=_p.r(33),a=_p.r(12),n=_p.r(2);function r(t,e,n){for(var r in this.timeline=t,this.target=t.target,this.type=e,n)n.hasOwnProperty(r)&&(this[r]=n[r])}var i=_p.r(11).createClass("Timeline",{mixins:[e],constructor:function(t,e,n,r){this.callMixin(),this.target=e,this.time=0,this.duration=n,this.easing=r,this.animator=t,this.beginValue=t.beginValue,this.finishValue=t.finishValue,this.setter=t.setter,this.status="ready"},nextFrame:function(t){"playing"==this.status&&(this.time+=t.dur,this.setValue(this.getValue()),this.time>=this.duration&&this.timeUp(),t.next())},getPlayTime:function(){return this.rollbacking?this.duration-this.time:this.time},getTimeProportion:function(){return this.getPlayTime()/this.duration},getValueProportion:function(){return this.easing(this.getPlayTime(),0,1,this.duration)},getValue:function(){var t,e,n,r=this.beginValue,i=this.finishValue,s=this.getValueProportion();return t=r,e=i,n=s,a.paralle(t,e,function(t,e){return t+(e-t)*n})},setValue:function(t){this.lastValue=this.currentValue,this.currentValue=t,this.setter.call(this.target,this.target,t,this)},getDelta:function(){return this.lastValue=void 0===this.lastValue?this.beginValue:this.lastValue,t=this.lastValue,e=this.currentValue,a.paralle(t,e,function(t,e){return e-t});var t,e},play:function(){var t=this.status;switch(this.status="playing",t){case"ready":a.isFunction(this.beginValue)&&(this.beginValue=this.beginValue.call(this.target,this.target)),a.isFunction(this.finishValue)&&(this.finishValue=this.finishValue.call(this.target,this.target)),this.time=0,this.setValue(this.beginValue),this.frame=n.requestFrame(this.nextFrame.bind(this));break;case"finished":case"stoped":this.time=0,this.frame=n.requestFrame(this.nextFrame.bind(this));break;case"paused":this.frame.next()}return this.fire("play",new r(this,"play",{lastStatus:t})),this},pause:function(){return this.status="paused",this.fire("pause",new r(this,"pause")),n.releaseFrame(this.frame),this},stop:function(){return this.status="stoped",this.setValue(this.finishValue),this.rollbacking=!1,this.fire("stop",new r(this,"stop")),n.releaseFrame(this.frame),this},timeUp:function(){this.repeatOption?(this.time=0,this.rollback?this.rollbacking?(this.decreaseRepeat(),this.rollbacking=!1):(this.rollbacking=!0,this.fire("rollback",new r(this,"rollback"))):this.decreaseRepeat(),this.repeatOption?this.fire("repeat",new r(this,"repeat")):this.finish()):this.finish()},finish:function(){this.setValue(this.finishValue),this.status="finished",this.fire("finish",new r(this,"finish")),n.releaseFrame(this.frame)},decreaseRepeat:function(){!0!==this.repeatOption&&this.repeatOption--},repeat:function(t,e){return this.repeatOption=t,this.rollback=e,this}});return i.requestFrame=n.requestFrame,i.releaseFrame=n.releaseFrame,i}},_p[9]={value:function(t){var e=_p.r(0),a=_p.r(11).createClass("TranslateAnimator",{base:e,constructor:function(t,e){this.callBase({x:0,y:0},{x:t,y:e},function(t,e,n){var r=n.getDelta();t.translate(r.x,r.y)})}}),n=_p.r(60);return _p.r(11).extendClass(n,{fxTranslate:function(t,e,n,r,i,s){return this.animate(new a(t,e),n,r,i,s)}}),a}},_p[10]={value:function(){return function(){var t,e,n=navigator.userAgent.toLowerCase(),r=window.opera;(t={platform:{win32:"Win",macintel:"Mac"}[navigator.platform.toLowerCase()]||"Lux",lb:!!~(e=n).indexOf("lbbrowser")&&(~e.indexOf("msie")?"ie":"chrome"),sg:/se[\s\S]+metasr/.test(n),bd:!!~n.indexOf("bidubrowser"),edge:!!~n.indexOf("edge"),chrome:!1,opera:!!r&&r.version,webkit:-1<n.indexOf(" applewebkit/"),mac:-1<n.indexOf("macintosh")}).ie=!t.lb&&/(msie\s|trident.*rv:)([\w.]+)/.test(n),t.gecko="Gecko"==navigator.product&&!t.webkit&&!t.opera&&!t.ie;var i=0;if(t.ie&&(i=+(n.match(/(msie\s|trident.*rv:)([\w.]+)/)[2]||0),t.ie11Compat=11==document.documentMode,t.ie9Compat=9==document.documentMode),t.gecko){var s=n.match(/rv:([\d\.]+)/);s&&(i=1e4*(s=s[1].split("."))[0]+100*(s[1]||0)+ +(s[2]||0))}return!/chrome\/(\d+\.\d)/i.test(n)||t.bd||t.opera||t.lb||t.sg||t.edge||(t.chrome=+RegExp.$1),/(\d+\.\d)?(?:\.\d)?\s+safari\/?(\d+\.\d+)?/i.test(n)&&!/chrome/i.test(n)&&(t.safari=+(RegExp.$1||RegExp.$2)),t.opera&&(i=parseFloat(r.version())),t.webkit&&(i=parseFloat(n.match(/ applewebkit\/(\d+)/)[1])),t.bd&&(i=parseFloat(n.match(/bidubrowser\/(\d+)/)[1])),t.opera&&(i=parseFloat(n.match(/opr\/(\d+)/)[1])),t.edge&&(i=parseFloat(n.match(/edge\/(\d+)/)[1])),t.version=i,t.isCompatible=!t.mobile&&(t.ie&&6<=i||t.gecko&&10801<=i||t.opera&&9.5<=i||t.air&&1<=i||t.webkit&&522<=i||!1),t}()}},_p[11]={value:function(require,exports){function Class(){}function checkBaseConstructorCall(t,e){var n=t.toString();if(!/this\.callBase/.test(n))throw new Error(e+" : 类构造函数没有调用父类的构造函数！为了安全，请调用父类的构造函数")}exports.Class=Class,Class.__KityClassName="Class",Class.prototype.base=function(t){return arguments.callee.caller.__KityMethodClass.__KityBaseClass.prototype[t].apply(this,Array.prototype.slice.call(arguments,1))},Class.prototype.callBase=function(){var t=arguments.callee.caller;return t.__KityMethodClass.__KityBaseClass.prototype[t.__KityMethodName].apply(this,arguments)},Class.prototype.mixin=function(t){var e=arguments.callee.caller.__KityMethodClass.__KityMixins;return e?e[t].apply(this,Array.prototype.slice.call(arguments,1)):this},Class.prototype.callMixin=function(){var t=arguments.callee.caller,e=t.__KityMethodName,n=t.__KityMethodClass.__KityMixins;if(!n)return this;var r=n[e];if("constructor"!=e)return r.apply(this,arguments);for(var i=0,s=r.length;i<s;i++)r[i].call(this);return this},Class.prototype.pipe=function(t){return"function"==typeof t&&t.call(this,this),this},Class.prototype.getType=function(){return this.__KityClassName},Class.prototype.getClass=function(){return this.constructor};var KITY_INHERIT_FLAG="__KITY_INHERIT_FLAG_"+ +new Date;function inherit(constructor,BaseClass,classname){var KityClass=eval("(function "+classname+"( __inherit__flag ) {if( __inherit__flag != KITY_INHERIT_FLAG ) {KityClass.__KityConstructor.apply(this, arguments);}this.__KityClassName = KityClass.__KityClassName;})");for(var methodName in KityClass.__KityConstructor=constructor,KityClass.prototype=new BaseClass(KITY_INHERIT_FLAG),BaseClass.prototype)BaseClass.prototype.hasOwnProperty(methodName)&&0!==methodName.indexOf("__Kity")&&(KityClass.prototype[methodName]=BaseClass.prototype[methodName]);return KityClass.prototype.constructor=KityClass,KityClass}function mixin(t,e){if(!1==e instanceof Array)return t;var n,r,i,s=e.length;for(t.__KityMixins={constructor:[]},n=0;n<s;n++)for(i in r=e[n].prototype)!1!==r.hasOwnProperty(i)&&0!==i.indexOf("__Kity")&&("constructor"===i?t.__KityMixins.constructor.push(r[i]):t.prototype[i]=t.__KityMixins[i]=r[i]);return t}function extend(t,e){for(var n in e.__KityClassName&&(e=e.prototype),e)if(e.hasOwnProperty(n)&&n.indexOf("__Kity")&&"constructor"!=n){var r=t.prototype[n]=e[n];r.__KityMethodClass=t,r.__KityMethodName=n}return t}exports.createClass=function(t,e){var n,r,i;return 1===arguments.length&&(e=t,t="AnonymousClass"),i=e.base||Class,e.hasOwnProperty("constructor")?(n=e.constructor,i!=Class&&checkBaseConstructorCall(n,t)):n=function(){this.callBase.apply(this,arguments),this.callMixin.apply(this,arguments)},(r=mixin(r=inherit(n,i,t),e.mixins)).__KityClassName=n.__KityClassName=t,r.__KityBaseClass=n.__KityBaseClass=i,r.__KityMethodName=n.__KityMethodName="constructor",r.__KityMethodClass=n.__KityMethodClass=r,delete e.mixins,delete e.constructor,delete e.base,r=extend(r,e)},exports.extendClass=extend}},_p[12]={value:function(){var o={each:function(t,e,n){if(null!==t)if(t.length===+t.length){for(var r=0,i=t.length;r<i;r++)if(!1===e.call(n,t[r],r,t))return!1}else for(var s in t)if(t.hasOwnProperty(s)&&!1===e.call(n,t[s],s,t))return!1},extend:function(t){for(var e=arguments,n=!!this.isBoolean(e[e.length-1])&&e[e.length-1],r=this.isBoolean(e[e.length-1])?e.length-1:e.length,i=1;i<r;i++){var s=e[i];for(var a in s)n&&t.hasOwnProperty(a)||(t[a]=s[a])}return t},deepExtend:function(t,e){for(var n=arguments,r=!!this.isBoolean(n[n.length-1])&&n[n.length-1],i=this.isBoolean(n[n.length-1])?n.length-1:n.length,s=1;s<i;s++){var a=n[s];for(var o in a)r&&t.hasOwnProperty(o)||(this.isObject(t[o])&&this.isObject(a[o])?this.deepExtend(t[o],a[o],r):t[o]=a[o])}return t},clone:function(t){var e={};for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},copy:function(t){return"object"!=typeof t?t:"function"==typeof t?null:JSON.parse(JSON.stringify(t))},queryPath:function(t,e){for(var n=t.split("."),r=0,i=e,s=n.length;r<s;){if(!(n[r]in i))return;if(i=i[n[r]],s<=++r||void 0===i)return i}},getValue:function(t,e){return void 0!==t?t:e},flatten:function(t){var e,n=[],r=t.length;for(e=0;e<r;e++)t[e]instanceof Array?n=n.concat(o.flatten(t[e])):n.push(t[e]);return n},paralle:function(t,e,n){var r,i,s,a;if(t instanceof Array){for(a=[],i=0;i<t.length;i++)a.push(o.paralle(t[i],e[i],n));return a}if(t instanceof Object){if((r=t.getClass&&t.getClass())&&r.parse)t=t.valueOf(),e=e.valueOf(),a=o.paralle(t,e,n),a=r.parse(a);else for(s in a={},t)t.hasOwnProperty(s)&&e.hasOwnProperty(s)&&(a[s]=o.paralle(t[s],e[s],n));return a}return!1===isNaN(parseFloat(t))?n(t,e):a},parallelize:function(n){return function(t,e){return o.paralle(t,e,n)}}};return o.each(["String","Function","Array","Number","RegExp","Object","Boolean"],function(e){o["is"+e]=function(t){return Object.prototype.toString.apply(t)=="[object "+e+"]"}}),o}},_p[13]={value:function(t,e,n){var r=_p.r(16),i=_p.r(12),s=_p.r(11).createClass("ColorMatrixEffect",{base:r,constructor:function(t,e){this.callBase(r.NAME_COLOR_MATRIX),this.set("type",i.getValue(t,s.TYPE_MATRIX)),this.set("in",i.getValue(e,r.INPUT_SOURCE_GRAPHIC))}});return i.extend(s,{TYPE_MATRIX:"matrix",TYPE_SATURATE:"saturate",TYPE_HUE_ROTATE:"hueRotate",TYPE_LUMINANCE_TO_ALPHA:"luminanceToAlpha",MATRIX_ORIGINAL:"10000010000010000010".split("").join(" "),MATRIX_EMPTY:"00000000000000000000".split("").join(" ")}),s}},_p[14]={value:function(t,e,n){var r=_p.r(16),i=_p.r(12),s=_p.r(11).createClass("CompositeEffect",{base:r,constructor:function(t,e,n){this.callBase(r.NAME_COMPOSITE),this.set("operator",i.getValue(t,s.OPERATOR_OVER)),e&&this.set("in",e),n&&this.set("in2",n)}});return i.extend(s,{OPERATOR_OVER:"over",OPERATOR_IN:"in",OPERATOR_OUT:"out",OPERATOR_ATOP:"atop",OPERATOR_XOR:"xor",OPERATOR_ARITHMETIC:"arithmetic"}),s}},_p[15]={value:function(t,e,n){var r=_p.r(16),i=_p.r(12),s=_p.r(11).createClass("ConvolveMatrixEffect",{base:r,constructor:function(t,e){this.callBase(r.NAME_CONVOLVE_MATRIX),this.set("edgeMode",i.getValue(t,s.MODE_DUPLICATE)),this.set("in",i.getValue(e,r.INPUT_SOURCE_GRAPHIC))}});return i.extend(s,{MODE_DUPLICATE:"duplicate",MODE_WRAP:"wrap",MODE_NONE:"none"}),s}},_p[16]={value:function(t,e,n){var r=_p.r(67),i=_p.r(11).createClass("Effect",{constructor:function(t){this.node=r.createNode(t)},getId:function(){return this.node.id},setId:function(t){return this.node.id=t,this},set:function(t,e){return this.node.setAttribute(t,e),this},get:function(t){return this.node.getAttribute(t)},getNode:function(){return this.node},toString:function(){return this.node.getAttribute("result")||""}});return _p.r(12).extend(i,{NAME_GAUSSIAN_BLUR:"feGaussianBlur",NAME_OFFSET:"feOffset",NAME_COMPOSITE:"feComposite",NAME_COLOR_MATRIX:"feColorMatrix",NAME_CONVOLVE_MATRIX:"feConvolveMatrix",INPUT_SOURCE_GRAPHIC:"SourceGraphic",INPUT_SOURCE_ALPHA:"SourceAlpha",INPUT_BACKGROUND_IMAGE:"BackgroundImage",INPUT_BACKGROUND_ALPHA:"BackgroundAlpha",INPUT_FILL_PAINT:"FillPaint",INPUT_STROKE_PAINT:"StrokePaint"}),i}},_p[17]={value:function(t,e,n){var r=_p.r(16),i=_p.r(12);return _p.r(11).createClass("GaussianblurEffect",{base:r,constructor:function(t,e){this.callBase(r.NAME_GAUSSIAN_BLUR),this.set("stdDeviation",i.getValue(t,1)),this.set("in",i.getValue(e,r.INPUT_SOURCE_GRAPHIC))}})}},_p[18]={value:function(t,e,n){var r=_p.r(16),i=_p.r(12);return _p.r(11).createClass("OffsetEffect",{base:r,constructor:function(t,e,n){this.callBase(r.NAME_OFFSET),this.set("dx",i.getValue(t,0)),this.set("dy",i.getValue(e,0)),this.set("in",i.getValue(n,r.INPUT_SOURCE_GRAPHIC))}})}},_p[19]={value:function(t){return _p.r(11).createClass("EffectContainer",{base:_p.r(29),addEffect:function(t,e){return this.addItem.apply(this,arguments)},prependEffect:function(){return this.prependItem.apply(this,arguments)},appendEffect:function(){return this.appendItem.apply(this,arguments)},removeEffect:function(t){return this.removeItem.apply(this,arguments)},addEffects:function(){return this.addItems.apply(this,arguments)},setEffects:function(){return this.setItems.apply(this,arguments)},getEffect:function(){return this.getItem.apply(this,arguments)},getEffects:function(){return this.getItems.apply(this,arguments)},getFirstEffect:function(){return this.getFirstItem.apply(this,arguments)},getLastEffect:function(){return this.getLastItem.apply(this,arguments)},handleAdd:function(t,e){var n=this.getEffects().length,r=this.getItem(e+1);n!==e+1?this.node.insertBefore(t.getNode(),r.getNode()):this.node.appendChild(t.getNode())}})}},_p[20]={value:function(t,e,n){var i=_p.r(67),r=_p.r(11),s=r.createClass("Filter",{mixins:[_p.r(19)],constructor:function(t,e,n,r){this.node=i.createNode("filter"),void 0!==t&&this.set("x",t),void 0!==e&&this.set("y",e),void 0!==n&&this.set("width",n),void 0!==r&&this.set("height",r)},getId:function(){return this.id},setId:function(t){return this.node.id=t,this},set:function(t,e){return this.node.setAttribute(t,e),this},get:function(t){return this.node.getAttribute(t)},getNode:function(){return this.node}}),a=_p.r(60);return r.extendClass(a,{applyFilter:function(t){var e=t.get("id");return e&&this.node.setAttribute("filter","url(#"+e+")"),this}}),s}},_p[21]={value:function(t,e,n){var r=_p.r(17);return _p.r(11).createClass("GaussianblurFilter",{base:_p.r(20),constructor:function(t){this.callBase(),this.addEffect(new r(t))}})}},_p[22]={value:function(t,e,n){var r=_p.r(17),i=_p.r(16),s=_p.r(13),a=_p.r(28),o=_p.r(12),u=_p.r(14),h=_p.r(18);return _p.r(11).createClass("ProjectionFilter",{base:_p.r(20),constructor:function(t,e,n){this.callBase(),this.gaussianblurEffect=new r(t,i.INPUT_SOURCE_ALPHA),this.gaussianblurEffect.set("result","gaussianblur"),this.addEffect(this.gaussianblurEffect),this.offsetEffect=new h(e,n,this.gaussianblurEffect),this.offsetEffect.set("result","offsetBlur"),this.addEffect(this.offsetEffect),this.colorMatrixEffect=new s(s.TYPE_MATRIX,this.offsetEffect),this.colorMatrixEffect.set("values",s.MATRIX_ORIGINAL),this.colorMatrixEffect.set("result","colorOffsetBlur"),this.addEffect(this.colorMatrixEffect),this.compositeEffect=new u(u.OPERATOR_OVER,i.INPUT_SOURCE_GRAPHIC,this.colorMatrixEffect),this.addEffect(this.compositeEffect)},setColor:function(t){var e=null,n=[];if(o.isString(t)&&(t=a.parse(t)),!t)return this;e=s.MATRIX_EMPTY.split(" "),n.push(t.get("r")),n.push(t.get("g")),n.push(t.get("b"));for(var r=0,i=n.length;r<i;r++)e[5*r+3]=n[r]/255;return e[18]=t.get("a"),this.colorMatrixEffect.set("values",e.join(" ")),this},setOpacity:function(t){var e=this.colorMatrixEffect.get("values").split(" ");return e[18]=t,this.colorMatrixEffect.set("values",e.join(" ")),this},setOffset:function(t,e){this.setOffsetX(t),this.setOffsetY(e)},setOffsetX:function(t){this.offsetEffect.set("dx",t)},setOffsetY:function(t){this.offsetEffect.set("dy",t)},setDeviation:function(t){this.gaussianblurEffect.set("stdDeviation",t)}})}},_p[23]={value:function(t,e,n){return _p.r(11).createClass("Bezier",{mixins:[_p.r(51)],base:_p.r(46),constructor:function(t){this.callBase(),t=t||[],this.changeable=!0,this.setBezierPoints(t)},getBezierPoints:function(){return this.getPoints()},setBezierPoints:function(t){return this.setPoints(t)},onContainerChanged:function(){this.changeable&&this.update()},update:function(){var t=null,e=this.getBezierPoints();if(!(e.length<2)){(t=this.getDrawer()).clear();var n=e[0].getVertex(),r=null,i=null;t.moveTo(n.x,n.y);for(var s=1,a=e.length;s<a;s++)n=e[s].getVertex(),i=e[s].getBackward(),r=e[s-1].getForward(),t.bezierTo(r.x,r.y,i.x,i.y,n.x,n.y);return this}}})}},_p[24]={value:function(t,e,n){var r=_p.r(63),s=_p.r(73),i=_p.r(11).createClass("BezierPoint",{constructor:function(t,e,n){this.vertex=new r(t,e),this.forward=new r(t,e),this.backward=new r(t,e),this.setSmooth(void 0===n||n),this.setSymReflaction(!0)},clone:function(){var t=new i,e=null;return e=this.getVertex(),t.setVertex(e.x,e.y),e=this.getForward(),t.setForward(e.x,e.y),e=this.getBackward(),t.setBackward(e.x,e.y),t.setSymReflaction(this.isSymReflaction),t.setSmooth(this.isSmooth()),t},setVertex:function(t,e){return this.vertex.setPoint(t,e),this.update(),this},moveTo:function(t,e){var n=this.forward.getPoint(),r=this.backward.getPoint(),i=this.vertex.getPoint(),s=t-i.x,a=e-i.y;this.forward.setPoint(n.x+s,n.y+a),this.backward.setPoint(r.x+s,r.y+a),this.vertex.setPoint(t,e),this.update()},setForward:function(t,e){return this.forward.setPoint(t,e),this.smooth&&this.updateAnother(this.forward,this.backward),this.update(),this.lastControlPointSet=this.forward,this},setBackward:function(t,e){return this.backward.setPoint(t,e),this.smooth&&this.updateAnother(this.backward,this.forward),this.update(),this.lastControlPointSet=this.backward,this},setSymReflaction:function(t){return this.symReflaction=t,this.smooth&&this.setSmooth(!0),this},isSymReflaction:function(){return this.symReflaction},updateAnother:function(t,e){var n=this.getVertex(),r=s.fromPoints(t.getPoint(),n),i=s.fromPoints(n,e.getPoint());return i=r.normalize(this.isSymReflaction()?r.length():i.length()),e.setPoint(n.x+i.x,n.y+i.y),this},setSmooth:function(t){var e;return this.smooth=!!t,this.smooth&&(e=this.lastControlPointSet)&&this.updateAnother(e,e==this.forward?this.backward:this.forward),this},isSmooth:function(){return this.smooth},getVertex:function(){return this.vertex.getPoint()},getForward:function(){return this.forward.getPoint()},getBackward:function(){return this.backward.getPoint()},update:function(){if(!this.container)return this;this.container.update&&this.container.update(this)}});return i}},_p[25]={value:function(t,e,n){var u=_p.r(11).createClass("Box",{constructor:function(t,e,n,r){var i=t;i&&"object"==typeof i&&(t=i.x,e=i.y,n=i.width,r=i.height),n<0&&(t-=n=-n),r<0&&(e-=r=-r),this.x=t||0,this.y=e||0,this.width=n||0,this.height=r||0,this.left=this.x,this.right=this.x+this.width,this.top=this.y,this.bottom=this.y+this.height,this.cx=this.x+this.width/2,this.cy=this.y+this.height/2},getRangeX:function(){return[this.left,this.right]},getRangeY:function(){return[this.top,this.bottom]},merge:function(t){if(this.isEmpty())return new u(t.x,t.y,t.width,t.height);var e=Math.min(this.left,t.left),n=Math.max(this.right,t.right),r=Math.min(this.top,t.top),i=Math.max(this.bottom,t.bottom);return new u(e,r,n-e,i-r)},intersect:function(t){!t instanceof u&&(t=new u(t));var e=Math.max(this.left,t.left),n=Math.min(this.right,t.right),r=Math.max(this.top,t.top),i=Math.min(this.bottom,t.bottom);return n<e||i<r?new u:new u(e,r,n-e,i-r)},expand:function(t,e,n,r){if(arguments.length<1)return new u(this);arguments.length<2&&(e=t),arguments.length<3&&(n=t),arguments.length<4&&(r=e);var i=this.left-r,s=this.top-t,a=this.width+e,o=this.height+t;return new u(i,s,a,o)},valueOf:function(){return[this.x,this.y,this.width,this.height]},toString:function(){return this.valueOf().join(" ")},isEmpty:function(){return!this.width||!this.height}});return u.parse=function(t){return"string"==typeof t?u.parse(t.split(/[\s,]+/).map(parseFloat)):t instanceof Array?new u(t[0],t[1],t[2],t[3]):"x"in t?new u(t):null},u}},_p[26]={value:function(t,e,n){return _p.r(11).createClass("Circle",{base:_p.r(32),constructor:function(t,e,n){this.callBase(t,t,e,n)},getRadius:function(){return this.getRadiusX()},setRadius:function(t){return this.callBase(t,t)}})}},_p[27]={value:function(t,e,n){var r=_p.r(11),i=_p.r(60),s=r.createClass("Clip",{base:i,mixins:[_p.r(61)],constructor:function(t){this.callBase("clipPath",t)},clip:function(t){return t.getNode().setAttribute("clip-path",this),this}});return r.extendClass(i,{clipWith:function(t){return t instanceof i&&(t=new s(t.getPaper()).addShape(t)),t.clip(this),this}}),s}},_p[28]={value:function(t,e,n){var s=_p.r(12),r=_p.r(64),i={},a=_p.r(11).createClass("Color",{constructor:function(){var t=null;"string"==typeof arguments[0]?null===(t=i.parseToValue(arguments[0]))&&(t={r:0,g:0,b:0,h:0,s:0,l:0,a:1}):(t={r:0|arguments[0],g:0|arguments[1],b:0|arguments[2],a:void 0===arguments[3]?1:parseFloat(arguments[3])},t=i.overflowFormat(t),t=s.extend(t,i.rgbValueToHslValue(t))),this._color=t},set:function(t,e){if(!a._MAX_VALUE[t])throw new Error("Color set(): Illegal parameter");return"a"!==t&&(e=Math.floor(e)),"h"==t&&(e=(e+360)%360),this._color[t]=Math.max(a._MIN_VALUE[t],Math.min(a._MAX_VALUE[t],e)),-1!=="rgb".indexOf(t)?this._color=s.extend(this._color,i.rgbValueToHslValue(this._color)):-1!=="hsl".indexOf(t)&&(this._color=s.extend(this._color,i.hslValueToRGBValue(this._color))),this},inc:function(t,e){return e=this.get(t)+e,e="h"==t?(e+360)%360:(e=Math.min(a._MAX_VALUE[t],e),Math.max(a._MIN_VALUE[t],e)),this.clone().set(t,e)},dec:function(t,e){return this.inc(t,-e)},clone:function(){return new a(this.toRGBA())},get:function(t){return a._MAX_VALUE[t]?this._color[t]:null},getValues:function(){return s.clone(this._color)},valueOf:function(){return this.getValues()},toRGB:function(){return i.toString(this._color,"rgb")},toRGBA:function(){return i.toString(this._color,"rgba")},toHEX:function(){return i.toString(this._color,"hex")},toHSL:function(){return i.toString(this._color,"hsl")},toHSLA:function(){return i.toString(this._color,"hsla")},toString:function(){return 1===this._color.a?this.toRGB():this.toRGBA()}});return s.extend(a,{_MAX_VALUE:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},_MIN_VALUE:{r:0,g:0,b:0,h:0,s:0,l:0,a:0},R:"r",G:"g",B:"b",H:"h",S:"s",L:"l",A:"a",parse:function(t){var e;return s.isString(t)&&(e=i.parseToValue(t)),s.isObject(t)&&"r"in t&&(e=t),null===e?new a:new a(e.r,e.g,e.b,e.a)},createHSL:function(t,e,n){return a.createHSLA(t,e,n,1)},createHSLA:function(t,e,n,r){var i=null;return i=["hsla("+t,e+="%",n+="%",r+")"],a.parse(i.join(", "))},createRGB:function(t,e,n){return a.createRGBA(t,e,n,1)},createRGBA:function(t,e,n,r){return new a(t,e,n,r)}}),s.extend(i,{parseToValue:function(t){var e={};if(t=r.EXTEND_STANDARD[t]||r.COLOR_STANDARD[t]||t,/^#([0-9a-f]{3}|[0-9a-f]{6})$/i.test(t))e=i.hexToValue(t);else if(/^(rgba?)/i.test(t))e=i.rgbaToValue(t);else{if(!/^(hsla?)/i.test(t))return null;e=i.hslaToValue(t)}return i.overflowFormat(e)},hexToValue:function(n){var r={};return/^#([0-9a-f]{3}|[0-9a-f]{6})$/i.test(n)?(n=RegExp.$1.split(""),s.each(["r","g","b"],function(t,e){3===n.length?r[t]=i.toNumber(n[e]+n[e]):r[t]=i.toNumber(n[2*e]+n[2*e+1])}),(r=s.extend(r,i.rgbValueToHslValue(r))).a=1,r):null},rgbaToValue:function(n){var r={},t=!1;return/^(rgba?)/i.test(n)?(t=4===RegExp.$1.length,n=n.replace(/^rgba?/i,"").replace(/\s+/g,"").replace(/[^0-9,.]/g,"").split(","),s.each(["r","g","b"],function(t,e){r[t]=0|n[e]}),(r=s.extend(r,i.rgbValueToHslValue(r))).a=t?parseFloat(n[3]):1,r):null},hslaToValue:function(t){var e={},n=!1;return/^(hsla?)/i.test(t)?(n=4===RegExp.$1.length,t=t.replace(/^hsla?/i,"").replace(/\s+/g,"").replace(/[^0-9,.]/g,"").split(","),e.h=0|t[0],e.s=0|t[1],e.l=0|t[2],e=s.extend(e,i.hslValueToRGBValue(e)),(e=i.hslValueToRGBValue(e)).a=n?parseFloat(t[3]):1,e):null},hslValueToRGBValue:function(t){function e(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+6*(e-t)*n:2*n<1?e:3*n<2?t+6*(2/3-n)*(e-t):t}var n=null,r=null,i={};return(t=s.extend({},t)).h=t.h/360,t.s=t.s/100,t.l=t.l/100,0===t.s?i.r=i.g=i.b=t.l:(n=t.l<.5?t.l*(1+t.s):t.l+t.s-t.l*t.s,r=2*t.l-n,i.r=e(r,n,t.h+1/3),i.g=e(r,n,t.h),i.b=e(r,n,t.h-1/3)),i.r=Math.min(Math.round(255*i.r),255),i.g=Math.min(Math.round(255*i.g),255),i.b=Math.min(Math.round(255*i.b),255),i},rgbValueToHslValue:function(t){var e,n,r={};return(t=s.extend({},t)).r=t.r/255,t.g=t.g/255,t.b=t.b/255,(e=Math.max(t.r,t.g,t.b))===(n=Math.min(t.r,t.g,t.b))?r.h=0:e===t.r?t.g>=t.b?r.h=60*(t.g-t.b)/(e-n):r.h=60*(t.g-t.b)/(e-n)+360:e===t.g?r.h=60*(t.b-t.r)/(e-n)+120:e===t.b&&(r.h=60*(t.r-t.g)/(e-n)+240),r.l=(e+n)/2,0===r.l||e===n?r.s=0:0<r.l&&r.l<=.5?r.s=(e-n)/(e+n):r.s=(e-n)/(2-e-n),r.h=Math.round(r.h),r.s=Math.round(100*r.s),r.l=Math.round(100*r.l),r},toString:function(e,t){var n=[];return e=s.extend({},e),-1!==t.indexOf("hsl")&&(e.s+="%",e.l+="%"),"hex"!==t?(s.each(t.split(""),function(t){n.push(e[t])}),(t+"("+n.join(", ")+")").toLowerCase()):(n.push(i.toHexValue(+e.r)),n.push(i.toHexValue(+e.g)),n.push(i.toHexValue(+e.b)),("#"+n.join("")).toLowerCase())},toNumber:function(t){return 0|Number("0x"+t)},toHexValue:function(t){var e=t.toString(16);return 1===e.length?"0"+e:e},overflowFormat:function(t){var e=s.extend({},t);return s.each("rgba".split(""),function(t){e.hasOwnProperty(t)&&(e[t]=Math.min(a._MAX_VALUE[t],e[t]),e[t]=Math.max(a._MIN_VALUE[t],e[t]))}),e}}),a}},_p[29]={value:function(t,e,n){function s(){return this.container.removeItem(this),this}return _p.r(11).createClass("Container",{getItems:function(){return this.items||(this.items=[])},getItem:function(t){return this.getItems()[t]},getFirstItem:function(){return this.getItem(0)},getLastItem:function(){return this.getItem(this.getItems().length-1)},indexOf:function(t){return this.getItems().indexOf(t)},eachItem:function(t){var e,n=this.getItems(),r=n.length;for(e=0;e<r;e++)t.call(this,e,n[e]);return this},addItem:function(t,e,n){var r=this.getItems(),i=r.length;return~r.indexOf(t)||(0<=e&&e<i||(e=i),r.splice(e,0,t),"object"==typeof t&&(t.container=this,t.remove=s),this.handleAdd(t,e),n||this.onContainerChanged("add",[t])),this},addItems:function(t){for(var e=0,n=t.length;e<n;e++)this.addItem(t[e],-1,!0);return this.onContainerChanged("add",t),this},setItems:function(t){return this.clear().addItems(t)},appendItem:function(t){return this.addItem(t)},prependItem:function(t){return this.addItem(t,0)},removeItem:function(t,e){if("number"!=typeof t)return this.removeItem(this.indexOf(t));var n=this.getItems(),r=(n.length,n[t]);return void 0===r||(n.splice(t,1),r.container&&delete r.container,r.remove&&delete r.remove,this.handleRemove(r,t),e||this.onContainerChanged("remove",[r])),this},clear:function(){for(var t,e=[];t=this.getFirstItem();)e.push(t),this.removeItem(0,!0);return this.onContainerChanged("remove",e),this},onContainerChanged:function(t,e){},handleAdd:function(t,e){},handleRemove:function(t,e){}})}},_p[30]={value:function(t,e,n){var c=_p.r(12),u={getCurvePanLines:function(t,e){var n=u.getCenterPoints(t),r=u.getPanLine(t.length,n);return u.getMovedPanLines(t,r,e)},getCenterPoints:function(t){for(var e={},n=0,r=0,i=t.length;n<i;n++)e[n+","+(r=n===i-1?0:n+1)]={x:(t[n].x+t[r].y)/2,y:(t[n].x+t[r].y)/2};return e},getPanLine:function(t,e){for(var n,r={},i=null,s=0;s<t;s++){var a,o;a=e[s+","+(i=n=(s+1)%t)],o=e[(s=n)+","+(n=(s+1)%t)],r[i]={points:[{x:a.x,y:a.y},{x:o.x,y:o.y}],center:{x:(a.x+o.x)/2,y:(a.y+o.y)/2}},s=(i+t-1)%t}return r},getMovedPanLines:function(t,i,h){var s={};return c.each(t,function(t,e){var n=i[e],r=n.center,a=r.x-t.x,o=r.y-t.y,u=s[e]={points:[],center:{x:t.x,y:t.y}};c.each(n.points,function(t,e){var n={x:t.x-a,y:t.y-o},r=u.center,i=n.x-r.x,s=n.y-r.y;n.x=r.x+h*i,n.y=r.y+h*s,u.points.push(n)})}),s}};return _p.r(11).createClass("Curve",{base:_p.r(46),mixins:[_p.r(51)],constructor:function(t,e){this.callBase(),this.setPoints(t||[]),this.closeState=!!e,this.changeable=!0,this.smoothFactor=1,this.update()},onContainerChanged:function(){this.changeable&&this.update()},setSmoothFactor:function(t){return this.smoothFactor=t<0?0:t,this.update(),this},getSmoothFactor:function(){return this.smoothFactor},update:function(){var t,e=this.getPoints(),n=this.getDrawer(),r=null,i=null,s=null;if(n.clear(),0===e.length)return this;if(n.moveTo(e[0]),1===e.length)return this;if(2===e.length)return n.lineTo(e[1]),this;t=u.getCurvePanLines(e,this.getSmoothFactor());for(var a=1,o=e.length;a<o;a++)r=t[a].center,i=this.closeState||a!=o-1?t[a].points[0]:t[a].center,s=this.closeState||1!=a?t[a-1].points[1]:t[a-1].center,n.bezierTo(s.x,s.y,i.x,i.y,r.x,r.y);return this.closeState&&(r=t[0].center,i=t[0].points[0],s=t[e.length-1].points[1],n.bezierTo(s.x,s.y,i.x,i.y,r.x,r.y)),this},close:function(){return this.closeState=!0,this.update()},open:function(){return this.closeState=!1,this.update()},isClose:function(){return!!this.closeState}})}},_p[31]={value:function(t,e,n){return _p.r(11).createClass("Data",{constructor:function(){this._data={}},setData:function(t,e){return this._data[t]=e,this},getData:function(t){return this._data[t]},removeData:function(t){return delete this._data[t],this}})}},_p[32]={value:function(t,e,n){_p.r(12);var r=_p.r(50);return _p.r(11).createClass("Ellipse",{base:_p.r(46),constructor:function(t,e,n,r){this.callBase(),this.rx=t||0,this.ry=e||0,this.cx=n||0,this.cy=r||0,this.update()},update:function(){var t=this.rx,e=this.ry,n=this.cx+t,r=this.cx-t,i=this.cy,s=this.getDrawer();return s.clear(),s.moveTo(n,i),s.arcTo(t,e,0,1,1,r,i),s.arcTo(t,e,0,1,1,n,i),this},getRadius:function(){return{x:this.rx,y:this.ry}},getRadiusX:function(){return this.rx},getRadiusY:function(){return this.ry},getCenter:function(){return new r(this.cx,this.cy)},getCenterX:function(){return this.cx},getCenterY:function(){return this.cy},setRadius:function(t,e){return this.rx=t,this.ry=e,this.update()},setRadiusX:function(t){return this.rx=t,this.update()},setRadiusY:function(t){return this.ry=t,this.update()},setCenter:function(t,e){if(1==arguments.length){var n=r.parse(t);t=n.x,e=n.y}return this.cx=t,this.cy=e,this.update()},setCenterX:function(t){return this.cx=t,this.update()},setCenterY:function(t){return this.cy=t,this.update()}})}},_p[33]={value:function(t,e,n){function r(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),n}r.prototype=window.Event.prototype,window.CustomEvent=r;var u=_p.r(12),o=_p.r(62),h={},c={},i=0;function s(t,e,n){return n=!!n,u.isString(t)&&(t=t.match(/\S+/g)),u.each(t,function(t){(function(t,r,e,i){var s=this._EVNET_UID,a=this;h[s]||(h[s]={});h[s][r]||(h[s][r]=function(n){n=new o(n||window.event),u.each(c[s][r],function(t){var e;return t&&(e=t.call(a,n),i&&a.off(r,t)),e},a)});c[s]||(c[s]={});c[s][r]?c[s][r].push(e):(c[s][r]=[e],t&&"on"+r in t&&function(t,e,n){t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent("on"+e,n)}(t,r,h[s][r]))}).call(this,this.node,t,e,n)},this),this}return _p.r(11).createClass("EventHandler",{constructor:function(){this._EVNET_UID=++i},addEventListener:function(t,e){return s.call(this,t,e,!1)},addOnceEventListener:function(t,e){return s.call(this,t,e,!0)},removeEventListener:function(t,e){return function(t,n){var e,r,i,s=null,a=this._EVNET_UID,o=void 0===n;return s=c[a][t],o||(o=!0,u.each(s,function(t,e){t===n?delete s[e]:o=!1})),o&&(e=this.node,r=t,i=h[a][t],e.removeEventListener?e.removeEventListener(r,i,!1):e.detachEvent(r,i),delete c[a][t],delete h[a][t]),this}.call(this,t,e)},on:function(t,e){return this.addEventListener.apply(this,arguments)},once:function(t,e){return this.addOnceEventListener.apply(this,arguments)},off:function(){return this.removeEventListener.apply(this,arguments)},fire:function(t,e){return this.trigger.apply(this,arguments)},trigger:function(t,e){var n,r,i,s,a,o;return r=t,i=e,a=null,(o=h[(n=this)._EVNET_UID])&&(a=o[r])&&(s=u.extend({type:r,target:n},i||{}),a.call(n,s)),this}})}},_p[34]={value:function(t){var e=_p.r(12),o=_p.r(50),a=_p.r(73),n=_p.r(43),v={},r=/([achlmrqstvz])[\s,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\s]*,?\s*)+)/gi,u=/(-?\d*\.?\d*(?:e[\-+]?\d+)?)\s*,?\s*/gi,h={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0};function c(t){var e,n,r,i,s;for(e=[],n=0;n<t.length;n++)for(i=t[n],e.push(s=[]),r=0;r<i.length;r++)s.push(i[r]);return t.isUniform&&(e.isUniform=!0),t.isAbsolute&&(e.isAbsolute=!0),t.isCurve&&(e.isCurve=!0),e}function i(s,a,o){return function t(){var e=Array.prototype.slice.call(arguments,0),n=e.join("␀"),r=t.cache=t.cache||{},i=t.count=t.count||[];return r.hasOwnProperty(n)?function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return t.push(t.splice(n,1)[0])}(i,n):(1e3<=i.length&&delete r[i.shift()],i.push(n),r[n]=s.apply(a,e)),o?o(r[n]):r[n]}}function j(t,e,n,r,i,s,a,o,u,h){function c(t,e,n){return{x:t*U.cos(n)-e*U.sin(n),y:t*U.sin(n)+e*U.cos(n)}}var l,f,p,d,g,_,m,v,y,b,x,w,A,C,P,T,M,E,O,S,R,I,N,B,k,V,L,F,D,U=Math,H=U.PI,G=Math.abs,K=120*H/180,X=H/180*(+i||0),z=[];if(h?(b=h[0],x=h[1],v=h[2],y=h[3]):(t=(l=c(t,e,-X)).x,e=l.y,o=(l=c(o,u,-X)).x,u=l.y,U.cos(H/180*i),U.sin(H/180*i),1<(f=(p=(t-o)/2)*p/(n*n)+(d=(e-u)/2)*d/(r*r))&&(n*=f=U.sqrt(f),r*=f),g=n*n,_=r*r,v=(m=(s==a?-1:1)*U.sqrt(G((g*_-g*d*d-_*p*p)/(g*d*d+_*p*p))))*n*d/r+(t+o)/2,y=m*-r*p/n+(e+u)/2,b=U.asin(((e-y)/r).toFixed(9)),x=U.asin(((u-y)/r).toFixed(9)),(b=t<v?H-b:b)<0&&(b=2*H+b),(x=o<v?H-x:x)<0&&(x=2*H+x),a&&x<b&&(b-=2*H),!a&&b<x&&(x-=2*H)),G(w=x-b)>K&&(A=x,C=o,P=u,x=b+K*(a&&b<x?1:-1),z=j(o=v+n*U.cos(x),u=y+r*U.sin(x),n,r,i,0,a,C,P,[x,A,v,y])),w=x-b,T=U.cos(b),M=U.sin(b),E=U.cos(x),O=U.sin(x),k=[o+(R=4/3*n*(S=U.tan(w/4)))*O,u-(I=4/3*r*S)*E],V=[o,u],(B=[t+R*M,e-I*T])[0]=2*(N=[t,e])[0]-B[0],B[1]=2*N[1]-B[1],h)return[B,k,V].concat(z);for(L=[],F=0,D=(z=[B,k,V].concat(z).join().split(",")).length;F<D;F++)L[F]=F%2?c(z[F-1],z[F],X).y:c(z[F],z[F+1],X).x;return L}function d(t,e,n,r,i,s){return[1/3*t+2/3*n,1/3*e+2/3*r,1/3*i+2/3*n,1/3*s+2/3*r,i,s]}function l(t,e){var n,r=(n=e||.5,function(t,e){return t+n*(e-t)}),i=t[0],s=t[1],a=t[2],o=t[3],u=t[4],h=t[5],c=t[6],l=t[7],f=r(i,a),p=r(s,o),d=r(a,u),g=r(o,h),_=r(u,c),m=r(h,l),v=r(f,d),y=r(p,g),b=r(d,_),x=r(g,m),w=r(v,b),A=r(y,x);return[[i,s,f,p,v,y,w,A],[w,A,b,x,_,m,c,l]]}v.pathToString=function(t){return"string"==typeof(t=t||this)?t:t instanceof Array?(t=e.flatten(t)).join(",").replace(/,?([achlmqrstvxz]),?/gi,"$1"):void 0},v.parsePathString=i(function(t){var s=[];return t.replace(r,function(t,e,n){var r=[],i=e.toLowerCase();if(n.replace(u,function(t,e){e&&r.push(+e)}),"m"==i&&2<r.length&&(s.push([e].concat(r.splice(0,2))),i="l",e="m"==e?"l":"L"),"r"==i)s.push([e].concat(r));else for(;r.length>=h[i]&&(s.push([e].concat(r.splice(0,h[i]))),h[i]););}),s.isUniform=!0,s.toString=v.pathToString,s}),v.pathToAbsolute=i(function(t){var e,n,r,i,s,a,o,u,h=t.isUniform?t:v.parsePathString(v.pathToString(t)),c=[],l=0,f=0,p=0,d=0,g=0;for("M"==h[0][0]&&(p=l=+h[0][1],d=f=+h[0][2],g++,c[0]=["M",l,f]),r=g,a=h.length;r<a;r++){if(c.push(e=[]),(n=h[r])[0]!=n[0].toUpperCase())switch(e[0]=n[0].toUpperCase(),e[0]){case"A":e[1]=n[1],e[2]=n[2],e[3]=n[3],e[4]=n[4],e[5]=n[5],e[6]=+(n[6]+l),e[7]=+(n[7]+f);break;case"V":e[1]=+n[1]+f;break;case"H":e[1]=+n[1]+l;break;case"M":p=+n[1]+l,d=+n[2]+f;break;default:for(i=1,o=n.length;i<o;i++)e[i]=+n[i]+(i%2?l:f)}else for(s=0,u=n.length;s<u;s++)e[s]=n[s];switch(e[0]){case"Z":l=p,f=d;break;case"H":l=e[1];break;case"V":f=e[1];break;case"M":p=e[e.length-2],d=e[e.length-1];break;default:l=e[e.length-2],f=e[e.length-1]}}return c.isUniform=!0,c.isAbsolute=!0,c.toString=v.pathToString,c}),v.pathToCurve=i(function(t){var e,n,r,i,s,a,o,u,h,c,l,f,p=[];for(t.isAbsolute||(t=v.pathToAbsolute(t)),e=0;e<t.length;e++)if(r=t[e][0],i=t[e].slice(1),"M"!=r){switch("Z"==r&&(u=!0,r="L",i=s),o=i.slice(i.length-2),"H"==r&&(o=[i[0],a[1]],r="L"),"V"==r&&(o=[a[0],i[0]],r="L"),"S"!=r&&"T"!=r||(c=[a[0]+(a[0]-h[0]),a[1]+(a[1]-h[1])]),r){case"L":l=a,f=o;break;case"C":l=i.slice(0,2),f=i.slice(2,4);break;case"S":l=c.slice(),f=i.slice(0,2);break;case"Q":h=i.slice(0,2),l=(i=d.apply(null,a.concat(i))).slice(0,2),f=i.slice(2,4);break;case"T":l=(i=d.apply(null,a.concat(c).concat(i))).slice(0,2),f=i.slice(2,4);break;case"A":for(i=j.apply(null,a.concat(i)),n=0;n in i;)l=i.slice(n,n+2),f=i.slice(n+2,n+4),o=i.slice(n+4,n+6),p.push(["C"].concat(l).concat(f).concat(o)),n+=6}"A"!=r&&p.push(["C"].concat(l).concat(f).concat(o)),a=o,"Q"!=r&&(h=f),u&&(p.push(["Z"]),u=!1)}else s=h=a=i,p.push(t[e]);return p.isUniform=!0,p.isAbsolute=!0,p.isCurve=!0,p.toString=v.pathToString,p}),v.cutBezier=i(l),v.subBezier=function(t,e,n){var r=l(t,e)[0];return n?l(r,n/e)[1]:r},v.pointAtBezier=function(t,e){var n=l(t,e)[0],r=o.parse(n.slice(6)),i=o.parse(n.slice(4,2)),s=a.fromPoints(i,r);return r.tan=0===e?v.pointAtBezier(t,.01).tan:s.normalize(),r},v.bezierLength=i(function(t){function e(t){var e=u*Math.pow(t,4)+h*Math.pow(t,3)+c*Math.pow(t,2)+l*t+f;return e<0&&(e=0),Math.pow(e,.5)}var n,r,i,s,a,o,u,h,c,l,f;return n=-3*t[0]+9*t[2]-9*t[4]+3*t[6],r=6*t[0]-12*t[2]+6*t[4],i=-3*t[0]+3*t[2],s=-3*t[1]+9*t[3]-9*t[5]+3*t[7],a=6*t[1]-12*t[3]+6*t[5],o=-3*t[1]+3*t[3],u=Math.pow(n,2)+Math.pow(s,2),h=2*(n*r+s*a),c=2*(n*i+s*o)+Math.pow(r,2)+Math.pow(a,2),l=2*(r*i+a*o),f=Math.pow(i,2)+Math.pow(o,2),(e(0)+e(1)+4*(e(.125)+e(.375)+e(.625)+e(.875))+2*(e(.25)+e(.5)+e(.75)))/24});var y=i(function(t){var e,n,r,i,s,a,o;for(a=[],e=o=0,n=t.length;e<n;e++)"M"!=(r=t[e])[0]?"Z"!=r[0]?(s=v.bezierLength(i.concat(r.slice(1))),a.push([o,o+s]),o+=s,i=r.slice(4)):a.push(null):(i=r.slice(1),a.push(null));return a.totalLength=o,a});v.subPath=function(t,e,n){var r;if(r=e-(n=n||0),1<(e=(n-=0|n)+(r-=0|r)))return v.subPath(t,1,n).concat(v.subPath(t,e-1));t.isCurve||(t=v.pathToCurve(t));var i,s,a,o,u,h,c,l,f,p=y(t),d=p.totalLength,g=d*e,_=d*(n||0),m=[];for(i=0,s=t.length;i<s;i++)if("M"!=t[i][0]){if("Z"!=t[i][0])if(a=p[i][0],u=(o=p[i][1])-a,c=h.concat(t[i].slice(1)),o<_)h=c.slice(c.length-2);else{if(a<=_)f=!0,h=(l=v.subBezier(c,Math.min((g-a)/u,1),(_-a)/u)).slice(0,2),m.push(["M"].concat(l.slice(0,2))),m.push(["C"].concat(l.slice(2)));else if(o<=g)m.push(t[i].slice());else{if(!(a<=g))break;l=v.subBezier(c,(g-a)/u),m.push(["C"].concat(l.slice(2))),f=!1}h=c.slice(c.length-2)}}else h=t[i].slice(1),f&&m.push(t[i].slice());return m.isAbsolute=!0,m.isCurve=!0,m.isUniform=!0,m.toString=v.pathToString,m},v.pointAtPath=function(t,e){t.isCurve||(t=v.pathToCurve(t));var n=v.subPath(t,e),r="Z"==n[n.length-1][0]?n[n.length-2]:n[n.length-1];r=r.slice(1);var i=o.parse(r.slice(4)),s=o.parse(r.slice(2,4));return i.tan=a.fromPoints(s,i).normalize(),i},v.pathLength=i(function(t){return t.isCurve||(t=v.pathToCurve(t)),y(t).totalLength}),v.pathKeyPoints=i(function(t){var e,n,r;for(t.isCurve||(t=v.pathToCurve(t)),r=[],e=0,n=t.length;e<n;e++)"z"!=t[e][0]&&r.push(t[e].slice(t[e].length-2));return r});var f=i(function(t,e){t.isCurve||(t=v.pathToCurve(t)),e.isCurve||(e=v.pathToCurve(e));var n=c(t),r=c(e);function i(t,e){return t[e||t.i]&&t[e||t.i][0]}function s(t,e){var n,r,i=(n=t)[(r=e)||n.i]&&n[r||n.i].slice(1);return i&&i.slice(-2)}function a(t){return"Z"==i(t)&&(t.splice(t.i,1),1)}function o(t){return"M"==i(t)&&(t.o.splice(t.o.i,0,["M"].concat(s(t.o,t.o.i-1))),t.i++,t.o.i++,1)}function u(t){for(var e,n=1;!e;)e=s(t,t.length-n++);for(t.o.i=t.i;t.length<t.o.length;)a(t.o)||o(t.o)||(t.push(["C"].concat(e).concat(e).concat(e)),t.i++,t.o.i++)}for(n.i=0,r.i=0,(n.o=r).o=n;n.i<n.length&&r.i<r.length;)a(n)||a(r)||i(n)!=i(r)&&(o(n)||o(r))||(n.i++,r.i++);return n.i==n.length&&u(n),r.i==r.length&&u(r),delete n.i,delete n.o,delete r.i,delete r.o,[n,r]});return v.alignCurve=f,v.pathTween=function(t,e,n){if(0===n)return t;if(1===n)return e;var r,i,s,a=f(t,e),o=[];for(t=a[0],e=a[1],i=0;i<t.length;i++)for(o.push(r=[]),r.push(t[i][0]),s=1;s<t[i].length;s++)r.push(t[i][s]+n*(e[i][s]-t[i][s]));return o.isUniform=o.isCurve=o.isAbsolute=!0,o},v.transformPath=i(function(t,e){var n,r,i,s,a;for(t.isCurve||(t=v.pathToCurve(t)),s=[],n=0,r=t.length;n<r;n++)for(s.push([t[n][0]]),i=1;i<t[n].length;i+=2)a=t[n].slice(i,i+2),a=e.transformPoint(o.parse(a)),s.push(a);return s}),_p.r(11).extendClass(n,{transformPath:function(t){return v.transformPath(t,this)}}),v}},_p[35]={value:function(t,e,n){var i=_p.r(67),r=_p.r(58),s=_p.r(28);return _p.r(11).createClass("GradientBrush",{base:r,constructor:function(t,e){this.callBase(t,e),this.stops=[]},addStop:function(t,e,n){var r=i.createNode("stop");return e instanceof s||(e=s.parse(e)),void 0===n&&(n=e.get("a")),r.setAttribute("offset",t),r.setAttribute("stop-color",e.toRGB()),n<1&&r.setAttribute("stop-opacity",n),this.node.appendChild(r),this}})}},_p[36]={value:function(t,e,n){var r=_p.r(61);return _p.r(11).createClass("Group",{mixins:[r],base:_p.r(60),constructor:function(){this.callBase("g")}})}},_p[37]={value:function(t,e,n){var r=_p.r(61);return _p.r(11).createClass("HyperLink",{mixins:[r],base:_p.r(60),constructor:function(t){this.callBase("a"),this.setHref(t)},setHref:function(t){return this.node.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t),this},getHref:function(){return this.node.getAttributeNS("xlink:href")},setTarget:function(t){return this.node.setAttribute("target",t),this},getTarget:function(){return this.node.getAttribute("target")}})}},_p[38]={value:function(t,e,n){return _p.r(11).createClass("Image",{base:_p.r(60),constructor:function(t,e,n,r,i){this.callBase("image"),this.url=t,this.width=e||0,this.height=n||0,this.x=r||0,this.y=i||0,this.update()},update:function(){return this.node.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",this.url),this.node.setAttribute("x",this.x),this.node.setAttribute("y",this.y),this.node.setAttribute("width",this.width),this.node.setAttribute("height",this.height),this},setUrl:function(t){return this.url=""===t?null:t,this.update()},getUrl:function(){return this.url},setWidth:function(t){return this.width=t,this.update()},getWidth:function(){return this.width},setHeight:function(t){return this.height=t,this.update()},getHeight:function(){return this.height},setX:function(t){return this.x=t,this.update()},getX:function(){return this.x},setY:function(t){return this.y=t,this.update()},getY:function(){return this.y}})}},_p[39]={value:function(t,e,n){return _p.r(11).createClass("Line",{base:_p.r(46),constructor:function(t,e,n,r){this.callBase(),this.point1={x:t||0,y:e||0},this.point2={x:n||0,y:r||0},this.update()},setPoint1:function(t,e){return this.point1.x=t,this.point1.y=e,this.update()},setPoint2:function(t,e){return this.point2.x=t,this.point2.y=e,this.update()},getPoint1:function(){return{x:this.point1.x,y:this.point1.y}},getPoint2:function(){return{x:this.point2.x,y:this.point2.y}},update:function(){var t=this.getDrawer();return t.clear(),t.moveTo(this.point1.x,this.point1.y),t.lineTo(this.point2.x,this.point2.y),this}})}},_p[40]={value:function(t,e,n){_p.r(67);var r=_p.r(35);return _p.r(11).createClass("LinearGradientBrush",{base:r,constructor:function(t){this.callBase("linearGradient",t),this.setStartPosition(0,0),this.setEndPosition(1,0)},setStartPosition:function(t,e){return this.node.setAttribute("x1",t),this.node.setAttribute("y1",e),this},setEndPosition:function(t,e){return this.node.setAttribute("x2",t),this.node.setAttribute("y2",e),this},getStartPosition:function(){return{x:+this.node.getAttribute("x1"),y:+this.node.getAttribute("y1")}},getEndPosition:function(){return{x:+this.node.getAttribute("x2"),y:+this.node.getAttribute("y2")}}})}},_p[41]={value:function(t,e,n){var r=_p.r(50),i=_p.r(11).createClass("Marker",{base:_p.r(58),mixins:[_p.r(61),_p.r(75)],constructor:function(){this.callBase("marker"),this.setOrient("auto")},setRef:function(t,e){return 1===arguments.length&&(e=t.y,t=t.x),this.node.setAttribute("refX",t),this.node.setAttribute("refY",e),this},getRef:function(){return new r(+this.node.getAttribute("refX"),+this.node.getAttribute("refY"))},setWidth:function(t){return this.node.setAttribute("markerWidth",this.width=t),this},setOrient:function(t){return this.node.setAttribute("orient",this.orient=t),this},getOrient:function(){return this.orient},getWidth:function(){return+this.width},setHeight:function(t){return this.node.setAttribute("markerHeight",this.height=t),this},getHeight:function(){return+this.height}}),s=_p.r(46);return _p.r(11).extendClass(s,{setMarker:function(t,e){return e=e||"end",t?this.node.setAttribute("marker-"+e,t.toString()):this.node.removeAttribute("marker-"+e),this}}),i}},_p[42]={value:function(t,e,n){var r=_p.r(11),i=_p.r(60),s=r.createClass("Mask",{base:i,mixins:[_p.r(61)],constructor:function(){this.callBase("mask")},mask:function(t){return t.getNode().setAttribute("mask","url(#"+this.getId()+")"),this}});return r.extendClass(i,{maskWith:function(t){return t.mask(this),this}}),s}},_p[43]={value:function(t,e,n){var c=_p.r(12),l=_p.r(25),i=/matrix\s*\((.+)\)/i,r=_p.r(50);function s(t,e){return{a:e.a*t.a+e.c*t.b,b:e.b*t.a+e.d*t.b,c:e.a*t.c+e.c*t.d,d:e.b*t.c+e.d*t.d,e:e.a*t.e+e.c*t.f+e.e,f:e.b*t.e+e.d*t.f+e.f}}function a(t){return t*Math.PI/180}var f=_p.r(11).createClass("Matrix",{constructor:function(){arguments.length?this.setMatrix.apply(this,arguments):this.setMatrix(1,0,0,1,0,0)},translate:function(t,e){return this.m=s(this.m,{a:1,c:0,e:t,b:0,d:1,f:e}),this},rotate:function(t){var e=a(t),n=Math.sin(e),r=Math.cos(e);return this.m=s(this.m,{a:r,c:-n,e:0,b:n,d:r,f:0}),this},scale:function(t,e){return void 0===e&&(e=t),this.m=s(this.m,{a:t,c:0,e:0,b:0,d:e,f:0}),this},skew:function(t,e){void 0===e&&(e=t);var n=Math.tan(a(t)),r=Math.tan(a(e));return this.m=s(this.m,{a:1,c:n,e:0,b:r,d:1,f:0}),this},inverse:function(){var t,e=this.m,n=e.a,r=e.b,i=e.c,s=e.d,a=e.e,o=e.f;return new f(s/(t=n*s-r*i),-r/t,-i/t,n/t,(i*o-a*s)/t,(r*a-n*o)/t)},setMatrix:function(t,e,n,r,i,s){return this.m=1===arguments.length?c.clone(t):{a:t,b:e,c:n,d:r,e:i,f:s},this},getMatrix:function(){return c.clone(this.m)},getTranslate:function(){var t=this.m;return{x:t.e/t.a,y:t.f/t.d}},mergeMatrix:function(t){return new f(s(this.m,t.m))},merge:function(t){return this.mergeMatrix(t)},toString:function(){return this.valueOf().join(" ")},valueOf:function(){var t=this.m;return[t.a,t.b,t.c,t.d,t.e,t.f]},equals:function(t){var e=this.m,n=t.m;return e.a==n.a&&e.b==n.b&&e.c==n.c&&e.d==n.d&&e.e==n.e&&e.f==n.f},transformPoint:function(){return f.transformPoint.apply(null,[].slice.call(arguments).concat([this.m]))},transformBox:function(t){return f.transformBox(t,this.m)},clone:function(){return new f(this.m)}});return f.parse=function(t){var e,n=parseFloat;if(t instanceof Array)return new f({a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5]});if(e=i.exec(t)){var r=e[1].split(",");return 6!=r.length&&(r=e[1].split(" ")),new f({a:n(r[0]),b:n(r[1]),c:n(r[2]),d:n(r[3]),e:n(r[4]),f:n(r[5])})}return new f},f.transformPoint=function(t,e,n){return 2===arguments.length&&(n=e,e=t.y,t=t.x),new r(n.a*t+n.c*e+n.e,n.b*t+n.d*e+n.f)},f.transformBox=function(t,e){for(var n,r,i=Number.MAX_VALUE,s=-Number.MAX_VALUE,a=Number.MAX_VALUE,o=-Number.MAX_VALUE,u=[[t.x,t.y],[t.x+t.width,t.y],[t.x,t.y+t.height],[t.x+t.width,t.y+t.height]],h=[];n=u.pop();)r=f.transformPoint(n[0],n[1],e),h.push(r),i=Math.min(i,r.x),s=Math.max(s,r.x),a=Math.min(a,r.y),o=Math.max(o,r.y);return t=new l({x:i,y:a,width:s-i,height:o-a}),c.extend(t,{closurePoints:h}),t},f.getCTM=function(t,e){var n={a:1,b:0,c:0,d:1,e:0,f:0},r=t.shapeNode||t.node;function i(t,e){var n;try{n=e.getScreenCTM().inverse()}catch(t){throw new Error("Can not inverse source element' ctm.")}return n.multiply(t.getScreenCTM())}switch(e=e||"parent"){case"screen":n=r.getScreenCTM();break;case"doc":case"paper":n=r.getCTM();break;case"view":case"top":t.getPaper()&&(n=void 0!==r.getTransformToElement?r.getTransformToElement(t.getPaper().shapeNode):i(r,t.getPaper().shapeNode));break;case"parent":t.node.parentNode&&(n=void 0!==r.getTransformToElement?r.getTransformToElement(t.node.parentNode):i(r,t.node.parentNode));break;default:e.node&&(n=void 0!==r.getTransformToElement?r.getTransformToElement(e.shapeNode||e.node):i(r,e.shapeNode||e.node))}return n?new f(n.a,n.b,n.c,n.d,n.e,n.f):new f},f}},_p[44]={value:function(t,e,n){var r=_p.r(64),i=_p.r(28),s=_p.r(12),a=_p.r(11).createClass("Palette",{constructor:function(){this.color={}},get:function(t){var e=this.color[t]||r.EXTEND_STANDARD[t]||r.COLOR_STANDARD[t]||"";return e?new i(e):null},getColorValue:function(t){return this.color[t]||r.EXTEND_STANDARD[t]||r.COLOR_STANDARD[t]||""},add:function(t,e){return this.color[t]="string"==typeof e?new i(e).toRGBA():e.toRGBA(),e},remove:function(t){return!!this.color.hasOwnProperty(t)&&(delete this.color[t],!0)}});return s.extend(a,{getColor:function(t){var e=r.EXTEND_STANDARD[t]||r.COLOR_STANDARD[t];return e?new i(e):null},getColorValue:function(t){return r.EXTEND_STANDARD[t]||r.COLOR_STANDARD[t]||""},addColor:function(t,e){return r.EXTEND_STANDARD[t]="string"==typeof e?new i(e).toRGBA():e.toRGBA(),e},removeColor:function(t){return!!r.EXTEND_STANDARD.hasOwnProperty(t)&&(delete r.EXTEND_STANDARD[t],!0)}}),a}},_p[45]={value:function(t,e,n){var r=_p.r(11),i=_p.r(12),s=_p.r(67),a=_p.r(29),o=_p.r(61),u=_p.r(75),h=_p.r(33),c=_p.r(66),l=_p.r(43),f=r.createClass("Paper",{mixins:[o,h,c,u],constructor:function(t){this.callBase(),this.node=this.createSVGNode(),(this.node.paper=this).node.appendChild(this.resourceNode=s.createNode("defs")),this.node.appendChild(this.shapeNode=s.createNode("g")),this.resources=new a,this.setWidth("100%").setHeight("100%"),t&&this.renderTo(t),this.callMixin()},renderTo:function(t){i.isString(t)&&(t=document.getElementById(t)),(this.container=t).appendChild(this.node)},createSVGNode:function(){var t=s.createNode("svg");return t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),t.setAttribute("version","1.1"),t},getNode:function(){return this.node},getContainer:function(){return this.container},getWidth:function(){return this.node.clientWidth},setWidth:function(t){return this.node.setAttribute("width",t),this},getHeight:function(){return this.node.clientHeight},setHeight:function(t){return this.node.setAttribute("height",t),this},setViewPort:function(t,e,n){var r,i;1==arguments.length&&(t=(r=t).center.x,e=r.center.y,n=r.zoom),n=n||1,i=this.getViewBox();var s=new l,a=i.x+i.width/2-t,o=i.y+i.height/2-e;return s.translate(-t,-e),s.scale(n),s.translate(t,e),s.translate(a,o),this.shapeNode.setAttribute("transform","matrix("+s+")"),this.viewport={center:{x:t,y:e},offset:{x:a,y:o},zoom:n},this},getViewPort:function(){if(this.viewport)return this.viewport;var t=this.getViewBox();return{zoom:1,center:{x:t.x+t.width/2,y:t.y+t.height/2},offset:{x:0,y:0}}},getViewPortMatrix:function(){return l.parse(this.shapeNode.getAttribute("transform"))},getViewPortTransform:function(){var t=this.shapeNode.getCTM();return new l(t.a,t.b,t.c,t.d,t.e,t.f)},getTransform:function(){return this.getViewPortTransform().reverse()},addResource:function(t){return this.resources.appendItem(t),t.node&&this.resourceNode.appendChild(t.node),this},removeResource:function(t){return t.remove&&t.remove(),t.node&&this.resourceNode.removeChild(t.node),this},getPaper:function(){return this}}),p=_p.r(60);return r.extendClass(p,{getPaper:function(){for(var t=this.container;t&&t instanceof f==!1;)t=t.container;return t},isAttached:function(){return!!this.getPaper()},whenPaperReady:function(e){var n=this;function r(){var t=n.getPaper();return t&&e&&e.call(n,t),t}return r()||this.on("add treeadd",function t(){r()&&(n.off("add",t),n.off("treeadd",t))}),this}}),f}},_p[46]={value:function(t,e,n){var r=_p.r(12),i=_p.r(11).createClass,s=_p.r(60),a=_p.r(67),o=_p.r(34),u=Array.prototype.slice,h=(r.flatten,i("PathDrawer",{constructor:function(t){this.segment=[],this.path=t,this.__clear=!1},getPath:function(){return this.path},redraw:function(){return this._transation=this._transation||[],this.clear()},done:function(){var t=this._transation;return this._transation=null,this.push(t),this},clear:function(){return this._transation?this._transation=[]:this.path.setPathData("M 0 0"),this._clear=!0,this},push:function(){var t,e=u.call(arguments);return this._transation?this._transation.push(e):(this._clear?(t="",this._clear=!1):t=this.path.getPathData(),t=t||"",this.path.setPathData(t+o.pathToString(e))),this},moveTo:function(t,e){return this.push("M",u.call(arguments))},moveBy:function(t,e){return this.push("m",u.call(arguments))},lineTo:function(t,e){return this.push("L",u.call(arguments))},lineBy:function(t,e){return this.push("l",u.call(arguments))},arcTo:function(t,e,n,r,i,s,a){return this.push("A",u.call(arguments))},arcBy:function(t,e,n,r,i,s,a){return this.push("a",arguments)},carcTo:function(t,e,n,r,i){return this.push("A",[t,t,0].concat(u.call(arguments,1)))},carcBy:function(t,e,n,r,i){return this.push("a",[t,t,0].concat(u.call(arguments,1)))},bezierTo:function(t,e,n,r,i,s){return this.push("C",u.call(arguments))},bezierBy:function(t,e,n,r,i,s){return this.push("c",u.call(arguments))},close:function(){return this.push("z")}}));return i("Path",{base:s,constructor:function(t){this.callBase("path"),t&&this.setPathData(t),this.node.setAttribute("fill",a.defaults.fill),this.node.setAttribute("stroke",a.defaults.stroke)},setPathData:function(t){return t=t||"M0,0",this.pathdata=o.pathToString(t),this.node.setAttribute("d",this.pathdata),this.trigger("shapeupdate",{type:"pathdata"}),this},getPathData:function(){return this.pathdata||""},getDrawer:function(){return new h(this)},isClosed:function(){var t=this.getPathData();return!!~t.indexOf("z")||!!~t.indexOf("Z")}})}},_p[47]={value:function(t,e,n){var r=_p.r(58),i=_p.r(61);_p.r(67);return _p.r(11).createClass("PatternBrush",{base:r,mixins:[i],constructor:function(t){this.callBase("pattern",t),this.node.setAttribute("patternUnits","userSpaceOnUse")},setX:function(t){return this.x=t,this.node.setAttribute("x",t),this},setY:function(t){return this.y=t,this.node.setAttribute("y",t),this},setWidth:function(t){return this.width=t,this.node.setAttribute("width",t),this},setHeight:function(t){return this.height=t,this.node.setAttribute("height",t),this},getWidth:function(){return this.width},getHeight:function(){return this.height}})}},_p[48]={value:function(t,e,n){var r=_p.r(28);return _p.r(11).createClass("Pen",{constructor:function(t,e){this.brush=t,this.width=e||1,this.linecap=null,this.linejoin=null,this.dashArray=null,this.opacity=1},getBrush:function(){return this.brush},setBrush:function(t){return this.brush=t,this},setColor:function(t){return this.setBrush(t)},getColor:function(){return this.brush instanceof r?this.brush:null},getWidth:function(){return this.width},setWidth:function(t){return this.width=t,this},getOpacity:function(){return this.opacity},setOpacity:function(t){this.opacity=t},getLineCap:function(){return this.linecap},setLineCap:function(t){return this.linecap=t,this},getLineJoin:function(){return this.linejoin},setLineJoin:function(t){return this.linejoin=t,this},getDashArray:function(){return this.dashArray},setDashArray:function(t){return this.dashArray=t,this},stroke:function(t){var e=t.node;e.setAttribute("stroke",this.brush.toString()),e.setAttribute("stroke-width",this.getWidth()),this.getOpacity()<1&&e.setAttribute("stroke-opacity",this.getOpacity()),this.getLineCap()&&e.setAttribute("stroke-linecap",this.getLineCap()),this.getLineJoin()&&e.setAttribute("stroke-linejoin",this.getLineJoin()),this.getDashArray()&&e.setAttribute("stroke-dasharray",this.getDashArray())}})}},_p[49]={value:function(t,e,n){return _p.r(11).createClass({base:_p.r(68),constructor:function(t,e,n){this.callBase([0,t],e,n)},getRadius:function(){return this.getSectionArray()[1]},setRadius:function(t){this.setSectionArray([0,t])}})}},_p[50]={value:function(t,e,n){var r=_p.r(11).createClass("Point",{constructor:function(t,e){this.x=t||0,this.y=e||0},offset:function(t,e){return 1==arguments.length&&(e=t.y,t=t.x),new r(this.x+t,this.y+e)},valueOf:function(){return[this.x,this.y]},toString:function(){return this.valueOf().join(" ")},spof:function(){return new r(.5+(0|this.x),.5+(0|this.y))},round:function(){return new r(0|this.x,0|this.y)},isOrigin:function(){return 0===this.x&&0===this.y}});return r.fromPolar=function(t,e,n){return"rad"!=n&&(e=e/180*Math.PI),new r(t*Math.cos(e),t*Math.sin(e))},r.parse=function(t){return t?t instanceof r?t:"string"==typeof t?r.parse(t.split(/\s*[\s,]\s*/)):"0"in t&&"1"in t?new r(t[0],t[1]):void 0:new r},r}},_p[51]={value:function(t,e,n){return _p.r(11).createClass("PointContainer",{base:_p.r(29),constructor:function(){this.callBase()},addPoint:function(t,e){return this.addItem.apply(this,arguments)},prependPoint:function(){return this.prependItem.apply(this,arguments)},appendPoint:function(){return this.appendItem.apply(this,arguments)},removePoint:function(t){return this.removeItem.apply(this,arguments)},addPoints:function(){return this.addItems.apply(this,arguments)},setPoints:function(){return this.setItems.apply(this,arguments)},getPoint:function(){return this.getItem.apply(this,arguments)},getPoints:function(){return this.getItems.apply(this,arguments)},getFirstPoint:function(){return this.getFirstItem.apply(this,arguments)},getLastPoint:function(){return this.getLastItem.apply(this,arguments)}})}},_p[52]={value:function(t,e,n){_p.r(12);return _p.r(11).createClass("Poly",{base:_p.r(46),mixins:[_p.r(51)],constructor:function(t,e){this.callBase(),this.closeable=!!e,this.setPoints(t||[]),this.changeable=!0,this.update()},onContainerChanged:function(){this.changeable&&this.update()},update:function(){var t=this.getDrawer(),e=this.getPoints();if(t.clear(),!e.length)return this;t.moveTo(e[0]);for(var n,r=1,i=e.length;r<i;r++)n=e[r],t.lineTo(n);return this.closeable&&2<e.length&&t.close(),this}})}},_p[53]={value:function(t,e,n){return _p.r(11).createClass("Polygon",{base:_p.r(52),constructor:function(t){this.callBase(t,!0)}})}},_p[54]={value:function(t,e,n){return _p.r(11).createClass("Polyline",{base:_p.r(52),constructor:function(t){this.callBase(t)}})}},_p[55]={value:function(t,e,n){var r=_p.r(35);return _p.r(11).createClass("RadialGradientBrush",{base:r,constructor:function(t){this.callBase("radialGradient",t),this.setCenter(.5,.5),this.setFocal(.5,.5),this.setRadius(.5)},setCenter:function(t,e){return this.node.setAttribute("cx",t),this.node.setAttribute("cy",e),this},getCenter:function(){return{x:+this.node.getAttribute("cx"),y:+this.node.getAttribute("cy")}},setFocal:function(t,e){return this.node.setAttribute("fx",t),this.node.setAttribute("fy",e),this},getFocal:function(){return{x:+this.node.getAttribute("fx"),y:+this.node.getAttribute("fy")}},setRadius:function(t){return this.node.setAttribute("r",t),this},getRadius:function(){return+this.node.getAttribute("r")}})}},_p[56]={value:function(t,e,n){var s={},r=_p.r(12),i=_p.r(50),a=_p.r(25);return r.extend(s,{formatRadius:function(t,e,n){var r=Math.floor(Math.min(t/2,e/2));return Math.min(r,n)}}),_p.r(11).createClass("Rect",{base:_p.r(46),constructor:function(t,e,n,r,i){this.callBase(),this.x=n||0,this.y=r||0,this.width=t||0,this.height=e||0,this.radius=s.formatRadius(this.width,this.height,i||0),this.update()},update:function(){var t=this.x,e=this.y,n=this.width,r=this.height,i=this.radius,s=this.getDrawer().redraw();return i?(n-=2*i,r-=2*i,s.push("M",t+i,e),s.push("h",n),s.push("a",i,i,0,0,1,i,i),s.push("v",r),s.push("a",i,i,0,0,1,-i,i),s.push("h",-n),s.push("a",i,i,0,0,1,-i,-i),s.push("v",-r),s.push("a",i,i,0,0,1,i,-i)):(s.push("M",t,e),s.push("h",n),s.push("v",r),s.push("h",-n)),s.push("z"),s.done(),this},setWidth:function(t){return this.width=t,this.update()},setHeight:function(t){return this.height=t,this.update()},setSize:function(t,e){return this.width=t,this.height=e,this.update()},setBox:function(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this.update()},getBox:function(){return new a(this.x,this.y,this.width,this.height)},getRadius:function(){return this.radius},setRadius:function(t){return this.radius=s.formatRadius(this.width,this.height,t||0),this.update()},getPosition:function(){return new i(this.x,this.y)},setPosition:function(t,e){if(1==arguments.length){var n=i.parse(t);e=n.y,t=n.x}return this.x=t,this.y=e,this.update()},getWidth:function(){return this.width},getHeight:function(){return this.height},getPositionX:function(){return this.x},getPositionY:function(){return this.y},setPositionX:function(t){return this.x=t,this.update()},setPositionY:function(t){return this.y=t,this.update()}})}},_p[57]={value:function(t,e,n){var s=_p.r(50);return _p.r(11).createClass("RegularPolygon",{base:_p.r(46),constructor:function(t,e,n,r){this.callBase(),this.radius=e||0,this.side=Math.max(t||3,3),2<arguments.length&&3==arguments.length&&(r=n.y,n=n.x),this.center=new s(n,r),this.draw()},getSide:function(){return this.side},setSide:function(t){return this.side=t,this.draw()},getRadius:function(){return this.radius},setRadius:function(t){return this.radius=t,this.draw()},draw:function(){var t,e=this.radius,n=this.side,r=2*Math.PI/n,i=this.getDrawer();for(i.clear(),i.moveTo(s.fromPolar(e,Math.PI/2,"rad").offset(this.center)),t=0;t<=n;t++)i.lineTo(s.fromPolar(e,r*t+Math.PI/2,"rad").offset(this.center));return i.close(),this}})}},_p[58]={value:function(t,e,n){var r=_p.r(67);return _p.r(11).createClass("Resource",{constructor:function(t,e){this.callBase(),this.node=r.createNode(t),e&&e.addResource(this)},toString:function(){return"url(#"+this.node.id+")"}})}},_p[59]={value:function(t,e,n){return _p.r(11).createClass({base:_p.r(68),constructor:function(t,e){this.callBase([t,e],360,0)},getInnerRadius:function(){return this.getSectionArray()[0]},getOuterRadius:function(){return this.getSectionArray()[1]},setInnerRadius:function(t){this.setSectionArray([t,this.getOuterRadius()])},setOuterRadius:function(t){this.setSectionArray([this.getInnerRadius(),t])}})}},_p[60]={value:function(t,e,n){var r=_p.r(67),i=_p.r(12),s=_p.r(33),a=_p.r(66),o=_p.r(31),u=_p.r(43),h=(_p.r(48),Array.prototype.slice),c=_p.r(25);return _p.r(11).createClass("Shape",{mixins:[s,a,o],constructor:function(t){this.node=r.createNode(t),(this.node.shape=this).transform={translate:null,rotate:null,scale:null,matrix:null},this.callMixin()},getId:function(){return this.node.id},setId:function(t){return this.node.id=t,this},getNode:function(){return this.node},getBoundaryBox:function(){var e;try{e=this.node.getBBox()}catch(t){e={x:this.node.clientLeft,y:this.node.clientTop,width:this.node.clientWidth,height:this.node.clientHeight}}return new c(e)},getRenderBox:function(t){var e=this.getBoundaryBox();return this.getTransform(t).transformBox(e)},getWidth:function(){return this.getRenderBox().width},getHeight:function(){return this.getRenderBox().height},getSize:function(){var t=this.getRenderBox();return delete t.x,delete t.y,t},setOpacity:function(t){return this.node.setAttribute("opacity",t),this},getOpacity:function(){var t=this.node.getAttribute("opacity");return t?+t:1},setVisible:function(t){return t?this.node.removeAttribute("display"):this.node.setAttribute("display","none"),this},getVisible:function(){this.node.getAttribute("display")},hasAncestor:function(t){for(var e=this.container;e;){if(e===t)return!0;e=e.container}return!1},getTransform:function(t){return u.getCTM(this,t)},clearTransform:function(){return this.node.removeAttribute("transform"),this.transform={translate:null,rotate:null,scale:null,matrix:null},this.trigger("shapeupdate",{type:"transform"}),this},_applyTransform:function(){var t=this.transform,e=[];return t.translate&&e.push(["translate(",t.translate,")"]),t.rotate&&e.push(["rotate(",t.rotate,")"]),t.scale&&e.push(["scale(",t.scale,")"]),t.matrix&&e.push(["matrix(",t.matrix,")"]),this.node.setAttribute("transform",i.flatten(e).join(" ")),this},setMatrix:function(t){return this.transform.matrix=t,this._applyTransform()},setTranslate:function(t){return this.transform.translate=null!==t&&h.call(arguments)||null,this._applyTransform()},setRotate:function(t){return this.transform.rotate=null!==t&&h.call(arguments)||null,this._applyTransform()},setScale:function(t){return this.transform.scale=null!==t&&h.call(arguments)||null,this._applyTransform()},translate:function(t,e){var n=this.transform.matrix||new u;return void 0===e&&(e=0),this.transform.matrix=n.translate(t,e),this._applyTransform()},rotate:function(t){var e=this.transform.matrix||new u;return this.transform.matrix=e.rotate(t),this._applyTransform()},scale:function(t,e){var n=this.transform.matrix||new u;return void 0===e&&(e=t),this.transform.matrix=n.scale(t,e),this._applyTransform()},skew:function(t,e){var n=this.transform.matrix||new u;return void 0===e&&(e=t),this.transform.matrix=n.skew(t,e),this._applyTransform()},stroke:function(t,e){return t&&t.stroke?t.stroke(this):t?(this.node.setAttribute("stroke",t.toString()),e&&this.node.setAttribute("stroke-width",e)):null===t&&this.node.removeAttribute("stroe"),this},fill:function(t){return t&&this.node.setAttribute("fill",t.toString()),null===t&&this.node.removeAttribute("fill"),this},setAttr:function(t,e){var n=this;return i.isObject(t)&&i.each(t,function(t,e){n.setAttr(e,t)}),null==e||""===e?this.node.removeAttribute(t):this.node.setAttribute(t,e),this},getAttr:function(t){return this.node.getAttribute(t)}})}},_p[61]={value:function(t,e,n){var r=_p.r(29),i=_p.r(12),s=_p.r(11).createClass("ShapeContainer",{base:r,isShapeContainer:!0,handleAdd:function(t,e){var n=this.getShapeNode();n.insertBefore(t.node,n.childNodes[e]||null),t.trigger("add",{container:this}),t.notifyTreeModification&&t.notifyTreeModification("treeadd",this)},handleRemove:function(t,e){this.getShapeNode().removeChild(t.node),t.trigger("remove",{container:this}),t.notifyTreeModification&&t.notifyTreeModification("treeremove",this)},notifyTreeModification:function(n,r){this.eachItem(function(t,e){e.notifyTreeModification&&e.notifyTreeModification(n,r),e.trigger(n,{container:r})})},getShape:function(t){return this.getItem(t)},addShape:function(t,e){return this.addItem(t,e)},put:function(t){return this.addShape(t),t},appendShape:function(t){return this.addShape(t)},prependShape:function(t){return this.addShape(t,0)},replaceShape:function(t,e){var n=this.indexOf(e);if(-1!==n)return this.removeShape(n),this.addShape(t,n),this},addShapeBefore:function(t,e){var n=this.indexOf(e);return this.addShape(t,n)},addShapeAfter:function(t,e){var n=this.indexOf(e);return this.addShape(t,-1===n?void 0:n+1)},addShapes:function(t){return this.addItems(t)},removeShape:function(t){return this.removeItem(t)},getShapes:function(){return this.getItems()},getShapesByType:function(n){var r=[];return function e(t){n.toLowerCase()==t.getType().toLowerCase()&&r.push(t),t.isShapeContainer&&i.each(t.getShapes(),function(t){e(t)})}(this),r},getShapeById:function(t){return this.getShapeNode().getElementById(t).shape},arrangeShape:function(t,e){return this.removeShape(t).addShape(t,e)},getShapeNode:function(){return this.shapeNode||this.node}}),a=_p.r(60);return _p.r(11).extendClass(a,{bringTo:function(t){return this.container.arrangeShape(this,t),this},bringFront:function(){return this.bringTo(this.container.indexOf(this)+1)},bringBack:function(){return this.bringTo(this.container.indexOf(this)-1)},bringTop:function(){return this.container.removeShape(this).addShape(this),this},bringRear:function(){return this.bringTo(0)},bringRefer:function(t,e){return t.container&&(this.remove&&this.remove(),t.container.addShape(this,t.container.indexOf(t)+(e||0))),this},bringAbove:function(t){return this.bringRefer(t)},bringBelow:function(t){return this.bringRefer(t,1)},replaceBy:function(t){return this.container&&(t.bringAbove(this),this.remove()),this}}),s}},_p[62]={value:function(t,e,n){var o=_p.r(43),r=_p.r(12),u=_p.r(50);return _p.r(11).createClass("ShapeEvent",{constructor:function(t){var e=null;r.isObject(t.target)?r.extend(this,t):(this.type=t.type,(e=t.target).correspondingUseElement&&(e=e.correspondingUseElement),this.originEvent=t,this.targetShape=e.shape||e.paper||t.currentTarget&&(t.currentTarget.shape||t.currentTarget.paper),t._kityParam&&r.extend(this,t._kityParam))},preventDefault:function(){var t=this.originEvent;return!t||(t.preventDefault?(t.preventDefault(),t.cancelable):!(t.returnValue=!1))},getPosition:function(t,e){if(!this.originEvent)return null;var n=this.originEvent.touches?this.originEvent.touches[e||0]:this.originEvent,r=this.targetShape,i=r.shapeNode||r.node,s=new u(n&&n.clientX||0,n&&n.clientY||0),a=o.transformPoint(s,i.getScreenCTM().inverse());return o.getCTM(r,t||"view").transformPoint(a)},stopPropagation:function(){var t=this.originEvent;if(!t)return!0;t.stopPropagation?t.stopPropagation():t.cancelBubble=!1}})}},_p[63]={value:function(t,e,n){return _p.r(11).createClass("ShapePoint",{base:_p.r(50),constructor:function(t,e){this.callBase(t,e)},setX:function(t){return this.setPoint(t,this.y)},setY:function(t){return this.setPoint(this.x,t)},setPoint:function(t,e){return this.x=t,this.y=e,this.update(),this},getPoint:function(){return this},update:function(){return this.container&&this.container.update&&this.container.update(),this}})}},_p[64]={value:{COLOR_STANDARD:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00"},EXTEND_STANDARD:{}}},_p[65]={value:function(t,e,n){var h={3:.2,5:.38196601125,6:.57735026919,8:.541196100146,10:.726542528005,12:.707106781187},c=_p.r(50);return _p.r(11).createClass("Star",{base:_p.r(46),constructor:function(t,e,n,r,i){this.callBase(),this.vertex=t||3,this.radius=e||0,this.shrink=n,this.offset=r||new c(0,0),this.angleOffset=i||0,this.draw()},getVertex:function(){return this.vertex},setVertex:function(t){return this.vertex=t,this.draw()},getRadius:function(){return this.radius},setRadius:function(t){return this.radius=t,this.draw()},getShrink:function(){return this.shrink},setShrink:function(t){return this.shrink=t,this.draw()},getOffset:function(){return this.offset},setOffset:function(t){return this.offset=t,this.draw()},getAngleOffset:function(){return this.angleOffset},setAngleOffset:function(t){return this.angleOffset=t,this.draw()},draw:function(){var t,e,n=this.radius,r=this.radius*(this.shrink||h[this.vertex]||.5),i=this.vertex,s=this.offset,a=180/i,o=this.angleOffset,u=this.getDrawer();for(u.clear(),u.moveTo(c.fromPolar(r,90)),t=1;t<=2*i;t++)e=90+a*t,t%2?u.lineTo(c.fromPolar(n,e+o).offset(s)):u.lineTo(c.fromPolar(r,e));u.close()}})}},_p[66]={value:function(t,e,n){var r=_p.r(11).createClass("ClassList",{constructor:function(t){this._node=t,this._list=t.className.toString().split(" ")},_update:function(){this._node.className=this._list.join(" ")},add:function(t){this._list.push(t),this._update()},remove:function(t){var e=this._list.indexOf(t);~e&&this._list.splice(e,1),this._update()},contains:function(t){return!!~this._list.indexOf(t)}});function i(t){return t.classList||(t.classList=new r(t)),t.classList}return _p.r(11).createClass("Styled",{addClass:function(t){return i(this.node).add(t),this},removeClass:function(t){return i(this.node).remove(t),this},hasClass:function(t){return i(this.node).contains(t)},setStyle:function(t){if(2==arguments.length)return this.node.style[t]=arguments[1],this;for(var e in t)t.hasOwnProperty(e)&&(this.node.style[e]=t[e]);return this}})}},_p[67]={value:function(t,e,n){var r=document,i=0,s={createNode:function(t){var e=r.createElementNS(s.ns,t);return e.id="kity_"+t+"_"+i++,e},defaults:{stroke:"none",fill:"none"},xlink:"http://www.w3.org/1999/xlink",ns:"http://www.w3.org/2000/svg"};return s}},_p[68]={value:function(t,e,n){var u=_p.r(50);return _p.r(11).createClass("Sweep",{base:_p.r(46),constructor:function(t,e,n){this.callBase(),this.sectionArray=t||[],this.angle=e||0,this.angleOffset=n||0,this.draw()},getSectionArray:function(){return this.sectionArray},setSectionArray:function(t){return this.sectionArray=t,this.draw()},getAngle:function(){return this.angle},setAngle:function(t){return this.angle=t,this.draw()},getAngleOffset:function(){return this.angleOffset},setAngleOffset:function(t){return this.angleOffset=t,this.draw()},draw:function(){var t,e=this.sectionArray;for(t=0;t<e.length;t+=2)this.drawSection(e[t],e[t+1]);return this},drawSection:function(t,e){var n=this.angle&&(this.angle%360?this.angle%360:360),r=this.angleOffset,i=r+n/2,s=r+n,a=n<0?0:1,o=this.getDrawer();o.redraw(),0!==n&&(o.moveTo(u.fromPolar(t,r)),o.lineTo(u.fromPolar(e,r)),e&&(o.carcTo(e,0,a,u.fromPolar(e,i)),o.carcTo(e,0,a,u.fromPolar(e,s))),o.lineTo(u.fromPolar(t,s)),t&&(o.carcTo(t,0,a,u.fromPolar(t,i)),o.carcTo(t,0,a,u.fromPolar(t,r))),o.close()),o.done()}})}},_p[69]={value:function(t,e,n){var r=_p.r(70),i=_p.r(61),s=_p.r(67),a=_p.r(12),o={};function u(t){var e=t._cachedFontHash;if(o[e])return o[e];var n=t.getContent();t.setContent("百度Fex");var r=t.getBoundaryBox(),i=t.getY();if(!r.height)return{top:0,bottom:0,middle:0};var s=i-r.y+ +t.node.getAttribute("dy"),a=s-r.height;return t.setContent(n),o[e]={top:s,bottom:a,middle:(s+a)/2}}return _p.r(11).createClass("Text",{base:r,mixins:[i],constructor:function(t){this.callBase("text"),void 0!==t&&this.setContent(t),this._buildFontHash()},fixPosition:function(){this.__fixedPosition||this.setVerticalAlign(this.getVerticalAlign())},_buildFontHash:function(){var t=window.getComputedStyle(this.node);this._cachedFontHash=[t.fontFamily,t.fontSize,t.fontStretch,t.fontStyle,t.fontVariant,t.fontWeight].join("-")},_fontChanged:function(t){var e=this._lastFont,n=a.extend({},e,t);if(!e)return this._lastFont=t,!0;var r=e.family!=n.family||e.size!=n.size||e.style!=n.style||e.weight!=n.weight;return this._lastFont=n,r},setX:function(t){return this.node.setAttribute("x",t),this},setPosition:function(t,e){return this.setX(t).setY(e)},setY:function(t){return this.node.setAttribute("y",t),this},getX:function(){return+this.node.getAttribute("x")||0},getY:function(){return+this.node.getAttribute("y")||0},setFont:function(t){return this.callBase(t),this._fontChanged(t)&&(this._buildFontHash(),this.setVerticalAlign(this.getVerticalAlign())),this},setTextAnchor:function(t){return this.node.setAttribute("text-anchor",t),this},getTextAnchor:function(){return this.node.getAttribute("text-anchor")||"start"},setVerticalAlign:function(e){return this.whenPaperReady(function(){var t;switch(e){case"top":t=u(this).top;break;case"bottom":t=u(this).bottom;break;case"middle":t=u(this).middle;break;default:t=0}t&&(this.__fixedPosition=!0),this.node.setAttribute("dy",t)}),this.verticalAlign=e,this},getVerticalAlign:function(){return this.verticalAlign||"baseline"},setStartOffset:function(t){this.shapeNode!=this.node&&this.shapeNode.setAttribute("startOffset",100*t+"%")},addSpan:function(t){return this.addShape(t),this},setPath:function(t){var e=this.shapeNode;if(this.shapeNode==this.node){for(e=this.shapeNode=s.createNode("textPath");this.node.firstChild;)this.shapeNode.appendChild(this.node.firstChild);this.node.appendChild(e)}return e.setAttributeNS(s.xlink,"xlink:href","#"+t.node.id),this.setTextAnchor(this.getTextAnchor()),this}})}},_p[70]={value:function(t,e,n){var r=_p.r(60);return _p.r(11).createClass("TextContent",{base:r,constructor:function(t){this.callBase(t),this.shapeNode=this.shapeNode||this.node,this.shapeNode.setAttribute("text-rendering","geometricPrecision")},clearContent:function(){for(;this.shapeNode.firstChild;)this.shapeNode.removeChild(this.shapeNode.firstChild);return this},setContent:function(t){return this.shapeNode.textContent=t,this},getContent:function(){return this.shapeNode.textContent},appendContent:function(t){return this.shapeNode.textContent+=t,this},setSize:function(t){return this.setFontSize(t)},setFontSize:function(t){return this.setFont({size:t})},setFontFamily:function(t){return this.setFont({family:t})},setFontBold:function(t){return this.setFont({weight:t?"bold":"normal"})},setFontItalic:function(t){return this.setFont({style:t?"italic":"normal"})},setFont:function(e){var n=this.node;return["family","size","weight","style"].forEach(function(t){null===e[t]?n.removeAttribute("font-"+t):e[t]&&n.setAttribute("font-"+t,e[t])}),this},getExtentOfChar:function(t){return this.node.getExtentOfChar(t)},getRotationOfChar:function(t){return this.node.getRotationOfChar(t)},getCharNumAtPosition:function(t,e){return this.node.getCharNumAtPosition(this.node.viewportElement.createSVGPoint(t,e))}})}},_p[71]={value:function(t,e,n){var r=_p.r(70),i=_p.r(66);return _p.r(11).createClass("TextSpan",{base:r,mixins:[i],constructor:function(t){this.callBase("tspan"),this.setContent(t)}})}},_p[72]={value:function(t,e,n){var r=_p.r(67),i=_p.r(11),s=i.createClass("Use",{base:_p.r(60),constructor:function(t){this.callBase("use"),this.ref(t)},ref:function(t){if(!t)return this.node.removeAttributeNS(r.xlink,"xlink:href"),this;var e=t.getId();return e&&this.node.setAttributeNS(r.xlink,"xlink:href","#"+e),"none"===t.node.getAttribute("fill")&&t.node.removeAttribute("fill"),"none"===t.node.getAttribute("stroke")&&t.node.removeAttribute("stroke"),this}}),a=_p.r(60);return i.extendClass(a,{use:function(){return new s(this)}}),s}},_p[73]={value:function(t,e,n){var r=_p.r(50),i=_p.r(43),s=_p.r(11).createClass("Vector",{base:r,constructor:function(t,e){this.callBase(t,e)},square:function(){return this.x*this.x+this.y*this.y},length:function(){return Math.sqrt(this.square())},add:function(t){return new s(this.x+t.x,this.y+t.y)},minus:function(t){return new s(this.x-t.x,this.y-t.y)},dot:function(t){return this.x*t.x+this.y*t.y},project:function(t){return t.multipy(this.dot(t)/t.square())},normalize:function(t){return void 0===t&&(t=1),this.multipy(t/this.length())},multipy:function(t){return new s(this.x*t,this.y*t)},rotate:function(t,e){"rad"==e&&(t=t/Math.PI*180);var n=(new i).rotate(t).transformPoint(this);return new s(n.x,n.y)},vertical:function(){return new s(this.y,-this.x)},reverse:function(){return this.multipy(-1)},getAngle:function(){var t=this.length();if(0===t)return 0;var e=Math.acos(this.x/t);return 180*(0<this.y?1:-1)*e/Math.PI}});return s.fromPoints=function(t,e){return new s(e.x-t.x,e.y-t.y)},s.fromPolar=function(){var t=r.fromPolar.apply(r,arguments);return new s(t.x,t.y)},_p.r(11).extendClass(r,{asVector:function(){return new s(this.x,this.y)}}),s}},_p[74]={value:function(t,e,n){var r=_p.r(61),i=_p.r(75);return _p.r(11).createClass("View",{mixins:[r,i],base:_p.r(74),constructor:function(){this.callBase("view")}})}},_p[75]={value:function(t,e,n){return _p.r(11).createClass("ViewBox",{getViewBox:function(){var t=this.node.getAttribute("viewBox");return null===t?{x:0,y:0,width:this.node.clientWidth||this.node.parentNode.clientWidth,height:this.node.clientHeight||this.node.parentNode.clientHeight}:{x:+(t=t.split(" "))[0],y:+t[1],width:+t[2],height:+t[3]}},setViewBox:function(t,e,n,r){return this.node.setAttribute("viewBox",[t,e,n,r].join(" ")),this}})}},_p[76]={value:function(t,e,n){var r={},i=_p.r(12);return r.version="2.0.0",i.extend(r,{createClass:_p.r(11).createClass,extendClass:_p.r(11).extendClass,Utils:i,Browser:_p.r(10),Box:_p.r(25),Bezier:_p.r(23),BezierPoint:_p.r(24),Circle:_p.r(26),Clip:_p.r(27),Color:_p.r(28),Container:_p.r(29),Curve:_p.r(30),Ellipse:_p.r(32),Group:_p.r(36),Gradient:_p.r(35),HyperLink:_p.r(37),Image:_p.r(38),Line:_p.r(39),LinearGradient:_p.r(40),Mask:_p.r(42),Matrix:_p.r(43),Marker:_p.r(41),Palette:_p.r(44),Paper:_p.r(45),Path:_p.r(46),Pattern:_p.r(47),Pen:_p.r(48),Point:_p.r(50),PointContainer:_p.r(51),Polygon:_p.r(53),Polyline:_p.r(54),Pie:_p.r(49),RadialGradient:_p.r(55),Resource:_p.r(58),Rect:_p.r(56),RegularPolygon:_p.r(57),Ring:_p.r(59),Shape:_p.r(60),ShapePoint:_p.r(63),ShapeContainer:_p.r(61),Sweep:_p.r(68),Star:_p.r(65),Text:_p.r(69),TextSpan:_p.r(71),Use:_p.r(72),Vector:_p.r(73),g:_p.r(34),Animator:_p.r(0),Easing:_p.r(1),OpacityAnimator:_p.r(4),RotateAnimator:_p.r(6),ScaleAnimator:_p.r(7),Timeline:_p.r(8),TranslateAnimator:_p.r(9),PathAnimator:_p.r(5),MotionAnimator:_p.r(3),requestFrame:_p.r(2).requestFrame,releaseFrame:_p.r(2).releaseFrame,Filter:_p.r(20),GaussianblurFilter:_p.r(21),ProjectionFilter:_p.r(22),ColorMatrixEffect:_p.r(13),CompositeEffect:_p.r(14),ConvolveMatrixEffect:_p.r(15),Effect:_p.r(16),GaussianblurEffect:_p.r(17),OffsetEffect:_p.r(18)}),window.kity=r}};var moduleMapping={kity:76};function use(t){_p.r([moduleMapping[t]])}use("kity")}();