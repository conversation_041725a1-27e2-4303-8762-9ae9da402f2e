import request from '@/utils/request'

export default {
  //分页
  list(data) {
    return request({
      url: '/activiti/onlineForm/list',
      method: 'GET',
      params: data
    })
  },
  add(data){
    return request({
      url: '/activiti/onlineForm/saveNew',
      method: 'POST',
      data
    })
  },
  update(data){
    return request({
      url: '/activiti/onlineForm/update',
      method: 'POST',
      data
    })
  },
  view(data){
    return request({
      url: '/activiti/onlineForm/view',
      method: 'GET',
      params: data
    })
  },
  delete(data){
    return request({
      url: '/activiti/onlineForm/delete',
      method: 'DELETE',
      params: data
    })
  },
  render(data){
    return request({
      url: '/activiti/onlineForm/render',
      method: 'GET',
      params: data
    })
  },
}
