<template>
  <div>
    <el-card class="process-card">
      <div slot="header" class="card-header">
        <div class="card-title">
          <i class="el-icon-s-grid" style="color: #bbbbbb"></i>
          <span style="margin-left: 6px">学中测试</span>
        </div>
        <el-button type="text" class="delete-button" icon="el-icon-delete" @click="$emit('delete')"></el-button>
      </div>
      <el-form ref="answerQuestionForm" :model="formData" label-width="100px" :rules="rules"
               class="process-form-content"
      >
        <p>答题阶段:</p>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="排序:" prop="sortsNum">
              <el-input-number v-model="formData.sortsNum" :min="1" :max="9999999999"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="9">
            <el-form-item label="阶段时长:" prop="stageDuration">
              <el-select v-model="formData.stageDuration" placeholder="请选择阶段时长">
                <el-option v-for="i in 60" :key="i" :label="i" :value="i"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="阶段名称:" prop="stageName">
              <el-input v-model="formData.stageName" placeholder="请输入阶段名称" maxlength="10" show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="阶段提醒:" prop="stageRemind">
              <el-radio-group v-model="formData.stageRemind">
                <el-radio label="0">阶段结束时需要弹框提醒</el-radio>
                <el-radio label="1">阶段结束时不作提醒</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="做题PK:" prop="isPk">
              <el-radio disabled v-model="formData.isPk" label="0">1V1V1V1 PK</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="题干选择:" prop="topic">
              <el-checkbox v-model="questionStemSelection.hasStem" label="包含题干" @change="handleHasStemChange"></el-checkbox>
              <el-checkbox v-model="questionStemSelection.hasNoStem" label="不含题干" @change="handleHasNoStemChange"></el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 含题干材料-->
      <div>
        <el-card v-if="questionStemSelection.hasStem" class="process-card">
          <div slot="header" class="clearfix">
            <span>题组一：包含题干材料</span>
          </div>
          <div style="display: flex;align-items: center;margin-bottom: 20px">
            <el-button type="primary" style="margin-right: 35px" @click="openQuestionStemDialog">选择题干材料
            </el-button>
            <el-form :inline="true">
              <div>{{
                  !this.stemIds || this.stemIds.length === 0 ? '请选择关联题干材料' : `已绑定题干材料ID：${this.stemIds[0] || ''} - ${this.stemTitle || ''}`
                }}
              </div>
            </el-form>
          </div>
          <div style="margin-bottom: 20px">
            <el-checkbox v-model="hasStemQuestionTypeSelection.singleChoice" :disabled="hasStemQuestionTypeDisabled"
                         label="单选题" border
                         @change="(val) => handleQuestionTypeChange(val, 'hasStem', 'singleChoice')">
            </el-checkbox>
            <el-checkbox v-model="hasStemQuestionTypeSelection.multipleChoice" :disabled="hasStemQuestionTypeDisabled"
                         label="多选题" border
                         @change="(val) => handleQuestionTypeChange(val, 'hasStem', 'multipleChoice')">
            </el-checkbox>
            <el-checkbox v-model="hasStemQuestionTypeSelection.gapFilling" :disabled="hasStemQuestionTypeDisabled"
                         label="填空题" border @change="(val) => handleQuestionTypeChange(val, 'hasStem', 'gapFilling')">
            </el-checkbox>
            <!-- 新增简答题选项 -->
            <el-checkbox v-model="hasStemQuestionTypeSelection.shortAnswer" :disabled="hasStemQuestionTypeDisabled"
                         label="简答题" border @change="(val) => handleQuestionTypeChange(val, 'hasStem', 'shortAnswer')">
            </el-checkbox>
          </div>
          <!-- 包含题干的题型列表 -->
          <div v-for="(questionType, index) in [...stemQuestionTypesWithStem].sort((a, b) => a.questionTypeSort - b.questionTypeSort)"
               :key="`${questionType.type}-${index}`">
            <el-card v-if="questionType.type === 'singleChoice' && hasStemQuestionTypeSelection.singleChoice"
                     style="margin-bottom: 20px"
            >
              <div slot="header" class="clearfix">
                <span>单选题 (总分: {{ calculateTotalScore(selectedStemMultipleChoiceQuestionList) }}分)</span>
                <i class="el-icon-delete" style="float: right; padding: 3px 0; cursor: pointer;"
                   @click="removeQuestionType('hasStem', 'singleChoice')"
                ></i>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 10px;justify-content: space-between;"
                   v-for="(item,qIndex) in selectedStemMultipleChoiceQuestionList" :key="qIndex"
              >
                <div style="display: flex;width: 90%">
                  <div style="margin-right: 20px">{{ qIndex + 1 }}</div>
                  <div>{{ item.questionText || item.questionName }}（{{ item.questionScore }}分）</div>
                </div>
                <div style="width: 10%; text-align: right;">
                  <i class="el-icon-tickets" style="margin: 0 5px; cursor: pointer;" @click="openPreviewDialog(item)"
                  ></i>
                  <i class="el-icon-top" style="margin: 0 5px; cursor: pointer;"
                     @click="moveUpQuestion(selectedStemMultipleChoiceQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-bottom" style="margin: 0 5px; cursor: pointer;"
                     @click="moveDownQuestion(selectedStemMultipleChoiceQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-delete-solid" style="margin: 0 5px; cursor: pointer;"
                     @click="deleteQuestion(selectedStemMultipleChoiceQuestionList, qIndex)"
                  ></i>
                </div>
              </div>
              <div style="border-top: #99a9bf 1px solid;padding-top: 10px">
                <el-button round @click="openAddQuestionDialog(0,1)">添加题目</el-button>
                <el-button round @click="moveQuestionTypeUp(stemQuestionTypesWithStem, index)" :disabled="index === 0">
                  上移
                </el-button>
                <el-button round @click="moveQuestionTypeDown(stemQuestionTypesWithStem, index)"
                           :disabled="index === stemQuestionTypesWithStem.length - 1"
                >下移
                </el-button>
              </div>
            </el-card>
            <el-card v-if="questionType.type === 'multipleChoice' && hasStemQuestionTypeSelection.multipleChoice"
                     style="margin-bottom: 20px"
            >
              <div slot="header" class="clearfix">
                <span>多选题 (总分: {{ calculateTotalScore(selectedStemManyChoiceQuestionList) }}分)</span>
                <i class="el-icon-delete" style="float: right; padding: 3px 0; cursor: pointer;"
                   @click="removeQuestionType('hasStem', 'multipleChoice')"
                ></i>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 10px;justify-content: space-between;"
                   v-for="(item,qIndex) in selectedStemManyChoiceQuestionList" :key="qIndex"
              >
                <div style="display: flex;width: 90%">
                  <div style="margin-right: 20px">{{ qIndex + 1 }}</div>
                  <div>{{ item.questionText || item.questionName }}（{{ item.questionScore }}分）</div>
                </div>
                <div style="width: 10%; text-align: right;">
                  <i class="el-icon-tickets" style="margin: 0 5px; cursor: pointer;" @click="openPreviewDialog(item)"
                  ></i>
                  <i class="el-icon-top" style="margin: 0 5px; cursor: pointer;"
                     @click="moveUpQuestion(selectedStemManyChoiceQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-bottom" style="margin: 0 5px; cursor: pointer;"
                     @click="moveDownQuestion(selectedStemManyChoiceQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-delete-solid" style="margin: 0 5px; cursor: pointer;"
                     @click="deleteQuestion(selectedStemManyChoiceQuestionList, qIndex)"
                  ></i>
                </div>
              </div>
              <div style="border-top: #99a9bf 1px solid;padding-top: 10px">
                <el-button round @click="openAddQuestionDialog(2,1)">添加题目</el-button>
                <el-button round @click="moveQuestionTypeUp(stemQuestionTypesWithStem, index)" :disabled="index === 0">
                  上移
                </el-button>
                <el-button round @click="moveQuestionTypeDown(stemQuestionTypesWithStem, index)"
                           :disabled="index === stemQuestionTypesWithStem.length - 1"
                >下移
                </el-button>
              </div>
            </el-card>
            <el-card v-if="questionType.type === 'gapFilling' && hasStemQuestionTypeSelection.gapFilling"
                     style="margin-bottom: 20px"
            >
              <div slot="header" class="clearfix">
                <span>填空题 (总分: {{ calculateTotalScore(selectedStemGapFillingQuestionList) }}分)</span>
                <i class="el-icon-delete" style="float: right; padding: 3px 0; cursor: pointer;"
                   @click="removeQuestionType('hasStem', 'gapFilling')"
                ></i>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 10px;justify-content: space-between;"
                   v-for="(item,qIndex) in selectedStemGapFillingQuestionList" :key="qIndex"
              >
                <div style="display: flex;width: 90%">
                  <div style="margin-right: 20px">{{ qIndex + 1 }}</div>
                  <div>{{ item.questionText || item.questionName }}（{{ item.questionScore }}分）</div>
                </div>
                <div style="width: 10%; text-align: right;">
                  <i class="el-icon-tickets" style="margin: 0 5px; cursor: pointer;" @click="openPreviewDialog(item)"
                  ></i>
                  <i class="el-icon-top" style="margin: 0 5px; cursor: pointer;"
                     @click="moveUpQuestion(selectedStemGapFillingQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-bottom" style="margin: 0 5px; cursor: pointer;"
                     @click="moveDownQuestion(selectedStemGapFillingQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-delete-solid" style="margin: 0 5px; cursor: pointer;"
                     @click="deleteQuestion(selectedStemGapFillingQuestionList, qIndex)"
                  ></i>
                </div>
              </div>
              <div style="border-top: #99a9bf 1px solid;padding-top: 10px">
                <el-button round @click="openAddQuestionDialog(1,1)">添加题目</el-button>
                <el-button round @click="moveQuestionTypeUp(stemQuestionTypesWithStem, index)" :disabled="index === 0">
                  上移
                </el-button>
                <el-button round @click="moveQuestionTypeDown(stemQuestionTypesWithStem, index)"
                           :disabled="index === stemQuestionTypesWithStem.length - 1"
                >下移
                </el-button>
              </div>
            </el-card>
            <!-- 新增简答题卡片 -->
            <el-card v-if="questionType.type === 'shortAnswer' && hasStemQuestionTypeSelection.shortAnswer"
                     style="margin-bottom: 20px">
              <div slot="header" class="clearfix">
                <span>简答题 (总分: {{ calculateTotalScore(selectedStemShortAnswerQuestionList) }}分)</span>
                <i class="el-icon-delete" style="float: right; padding: 3px 0; cursor: pointer;"
                   @click="removeQuestionType('hasStem', 'shortAnswer')">
                </i>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 10px;justify-content: space-between;"
                   v-for="(item,qIndex) in selectedStemShortAnswerQuestionList" :key="qIndex">
                <div style="display: flex;width: 90%">
                  <div style="margin-right: 20px">{{ qIndex + 1 }}</div>
                  <div>{{ item.questionText || item.questionName }}（{{ item.questionScore }}分）</div>
                </div>
                <div style="width: 10%; text-align: right;">
                  <i class="el-icon-tickets" style="margin: 0 5px; cursor: pointer;" @click="openPreviewDialog(item)"></i>
                  <i class="el-icon-top" style="margin: 0 5px; cursor: pointer;"
                     @click="moveUpQuestion(selectedStemShortAnswerQuestionList, qIndex)"></i>
                  <i class="el-icon-bottom" style="margin: 0 5px; cursor: pointer;"
                     @click="moveDownQuestion(selectedStemShortAnswerQuestionList, qIndex)"></i>
                  <i class="el-icon-delete-solid" style="margin: 0 5px; cursor: pointer;"
                     @click="deleteQuestion(selectedStemShortAnswerQuestionList, qIndex)"></i>
                </div>
              </div>
              <div style="border-top: #99a9bf 1px solid;padding-top: 10px">
                <el-button round @click="openAddQuestionDialog(4,1)">添加题目</el-button>
                <el-button round @click="moveQuestionTypeUp(stemQuestionTypesWithStem, index)" :disabled="index === 0">
                  上移
                </el-button>
                <el-button round @click="moveQuestionTypeDown(stemQuestionTypesWithStem, index)"
                           :disabled="index === stemQuestionTypesWithStem.length - 1">
                  下移
                </el-button>
              </div>
            </el-card>
          </div>
        </el-card>
        <el-card v-if="questionStemSelection.hasNoStem" class="process-card">
          <div slot="header" class="clearfix">
            <span>题组二：不含题干材料</span>
          </div>
          <div style="margin-bottom: 20px">
            <el-checkbox v-model="hasNoStemQuestionTypeSelection.singleChoice" label="单选题" border
                         @change="(val) => handleQuestionTypeChange(val, 'hasNoStem', 'singleChoice')"
            ></el-checkbox>
            <el-checkbox v-model="hasNoStemQuestionTypeSelection.multipleChoice" label="多选题" border
                         @change="(val) => handleQuestionTypeChange(val, 'hasNoStem', 'multipleChoice')"
            ></el-checkbox>
            <el-checkbox v-model="hasNoStemQuestionTypeSelection.gapFilling" label="填空题" border
                         @change="(val) => handleQuestionTypeChange(val, 'hasNoStem', 'gapFilling')"
            ></el-checkbox>
            <!-- 新增简答题选项 -->
            <el-checkbox v-model="hasNoStemQuestionTypeSelection.shortAnswer" label="简答题" border
                         @change="(val) => handleQuestionTypeChange(val, 'hasNoStem', 'shortAnswer')">
            </el-checkbox>
          </div>
          <!-- 不包含题干的题型列表 -->
          <div v-for="(questionType, index) in [...stemQuestionTypesWithoutStem].sort((a, b) => a.questionTypeSort - b.questionTypeSort)"
               :key="`${questionType.type}-${index}`">
            <el-card v-if="questionType.type === 'singleChoice' && hasNoStemQuestionTypeSelection.singleChoice"
                     style="margin-bottom: 20px"
            >
              <div slot="header" class="clearfix">
                <span>单选题 (总分: {{ calculateTotalScore(selectedNoStemMultipleChoiceQuestionList) }}分)</span>
                <i class="el-icon-delete" style="float: right; padding: 3px 0; cursor: pointer;"
                   @click="removeQuestionType('hasNoStem', 'singleChoice')"
                ></i>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 10px;justify-content: space-between;"
                   v-for="(item,qIndex) in selectedNoStemMultipleChoiceQuestionList" :key="qIndex"
              >
                <div style="display: flex;width: 90%">
                  <div style="margin-right: 20px">{{ qIndex + 1 }}</div>
                  <div>{{ item.questionText || item.questionName }}（{{ item.questionScore }}分）</div>
                </div>
                <div  style="width: 10%; text-align: right;">
                  <i class="el-icon-tickets" style="margin: 0 5px; cursor: pointer;" @click="openPreviewDialog(item)"
                  ></i>
                  <i class="el-icon-top" style="margin: 0 5px; cursor: pointer;"
                     @click="moveUpQuestion(selectedNoStemMultipleChoiceQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-bottom" style="margin: 0 5px; cursor: pointer;"
                     @click="moveDownQuestion(selectedNoStemMultipleChoiceQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-delete-solid" style="margin: 0 5px; cursor: pointer;"
                     @click="deleteQuestion(selectedNoStemMultipleChoiceQuestionList, qIndex)"
                  ></i>
                </div>
              </div>
              <div style="border-top: #99a9bf 1px solid;padding-top: 10px">
                <el-button round @click="openAddQuestionDialog(0,2)">添加题目</el-button>
                <el-button round @click="moveQuestionTypeUp(stemQuestionTypesWithoutStem, index)"
                           :disabled="index === 0"
                >上移
                </el-button>
                <el-button round @click="moveQuestionTypeDown(stemQuestionTypesWithoutStem, index)"
                           :disabled="index === stemQuestionTypesWithoutStem.length - 1"
                >下移
                </el-button>
              </div>
            </el-card>
            <el-card v-if="questionType.type === 'multipleChoice' && hasNoStemQuestionTypeSelection.multipleChoice"
                     style="margin-bottom: 20px"
            >
              <div slot="header" class="clearfix">
                <span>多选题 (总分: {{ calculateTotalScore(selectedNoStemManyChoiceQuestionList) }}分)</span>
                <i class="el-icon-delete" style="float: right; padding: 3px 0; cursor: pointer;"
                   @click="removeQuestionType('hasNoStem', 'multipleChoice')"
                ></i>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 10px;justify-content: space-between;"
                   v-for="(item,qIndex) in selectedNoStemManyChoiceQuestionList" :key="qIndex"
              >
                <div style="display: flex;width: 90%">
                  <div style="margin-right: 20px">{{ qIndex + 1 }}</div>
                  <div>{{ item.questionText || item.questionName }}（{{ item.questionScore }}分）</div>
                </div>
                <div style="width: 10%; text-align: right;">
                  <i class="el-icon-tickets" style="margin: 0 5px; cursor: pointer;" @click="openPreviewDialog(item)"
                  ></i>
                  <i class="el-icon-top" style="margin: 0 5px; cursor: pointer;"
                     @click="moveUpQuestion(selectedNoStemManyChoiceQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-bottom" style="margin: 0 5px; cursor: pointer;"
                     @click="moveDownQuestion(selectedNoStemManyChoiceQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-delete-solid" style="margin: 0 5px; cursor: pointer;"
                     @click="deleteQuestion(selectedNoStemManyChoiceQuestionList, qIndex)"
                  ></i>
                </div>
              </div>
              <div style="border-top: #99a9bf 1px solid;padding-top: 10px">
                <el-button round @click="openAddQuestionDialog(2,2)">添加题目</el-button>
                <el-button round @click="moveQuestionTypeUp(stemQuestionTypesWithoutStem, index)"
                           :disabled="index === 0"
                >上移
                </el-button>
                <el-button round @click="moveQuestionTypeDown(stemQuestionTypesWithoutStem, index)"
                           :disabled="index === stemQuestionTypesWithoutStem.length - 1"
                >下移
                </el-button>
              </div>
            </el-card>
            <el-card v-if="questionType.type === 'gapFilling' && hasNoStemQuestionTypeSelection.gapFilling"
                     style="margin-bottom: 20px"
            >
              <div slot="header" class="clearfix">
                <span>填空题 (总分: {{ calculateTotalScore(selectedNoStemGapFillingQuestionList) }}分)</span>
                <i class="el-icon-delete" style="float: right; padding: 3px 0; cursor: pointer;"
                   @click="removeQuestionType('hasNoStem', 'gapFilling')"
                ></i>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 10px;justify-content: space-between;"
                   v-for="(item,qIndex) in selectedNoStemGapFillingQuestionList" :key="qIndex"
              >
                <div style="display: flex;width: 90%">
                  <div style="margin-right: 20px">{{ qIndex + 1 }}</div>
                  <div>{{ item.questionText || item.questionName }}（{{ item.questionScore }}分）</div>
                </div>
                <div style="width: 10%; text-align: right;">
                  <i class="el-icon-tickets" style="margin: 0 5px; cursor: pointer;" @click="openPreviewDialog(item)"
                  ></i>
                  <i class="el-icon-top" style="margin: 0 5px; cursor: pointer;"
                     @click="moveUpQuestion(selectedNoStemGapFillingQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-bottom" style="margin: 0 5px; cursor: pointer;"
                     @click="moveDownQuestion(selectedNoStemGapFillingQuestionList, qIndex)"
                  ></i>
                  <i class="el-icon-delete-solid" style="margin: 0 5px; cursor: pointer;"
                     @click="deleteQuestion(selectedNoStemGapFillingQuestionList, qIndex)"
                  ></i>
                </div>
              </div>
              <div style="border-top: #99a9bf 1px solid;padding-top: 10px">
                <el-button round @click="openAddQuestionDialog(1,2)">添加题目</el-button>
                <el-button round @click="moveQuestionTypeUp(stemQuestionTypesWithoutStem, index)"
                           :disabled="index === 0"
                >上移
                </el-button>
                <el-button round @click="moveQuestionTypeDown(stemQuestionTypesWithoutStem, index)"
                           :disabled="index === stemQuestionTypesWithoutStem.length - 1"
                >下移
                </el-button>
              </div>
            </el-card>
            <!-- 新增简答题卡片 -->
            <el-card v-if="questionType.type === 'shortAnswer' && hasNoStemQuestionTypeSelection.shortAnswer"
                     style="margin-bottom: 20px">
              <div slot="header" class="clearfix">
                <span>简答题 (总分: {{ calculateTotalScore(selectedNoStemShortAnswerQuestionList) }}分)</span>
                <i class="el-icon-delete" style="float: right; padding: 3px 0; cursor: pointer;"
                   @click="removeQuestionType('hasNoStem', 'shortAnswer')">
                </i>
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 10px;justify-content: space-between;"
                   v-for="(item,qIndex) in selectedNoStemShortAnswerQuestionList" :key="qIndex">
                <div style="display: flex;width: 90%">
                  <div style="margin-right: 20px">{{ qIndex + 1 }}</div>
                  <div>{{ item.questionText || item.questionName }}（{{ item.questionScore }}分）</div>
                </div>
                <div style="width: 10%; text-align: right;">
                  <i class="el-icon-tickets" style="margin: 0 5px; cursor: pointer;" @click="openPreviewDialog(item)"></i>
                  <i class="el-icon-top" style="margin: 0 5px; cursor: pointer;"
                     @click="moveUpQuestion(selectedNoStemShortAnswerQuestionList, qIndex)"></i>
                  <i class="el-icon-bottom" style="margin: 0 5px; cursor: pointer;"
                     @click="moveDownQuestion(selectedNoStemShortAnswerQuestionList, qIndex)"></i>
                  <i class="el-icon-delete-solid" style="margin: 0 5px; cursor: pointer;"
                     @click="deleteQuestion(selectedNoStemShortAnswerQuestionList, qIndex)"></i>
                </div>
              </div>
              <div style="border-top: #99a9bf 1px solid;padding-top: 10px">
                <el-button round @click="openAddQuestionDialog(4,2)">添加题目</el-button>
                <el-button round @click="moveQuestionTypeUp(stemQuestionTypesWithoutStem, index)"
                           :disabled="index === 0">
                  上移
                </el-button>
                <el-button round @click="moveQuestionTypeDown(stemQuestionTypesWithoutStem, index)"
                           :disabled="index === stemQuestionTypesWithoutStem.length - 1">
                  下移
                </el-button>
              </div>
            </el-card>
          </div>
        </el-card>
      </div>
    </el-card>
    <el-dialog :visible.sync="questionStemDialog" :key="dialogKey">
      <div slot="title" style="display: flex;justify-content: center">
        <span>{{ dialogTitle }}</span>
      </div>
      <KnowledgePoint
        v-if="questionStemDialog"
        ref="knowledgePoint"
        :disciplineId="courseSubjectNodeId"
        :curriculumId="curriculumId"
        :stemIds="stemIds"
        :questionIds="questionIds"
        :questionType="questionType"
        :type="type"
        :questionMaterialId="questionMaterialId"
      ></KnowledgePoint>
      <div slot="footer" style="display: flex;justify-content: center">
        <el-button @click="dialogBeforeClose">取 消</el-button>
        <el-button type="primary" @click="confirmKonwLedge">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog center title="查看题目" :visible.sync="previewDialog" width="60%">
      <chineseViewAnalysisDialog v-if="dialogVisible" :viewId="viewId"></chineseViewAnalysisDialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="previewDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ElementForm from '@/views/activiti/workFlow/package/refactor/form/ElementForm.vue';
import KnowledgePoint from '../components/knowledgePoint.vue';
import { getQuestionDetail } from '@/api/superReaderAPI/testBaseManagement';
import chineseViewAnalysisDialog from '@/views/chineseCourseManagement/components/chineseViewAnalysisDialog.vue';

export default {
  name: 'superReaderMiddleText',
  components: { chineseViewAnalysisDialog, ElementForm, KnowledgePoint },
  props: {
    index: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      type: '',
      dialogTitle: '',
      questionType: 0,
      dialogVisible: false,
      previewDialog: false,
      previewData: {},
      stemStatus: '',
      activeItem: '',
      questionTypes: [],
      rules: {
        stageDuration: [{ required: true, message: '请选择阶段时长', trigger: 'change' }],
        stageName: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],
        stageRemind: [{ required: true, message: '请选择阶段提醒方式', trigger: 'change' }]
      },
      isEdit: false,
      addQuestionForm: {
        questionType: ''
      },
      // 题干选择
      questionStemSelection: {
        hasStem: false,
        hasNoStem: false
      },
      // 包含题干题型选择
      hasStemQuestionTypeSelection: {
        singleChoice: false,
        multipleChoice: false,
        gapFilling: false,
        // 新增简答题选项
        shortAnswer: false
      },
      // 不含题干题型选择
      hasNoStemQuestionTypeSelection: {
        singleChoice: false,
        multipleChoice: false,
        gapFilling: false,
        // 新增简答题选项
        shortAnswer: false
      },
      // 用于跟踪题型顺序的数组
      stemQuestionTypesWithStem: [],
      stemQuestionTypesWithoutStem: [],
      questionStemDialog: false,
      addQuestionDialog: false,
      stemTitle: '', // 题干材料标题
      courseSubjectNodeId: '',
      curriculumId: '',
      stemIds: [],
      questionIds: [],
      selectedStemMultipleChoiceQuestionList: [], // 含题干单选题目列表
      selectedStemManyChoiceQuestionList: [], // 含题干多选题目列表
      selectedStemGapFillingQuestionList: [], // 含题干填空题列表
      selectedStemShortAnswerQuestionList: [], // 含题干简答题列表
      selectedNoStemMultipleChoiceQuestionList: [], // 不含题干单选题列表
      selectedNoStemManyChoiceQuestionList: [], // 不含题干多选题列表
      selectedNoStemGapFillingQuestionList: [], // 不含题干填空题列表
      selectedNoStemShortAnswerQuestionList: [], // 不含题干简答题列表
      dialogKey: 0, // 用于强制重新渲染dialog
      questionMaterialId: '',
      viewId: ''
    };
  },
  props: {
    formData: {
      type: Object,
      default: () => ({
        processType: 5,
        sortsNum: 1,
        stageDuration: '',
        stageName: '',
        isPk: '0',
        topic: '',
        knowledgeScope: '',
        questionType: [], // 当前已添加的题型
        solveStageDuration: '',
        solveStageName: '',
        solveStageRemind: '1',
        stageRemind: '1',
        courseProcessStudyTestCoList: []
      })
    }
  },
  watch: {
    formData: {
      handler(newVal) {
        console.log('formData', newVal);
        // 当表单数据变化时，通知父组件，只传递必要的数据
        this.$emit('update', newVal);
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    // 当这个数据有值时是编辑状态
    if (this.formData.courseProcessStudyTestCoList) {
      this.isEdit = true;
    }
    if (!this.formData.isPk) {
      this.formData.isPk = '0';
    }
    this.courseSubjectNodeId = sessionStorage.getItem('courseSubjectNodeId');
    this.curriculumId = localStorage.getItem('curriculumId');
    // 添加处理题干选择的逻辑
    this.handleQuestionStemSelection();
  },
  computed: {
    // 添加一个计算属性来判断是否可以选择包含题干的题型
    hasStemQuestionTypeDisabled() {
      return !this.stemIds || this.stemIds.length === 0;
    }
  },
  methods: {
    // 处理题型选择变化
    handleQuestionTypeChange(checked, group, type) {
      const groupArray = group === 'hasStem' ? this.stemQuestionTypesWithStem : this.stemQuestionTypesWithoutStem;

      if (checked) {
        // 添加题型到数组中，并设置 questionTypeSort
        const maxSort = groupArray.length > 0
          ? Math.max(...groupArray.map(item => item.questionTypeSort))
          : 0;
        groupArray.push({
          type,
          questionTypeSort: maxSort + 1
        });
      } else {
        // 从数组中移除题型
        const index = groupArray.findIndex(item => item.type === type);
        if (index > -1) {
          groupArray.splice(index, 1);
          // 重新排列剩余项的排序值
          this.reorderQuestionTypes(groupArray);
        }

        // 清除对应题型的题目列表
        if (group === 'hasStem') {
          if (type === 'singleChoice') {
            // 从formData.courseProcessStudyTestCoList中删除对应题目
            this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
              !(question.questionMaterialId && question.questionMaterialId !== '' && question.questionType == 0)
            );
            this.selectedStemMultipleChoiceQuestionList = [];
          } else if (type === 'multipleChoice') {
            // 从formData.courseProcessStudyTestCoList中删除对应题目
            this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
              !(question.questionMaterialId && question.questionMaterialId !== '' && question.questionType == 2)
            );
            this.selectedStemManyChoiceQuestionList = [];
          } else if (type === 'gapFilling') {
            // 从formData.courseProcessStudyTestCoList中删除对应题目
            this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
              !(question.questionMaterialId && question.questionMaterialId !== '' && question.questionType == 1)
            );
            this.selectedStemGapFillingQuestionList = [];
          }// 新增简答题处理
          else if (type === 'shortAnswer') {
            this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
              !(question.questionMaterialId && question.questionMaterialId !== '' && question.questionType == 4)
            );
            this.selectedStemShortAnswerQuestionList = [];
          }
        } else {
          if (type === 'singleChoice') {
            // 从formData.courseProcessStudyTestCoList中删除对应题目
            this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
              !(!question.questionMaterialId && question.questionType == 0)
            );
            this.selectedNoStemMultipleChoiceQuestionList = [];
          } else if (type === 'multipleChoice') {
            // 从formData.courseProcessStudyTestCoList中删除对应题目
            this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
              !(!question.questionMaterialId && question.questionType == 2)
            );
            this.selectedNoStemManyChoiceQuestionList = [];
          } else if (type === 'gapFilling') {
            // 从formData.courseProcessStudyTestCoList中删除对应题目
            this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
              !(!question.questionMaterialId && question.questionType == 1)
            );
            this.selectedNoStemGapFillingQuestionList = [];
          }// 新增简答题处理
          else if (type === 'shortAnswer') {
            this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
              !(!question.questionMaterialId && question.questionType == 4)
            );
            this.selectedNoStemShortAnswerQuestionList = [];
          }
        }
      }
    },
    // 处理题干选择逻辑
    // handleQuestionStemSelection() {
    //   const list = this.formData.courseProcessStudyTestCoList;
    //   console.log('list', list);
    //
    //   // 如果列表为空或不存在，则默认不勾选
    //   if (!list || list.length === 0) {
    //     this.questionStemSelection.hasStem = false;
    //     this.questionStemSelection.hasNoStem = false;
    //     return;
    //   }
    //
    //   // 检查是否有带题干和不带题干的题目
    //   let hasStem = false;
    //   let hasNoStem = false;
    //
    //   for (const item of list) {
    //     if (item.questionMaterialId && item.questionMaterialId !== '') {
    //       hasStem = true;
    //     } else {
    //       hasNoStem = true;
    //     }
    //
    //     // 如果两个条件都满足，可以提前结束循环
    //     if (hasStem && hasNoStem) {
    //       break;
    //     }
    //   }
    //
    //   // 根据检查结果设置勾选状态
    //   if (hasStem && hasNoStem) {
    //     // 既有带题干也有不带题干的题目
    //     this.questionStemSelection.hasStem = true;
    //     this.questionStemSelection.hasNoStem = true;
    //   } else if (hasStem) {
    //     // 只有带题干的题目
    //     this.questionStemSelection.hasStem = true;
    //     this.questionStemSelection.hasNoStem = false;
    //   } else {
    //     // 只有不带题干的题目（或者都没有）
    //     this.questionStemSelection.hasStem = false;
    //     this.questionStemSelection.hasNoStem = true;
    //   }
    //
    //   // 清空现有列表
    //   this.selectedStemMultipleChoiceQuestionList = [];
    //   this.selectedStemGapFillingQuestionList = [];
    //   this.selectedStemManyChoiceQuestionList = [];
    //   this.selectedNoStemMultipleChoiceQuestionList = [];
    //   this.selectedNoStemGapFillingQuestionList = [];
    //   this.selectedNoStemManyChoiceQuestionList = [];
    //   // 新增简答题列表清空
    //   this.selectedStemShortAnswerQuestionList = [];
    //   this.selectedNoStemShortAnswerQuestionList = [];
    //
    //   // 重置题型选择状态
    //   this.hasStemQuestionTypeSelection.singleChoice = false;
    //   this.hasStemQuestionTypeSelection.multipleChoice = false;
    //   this.hasStemQuestionTypeSelection.gapFilling = false;
    //   this.hasStemQuestionTypeSelection.shortAnswer = false; // 新增
    //   this.hasNoStemQuestionTypeSelection.singleChoice = false;
    //   this.hasNoStemQuestionTypeSelection.multipleChoice = false;
    //   this.hasNoStemQuestionTypeSelection.gapFilling = false;
    //   this.hasNoStemQuestionTypeSelection.shortAnswer = false; // 新增
    //
    //   // 清空题型顺序数组
    //   this.stemQuestionTypesWithStem = [];
    //   this.stemQuestionTypesWithoutStem = [];
    //
    //   // 遍历courseProcessStudyTestCoList，根据条件填充对应列表并选中相应选择框
    //   if (list && list.length > 0) {
    //     list.forEach(item => {
    //       // 判断条件：questionMaterialId，同时questionType是0（单选题）
    //       if (item.questionMaterialId && item.questionMaterialId !== '' && item.questionType == 0) {
    //         this.selectedStemMultipleChoiceQuestionList.push(item);
    //         this.stemIds.push(item.questionMaterialId);
    //         this.stemTitle = item.questionStemText;
    //         // 选中包含题干材料的单选题选择框
    //         if (!this.hasStemQuestionTypeSelection.singleChoice) {
    //           this.hasStemQuestionTypeSelection.singleChoice = true;
    //           this.stemQuestionTypesWithStem.push({
    //             type: 'singleChoice',
    //             questionTypeSort: this.stemQuestionTypesWithStem.length + 1
    //           });
    //         }
    //       }
    //       // 判断条件：questionStemId有值且不为空，同时questionType是1（填空题）
    //       else if (item.questionMaterialId && item.questionMaterialId !== '' && item.questionType == 1) {
    //         this.selectedStemGapFillingQuestionList.push(item);
    //         this.stemIds.push(item.questionMaterialId);
    //         this.stemTitle = item.questionStemText;
    //         // 选中包含题干材料的填空题选择框
    //         if (!this.hasStemQuestionTypeSelection.gapFilling) {
    //           this.hasStemQuestionTypeSelection.gapFilling = true;
    //           this.stemQuestionTypesWithStem.push({
    //             type: 'gapFilling',
    //             questionTypeSort: this.stemQuestionTypesWithStem.length + 1
    //           });
    //         }
    //       }
    //       // 判断条件：questionStemId有值且不为空，同时questionType是2（多选题）
    //       else if (item.questionMaterialId && item.questionMaterialId !== '' && item.questionType == 2) {
    //         this.selectedStemManyChoiceQuestionList.push(item);
    //         this.stemIds.push(item.questionMaterialId);
    //         this.stemTitle = item.questionStemText;
    //         // 选中包含题干材料的多选题选择框
    //         if (!this.hasStemQuestionTypeSelection.multipleChoice) {
    //           this.hasStemQuestionTypeSelection.multipleChoice = true;
    //           this.stemQuestionTypesWithStem.push({
    //             type: 'multipleChoice',
    //             questionTypeSort: this.stemQuestionTypesWithStem.length + 1
    //           });
    //         }
    //       }
    //       // 新增简答题处理 - 包含题干
    //       else if (item.questionMaterialId && item.questionMaterialId !== '' && item.questionType == 4) {
    //         this.selectedStemShortAnswerQuestionList.push(item);
    //         this.stemIds.push(item.questionMaterialId);
    //         this.stemTitle = item.questionStemText;
    //         // 选中包含题干材料的简答题选择框
    //         if (!this.hasStemQuestionTypeSelection.shortAnswer) {
    //           this.hasStemQuestionTypeSelection.shortAnswer = true;
    //           this.stemQuestionTypesWithStem.push({
    //             type: 'shortAnswer',
    //             questionTypeSort: this.stemQuestionTypesWithStem.length + 1
    //           });
    //         }
    //       }
    //       // 判断条件：questionStemId为空或无值，同时questionType是0（单选题）
    //       else if (!item.questionMaterialId && item.questionType == 0) {
    //         this.selectedNoStemMultipleChoiceQuestionList.push(item);
    //         // 选中无题干单选题选择框
    //         if (!this.hasNoStemQuestionTypeSelection.singleChoice) {
    //           this.hasNoStemQuestionTypeSelection.singleChoice = true;
    //           this.stemQuestionTypesWithoutStem.push({
    //             type: 'singleChoice',
    //             questionTypeSort: this.stemQuestionTypesWithoutStem.length + 1
    //           });
    //         }
    //       }
    //       // 判断条件：questionStemId为空或无值，同时questionType是1（填空题）
    //       else if (!item.questionMaterialId && item.questionType == 1) {
    //         this.selectedNoStemGapFillingQuestionList.push(item);
    //         // 选中无题干填空题选择框
    //         if (!this.hasNoStemQuestionTypeSelection.gapFilling) {
    //           this.hasNoStemQuestionTypeSelection.gapFilling = true;
    //           this.stemQuestionTypesWithoutStem.push({
    //             type: 'gapFilling',
    //             questionTypeSort: this.stemQuestionTypesWithoutStem.length + 1
    //           });
    //         }
    //       }
    //       // 判断条件：questionStemId为空或无值，同时questionType是2（多选题）
    //       else if (!item.questionMaterialId && item.questionType == 2) {
    //         this.selectedNoStemManyChoiceQuestionList.push(item);
    //         // 选中无题干多选题选择框
    //         if (!this.hasNoStemQuestionTypeSelection.multipleChoice) {
    //           this.hasNoStemQuestionTypeSelection.multipleChoice = true;
    //           this.stemQuestionTypesWithoutStem.push({
    //             type: 'multipleChoice',
    //             questionTypeSort: this.stemQuestionTypesWithoutStem.length + 1
    //           });
    //         }
    //       }// 新增简答题处理 - 不包含题干
    //       else if (!item.questionMaterialId && item.questionType == 4) {
    //         this.selectedNoStemShortAnswerQuestionList.push(item);
    //         // 选中无题干简答题选择框
    //         if (!this.hasNoStemQuestionTypeSelection.shortAnswer) {
    //           this.hasNoStemQuestionTypeSelection.shortAnswer = true;
    //           this.stemQuestionTypesWithoutStem.push({
    //             type: 'shortAnswer',
    //             questionTypeSort: this.stemQuestionTypesWithoutStem.length + 1
    //           });
    //         }
    //       }
    //     });
    //     // 根据 questionTypeSort 排序
    //     this.stemQuestionTypesWithStem.sort((a, b) => a.questionTypeSort - b.questionTypeSort);
    //     this.stemQuestionTypesWithoutStem.sort((a, b) => a.questionTypeSort - b.questionTypeSort);
    //
    //     // 根据sortsNum对各个题目列表进行排序
    //     this.selectedStemMultipleChoiceQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
    //     this.selectedStemGapFillingQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
    //     this.selectedStemManyChoiceQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
    //     this.selectedNoStemMultipleChoiceQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
    //     this.selectedNoStemGapFillingQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
    //     this.selectedNoStemManyChoiceQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
    //     // 新增简答题排序
    //     this.selectedStemShortAnswerQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
    //     this.selectedNoStemShortAnswerQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
    //   }
    // },

    // 处理题干选择逻辑
    handleQuestionStemSelection() {
      const list = this.formData.courseProcessStudyTestCoList;
      console.log('list', list);

      // 如果列表为空或不存在，则默认不勾选
      if (!list || list.length === 0) {
        this.questionStemSelection.hasStem = false;
        this.questionStemSelection.hasNoStem = false;
        return;
      }

      // 检查是否有带题干和不带题干的题目
      let hasStem = false;
      let hasNoStem = false;

      for (const item of list) {
        if (item.questionMaterialId && item.questionMaterialId !== '') {
          hasStem = true;
        } else {
          hasNoStem = true;
        }

        // 如果两个条件都满足，可以提前结束循环
        if (hasStem && hasNoStem) {
          break;
        }
      }

      // 根据检查结果设置勾选状态
      if (hasStem && hasNoStem) {
        // 既有带题干也有不带题干的题目
        this.questionStemSelection.hasStem = true;
        this.questionStemSelection.hasNoStem = true;
      } else if (hasStem) {
        // 只有带题干的题目
        this.questionStemSelection.hasStem = true;
        this.questionStemSelection.hasNoStem = false;
      } else {
        // 只有不带题干的题目（或者都没有）
        this.questionStemSelection.hasStem = false;
        this.questionStemSelection.hasNoStem = true;
      }

      // 清空现有列表
      this.selectedStemMultipleChoiceQuestionList = [];
      this.selectedStemGapFillingQuestionList = [];
      this.selectedStemManyChoiceQuestionList = [];
      this.selectedNoStemMultipleChoiceQuestionList = [];
      this.selectedNoStemGapFillingQuestionList = [];
      this.selectedNoStemManyChoiceQuestionList = [];
      // 新增简答题列表清空
      this.selectedStemShortAnswerQuestionList = [];
      this.selectedNoStemShortAnswerQuestionList = [];

      // 重置题型选择状态
      this.hasStemQuestionTypeSelection.singleChoice = false;
      this.hasStemQuestionTypeSelection.multipleChoice = false;
      this.hasStemQuestionTypeSelection.gapFilling = false;
      this.hasStemQuestionTypeSelection.shortAnswer = false; // 新增
      this.hasNoStemQuestionTypeSelection.singleChoice = false;
      this.hasNoStemQuestionTypeSelection.multipleChoice = false;
      this.hasNoStemQuestionTypeSelection.gapFilling = false;
      this.hasNoStemQuestionTypeSelection.shortAnswer = false; // 新增

      // 清空题型顺序数组
      this.stemQuestionTypesWithStem = [];
      this.stemQuestionTypesWithoutStem = [];

      // 遍历courseProcessStudyTestCoList，根据条件填充对应列表并选中相应选择框
      if (list && list.length > 0) {
        list.forEach(item => {
          // 判断条件：questionMaterialId，同时questionType是0（单选题）
          if (item.questionMaterialId && item.questionMaterialId !== '' && item.questionType == 0) {
            this.selectedStemMultipleChoiceQuestionList.push(item);
            this.stemIds.push(item.questionMaterialId);
            this.stemTitle = item.questionStemText;
            // 选中包含题干材料的单选题选择框
            if (!this.hasStemQuestionTypeSelection.singleChoice) {
              this.hasStemQuestionTypeSelection.singleChoice = true;
              // 查找已存在的 questionTypeSort，如果不存在则使用默认值
              const existingSort = item.questionTypeSort || (this.stemQuestionTypesWithStem.length + 1);
              this.stemQuestionTypesWithStem.push({
                type: 'singleChoice',
                questionTypeSort: existingSort
              });
            }
          }
          // 判断条件：questionStemId有值且不为空，同时questionType是1（填空题）
          else if (item.questionMaterialId && item.questionMaterialId !== '' && item.questionType == 1) {
            this.selectedStemGapFillingQuestionList.push(item);
            this.stemIds.push(item.questionMaterialId);
            this.stemTitle = item.questionStemText;
            // 选中包含题干材料的填空题选择框
            if (!this.hasStemQuestionTypeSelection.gapFilling) {
              this.hasStemQuestionTypeSelection.gapFilling = true;
              // 查找已存在的 questionTypeSort，如果不存在则使用默认值
              const existingSort = item.questionTypeSort || (this.stemQuestionTypesWithStem.length + 1);
              this.stemQuestionTypesWithStem.push({
                type: 'gapFilling',
                questionTypeSort: existingSort
              });
            }
          }
          // 判断条件：questionStemId有值且不为空，同时questionType是2（多选题）
          else if (item.questionMaterialId && item.questionMaterialId !== '' && item.questionType == 2) {
            this.selectedStemManyChoiceQuestionList.push(item);
            this.stemIds.push(item.questionMaterialId);
            this.stemTitle = item.questionStemText;
            // 选中包含题干材料的多选题选择框
            if (!this.hasStemQuestionTypeSelection.multipleChoice) {
              this.hasStemQuestionTypeSelection.multipleChoice = true;
              // 查找已存在的 questionTypeSort，如果不存在则使用默认值
              const existingSort = item.questionTypeSort || (this.stemQuestionTypesWithStem.length + 1);
              this.stemQuestionTypesWithStem.push({
                type: 'multipleChoice',
                questionTypeSort: existingSort
              });
            }
          }
          // 新增简答题处理 - 包含题干
          else if (item.questionMaterialId && item.questionMaterialId !== '' && item.questionType == 4) {
            this.selectedStemShortAnswerQuestionList.push(item);
            this.stemIds.push(item.questionMaterialId);
            this.stemTitle = item.questionStemText;
            // 选中包含题干材料的简答题选择框
            if (!this.hasStemQuestionTypeSelection.shortAnswer) {
              this.hasStemQuestionTypeSelection.shortAnswer = true;
              // 查找已存在的 questionTypeSort，如果不存在则使用默认值
              const existingSort = item.questionTypeSort || (this.stemQuestionTypesWithStem.length + 1);
              this.stemQuestionTypesWithStem.push({
                type: 'shortAnswer',
                questionTypeSort: existingSort
              });
            }
          }
          // 判断条件：questionStemId为空或无值，同时questionType是0（单选题）
          else if (!item.questionMaterialId && item.questionType == 0) {
            this.selectedNoStemMultipleChoiceQuestionList.push(item);
            // 选中无题干单选题选择框
            if (!this.hasNoStemQuestionTypeSelection.singleChoice) {
              this.hasNoStemQuestionTypeSelection.singleChoice = true;
              // 查找已存在的 questionTypeSort，如果不存在则使用默认值
              const existingSort = item.questionTypeSort || (this.stemQuestionTypesWithoutStem.length + 1);
              this.stemQuestionTypesWithoutStem.push({
                type: 'singleChoice',
                questionTypeSort: existingSort
              });
            }
          }
          // 判断条件：questionStemId为空或无值，同时questionType是1（填空题）
          else if (!item.questionMaterialId && item.questionType == 1) {
            this.selectedNoStemGapFillingQuestionList.push(item);
            // 选中无题干填空题选择框
            if (!this.hasNoStemQuestionTypeSelection.gapFilling) {
              this.hasNoStemQuestionTypeSelection.gapFilling = true;
              // 查找已存在的 questionTypeSort，如果不存在则使用默认值
              const existingSort = item.questionTypeSort || (this.stemQuestionTypesWithoutStem.length + 1);
              this.stemQuestionTypesWithoutStem.push({
                type: 'gapFilling',
                questionTypeSort: existingSort
              });
            }
          }
          // 判断条件：questionStemId为空或无值，同时questionType是2（多选题）
          else if (!item.questionMaterialId && item.questionType == 2) {
            this.selectedNoStemManyChoiceQuestionList.push(item);
            // 选中无题干多选题选择框
            if (!this.hasNoStemQuestionTypeSelection.multipleChoice) {
              this.hasNoStemQuestionTypeSelection.multipleChoice = true;
              // 查找已存在的 questionTypeSort，如果不存在则使用默认值
              const existingSort = item.questionTypeSort || (this.stemQuestionTypesWithoutStem.length + 1);
              this.stemQuestionTypesWithoutStem.push({
                type: 'multipleChoice',
                questionTypeSort: existingSort
              });
            }
          }
          // 新增简答题处理 - 不包含题干
          else if (!item.questionMaterialId && item.questionType == 4) {
            this.selectedNoStemShortAnswerQuestionList.push(item);
            // 选中无题干简答题选择框
            if (!this.hasNoStemQuestionTypeSelection.shortAnswer) {
              this.hasNoStemQuestionTypeSelection.shortAnswer = true;
              // 查找已存在的 questionTypeSort，如果不存在则使用默认值
              const existingSort = item.questionTypeSort || (this.stemQuestionTypesWithoutStem.length + 1);
              this.stemQuestionTypesWithoutStem.push({
                type: 'shortAnswer',
                questionTypeSort: existingSort
              });
            }
          }
        });
        // 根据 questionTypeSort 排序
        this.stemQuestionTypesWithStem.sort((a, b) => a.questionTypeSort - b.questionTypeSort);
        this.stemQuestionTypesWithoutStem.sort((a, b) => a.questionTypeSort - b.questionTypeSort);

        // 根据sortsNum对各个题目列表进行排序
        this.selectedStemMultipleChoiceQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
        this.selectedStemGapFillingQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
        this.selectedStemManyChoiceQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
        this.selectedNoStemMultipleChoiceQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
        this.selectedNoStemGapFillingQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
        this.selectedNoStemManyChoiceQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
        // 新增简答题排序
        this.selectedStemShortAnswerQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
        this.selectedNoStemShortAnswerQuestionList.sort((a, b) => a.sortsNum - b.sortsNum);
      }
    },

    // 处理包含题干选项变化
    handleHasStemChange(val) {
      if (!val) {
        // 取消选中时清空包含题干的数据
        this.selectedStemMultipleChoiceQuestionList = [];
        this.selectedStemGapFillingQuestionList = [];
        this.selectedStemManyChoiceQuestionList = [];
        // 新增简答题清空
        this.selectedStemShortAnswerQuestionList = [];

        // 清空题干材料相关数据
        this.stemIds = [];
        this.stemTitle = '';

        // 重置包含题干的题型选择状态
        this.hasStemQuestionTypeSelection.singleChoice = false;
        this.hasStemQuestionTypeSelection.multipleChoice = false;
        this.hasStemQuestionTypeSelection.gapFilling = false;
        this.hasStemQuestionTypeSelection.shortAnswer = false; // 新增

        // 清空题型顺序数组中包含题干的部分
        this.stemQuestionTypesWithStem = [];

        // 从formData.courseProcessStudyTestCoList中删除包含题干的题目
        this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
          !question.questionMaterialId || question.questionMaterialId === ''
        );
      }
    },

    // 处理不含题干选项变化
    handleHasNoStemChange(val) {
      if (!val) {
        // 取消选中时清空不含题干的数据
        this.selectedNoStemMultipleChoiceQuestionList = [];
        this.selectedNoStemGapFillingQuestionList = [];
        this.selectedNoStemManyChoiceQuestionList = [];
        // 新增简答题清空
        this.selectedNoStemShortAnswerQuestionList = [];

        // 重置不含题干的题型选择状态
        this.hasNoStemQuestionTypeSelection.singleChoice = false;
        this.hasNoStemQuestionTypeSelection.multipleChoice = false;
        this.hasNoStemQuestionTypeSelection.gapFilling = false;
        this.hasNoStemQuestionTypeSelection.shortAnswer = false; // 新增

        // 清空题型顺序数组中不含题干的部分
        this.stemQuestionTypesWithoutStem = [];

        // 从formData.courseProcessStudyTestCoList中删除不包含题干的题目
        this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
          question.questionMaterialId && question.questionMaterialId !== ''
        );
      }
    },
    // 移除题型
    removeQuestionType(group, type) {
      if (group === 'hasStem') {
        this.hasStemQuestionTypeSelection[type] = false;
        // 清除对应的题目列表
        if (type === 'singleChoice') {
          // 从formData.courseProcessStudyTestCoList中删除对应题目
          this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
            !(question.questionMaterialId && question.questionMaterialId !== '' && question.questionType == 0)
          );
          this.selectedStemMultipleChoiceQuestionList = [];
        } else if (type === 'multipleChoice') {
          // 从formData.courseProcessStudyTestCoList中删除对应题目
          this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
            !(question.questionMaterialId && question.questionMaterialId !== '' && question.questionType == 2)
          );
          this.selectedStemManyChoiceQuestionList = [];
        } else if (type === 'gapFilling') {
          // 从formData.courseProcessStudyTestCoList中删除对应题目
          this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
            !(question.questionMaterialId && question.questionMaterialId !== '' && question.questionType == 1)
          );
          this.selectedStemGapFillingQuestionList = [];
        } // 新增简答题处理
        else if (type === 'shortAnswer') {
          this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
            !(question.questionMaterialId && question.questionMaterialId !== '' && question.questionType == 4)
          );
          this.selectedStemShortAnswerQuestionList = [];
        }
        // 从题型顺序数组中移除
        const index = this.stemQuestionTypesWithStem.findIndex(item => item.type === type);
        if (index > -1) {
          this.stemQuestionTypesWithStem.splice(index, 1);
          // 重新排列剩余项的排序值
          this.reorderQuestionTypes(this.stemQuestionTypesWithStem);
        }
      } else {
        this.hasNoStemQuestionTypeSelection[type] = false;
        // 清除对应的题目列表
        if (type === 'singleChoice') {
          // 从formData.courseProcessStudyTestCoList中删除对应题目
          this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
            !(!question.questionMaterialId && question.questionType == 0)
          );
          this.selectedNoStemMultipleChoiceQuestionList = [];
        } else if (type === 'multipleChoice') {
          // 从formData.courseProcessStudyTestCoList中删除对应题目
          this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
            !(!question.questionMaterialId && question.questionType == 2)
          );
          this.selectedNoStemManyChoiceQuestionList = [];
        } else if (type === 'gapFilling') {
          // 从formData.courseProcessStudyTestCoList中删除对应题目
          this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
            !(!question.questionMaterialId && question.questionType == 1)
          );
          this.selectedNoStemGapFillingQuestionList = [];
        } // 新增简答题处理
        else if (type === 'shortAnswer') {
          this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question =>
            !(!question.questionMaterialId && question.questionType == 4)
          );
          this.selectedNoStemShortAnswerQuestionList = [];
        }
        // 从题型顺序数组中移除
        const index = this.stemQuestionTypesWithoutStem.findIndex(item => item.type === type);
        if (index > -1) {
          this.stemQuestionTypesWithoutStem.splice(index, 1);
          // 重新排列剩余项的排序值
          this.reorderQuestionTypes(this.stemQuestionTypesWithoutStem);
        }
      }
    },

    // 上移题型
    moveQuestionTypeUp(list, index) {
      if (index > 0) {
        const temp = list[index];
        this.$set(list, index, list[index - 1]);
        this.$set(list, index - 1, temp);

        // 交换 questionTypeSort 值
        const tempSort = list[index].questionTypeSort;
        list[index].questionTypeSort = list[index - 1].questionTypeSort;
        list[index - 1].questionTypeSort = tempSort;

        // 同步更新对应题目中的 questionTypeSort 值
        this.updateQuestionsQuestionTypeSort(list[index].type, list[index].questionTypeSort);
        this.updateQuestionsQuestionTypeSort(list[index - 1].type, list[index - 1].questionTypeSort);
      }
    },

    // 下移题型
    moveQuestionTypeDown(list, index) {
      if (index < list.length - 1) {
        const temp = list[index];
        this.$set(list, index, list[index + 1]);
        this.$set(list, index + 1, temp);

        // 交换 questionTypeSort 值
        const tempSort = list[index].questionTypeSort;
        list[index].questionTypeSort = list[index + 1].questionTypeSort;
        list[index + 1].questionTypeSort = tempSort;

        // 同步更新对应题目中的 questionTypeSort 值
        this.updateQuestionsQuestionTypeSort(list[index].type, list[index].questionTypeSort);
        this.updateQuestionsQuestionTypeSort(list[index + 1].type, list[index + 1].questionTypeSort);
      }
    },
    // 根据题型更新对应题目中的 questionTypeSort 值
    updateQuestionsQuestionTypeSort(questionType, newQuestionTypeSort) {
      let questionList = [];
      let isWithStem = false;

      // 根据题型类型确定要更新的题目列表
      switch(questionType) {
        case 'singleChoice':
          questionList = this.hasStemQuestionTypeSelection.singleChoice ?
            this.selectedStemMultipleChoiceQuestionList : this.selectedNoStemMultipleChoiceQuestionList;
          isWithStem = this.hasStemQuestionTypeSelection.singleChoice;
          break;
        case 'multipleChoice':
          questionList = this.hasStemQuestionTypeSelection.multipleChoice ?
            this.selectedStemManyChoiceQuestionList : this.selectedNoStemManyChoiceQuestionList;
          isWithStem = this.hasStemQuestionTypeSelection.multipleChoice;
          break;
        case 'gapFilling':
          questionList = this.hasStemQuestionTypeSelection.gapFilling ?
            this.selectedStemGapFillingQuestionList : this.selectedNoStemGapFillingQuestionList;
          isWithStem = this.hasStemQuestionTypeSelection.gapFilling;
          break;
        case 'shortAnswer':
          questionList = this.hasStemQuestionTypeSelection.shortAnswer ?
            this.selectedStemShortAnswerQuestionList : this.selectedNoStemShortAnswerQuestionList;
          isWithStem = this.hasStemQuestionTypeSelection.shortAnswer;
          break;
      }

      // 更新题目列表中每个题目的 questionTypeSort 值
      questionList.forEach(question => {
        question.questionTypeSort = newQuestionTypeSort;
      });

      // 同步更新 formData.courseProcessStudyTestCoList 中的题目
      this.formData.courseProcessStudyTestCoList.forEach(question => {
        // 判断是否为当前题型的题目
        const isCurrentQuestionType = (
          (questionType === 'singleChoice' && question.questionType == 0) ||
          (questionType === 'multipleChoice' && question.questionType == 2) ||
          (questionType === 'gapFilling' && question.questionType == 1) ||
          (questionType === 'shortAnswer' && question.questionType == 4)
        );

        // 判断题干条件是否匹配
        const stemConditionMatch = isWithStem ?
          (question.questionMaterialId && question.questionMaterialId !== '') :
          (!question.questionMaterialId);

        if (isCurrentQuestionType && stemConditionMatch) {
          question.questionTypeSort = newQuestionTypeSort;
        }
      });
    },

    // 重新排列题型排序值
    reorderQuestionTypes(list) {
      list.sort((a, b) => a.questionTypeSort - b.questionTypeSort)
        .forEach((item, index) => {
          item.questionTypeSort = index + 1;
        });
    },

    // 上移题目
    moveUpQuestion(list, index) {
      console.log('上移', list);
      if (index > 0) {
        const temp = list[index];
        this.$set(list, index, list[index - 1]);
        this.$set(list, index - 1, temp);
        // 更新sortsNum属性
        const tempSortsNum = list[index].sortsNum;
        list[index].sortsNum = list[index - 1].sortsNum;
        list[index - 1].sortsNum = tempSortsNum;

        // 同步更新formData.courseProcessStudyTestCoList中的sortsNum
        const questionIndex1 = this.formData.courseProcessStudyTestCoList.findIndex(q => q.id === list[index].id);
        const questionIndex2 = this.formData.courseProcessStudyTestCoList.findIndex(q => q.id === list[index - 1].id);
        if (questionIndex1 > -1) {
          this.formData.courseProcessStudyTestCoList[questionIndex1].sortsNum = list[index].sortsNum;
        }
        if (questionIndex2 > -1) {
          this.formData.courseProcessStudyTestCoList[questionIndex2].sortsNum = list[index - 1].sortsNum;
        }
      }
    },

    // 下移题目
    moveDownQuestion(list, index) {
      console.log('下移', list);
      if (index < list.length - 1) {
        const temp = list[index];
        this.$set(list, index, list[index + 1]);
        this.$set(list, index + 1, temp);
        // 更新sortsNum属性
        const tempSortsNum = list[index].sortsNum;
        list[index].sortsNum = list[index + 1].sortsNum;
        list[index + 1].sortsNum = tempSortsNum;

        // 同步更新formData.courseProcessStudyTestCoList中的sortsNum
        const questionIndex1 = this.formData.courseProcessStudyTestCoList.findIndex(q => q.id === list[index].id);
        const questionIndex2 = this.formData.courseProcessStudyTestCoList.findIndex(q => q.id === list[index + 1].id);
        if (questionIndex1 > -1) {
          this.formData.courseProcessStudyTestCoList[questionIndex1].sortsNum = list[index].sortsNum;
        }
        if (questionIndex2 > -1) {
          this.formData.courseProcessStudyTestCoList[questionIndex2].sortsNum = list[index + 1].sortsNum;
        }
      }
    },

    // 删除题目
    deleteQuestion(list, index) {
      // 从显示列表中删除题目
      const removedQuestion = list.splice(index, 1)[0];

      // 同时从formData.courseProcessStudyTestCoList中删除该题目，避免重复检查时误判
      const questionIndex = this.formData.courseProcessStudyTestCoList.findIndex(q => q.id === removedQuestion.id);
      if (questionIndex > -1) {
        this.formData.courseProcessStudyTestCoList.splice(questionIndex, 1);
      }
    },

    // 打开选择题干材料弹窗
    openQuestionStemDialog() {
      this.type = '2';
      this.dialogTitle = '选择题干材料';
      this.questionStemDialog = true;
    },
    // 打开添加题目弹窗
    openAddQuestionDialog(index, item) {
      this.questionType = index;
      this.questionMaterialId = item == 1 ? this.stemIds[0] : '';
      this.type = '3';
      this.stemStatus = item;
      this.dialogTitle = '选择题目';
      this.questionStemDialog = true;
    },
    async openPreviewDialog(item) {
      console.log('item', item);
      this.viewId = item.questionId;
      this.previewDialog = true;
      this.dialogVisible = true;
    },
    // 计算题目总分
    calculateTotalScore(questionList) {
      if (!questionList || questionList.length === 0) {
        return 0;
      }
      return questionList.reduce((total, question) => {
        return total + (parseInt(question.questionScore) || 0);
      }, 0);
    },
    // 获取题干/题目列表
    // 获取题干/题目列表
    confirmKonwLedge() {
      if (this.type == 2) {
        const selectedStemList = this.$refs.knowledgePoint.materialSelection;
        if (selectedStemList.length > 0) {
          // 获取新的题干材料信息
          const newStemId = selectedStemList[0].id;
          const newStemTitle = selectedStemList[0].questionMaterialTitle;
          // 获取旧的题干材料ID
          const oldStemId = this.stemIds && this.stemIds.length > 0 ? this.stemIds[0] : null;
          // 更新题干材料ID和标题
          this.stemIds = [newStemId];
          this.stemTitle = newStemTitle;
          // 清空包含旧题干材料的题目列表
          this.selectedStemMultipleChoiceQuestionList = [];
          this.selectedStemGapFillingQuestionList = [];
          this.selectedStemManyChoiceQuestionList = [];

          // 从formData.courseProcessStudyTestCoList中过滤掉包含旧题干材料ID的题目
          this.formData.courseProcessStudyTestCoList = this.formData.courseProcessStudyTestCoList.filter(question => {
            return !(question.questionMaterialId && question.questionMaterialId === oldStemId);
          });
        }
      } else if (this.type == 3) {
        const selectedQuestionList = this.$refs.knowledgePoint.questionSelection;
        // 检查是否有重复的题目
        const existingQuestionIds = this.formData.courseProcessStudyTestCoList.map(q => q.questionId);
        const duplicateQuestions = selectedQuestionList.filter(question => existingQuestionIds.includes(question.questionId));

        if (duplicateQuestions.length > 0) {
          // 提示用户有重复的题目
          this.$message.warning('检测到重复的题目，请重新选择');
          return;
        }

        const formattedQuestions = selectedQuestionList.map((question, index) => {
          // 根据题型和是否有题干确定 questionTypeSort 值
          let questionTypeSort = 1;
          if (this.stemStatus == 1) { // 包含题干
            if (this.questionType == 0) { // 单选题
              const stemTypeIndex = this.stemQuestionTypesWithStem.findIndex(item => item.type === 'singleChoice');
              questionTypeSort = stemTypeIndex >= 0 ? this.stemQuestionTypesWithStem[stemTypeIndex].questionTypeSort : 1;
            } else if (this.questionType == 1) { // 填空题
              const stemTypeIndex = this.stemQuestionTypesWithStem.findIndex(item => item.type === 'gapFilling');
              questionTypeSort = stemTypeIndex >= 0 ? this.stemQuestionTypesWithStem[stemTypeIndex].questionTypeSort : 2;
            } else if (this.questionType == 2) { // 多选题
              const stemTypeIndex = this.stemQuestionTypesWithStem.findIndex(item => item.type === 'multipleChoice');
              questionTypeSort = stemTypeIndex >= 0 ? this.stemQuestionTypesWithStem[stemTypeIndex].questionTypeSort : 3;
            } else if (this.questionType == 4) { // 简答题
              const stemTypeIndex = this.stemQuestionTypesWithStem.findIndex(item => item.type === 'shortAnswer');
              questionTypeSort = stemTypeIndex >= 0 ? this.stemQuestionTypesWithStem[stemTypeIndex].questionTypeSort : 4;
            }
          } else { // 不包含题干
            if (this.questionType == 0) { // 单选题
              const noStemTypeIndex = this.stemQuestionTypesWithoutStem.findIndex(item => item.type === 'singleChoice');
              questionTypeSort = noStemTypeIndex >= 0 ? this.stemQuestionTypesWithoutStem[noStemTypeIndex].questionTypeSort : 1;
            } else if (this.questionType == 1) { // 填空题
              const noStemTypeIndex = this.stemQuestionTypesWithoutStem.findIndex(item => item.type === 'gapFilling');
              questionTypeSort = noStemTypeIndex >= 0 ? this.stemQuestionTypesWithoutStem[noStemTypeIndex].questionTypeSort : 2;
            } else if (this.questionType == 2) { // 多选题
              const noStemTypeIndex = this.stemQuestionTypesWithoutStem.findIndex(item => item.type === 'multipleChoice');
              questionTypeSort = noStemTypeIndex >= 0 ? this.stemQuestionTypesWithoutStem[noStemTypeIndex].questionTypeSort : 3;
            } else if (this.questionType == 4) { // 简答题
              const noStemTypeIndex = this.stemQuestionTypesWithoutStem.findIndex(item => item.type === 'shortAnswer');
              questionTypeSort = noStemTypeIndex >= 0 ? this.stemQuestionTypesWithoutStem[noStemTypeIndex].questionTypeSort : 4;
            }
          }

          return {
            id: question.id,
            questionMaterialId: question.questionMaterialId,
            questionStemText: question.questionMaterialTitle || this.stemTitle,
            questionId: question.questionId,
            questionName: question.questionText,
            questionType: question.questionType,
            questionImg: Array.isArray(question.questionImg) && question.questionImg.length > 0 ? question.questionImg.join(',') : '',
            sortsNum: index + 1,
            questionScore: parseInt(question.questionScore),
            analysis: question.analysis,
            correctAnswer: question.correctAnswer,
            questionDifficulty: question.questionDifficulty,
            answerTime: question.answerTime,
            disciplineId: question.disciplineId,
            gradeId: question.gradeId,
            questionTypeSort: questionTypeSort // 添加 questionTypeSort 字段
          };
        });
        this.formData.courseProcessStudyTestCoList.push(...formattedQuestions);
        if (formattedQuestions.length > 0) {
          // 创建一个映射对象来简化条件判断
          const questionListMap = {
            // stemStatus == 1 的情况（包含题干）
            '0-1': 'selectedStemMultipleChoiceQuestionList',
            '1-1': 'selectedStemGapFillingQuestionList',
            '2-1': 'selectedStemManyChoiceQuestionList',
            '4-1': 'selectedStemShortAnswerQuestionList', // 新增简答题映射
            // stemStatus == 2 的情况（不含题干）
            '0-2': 'selectedNoStemMultipleChoiceQuestionList',
            '1-2': 'selectedNoStemGapFillingQuestionList',
            '2-2': 'selectedNoStemManyChoiceQuestionList',
            '4-2': 'selectedNoStemShortAnswerQuestionList' // 新增简答题映射
          };
          const key = `${this.questionType}-${this.stemStatus}`;
          if (questionListMap[key]) {
            formattedQuestions.forEach((item) => {
              this[questionListMap[key]].push(item);
            });
          }
        }
      }
      this.questionStemDialog = false;
      this.dialogKey++;
    },
    dialogBeforeClose() {
      this.questionStemDialog = false;
      // 增加key值强制重新渲染组件
      this.dialogKey++;
    },
    showAddQuestionDialog() {
      if (this.formData.topic === '') {
        this.$message.warning('请选择是否包含题干');
        return;
      }
      this.dialogVisible = true;
      //丢失找回部分
      this.addQuestionForm.questionType = '';
      //end
      // 回显已添加的题型
      this.questionTypes = this.formData.processQuestionTypeCoList.map((item) => item.questionType);
      // 延迟重置表单，确保DOM已更新
      this.$nextTick(() => {
        if (this.$refs.addQuestionForm) {
          this.$refs.addQuestionForm.resetFields();
          this.$refs.addQuestionForm.clearValidate();
        }
      });
    },
    handleDialogClose() {
      this.addQuestionForm.questionType = '';
      if (this.$refs.addQuestionForm) {
        this.$refs.addQuestionForm.resetFields();
        this.$refs.addQuestionForm.clearValidate();
      }
    },
    cancelAddQuestion() {
      this.dialogVisible = false;
      this.addQuestionForm.questionType = '';
      if (this.$refs.addQuestionForm) {
        this.$refs.addQuestionForm.resetFields();
        this.$refs.addQuestionForm.clearValidate();
      }
    },
    getQuestionTypeTitle(type) {
      return this.displayTitles[type] || '未知题型';
    },
    // 获取传递给父组件的数据
    getData() {
      // 去除多余参数
      const { questionType, questions, ...rest } = this.formData;
      return rest;
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.answerQuestionForm.validate((valid) => {
          if (valid) {
            resolve(true);
          } else {
            reject(false);
          }
        });
      });
    },
    // 根据标题搜索题干材料
    searchStem() {

    }
  }
};
</script>

<style scoped>
.process-card {
  margin-top: 10px;
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
}

.delete-button {
  padding: 0;
  font-size: 20px;
  color: #2a2a3b;
}

.process-form-content {
  padding: 0 20px 0 0;
}

::v-deep .el-card__header {
  padding: 5px 20px;
}

.process-card ::v-deep .el-form-item {
  margin-bottom: 12px;
}

.process-card ::v-deep .el-select {
  width: 100%;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
}

.checkbox-group-horizontal {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 30px;
}

.checkbox-group .el-checkbox {
  margin-right: 0;
  margin-bottom: 10px;
}

.question-section {
  /* padding-left: 100px;
  margin-top: -10px; */
}

.section-title {
  font-weight: bold;
  color: #606266;
}

.question-type-list {
  margin-bottom: 10px;
}

.question-type-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 5px 10px;
  margin-bottom: 10px;
  background-color: #fff;
}

.active-item {
  border: 1px solid #409eff;
}

.question-type-header {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.dots {
  color: #909399;
  margin-right: 5px;
  font-weight: bold;
}

.question-type-title {
  flex: 1;
  font-weight: bold;
}

.delete-btn {
  color: #606266;
  padding: 0;
}

.delete-btn i {
  font-size: 18px;
}

.question-config {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
  padding: 5px 0;
}

.question-config-item {
  display: flex;
  align-items: center;
  width: 45%;
}

.question-label {
  width: 130px;
  text-align: right;
  padding-right: 10px;
}

.question-label.required::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.add-question-btn-container {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.add-question-btn {
  width: 100%;
  background-color: #f0f9ff;
  color: #409eff;
  border: 1px dashed #409eff;
}

::v-deep .el-radio {
  margin-right: 20px;
  margin-bottom: 10px;
}

::v-deep .el-form-item {
  margin-bottom: 15px;
}

::v-deep .el-form-item.is-required .el-form-item__label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.process-form {
  padding: 0 20px;
}

.dialog-footer {
  text-align: center;
}

::v-deep .el-radio input[aria-hidden='true'] {
  display: none !important;
}

::v-deep .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}

::v-deep .custom-input-number {
  width: 300px;
}

::v-deep .custom-input-number .el-input__inner {
  text-align: left;
  padding-left: 10px;
}
</style>
