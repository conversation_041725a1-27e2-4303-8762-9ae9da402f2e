!function(){var e=window.parent;dialog=e.$EDITORUI[window.frameElement.id.replace(/_iframe$/,"")],editor=dialog.editor,UE=e.UE,domUtils=UE.dom.domUtils,utils=UE.utils,browser=UE.browser,ajax=UE.ajax,$G=function(e){return document.getElementById(e)},$focus=function(t){setTimeout(function(){if(browser.ie){var e=t.createTextRange();e.collapse(!1),e.select()}else t.focus()},0)},utils.loadFile(document,{href:"../themes/default/dialogbase.css?cache="+Math.random(),tag:"link",type:"text/css"}),lang=editor.getLang(dialog.className.split("-")[2]),lang&&domUtils.on(window,"load",function(){var e=editor.options.langPath+editor.options.lang+"/images/";for(var t in lang.static){var a=$G(t);if(a){var o=a.tagName,i=lang.static[t];switch(i.src&&((i=utils.extend({},i,!1)).src=e+i.src),i.style&&((i=utils.extend({},i,!1)).style=i.style.replace(/url\s*\(/g,"url("+e)),o.toLowerCase()){case"var":a.parentNode.replaceChild(document.createTextNode(i),a);break;case"select":for(var s,r=a.options,l=0;s=r[l];)s.innerHTML=i.options[l++];for(var n in i)"options"!=n&&a.setAttribute(n,i[n]);break;default:domUtils.setAttributes(a,i)}}}})}();
