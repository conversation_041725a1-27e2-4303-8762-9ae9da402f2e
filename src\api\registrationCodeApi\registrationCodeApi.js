// 采购管理-采购申请相关接口
import request from '@/utils/request';

export default {
  // 俱乐部注册码
  ClubReviewCode(params) {
    return request({
      url: '/znyy/operations/v2/operationsRegistrationCode',
      method: 'GET',
      params
    });
  },
  // 品牌二维码
  StoreQRCode(params) {
    return request({
      url: '/znyy/V2/merchant/schoolRegistrationCode',
      method: 'GET',
      params
    });
  },
  // 俱乐部审核列表
  ClubReviewDataList(params) {
    return request({
      url: '/znyy/operations/v2/operationsAuditPage',
      method: 'GET',
      params
    });
  },
  // 俱乐部审核详情
  ClubReviewDataDetail(params) {
    return request({
      url: '/znyy/operations/v2/queryAuditInfo',
      method: 'GET',
      params
    });
  },

  // 俱乐部审核驳回或通过
  ClubReviewRejectOrPass(data) {
    return request({
      url: '/znyy/operations/v2/operationsAudit',
      method: 'put',
      params: data
    });
  },
  // 门店列表
  StoreListData(params) {
    return request({
      url: '/znyy/V2/merchant/schoolAuditPage',
      method: 'GET',
      params
    });
  },
  // 门店审核详情
  StoreListDataDetail(params) {
    return request({
      url: '/znyy/V2/merchant/queryAuditInfo',
      method: 'GET',
      params
    });
  },
  // 门店审核驳回或通过
  StoreListDataRejectOrPass(data) {
    return request({
      url: '/znyy/V2/merchant/schoolAudit',
      method: 'put',
      params: data
    });
  }
};
