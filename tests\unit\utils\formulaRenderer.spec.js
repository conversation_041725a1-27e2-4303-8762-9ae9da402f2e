import { renderFormulaAuto, renderMixed, renderHtmlWithMath, isLikelyFormula } from '@/utils/formulaRenderer';
import { renderWithLineBreaks } from '@/utils/formulaRenderer';

describe('utils/formulaRenderer', () => {
  describe('renderFormulaAuto', () => {
    it('returns plain text when input is pure Chinese', () => {
      const text = '这是一个中文句子，用于测试渲染结果';
      expect(renderFormulaAuto(text)).toBe(text);
    });

    it('returns plain text for standard English sentences', () => {
      const text = 'This is a simple sentence for renderer baseline.';
      expect(renderFormulaAuto(text)).toBe(text);
    });

    it('returns plain text for very long English paragraphs', () => {
      const text = Array(12).fill('This is an extended narrative describing scientific observations over multiple decades without mathematical intent or notation.').join(' ');
      expect(renderFormulaAuto(text)).toBe(text);
    });

    it('renders math within mixed language text', () => {
      const text = '面积是 $\\pi r^2$，同时包含中文与 English 描述。';
      const html = renderFormulaAuto(text);
      expect(html).toContain('katex');
      expect(html).toContain('面积是 ');
      expect(html).toContain('，同时包含中文与 English 描述。');
      expect(html).not.toContain('ParseError');
    });

    it('does not duplicate raw tuples when rendering', () => {
      const html = renderFormulaAuto('$(0,1)$');
      expect(html).toContain('katex');
      expect(html).not.toContain('(0,1)(0,1)');
      expect(html).not.toContain('ParseError');
    });

    it('handles batch dataset without parse errors', () => {
      const samples = ['纯文本对照组', 'Equation: E=mc^2', '混合 $a^2 + b^2 = c^2$ 测试', '<span>HTML 包裹</span> $\\alpha + \\beta$'];
      samples.forEach((input) => {
        const output = renderFormulaAuto(input);
        expect(output).not.toContain('ParseError');
        if (/\$|\\alpha|\\beta/.test(input)) {
          expect(output).toContain('katex');
        }
      });
    });
    describe('renderWithLineBreaks', () => {
      it('preserves line breaks in plain text', () => {
        const input = '第一行\n第二行\n第三行';
        const html = renderWithLineBreaks(input);
        expect(html.split('<br/>').length - 1).toBe(2);
        expect(html).toContain('第一行');
        expect(html).toContain('第二行');
        expect(html).toContain('第三行');
      });

      it('keeps formula span intact without inserting <br/> inside', () => {
        const input = '行1\n$a^2+b^2=c^2$\n行3';
        const html = renderWithLineBreaks(input);
        // 两个换行 => 两个 <br/>
        expect(html.split('<br/>').length - 1).toBe(2);
        // 公式存在且没有被拆分插入 <br/>
        const katexSpanMatch = html.match(/<span class="katex[\s\S]*?<\/span>/);
        expect(katexSpanMatch).toBeTruthy();
        expect(katexSpanMatch[0]).not.toContain('<br/>');
      });

      it('returns empty string for empty input', () => {
        expect(renderWithLineBreaks('')).toBe('');
      });

      it('handles multi-line geometry problem with escaped newlines', () => {
        const input =
          '在直四棱柱 ABCD-A1B1C1D1 中， 已知底面四边形 ABCD 是边长为 3 的菱形，且 DB=3，A1A=2，点 E 在线段 BC 上，点 F 在线段 D1C1 ,且 BE=D1F=1 .\\n（I） 求证： 直线 EF ∥平面 B1D1DB；\\n（II） 求二面角 F-DB-C 的余弦值 .\\n如图，四棱锥 P-ABCD  中，底面  ABCD 为正方形，PA⊥平面  ABCD，且 PA=AB=2 ,E 、F 分别为 PB 、PD 的中点 .\\n（I） 求证：PC⊥平面 AEF；\\n（II） 直线 ED  与平面 AEF 所成角的余弦值：\\n（III） 求二面角 B-PC-D 的大小';
        const html = renderWithLineBreaks(input);
        // 原始包含 6 个 \\n => 6 行分隔 -> 6 个 <br/> (行数-1)
        expect(html.split('<br/>').length - 1).toBe(6);
        expect(html).toContain('在直四棱柱 ABCD-A1B1C1D1 中');
        expect(html).toContain('（III） 求二面角 B-PC-D 的大小');
        expect(html).not.toContain('ParseError');
        // 不应出现 katex（没有公式定界符）
        expect(html).not.toContain('katex');
      });
    });
  });

  describe('renderMixed', () => {
    it('renders hybrid content with inline math', () => {
      const html = renderMixed('温度 $T=300K$ with explanation.');
      expect(html).toContain('温度 ');
      expect(html).toContain(' with explanation.');
      expect(html).toContain('katex');
      expect(html).not.toContain('ParseError');
    });
  });

  describe('renderHtmlWithMath', () => {
    it('retains safe tags and renders formulas', () => {
      const html = renderHtmlWithMath('<strong>速度</strong> = $\\frac{d}{t}$');
      expect(html).toContain('<strong>速度</strong>');
      expect(html).toContain('katex');
      expect(html).not.toContain('<script');
      expect(html).not.toContain('ParseError');
    });

    it('supports nested tags with inline formulas', () => {
      const html = renderHtmlWithMath('<p><span><strong>能量</strong></span> 等于 $E=mc^2$</p>');
      expect(html).toContain('<p><span><strong>能量</strong></span>');
      expect(html).toContain('katex');
      expect(html).toContain('</p>');
      expect(html).not.toContain('ParseError');
    });
  });

  describe('isLikelyFormula', () => {
    it('detects obvious formulas', () => {
      expect(isLikelyFormula('E=mc^2')).toBe(true);
      expect(isLikelyFormula('\\frac{a}{b} + c^2')).toBe(true);
      expect(isLikelyFormula('$x+y$')).toBe(true);
    });

    it('rejects plain language', () => {
      expect(isLikelyFormula('纯中文段落没有公式')).toBe(false);
      expect(isLikelyFormula('This is just ordinary text without math.')).toBe(false);
    });
  });
});
