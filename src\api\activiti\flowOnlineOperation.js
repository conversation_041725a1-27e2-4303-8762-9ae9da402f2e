import request from '@/utils/request'

export default {
  //分页
  listWorkOrder(data) {
    return request({
      url: '/activiti/flowOnlineOperation/listWorkOrder',
      method: 'GET',
      params: data
    })
  },
  startAndTakeUserTask(key,data){
    return request({
      url: '/activiti/flowOnlineOperation/startAndTakeUserTask/'+key,
      method: 'POST',
      data
    })
  },
  submitUserTask(data){
    return request({
      url: '/activiti/flowOnlineOperation/submitUserTask',
      method: 'POST',
      data
    })
  },
  viewUserTask(data){
    return request({
      url: '/activiti/flowOnlineOperation/viewUserTask',
      method: 'GET',
      params: data
    })
  },
  viewHistoricProcessInstance(data){
    return request({
      url: '/activiti/flowOnlineOperation/viewHistoricProcessInstance',
      method: 'GET',
      params: data
    })
  },
  completeTask(data){
    return request({
      url: '/activiti/flowOnlineOperation/completeTask',
      method: 'POST',
      params: data
    })
  },
}
