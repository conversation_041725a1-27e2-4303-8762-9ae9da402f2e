<template>
  <div>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="80%" :before-close="handleClose">
      <div>
        <!-- {{ knowledgeIds }} -->
        <el-form ref="form" :model="params">
          <el-col :span="20">
            <el-col :span="3">
              <el-form-item>
                <el-input style="width: 90%" v-model="params.id" placeholder="请输入题目ID搜索"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item>
                <el-select style="width: 90%" v-model="params.questionType" placeholder="请选择" clearable>
                  <el-option v-for="item in questionTypeList" :key="item.value" :value="item.value" :label="item.desc"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item>
                <el-select style="width: 90%" v-model="params.knowledgeId" filterable placeholder="请选择知识点搜索">
                  <el-option :label="i.knowledgeName" :value="i.id" v-for="i in knowledgeIds" :key="i.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
              <el-button type="primary" @click="searchGetList" icon="el-icon-search">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <div>
        <!-- @handleSelectionChange="handleSelectionChange" -->
        <Table
          ref="mTable"
          @changPage="changPage"
          :bindingIds="bindingIds"
          :data="tableData"
          :tableData="tableData"
          :nameList="nameList"
          :pageSize="params.pageSize"
          :total="params.totalItems"
        >
          <template v-slot:courseKnowledgeDtoList="scope">
            <div>
              <div v-if="!scope.row.knowledgeNameList">{{ scope.row.knowledgeNameList || '-' }}</div>
              <div v-else>
                <div class="ellipsis" :title="scope.row.knowledgeNameList" v-for="i in scope.row.knowledgeNameList" :key="i.id">
                  {{ i }}
                </div>
              </div>
            </div>
          </template>
          <template v-slot:questionType="scope">
            <div>
              <div class="ellipsis" :title="scope.row.questionType">{{ questType[scope.row.questionType] || '-' }}</div>
            </div>
          </template>
          <template v-slot:questionDifficulty="scope">
            <div>
              <div class="ellipsis" :title="scope.row.questionDifficulty">{{ questDiff[scope.row.questionDifficulty] || '-' }}</div>
            </div>
          </template>
          <template v-slot:questionText="scope">
            <div>
              <div class="ellipsis">
                <mathTest :formulaText="scope.row.questionText" />
                <!-- {{ scope.row.questionText || '-' }} -->
              </div>
            </div>
          </template>
        </Table>
      </div>
      <div></div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import request from '@/utils/request';
  import testpaper from '@/api/mathApi/testPaperManagementAPI';
  import Table from '@/views/_aaa_demo/testPaperManagement/components/nTable.vue';
  import mathTest from '@/views/_aaa_demo/testPaperManagement/components/mathTest.vue';
  export default {
    components: { Table, mathTest },
    props: {
      knowledgeIds: {
        type: Array,
        default: () => []
      },
      curriculumId: {}
    },
    data() {
      return {
        questionTypeList: [],
        url: '/dsx/math/web/mathQuestionBank/question/batchKnowledge',
        params: {
          pageSize: 30,
          pageNum: 1,
          knowledgeId: '',
          totalItems: 0
          // total: 0
        },
        nameList: [
          { label: 'selection', prop: 'selection', width: '55' },
          { label: 'ID', prop: 'id' },
          { label: '题目难度', prop: 'questionDifficulty', slot: true },
          { label: '题型', prop: 'questionType', slot: true },
          { label: '题干', prop: 'questionText', slot: true },
          { label: '关联知识点', prop: 'courseKnowledgeDtoList', slot: true }
        ],
        tableData: [],
        dialogVisible: false,
        multipleSelection: [],
        bindingIds: [],
        questType: {},
        questDiff: {
          0: '低',
          1: '中',
          2: '高'
        }
        // multipleSelectionIds: []
      };
    },
    created() {
      this.getType();
    },
    methods: {
      searchGetList() {
        this.params.pageNum = 1;
        this.getList();
      },
      changPage(e) {
        this.params.pageNum = e;
        this.getList();
        // this.$refs.mTable.addUniqueData(this.multipleSelection);
      },
      reset() {
        this.params = {
          curriculumId: this.curriculumId,
          pageSize: 30,
          pageNum: 1
        };
        this.getList();
      },
      confirm() {
        // 从子表组件汇总当前已选择的题目；当返回 false 表示超过上限
        let muData = JSON.parse(JSON.stringify(this.$refs.mTable.addUniqueData()));
        // 非数组/超限直接提示并中断
        if (!muData) return this.$message.error('最多只能选择50道题！');
        // 覆盖本地选中集合
        this.multipleSelection = muData;
        // 二次保险：数量上限校验（与子组件逻辑一致）
        if (this.multipleSelection.length > 50) {
          return this.$message.error('最多只能选择50道题！');
        }
        // 依据绑定关系同步 cpId（例如试卷与题目的绑定信息）
        this.bindingIds.forEach((i) => {
          JSON.parse(JSON.stringify(this.multipleSelection)).forEach((e) => {
            if (i.id === e.id) {
              e.cpId = i.cpId;
            }
          });
        });
        // 将选择结果通知父组件；保持对外数据的不可变性
        this.$emit('confirm', JSON.parse(JSON.stringify(this.multipleSelection)));
        // 关闭弹窗
        this.dialogVisible = false;
      },
      getList() {
        this.$nextTick(() => {
          this.$refs.mTable.addUniqueData();
        });
        setTimeout(() => {
          this.tableData = [];
          request({
            url: this.url,
            method: 'post',
            data: {
              batchKnowledgeIdList: this.params.knowledgeId ? [this.params.knowledgeId] : this.knowledgeIds.map((i) => i.id),
              ...this.params
            }
          }).then((res) => {
            this.tableData = [...res.data.data];
            this.params.totalItems = Number(res.data.totalItems);
            console.log(this.multipleSelection, '---------145');
          });
        });
      },
      // 获取题目类型
      async getType() {
        const res = await testpaper.getPaperType({ type: 'testPaperQuestionType' });
        if (res.success) {
          this.questionTypeList = res.data;
          this.questType = {};
          this.questionTypeList.forEach((i) => {
            this.questType[i.value] = i.desc;
          });
        }
      },
      init(v, multipleSelection) {
        // console.log(multipleSelection, '157');
        this.multipleSelection = multipleSelection ? JSON.parse(JSON.stringify(multipleSelection)) : [];
        // console.log(this.multipleSelection);
        this.params.curriculumId = this.curriculumId;
        if (this.knowledgeIds.length > 0) {
          this.dialogVisible = v;
          this.getList();
        } else {
          this.$message.error('请选择知识点后添加题目！');
        }
        this.bindingIds = [];
        this.bindingIds = this.multipleSelection.map((i) => {
          let obj = { id: i.id, cpId: null, questionGrade: null };
          if (i.cpId) {
            obj.cpId = i.cpId;
          }
          if (i.questionGrade) {
            obj.questionGrade = i.questionGrade;
          }
          return obj;
        });
        console.log(this.bindingIds, '-----169');
        this.$nextTick(() => {
          this.$refs.mTable.addUniqueData(this.multipleSelection);
        });
      },
      handleClose(done) {
        this.tableData = [];
        this.multipleSelection = JSON.parse(JSON.stringify(this.$refs.mTable.addUniqueData()));
        console.log(this.multipleSelection, '---09');
        this.$emit('confirm', this.multipleSelection);
        done();
      }
    }
  };
</script>

<style>
  .ellipsis {
    display: -webkit-box;
    line-clamp: 2; /* Standard property for compatibility */
    -webkit-line-clamp: 2; /* 限制显示两行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word; /* 允许在单词内换行 */
  }
</style>
