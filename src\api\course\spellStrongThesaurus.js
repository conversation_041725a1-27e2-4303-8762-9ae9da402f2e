import request from '@/utils/request';

export default {
  // 词库文件上传
  thesaurusFileUpload(data, file) {
    return request({
      url: 'znyy/pd/wordStock/upload',
      method: 'POST',
      params: data,
      data: file
    });
  },
  // 词库文件列表
  thesaurusFileList(data) {
    return request({
      url: `znyy/pd/wordStock/attachment/list`,
      method: 'GET',
      params: data
    });
  },
  // 词库文件删除
  thesaurusFileDelete(data) {
    return request({
      url: `znyy/pd/wordStock/attachment/deleteById`,
      method: 'DELETE',
      params: data
    });
  },
  // 词库单词列表
  thesaurusWordList(data) {
    return request({
      url: `znyy/pd/wordStock/word/list`,
      method: 'GET',
      params: data
    });
  },
  // 词库单词编辑
  thesaurusWordEdit(data) {
    return request({
      url: `znyy/pd/wordStock/editWord`,
      method: 'POST',
      data
    });
  },
  // 词库单词删除
  thesaurusWordDelete(data) {
    return request({
      url: `znyy/pd/wordStock/words/deleteById`,
      method: 'DELETE',
      params: data
    });
  },
  // 单个单词详情查询
  thesaurusWordDetail(data) {
    return request({
      url: `znyy/pd/wordStock/word/detail`,
      method: 'GET',
      params: data
    });
  },
  // 单个单词保存
  thesaurusWordSave(data) {
    return request({
      url: `znyy/pd/wordStock/saveWord`,
      method: 'POST',
      data
    });
  }
};
