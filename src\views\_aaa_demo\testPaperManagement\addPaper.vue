<!-- 新增试卷 -->
<template>
  <div class="app-container" v-loading="btnLoading" element-loading-text="保存中">
    <el-radio-group v-model="tabPosition" size="medium" class="groupItem" :disabled="true">
      <el-radio-button :class="{ 'active-highlight': tabPosition === 0 }" label="0">1、考试信息</el-radio-button>
      <el-radio-button :class="{ 'active-highlight': tabPosition === 1 }" label="1">2、题目配置</el-radio-button>
    </el-radio-group>
    <el-form ref="importFrom" :model="importFrom" label-position="top" label-width="120px" style="width: 100%" v-if="tabPosition == 0">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="课程大类" label-width="80px" required>
            <el-select v-model="importFrom.curriculumId" placeholder="请选择课程大类" @change="changeCurriculumId">
              <el-option v-for="item in curriculumList" :key="item.enCode" :value="item.id" :label="item.enName"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="试卷类型" label-width="80px" required>
            <el-select v-model="importFrom.examinePaperType" placeholder="请选择" @change="changePaper" @clear="clearPaper" clearable>
              <el-option v-for="item in testpaperList" :key="item.value" :value="item.value" :label="item.desc"></el-option>
            </el-select>
            &nbsp;&nbsp;
            <el-select v-model="importFrom.courseVersionId" placeholder="请选择版本" clearable @change="changeVersion" @clear="clearVersionId">
              <el-option v-for="item in versionList" :key="item.id" :value="item.id" :label="item.versionName"></el-option>
            </el-select>
            &nbsp;&nbsp;
            <el-select v-model="importFrom.subjectId" placeholder="请选择学科" clearable @change="updateChildOptions">
              <el-option v-for="item in subjectList" :key="item.id" :value="item.id" :label="item.nodeName"></el-option>
            </el-select>
            &nbsp;&nbsp;
            <el-select v-model="importFrom.gradeId" placeholder="请选择学段" clearable>
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.nodeName"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="试卷名称" label-width="80px" required>
            <el-input v-model.trim="importFrom.examinePaperName" style="width: 600px" maxlength="20" show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="答题时间" label-width="80px" required>
            <span>
              整卷限时
              <el-input v-model.trim="importFrom.answerTime" style="width: 60px"></el-input>
              分钟
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <!-- TODO 时间 -->
          <el-form-item label="考试周期" label-width="80px">
            <el-row>
              <el-col :span="5">
                <!-- v-model="examTime"  -->
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  v-model="importFrom.examinePaperStartDate"
                  clearable
                  :picker-options="pickerOptionsFront"
                  type="date"
                  align="right"
                  unlink-panels
                  placeholder="开始日期"
                ></el-date-picker>
              </el-col>
              <el-col :span="1" style="text-align: center">至</el-col>
              <el-col :span="5">
                <!-- v-model="examTime" -->
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  v-model="importFrom.examinePaperEndDate"
                  clearable
                  type="date"
                  align="right"
                  unlink-panels
                  :picker-options="pickerOptionsLater"
                  placeholder="结束日期"
                ></el-date-picker>
              </el-col>
            </el-row>
            <el-row>不填长期有效</el-row>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item style="display: flex">
            <div style="margin-bottom: 10px; font-weight: bolder; color: #606266">
              限时强制交卷&nbsp;&nbsp;
              <el-switch v-model="importFrom.isForce" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
            </div>
          </el-form-item>
        </el-col>
        <el-form-item label="试卷发送周期（分班测试卷）" label-width="80px" required v-show="importFrom.examinePaperType == 1">
          <div :span="24" style="display: flex; align-items: center; padding-left: 10px; padding-right: 10px">
            <div style="display: flex; align-items: center">
              <el-select style="width: 100px" v-model="importFrom.divideSendStartMonth" placeholder="请选择" clearable>
                <el-option v-for="item in divideSendStartMonthS" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </div>
            <div style="display: flex; align-items: center">&nbsp;至&nbsp;</div>
            <div style="display: flex; align-items: center">
              <el-select style="width: 100px" v-model="importFrom.divideSendEndMonth" placeholder="请选择" clearable>
                <el-option v-for="item in divideSendStartMonthS" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="试卷发送时间（单元测试卷）" label-width="80px" required v-show="importFrom.examinePaperType == 2">
          <el-row :span="24" style="padding-left: 10px; padding-right: 10px">
            <el-col :span="3">
              <el-select style="width: 80px" v-model="importFrom.unitSendDateMonth" placeholder="请选择" clearable>
                <el-option v-for="item in divideSendStartMonthS" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
              /
              <el-select style="width: 80px" v-model="importFrom.unitSendDateData" placeholder="请选择" clearable>
                <el-option v-for="item in datas" :key="Number(item.value)" :value="Number(item.value)" :label="item.label"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
      </el-row>
    </el-form>
    <el-form ref="titleFrom" :model="titleFrom" label-position="top" label-width="120px" style="width: 100%" v-else>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="组卷方式" label-width="80px">
            <el-radio-group v-model="titleFrom.groupType">
              <el-radio :label="1">AI组卷</el-radio>
              <el-radio :label="2">手动组卷</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="关联知识点" label-width="80px" required>
            <el-button style="width: 600px" @click="handleChangeKnowledgeId" clearable>
              {{ !this.titleFrom.knowledgeIds ? '请选择关联知识点' : `已选择${this.titleFrom.knowledgeIds.length}个知识点` }}
            </el-button>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="titleFrom.groupType === 1">
          <el-form-item>
            <div style="margin-bottom: 18px; font-weight: bolder; color: #606266">
              <span class="required-star">*</span>
              试卷题型&nbsp;&nbsp;
              <el-button size="mini" @click="addTitle">添加题型</el-button>
              <span style="margin-left: 10px">试卷共{{ titleFrom.questionConfigCoList ? titleFrom.questionConfigCoList.length : 0 }}种题型</span>
            </div>
            <div v-for="(config, index) in titleFrom.questionConfigCoList" :key="config.id" style="margin-bottom: 18px; display: flex; align-items: center">
              <span v-if="getQuestionTypeName(config.questionsType)">
                &nbsp;&nbsp;{{ getQuestionTypeName(config.questionsType) }}共
                <el-input v-model.trim="config.size" placeholder="请输入题目数量" style="width: 200px; margin: 0 10px"></el-input>
                题，每题
                <el-input v-model.trim="config.num" placeholder="请输入分数" style="width: 200px; margin: 0 10px"></el-input>
                分
              </span>
              <i v-if="getQuestionTypeName(config.questionsType)" class="el-icon-delete" style="font-size: 26px; cursor: pointer" @click="deleteQuestion(index)"></i>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="titleFrom.groupType === 2">
          <el-form-item>
            <div>*已添加{{ filteredSelection.length }}题，最多添加50题</div>
            <Table :data="filteredSelection" :nameList="nameList">
              <template v-slot:scope="row">
                <!-- <el-button size="mini">编辑{{ row.row.id }}</el-button> -->
                <el-button @click="deleteMultip(row.row)" type="danger" size="mini">删除</el-button>
              </template>
              <template v-slot:questionGrade="row">
                <el-input v-model="row.row.questionGrade" type="number" :max="999" oninput="value=value.replace(/[^\d]/g, '').slice(0,3);" placeholder="请输入内容"></el-input>
              </template>
              <template v-slot:courseKnowledgeDtoList="scope">
                <div class="ellipsis" v-for="i in scope.row.knowledgeNameList" :key="i">
                  {{ i || '-' }}
                </div>
              </template>
              <template v-slot:questionType="scope">
                <div>
                  <div class="ellipsis">{{ questType[scope.row.questionType] || '-' }}</div>
                </div>
              </template>
              <template v-slot:questionDifficulty="scope">
                <div>
                  <div class="ellipsis">{{ questDiff[scope.row.questionDifficulty] || '-' }}</div>
                </div>
              </template>
              <template v-slot:questionText="row">
                <div>
                  <mathTest :formulaText="row.row.questionText" />
                  <!-- <div class="ellipsis" :title="row.row.questionText">{{ row.row.questionText || '-' }}</div> -->
                </div>
              </template>
            </Table>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="titleFrom.groupType === 2" style="margin-top: 30px">
          <el-form-item label-width="80px" required>
            <el-button style="width: 50%" @click="addTopic" clearable>
              {{ '添加题目' }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="pageBack" v-if="tabPosition == 1">上一步</el-button>
      <el-button @click="backList" v-if="tabPosition == 0">取 消</el-button>
      <el-button type="info" @click="saveDemo" v-if="isAdd || btnIsEnable != 1">存为草稿</el-button>
      <el-button type="primary" @click="nextStep" v-if="tabPosition == 0">下一步</el-button>
      <el-button type="primary" @click="saveQuestion" v-if="tabPosition == 1">保 存</el-button>
    </div>
    <el-dialog title="新添加题型" :visible.sync="titleDiaOpen" width="30%" :close-on-click-modal="false" center>
      <el-form ref="titleDiaFrom" :model="titleDiaFrom" label-position="right">
        <el-row :gutter="20">
          <el-col :span="18">
            <el-form-item label="课程大类" label-width="100px">
              <span>{{ titleDiaFrom.curriculumName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item label="题型" label-width="100px">
              <el-select v-model="valueType" multiple placeholder="请选择" clearable>
                <el-option v-for="item in questionTypeList" :key="item.value" :value="item.value" :label="item.desc"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="titleDiaOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmAddType">保 存</el-button>
      </span>
    </el-dialog>
    <el-dialog title="添加知识点" :visible.sync="isKnow" width="70%" @close="isKnow = false" :before-close="dialogBeforeClose">
      <span>
        <knowledge-point
          ref="knowledgePointRef"
          v-if="isKnow"
          :disciplineId="this.importFrom.subjectId"
          :curriculumId="this.importFrom.curriculumId"
          :knowledgePointIds="this.titleFrom.knowledgeIds"
        ></knowledge-point>
      </span>
      <span slot="footer">
        <el-button @click="isKnow = false">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确定</el-button>
      </span>
    </el-dialog>
    <NewQuestionAdded @confirm="confirmNewQuestionAdded" ref="newQuestionAdded" :curriculumId="importFrom.curriculumId" :knowledgeIds="titleFrom.knowledgeIds" />
  </div>
</template>

<script>
  import mathTest from '@/views/_aaa_demo/testPaperManagement/components/mathTest.vue';
  import NewQuestionAdded from '@/views/_aaa_demo/testPaperManagement/components/newQuestionAdded.vue';
  import Table from '@/views/_aaa_demo/testPaperManagement/components/nTable.vue';
  import { mapGetters, mapState } from 'vuex';
  import testpaper from '@/api/mathApi/testPaperManagementAPI';
  import videoManageMentAPI from '@/api/mathApi/videoManageMentAPI';
  import '/public/components/ueditor/themes/iframe.css';

  export default {
    name: 'addPaper',
    components: { Table, mathTest, NewQuestionAdded },
    data() {
      return {
        nameList: [
          { label: '序号', prop: 'index' },
          { label: 'ID', prop: 'id' },
          { label: '题目难度', prop: 'questionDifficulty', slot: true },
          { label: '题型', prop: 'questionType', slot: true },
          { label: '题干', prop: 'questionText', slot: true },
          { label: '关联知识点', prop: 'knowledgeNameList', slot: true },
          { label: '题目分值', prop: 'questionGrade', slot: true },
          { label: '操作', prop: 'scope', slot: true }
        ],

        questType: { 0: '单选', 1: '填空', 2: '计算', 3: '解方程', 4: '证明题', 5: '几何综合题' },
        questDiff: {
          0: '低',
          1: '中',
          2: '高'
        },
        multipleSelection: [],

        btnLoading: false,
        isKnow: false,
        isAdd: true, // 是否新增
        parentSendData: {},
        titleDiaOpen: false,
        nodeLevel: 2, // 学科节点级别
        curriculumList: [], // 课程大类
        versionList: [], // 版本
        subjectList: [], // 学科
        gradeList: [], // 学段
        questionTypeList: [], // 题目类型
        testpaperList: [], // 试卷类型
        sizeOptions: [
          { label: 1, value: 1 },
          { label: 2, value: 2 },
          { label: 3, value: 3 },
          { label: 4, value: 4 }
        ],
        numOptions: [
          { label: 1, value: 1 },
          { label: 2, value: 2 },
          { label: 3, value: 3 },
          { label: 4, value: 4 }
        ],
        tabPosition: 0, // 状态：0-考试信息 1-题目配置
        titleFrom: {
          groupType: 1,
          knowledgeIds: [],
          questionConfigCoList: []
        },
        examTime: '',
        valueType: [],
        isEnable: 1,
        btnIsEnable: 1, // 存为草稿按钮是否可用
        importFrom: {
          divideSendEndMonth: null,
          divideSendStartMonth: null,
          id: '', // 试卷id
          curriculumId: '', // 课程大类id
          curriculumName: '', // 课程大类名称
          courseVersionId: '', // 版本id
          courseVersionName: '', // 版本名称
          examinePaperType: '', // 试卷类型
          subjectId: '', // 学科id
          subjectName: '', // 学科名称
          gradeId: '', // 学段id
          examinePaperName: '', // 试卷名称
          answerTime: '', // 答题时间
          examinePaperEndDate: '', // 试卷截止日期
          examinePaperStartDate: '', // 试卷开始日期
          isForce: false
        },
        divideSendStartMonthS: [
          { value: 1, label: '1月' },
          { value: 2, label: '2月' },
          { value: 3, label: '3月' },
          { value: 4, label: '4月' },
          { value: 5, label: '5月' },
          { value: 6, label: '6月' },
          { value: 7, label: '7月' },
          { value: 8, label: '8月' },
          { value: 9, label: '9月' },
          { value: 10, label: '10月' },
          { value: 11, label: '11月' },
          { value: 12, label: '12月' }
        ]
      };
    },
    watch: {
      'importFrom.curriculumId': function (newVal) {
        // 清空相关数据
        // this.importFrom.courseVersionId = '';
        // this.importFrom.subjectId = '';
        // this.importFrom.gradeId = '';
        if (!newVal) {
          // 如果课程大类的 id 为空，则不请求数据
          return;
        }
        // 请求版本和学科数据
        this.getVersion({ curriculumId: newVal });
      },

      'importFrom.courseVersionId': function (newVal) {
        console.log('版本变化了', newVal);
        // 清空相关数据
        // this.importFrom.subjectId = '';
        // this.importFrom.gradeId = '';
        if (!newVal) {
          // 如果版本的 id 为空，则不请求数据
          return;
        }
        // 请求版本和学科数据
        this.getTreeData({ courseVersionId: newVal });
      }
    },

    created() {
      console.log();

      this.getCurriculum();
      this.getPaperType({ type: 'testPaperType' });
      this.getType();
    },
    mounted() {
      this.reCoverData();
      // setTimeout(() => {
      //   this.addTopic();
      // }, 2000);
    },
    methods: {
      deleteMultip(e) {
        console.log(e.cpId);

        if (e.cpId) {
          this.multipleSelection.forEach((item) => {
            if (item.id == e.id) {
              item.isDeleted = 1;
            }
          });
        } else {
          this.multipleSelection = this.multipleSelection.filter((item) => item.id != e.id);
        }
        console.log(this.multipleSelection);
      },
      confirmNewQuestionAdded(newSelection) {
        //this.multipleSelection  newSelectionCopy ，两个数组做对比
        //this.multipleSelection 是原数组，如果这里面的子项存在cpId,那么他就是已经入库的数据
        //newSelectionCopy是操作过后的数组，其中的可能也会存在cpid，如果有，也是已经入库的状态，没有就是新增状态
        //操作过后的数组需要跟原数组做比较，如果这条数据已经入库了，操作过后没有这条数据，那就修改这条数据的变量isDeleted为0
        //操作过后的数组需要跟原数组做比较 如果这条数据没有入库，但是老数据里面存在这条数据，那就判断老数据有无入库条件，如果有就修改状态，如果没有就删除当前这条数
        //操作过后的数组需要跟原数组做比较 如果这条数据没有入库，但是存在于老数据中，他的isDeleted参数为1，那么isDeleted需要改为0，并且需要存在于最终数组中
        //
        let newSelectionCopy = newSelection;
        let resultArray = []; // 最终结果数组
        let cpIdMap = new Map(); // 用于快速查找原数组中的数据，通过cpId作为键
        this.multipleSelection.forEach((item) => cpIdMap.set(item.cpId, item));
        newSelectionCopy.forEach((item) => {
          // 如果新数组中的数据有cpId，则为已入库状态
          if (item.cpId) {
            // 查找原数组中是否有对应的cpId
            let originalItem = cpIdMap.get(item.cpId);
            if (originalItem) {
              // 如果原数组中存在，说明这条数据在新数组中保留，直接保留
              resultArray.push(item);
            } else {
              // 如果原数组中不存在，说明这条数据是新增的，但已经入库了，所以直接保留
              resultArray.push(item);
            }
          } else {
            // 未入库状态，查找原数组中是否有相同数据（其他字段相同，但cpId可能不同）
            let matchIndex = this.multipleSelection.findIndex((originalItem) => originalItem.id === item.id);
            console.log(matchIndex, 'matchIndex');
            if (matchIndex !== -1) {
              // 如果原数组中存在该数据，判断是否满足入库条件
              let originalItem = this.multipleSelection[matchIndex];
              console.log(originalItem, 'originalItem');
              if (originalItem.cpId) {
                // 如果原数据已入库，则更新状态
                item.cpId = originalItem.cpId;
                if (originalItem.isDeleted == 1) {
                  originalItem.isDeleted = 0;
                  resultArray.push(item);
                }
              } else {
                // 如果原数据未入库，则添加到结果数组中
                resultArray.push(item);
              }
              console.log(originalItem, 'originalItem11');
              // 如果不满足入库条件，则不添加到结果数组中
            } else {
              // 如果原数组中不存在，直接保留为新增状态
              resultArray.push(item);
            }
          }
        });
        // 处理原数组中存在的但未在新数组中出现的数据
        this.multipleSelection.forEach((originalItem) => {
          let isExistInNew = newSelectionCopy.some((newItem) => newItem.cpId === originalItem.cpId);
          if (!isExistInNew && originalItem.cpId) {
            // 如果原数组中的数据已入库但不在新数组中，设置isDeleted为0
            originalItem.isDeleted = 1;
            resultArray.push(originalItem);
          }
        });
        this.multipleSelection = resultArray; // 更新最终结果
        console.log(this.multipleSelection, '-----465');

        // // 接收从子组件返回的最新选择集合
        // console.log(newSelection, '---------3866');
        // let newSelectionCopy = JSON.parse(JSON.stringify(newSelection));

        // // 情况一：新集合为空 → 将已存在且已入库的数据标记为删除
        // if (newSelection.length === 0) {
        //   this.multipleSelection = this.multipleSelection.map((item) => {
        //     return { ...item, isDeleted: 1 };
        //   });
        // } else {
        //   // 情况二：新集合不为空
        //   // 1) 找出被移除的旧项（旧集合中存在，但新集合中不存在的项）
        //   let existingIds = newSelectionCopy.map((item) => item.id);
        //   let deletedItems = JSON.parse(JSON.stringify(this.multipleSelection)).filter((item) => {
        //     return !existingIds.includes(item.id);
        //   });
        //   console.log(deletedItems);

        //   // 2) 对于已入库的旧项（存在 cpId），做软删除标记；并回灌进返回集合中
        //   deletedItems.forEach((item) => {
        //     if (item.cpId) {
        //       item.isDeleted = 1;
        //     }
        //     newSelectionCopy.push(item);
        //   });
        //   // 3) 合并：新项直接追加；旧项（已存在）保持 isDeleted=1（如果已标记）
        //   let idS = this.multipleSelection.map((i) => {
        //     return i.id;
        //   });
        //   console.log(idS);
        //   JSON.parse(JSON.stringify(newSelectionCopy)).forEach((item) => {
        //     if (idS.indexOf(item.id) == -1) {
        //       // 追加全新项
        //       this.multipleSelection.push(item);
        //     } else {
        //       // 维持已存在项的删除标记
        //       this.multipleSelection.forEach((i) => {
        //         if (i.id == item.id) {
        //           if (item.cpId) {
        //             i.isDeleted = 1;
        //           } else {
        //           }
        //         }
        //       });
        //     }
        //   });
        //   // 4) 用新集合作为最新快照
        //   this.multipleSelection = newSelectionCopy;
        //   console.log(this.multipleSelection, '有没有--1');
        // }
        // // 最终集合输出
        // console.log(this.multipleSelection);
      },
      addTopic() {
        console.log(JSON.parse(JSON.stringify(this.multipleSelection)), '---------360');
        this.$refs.newQuestionAdded.init(
          true,
          JSON.parse(JSON.stringify(this.multipleSelection)).filter((item) => item.isDeleted != 1)
        );
      },
      changeCurriculumId(e) {
        if (e) {
          this.$set(this.importFrom, 'examinePaperType', '');
          this.$set(this.importFrom, 'courseVersionId', '');
          this.$set(this.importFrom, 'subjectId', '');
          this.$set(this.importFrom, 'gradeId', '');
        }
      },
      clearVersionId() {
        this.$set(this.importFrom, 'courseVersionId', '');
        this.$set(this.importFrom, 'subjectId', '');
        this.$set(this.importFrom, 'gradeId', '');
      },
      changeVersion(e) {
        console.log(e);
        if (e) {
          this.$set(this.importFrom, 'subjectId', '');
          this.$set(this.importFrom, 'gradeId', '');
        }
      },
      clearPaper() {
        this.$set(this.importFrom, 'examinePaperType', '');
        this.$set(this.importFrom, 'courseVersionId', '');
        this.$set(this.importFrom, 'subjectId', '');
        this.$set(this.importFrom, 'gradeId', '');
      },
      changePaper(e) {
        if (e) {
          this.$set(this.importFrom, 'courseVersionId', '');
          this.$set(this.importFrom, 'subjectId', '');
          this.$set(this.importFrom, 'gradeId', '');
        }
      },
      confirmDialog() {
        const selectedKnowledge = this.$refs.knowledgePointRef.multipleSelection;
        console.log('🚀 ~ confirmDialog ~ selectedKnowledge:', selectedKnowledge);
        console.log('1111111:', this.importFrom.subjectId);
        // 处理你的逻辑，比如赋值到表单
        this.titleFrom.knowledgeIds = selectedKnowledge;
        this.isKnow = false;
      },
      dialogBeforeClose() {
        this.isKnow = false;
      },
      handleChangeKnowledgeId() {
        if (this.importFrom.curriculumId == '' || this.importFrom.curriculumId == null) {
          this.$message.error('请先选择课程大类');
          return false;
        }
        if (this.importFrom.subjectId == '' || this.importFrom.subjectId == null) {
          this.$message.error('请先选择学科');
          return;
        }
        this.isKnow = true;
      },
      reCoverData() {
        // 获取路由传递的 query 参数
        const importFromData = this.$route.query.importFrom;
        if (importFromData) {
          this.isAdd = false;
          // 反序列化
          this.parentSendData = JSON.parse(decodeURIComponent(importFromData));
          console.log('🚀 ~ reCoverData ~ this.parentSendData:', this.parentSendData);
        } else {
          this.isAdd = true;
          console.log('🚀 ~ reCoverData ~  this.importFrom:', this.importFrom);
          return;
        }
        console.log(this.parentSendData);
        this.btnIsEnable = this.parentSendData.isEnable;
        this.titleFrom.knowledgeIds = this.parentSendData.courseKnowledgeDtoList;
        this.importFrom.id = this.parentSendData.id || null;
        this.importFrom.divideSendEndMonth = this.parentSendData.divideSendEndMonth || null;
        console.log(this.parentSendData, this.parentSendData['coursePaperManualConfigVoList'], '--------list');
        this.multipleSelection = this.parentSendData.coursePaperManualConfigVoList
          ? this.parentSendData.coursePaperManualConfigVoList.length > 0
            ? this.parentSendData.coursePaperManualConfigVoList.map((item) => {
                console.log(item, '-------439');
                return {
                  knowledgeNameList: item.knowledgeNameList,
                  questionDifficulty: item.questionDifficulty,
                  questionType: item.questionType,
                  questionText: item.questionText,
                  cpId: item.id,
                  parperConfigId: item.parperConfigId || 0, // 如果是新增，parperConfigId 为 0,
                  id: item.questionBankId,
                  questionGrade: item.questionGrade,
                  createTime: item.createTime,
                  updateTime: item.updateTime || null,
                  createUser: item.createUser || null,
                  updateUser: item.updateUser || null,
                  isDeleted: item.isDeleted || 0
                };
              })
            : []
          : [];
        let unitset = null;
        if (this.parentSendData.unitSendDate) {
          unitset = this.parentSendData.unitSendDate ? this.parentSendData.unitSendDate.split('-') : '';
        }
        if (unitset) {
          this.$set(this.importFrom, 'unitSendDateMonth', Number(unitset[0]));
          this.$set(this.importFrom, 'unitSendDateData', Number(unitset[1]));
        } else {
          this.$set(this.importFrom, 'unitSendDateMonth', 1);
          this.$set(this.importFrom, 'unitSendDateData', 1);
        }

        // this.importFrom.unitSendDate = this.parentSendData.unitSendDate || '';
        this.importFrom.divideSendStartMonth = this.parentSendData.divideSendStartMonth || null;
        if (this.parentSendData.examinePaperEndDate) {
          this.importFrom.examinePaperEndDate = this.parentSendData.examinePaperEndDate.split(' ')[0] || null;
        }
        if (this.parentSendData.examinePaperStartDate) {
          this.importFrom.examinePaperStartDate = this.parentSendData.examinePaperStartDate.split(' ')[0] || null;
        }

        this.importFrom.curriculumId = this.parentSendData.curriculumId || '';
        this.importFrom.examinePaperType = this.parentSendData.examinePaperType || '';
        this.importFrom.examinePaperName = this.parentSendData.examinePaperName || '';
        this.importFrom.answerTime = this.parentSendData.answerTime || '';
        this.importFrom.isForce = this.parentSendData.isForce == 1 ? true : false || false;
        this.titleFrom.groupType = this.parentSendData.groupType;
        this.$set(this.importFrom, 'courseVersionId', this.parentSendData.courseVersionId);
        this.$set(this.importFrom, 'subjectId', this.parentSendData.courseSubjectId);
        this.$set(this.importFrom, 'gradeId', this.parentSendData.coursePeriodId);
        console.log(this.importFrom, '=------------');
        const typeDtoList = Array.isArray(this.parentSendData.typeDtoList)
          ? this.parentSendData.typeDtoList.map((item) => ({
              // 从数组元素中获取 questionsNum 赋值给 size
              size: item.questionsNum,
              // 从数组元素中获取 score 赋值给 num
              num: item.score,
              // 从数组元素中获取 questionsType 赋值给 questionsType
              questionsType: item.questionsType,
              id: item.id
            }))
          : [];
        this.titleFrom.questionConfigCoList = typeDtoList;
        console.log(this.importFrom, '------------------------09');

        // if (this.parentSendData.examinePaperStartDate && this.parentSendData.examinePaperEndDate) {
        //   this.examTime = [this.parentSendData.examinePaperStartDate.split(' ')[0], this.parentSendData.examinePaperEndDate.split(' ')[0]];
        // } else {
        //   this.examTime = [];
        // }
      },
      addTitle() {
        this.titleDiaOpen = true;
      },
      // 获取课程大类id
      getCurriculum() {
        testpaper.getCurriculum().then((res) => {
          if (res.success) {
            this.curriculumList = res.data;
            if (Object.keys(this.parentSendData).length !== 0) {
              this.importFrom.curriculumId = this.parentSendData.curriculumId;
            } else {
              this.importFrom.curriculumId = this.curriculumList[0].id;
            }
            if (this.$route.query.curriculumId) {
              this.importFrom.curriculumId = this.$route.query.curriculumId;
            }
          }
        });
      },
      // 获取试卷类型
      getPaperType(query) {
        testpaper.getPaperType(query).then((res) => {
          if (res.success) {
            this.testpaperList = res.data;
            if (Object.keys(this.parentSendData).length !== 0) {
              this.importFrom.examinePaperType = this.parentSendData.examinePaperType;
            } else {
              this.importFrom.examinePaperType = this.testpaperList[0].value;
            }
            // this.testpaperList = Object.entries(res.data).map(([key, value]) => ({
            //   value: key,
            //   label: value
            // }));
          }
        });
      },
      // 获取题目类型
      async getType() {
        const res = await testpaper.getPaperType({ type: 'testPaperQuestionType' });
        if (res.success) {
          this.questionTypeList = res.data;
        }
      },
      // 获取版本
      getVersion(query) {
        return new Promise((resolve) => {
          videoManageMentAPI.getVersionIdAPI(query).then((res) => {
            if (res.success) {
              this.versionList = res.data;
              if (Object.keys(this.parentSendData).length !== 0) {
                this.importFrom.courseVersionId = this.parentSendData.courseVersionId;
              } else {
                // this.importFrom.courseVersionId = this.versionList[0].id;
              }
              this.getTreeData(query);
            } else {
              this.versionList = [];
              this.importFrom.courseVersionId = '';
            }
            resolve();
          });
        });
      },
      getTreeData(query) {
        console.log('🚀 ~ getTreeData ~ query:', query);
        console.log('🚀 ~ this.importFromData.curriculumId ~ query:', this.titleDiaFrom.curriculumId);
        if (!query.courseVersionId) return;
        videoManageMentAPI
          .getTreeDataAPI({
            curriculumId: this.importFrom.curriculumId,
            versionId: query.courseVersionId,
            nodeLevel: 2
          })
          .then((res) => {
            if (res.success) {
              // // 转换数据结构
              this.subjectList = res.data;
              let arr = this.subjectList.filter((item) => item.id == this.importFrom.subjectId);
              console.log(arr, '==========');
              if (arr && arr.length > 0) {
                this.gradeList = arr[0].childList;
              }
              // if (!this.isAdd) {
              //   // this.importFrom.subjectId = this.subjectList[0].id;
              //   this.importFrom.subjectId = this.parentSendData.courseSubjectId;
              //   const currentSubject = this.subjectList.find((item) => item.id === this.importFrom.subjectId);
              //   this.gradeList = currentSubject ? currentSubject.childList : [];
              //   this.importFrom.gradeId = this.parentSendData.coursePeriodId;
              // }
            }
          });
      },
      updateChildOptions() {
        // 根据选中的父选项 ID 找到对应的父选项对象
        const selectedParentObj = this.subjectList.find((item) => item.id === this.importFrom.subjectId);
        if (selectedParentObj) {
          // 更新子选项列表
          this.gradeList = selectedParentObj.childList;
        } else {
          this.gradeList = [];
        }
        // 清空子选项的选择
        this.importFrom.gradeId = '';
      },
      saveDemo() {
        this.isEnable = 2; // 草稿状态
        this.justDoSave();
        this.titleDiaOpen = false;
        this.tabPosition = 0;
      },
      validateForm() {
        if (!this.importFrom.curriculumId) {
          this.$message.error('请选择课程大类');
          return false;
        }
        if (!this.importFrom.courseVersionId) {
          this.$message.error('请选择版本');
          return false;
        }
        if (!this.importFrom.subjectId) {
          this.$message.error('请选择学科');
          return false;
        }
        if (!this.importFrom.gradeId) {
          this.$message.error('请选择学段');
          return false;
        }
        if (!this.importFrom.examinePaperName) {
          this.$message.error('请输入试卷名称');
          return false;
        }
        if (this.importFrom.examinePaperType == 1) {
          if (!this.importFrom.divideSendStartMonth || !this.importFrom.divideSendEndMonth) {
            this.$message.error('时间不能为空');
            return false;
          }
        }
        if (this.importFrom.examinePaperType == 2) {
          if (!this.importFrom.unitSendDateData || !this.importFrom.unitSendDateMonth) {
            this.$message.error('时间不能为空');
            return false;
          }
        }
        const answerTime = parseInt(this.importFrom.answerTime, 10);
        if (!answerTime || answerTime <= 0 || isNaN(answerTime)) {
          this.$message.error('答题时间必须为大于0的正整数');
          return false;
        }
        // if (!this.examTime || this.examTime.length !== 2) {
        //   this.$message.error('请选择考试周期');
        //   return false;
        // }
        return true;
      },
      nextStep() {
        if (!this.validateForm()) {
          return false;
        }
        this.tabPosition = 1;
      },
      validateSize(config) {
        const size = parseInt(config.size, 10);
        if (!size || size <= 0 || !Number.isInteger(size)) {
          this.$message.error('题目数量必须为正整数');
          config.size = null;
        }
      },
      validateNum(config) {
        const num = parseFloat(config.num);
        if (!num || num <= 0 || num % 1 !== 0) {
          this.$message.error('分数必须为正整数');
          config.num = null;
        }
      },
      saveQuestion() {
        // this.isEnable = 1; // 启用状态
        // isEnable 0禁用 1 启用 2 草稿
        // 如果是草稿，则改为启用；否则保持原来的状态
        this.isEnable = this.btnIsEnable == 2 ? 1 : this.btnIsEnable;
        // 校验知识点
        if (!this.titleFrom.knowledgeIds || this.titleFrom.knowledgeIds.length === 0) {
          this.$message.error('请选择关联知识点');
          return;
        }
        if (this.titleFrom.groupType == 1) {
          // 校验题型必填
          if (!this.titleFrom.questionConfigCoList || this.titleFrom.questionConfigCoList.length === 0) {
            this.$message.error('请添加题型');
            return;
          }
          // 校验题型配置
          for (let i = 0; i < this.titleFrom.questionConfigCoList.length; i++) {
            const config = this.titleFrom.questionConfigCoList[i];
            if (!config.size || !config.num) {
              this.$message.error('请填写完整的题型配置');
              return;
            }

            const size = parseInt(config.size, 10);
            const num = parseFloat(config.num);

            if (size <= 0 || num <= 0) {
              this.$message.error('题目数量和分数必须大于0');
              return;
            }

            if (!Number.isInteger(size) || num % 1 !== 0) {
              this.$message.error('题目数量必须为正整数');
              return;
            }

            if (num % 1 !== 0) {
              this.$message.error('分数必须为整数');
              return;
            }
          }
        } else {
          console.log('手动增题目');
        }
        if (this.multipleSelection.length > 0) {
          console.log(this.multipleSelection);

          const allQuestionGradesPresent = this.multipleSelection.every((item) => item.questionGrade != undefined && item.questionGrade != null);
          const anyQuestionGradesZero = this.multipleSelection.some((item) => item.questionGrade == 0);
          console.log(allQuestionGradesPresent);
          console.log(anyQuestionGradesZero);
          if (anyQuestionGradesZero) return this.$message.error('添加的题目分数不能为0！！！');
          if (!allQuestionGradesPresent) return this.$message.error('添加的题目分数不能为空！！！');
        }
        this.justDoSave();
        this.titleDiaOpen = false;
      },
      justDoSave() {
        console.log(this.importFrom);

        const params = {
          divideSendStartMonth: this.importFrom.divideSendStartMonth,
          divideSendEndMonth: this.importFrom.divideSendEndMonth,
          unitSendDate: this.importFrom.unitSendDateMonth + '-' + this.importFrom.unitSendDateData,

          coursePaperManualConfigCoList: this.multipleSelection.map((i) => {
            let obj = {
              parperConfigId: i.parperConfigId || 0, // 如果是新增，parperConfigId 为 0,
              questionBankId: i.id,
              questionGrade: i.questionGrade,
              createTime: i.createTime,
              updateTime: i.updateTime || null,
              createUser: i.createUser || null,
              updateUser: i.updateUser || null,
              isDeleted: i.isDeleted || 0
            };
            if (i.cpId) {
              obj['id'] = i.cpId;
            }
            return obj;
          }),
          id: this.importFrom.id,
          examinePaperType: this.importFrom.examinePaperType,
          curriculumId: this.importFrom.curriculumId,
          curriculumName: this.curriculumList.find((item) => item.id === this.importFrom.curriculumId)?.enName || '',
          courseVersionId: this.importFrom.courseVersionId,
          courseVersionName: this.versionList.find((item) => item.id === this.importFrom.courseVersionId)?.versionName || '',
          courseSubjectId: this.importFrom.subjectId,
          courseSubjectName: this.subjectList.find((item) => item.id === this.importFrom.subjectId)?.nodeName || '',
          coursePeriodId: this.importFrom.gradeId,
          coursePeriodName: this.gradeList.find((item) => item.id === this.importFrom.gradeId)?.nodeName || '',
          examinePaperName: this.importFrom.examinePaperName,
          answerTime: this.importFrom.answerTime,
          examinePaperStartDate: this.importFrom.examinePaperStartDate,
          examinePaperEndDate: this.importFrom.examinePaperEndDate,
          // examinePaperStartDate: this.examTime[0],
          // examinePaperEndDate: this.examTime[1],
          isForce: this.importFrom.isForce ? 1 : 0,
          groupType: this.titleFrom.groupType,
          courseKnowledgeDtoList: this.titleFrom.knowledgeIds, // 知识点 ID 列表
          questionConfigCoList:
            this.titleFrom.groupType == 1
              ? Array.isArray(this.titleFrom.questionConfigCoList) && this.titleFrom.questionConfigCoList.length > 0
                ? this.titleFrom.questionConfigCoList.map((config) => ({
                    id: config.id || '', // 如果是新增，id 为 0
                    parperConfigId: config.parperConfigId || 0, // 如果是新增，parperConfigId 为 0
                    questionsType: config.questionsType,
                    questionsNum: config.size, // 题目数量
                    score: config.num, // 每题分数
                    deleted: 0 // 默认未删除
                  }))
                : []
              : [],
          knowledgeName: this.titleFrom.knowledgeName
        };
        params.isEnable = this.isEnable;
        this.btnLoading = true;
        testpaper
          .savePaper(params)
          .then((res) => {
            if (res.success) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.importFrom = {};
              this.$router.push({ path: '/_aaa_demo/testPaperManagement' });
            }
          })
          .finally(() => {
            this.btnLoading = false; // 不管接口调用成功与否，都关闭加载状态
          });
      },
      pageBack() {
        this.tabPosition = 0;
      },
      backList() {
        this.$router.go(-1);
      },
      confirmAddType() {
        if (this.valueType.length === 0) {
          this.$message.warning('请选择至少一个题型');
          return;
        }
        // 遍历选中的题型，生成对应的配置
        this.valueType.forEach((type) => {
          if (this.isTypeExists(type)) {
            const questionType = this.getQuestionTypeName(type);
            this.$message.warning(`题型 ${questionType} 已存在，请勿重复添加`);
          } else {
            this.titleFrom.questionConfigCoList.push({
              questionsType: type, // 题型
              size: null, // 题目数量
              num: null // 每题分数
            });
          }
        });

        // 清空选择并关闭对话框
        this.valueType = [];
        this.titleDiaOpen = false;
      },
      // 检查题型是否已存在
      isTypeExists(type) {
        return this.titleFrom.questionConfigCoList.some((item) => item.questionsType === type);
      },
      getQuestionTypeName(type) {
        const questionType = this.questionTypeList.find((item) => item.value === type);
        return questionType ? questionType.desc : '';
      },
      deleteQuestion(index) {
        this.titleFrom.questionConfigCoList.splice(index, 1);
      }
    },
    computed: {
      pickerOptionsFront() {
        return {
          disabledDate: (time) => {
            // 如果没有选择开始日期，则不禁用任何日期
            if (!this.importFrom.examinePaperEndDate) {
              return false;
            }
            // 将开始日期和当前判断的时间都转换为当天的 00:00:00 的时间戳
            const startDate = new Date(this.importFrom.examinePaperEndDate);
            startDate.setHours(0, 0, 0, 0);
            const currentTime = new Date(time);
            currentTime.setHours(0, 0, 0, 0);
            // 禁用开始日期之前的所有日期
            return currentTime.getTime() > startDate.getTime();
          }
        };
      },
      pickerOptionsLater() {
        return {
          disabledDate: (time) => {
            // 如果没有选择开始日期，则不禁用任何日期
            if (!this.importFrom.examinePaperStartDate) {
              return false;
            }
            // 将开始日期和当前判断的时间都转换为当天的 00:00:00 的时间戳
            const startDate = new Date(this.importFrom.examinePaperStartDate);
            startDate.setHours(0, 0, 0, 0);
            const currentTime = new Date(time);
            currentTime.setHours(0, 0, 0, 0);
            // 禁用开始日期之前的所有日期
            return currentTime.getTime() < startDate.getTime();
          }
        };
      },

      datas() {
        // 根据月份确定天数
        let daysInMonth;
        if (this.importFrom.unitSendDateMonth == 2) {
          // 2月，假设是闰年有29天，平年有28天
          daysInMonth = 29; // 或者根据实际需求判断是否闰年
        } else if (this.importFrom.unitSendDateMonth % 2 == 0) {
          // 4, 6, 9, 11月有30天
          daysInMonth = 30;
        } else {
          // 1, 3, 5, 7, 8, 10, 12月有31天
          daysInMonth = 31;
        }

        // 创建一个数组，包含从1到daysInMonth的数字
        let list = Array.from({ length: daysInMonth }, (_, i) => i + 1);

        // 使用.map生成新的数组
        let result = list.map((i) => {
          return {
            value: i,
            label: i + '日'
          };
        });

        console.log(result);
        return result;
      },
      filteredSelection() {
        const list = Array.isArray(this.multipleSelection) ? this.multipleSelection : [];
        return list.filter((i) => Number(i.isDeleted) == 0);
        // return list
      },
      // 计算 titleDiaFrom 为 curriculumList 中当前选中的一项
      titleDiaFrom() {
        const selectedItem = this.curriculumList.find((item) => item.id === this.importFrom.curriculumId);
        return selectedItem
          ? {
              curriculumName: selectedItem.enName,
              curriculumId: selectedItem.id
            }
          : {
              curriculumName: '',
              curriculumId: ''
            };
      },
      ...mapGetters('enumItem', ['enumFormat', 'subjectFormat', 'pageFormat']),
      ...mapState('enumItem', {
        editUrlEnum: (state) => state.question.editUrlEnum
      })
    }
  };
</script>

<style lang="less" scoped>
  /deep/.el-dialog__header {
    border-bottom: 1px solid #dfdfdf;
  }
  .groupItem {
    margin-bottom: 20px;
  }
  /deep/.el-radio-button__inner {
    width: 500px;
    font-weight: bolder;
  }
  /deep/.el-form-item__label {
    font-weight: bolder;
  }
  /* 新增高亮样式 */
  /deep/.active-highlight .el-radio-button__inner {
    background-color: #409eff !important; /* 高亮背景色 */
    border-color: #409eff !important; /* 高亮边框色 */
    color: #fff !important; /* 高亮文字颜色 */
  }
  .required-star {
    color: #f55353;
  }
  .ellipsis {
    display: -webkit-box;
    line-clamp: 2; /* Standard property for compatibility */
    -webkit-line-clamp: 2; /* 限制显示两行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word; /* 允许在单词内换行 */
  }
</style>
