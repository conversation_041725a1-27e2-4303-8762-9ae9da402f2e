<template>
  <div class="nomore">
    <el-image style="width: 100px; height: 100px" :src="image" />
    <div style="color: #999; margin-top: 20px">{{ text }}</div>
  </div>
</template>

<script>
  export default {
    name: '<PERSON><PERSON>',
    props: {
      image: {
        type: String,
        default: 'https://document.dxznjy.com/course/62d2e3e31783418d87573769b3947b5a.png'
      },
      text: {
        type: String,
        default: '暂无数据'
      }
    }
  };
</script>

<style scoped>
  .nomore {
    padding: 200px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
