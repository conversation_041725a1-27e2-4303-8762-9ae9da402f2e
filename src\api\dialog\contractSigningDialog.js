/**
 * 合伙人、俱乐部完款后，合同签署弹窗相关接口
 */
import request from '@/utils/request';
import { createFakeClubPersonContract, createFalseContract } from '@/api/verifyCode';

export const getChangeNoticeStatusData = () => {
  return request({
    url: '/znyy/operation/change/knownNumberExist'
  });
  // TODO: 获取上级关系变更状态
  return Promise.resolve({
    data: { status: false, brandName: 'test_新品牌', oldBrandName: 'test_原品牌' }
  });
  // return request({})
};
/**
 * 设置变更上下级已知晓弹窗已读
 */
export const setChangeNoticeStatus = () => {
  return request({
    url: '/znyy/operation/change/clickKnown'
  });
};

/**
 * 获取合同签署弹窗显示开关状态
 */
export const getContractSigningDialogShowStatus = () => {
  return request({
    url: '/znyy/contractInvolve/v2/getContractSwitch',
    method: 'GET'
  });
};

/**
 * 获取所有合同相关状态及剩余天数
 */
export const getNeedSigningContractStatusAndDays = () => {
  return request({
    url: '/znyy/contractInvolve/v2/checkContractStatus',
    method: 'GET'
  });
};

/**
 * 获取合同签署用户信息
 * @param {*} params
 */
export const getContractUserInfo = () => {
  return request({
    url: '/znyy/contractInvolve/v2/getContractUserInfo',
    method: 'GET'
  });
};
/**
 * 校验企业名称合法性
 */
export const checkEnterpriseName = (params) => {
  return request({
    url: '/znyy/operations/v2/businessLicense',
    method: 'GET',
    params
  });
};

/**
 * 修改合同签署用户信息
 * @param {*} data
 */
export const updateContractUserInfo = (data) => {
  return request({
    url: '/znyy/contractInvolve/v2/updateContractUserInfo',
    method: 'POST',
    data
  });
};
export const checkContractUserInfo = (data) => {
  return request({
    url: '/znyy/contractInvolve/v2/checkContractUserInfo',
    method: 'POST',
    data
  });
};




/**
 * 生成步骤一合同
 * @param {*} type Operations: 俱乐部; School: 合伙人
 * @param {*} recommend 甲乙方信息是否相同
 */
export const addContractOne = (type, recommend) => {
  if (type === 'Operations') {
    if (recommend) {
      return createFakeClubPersonContract({ contractType: 1 });
    } else {
      return request({
        url: '/znyy/operations/v2/clubBuildStudyContract',
        method: 'POST'
      });
    }
  } else if (type === 'School') {
    if (recommend) {
      return createFalseContract({ contractType: 1 });
    } else {
      return request({
        url: '/znyy/V2/merchant/merchantBuildStudyContract',
        method: 'POST'
      });
    }
  }
};

/**
 * 生成步骤二合同
 * @param {*} type Operations: 俱乐部; School: 合伙人
 * @param {*} recommend 甲乙方信息是否相同
 */
export const addContractTwo = (type, recommend) => {
  if (type === 'Operations') {
    if (recommend) {
      return createFakeClubPersonContract({ contractType: 2 });
    } else {
      return request({
        url: '/znyy/operations/v2/clubBuildRecommendContract',
        method: 'POST'
      });
    }
  } else if (type === 'School') {
    if (recommend) {
      return createFalseContract({ contractType: 3 });
    } else {
      return request({
        url: '/znyy/V2/merchant/merchantBuildRecommendContract',
        method: 'POST'
      });
    }
  } else {
    return Promise.reject();
  }
};
/**
 * 生成变更上下级相关合同(沿用旧渠道、推广大使合同)
 */
export const addChangeRelationshipContract = (type, recommend) => {
  if (type === 'Operations') {
    if (recommend) {
      return createFakeClubPersonContract({ contractType: 3 });
    } else {
      return request({
        url: '/znyy/operations/v2/clubBuildRecommendContract',
        method: 'POST'
      });
    }
  } else if (type === 'School') {
    if (recommend) {
      return createFalseContract({ contractType: 2 });
    } else {
      return request({
        url: '/znyy/V2/merchant/merchantBuildRecommendContract',
        method: 'POST'
      });
    }
  } else {
    return Promise.reject();
  }
};
/**
 * 通过合同流程id获取合同二维码
 * @param {*} flowId 合同流程id
 * @param {*} isFirstParty 是否是甲方 1-乙方 0-甲方 2-丙方
 * @returns
 */
export const getContractQRUrl = (flowId, isFirstParty) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/znyy/sign/contract/qr-link',
      method: 'GET',
      params: { flowId, isQrLink: 1 }
    })
      .then((res) => {
        let contractQR = null;
        if (Array.isArray(res.data)) {
          contractQR = res.data.find((item) => item.isFirstParty === isFirstParty);
        }
        resolve(contractQR);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

/**
 * 获取合同二维码签署状态
 * @param {object} params
 * @param {string} params.flowId 流程id
 * @param {number} params.signSource 签署来源：0-e签宝 ，1-电子签
 * @param {string} params.participantFlag 签署方：甲方 ，乙方
 * @returns
 */
export const getContractSigingStatus = (params) => {
  return request({
    url: '/znyy/sign/contract/getContractQrLinkStatus',
    method: 'GET',
    params: { ...params, signSource: 1 }
  });
};
export const checkedVerificationVodeStatus = (params) => {
  return request({
    url: 'znyy/contractInvolve/v2/checkFirstStudyState',
    method: 'GET',
    params
  })
}
