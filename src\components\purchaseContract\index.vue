<template>
  <div>
    <!-- 合同购买弹窗 -->
    <el-dialog :visible.sync="purchaseDialogVisible" width="25%" title="合同购买" center :before-close="closeDialog" :close-on-click-modal="false">
      <el-form label-width="110px" label-position="left" ref="sendForm" :model="sendForm">
        <el-form-item label="购买合同数" prop="contractNum">
          <el-input-number v-model="sendForm.contractNum" :min="1" label="描述文字"></el-input-number>
        </el-form-item>
        <el-form-item label="有效期">
          <span>
            1年
            <span class="info">(即订单付款成功后开始计算)</span>
          </span>
        </el-form-item>
        <el-form-item label="总金额（元）">
          <span>{{ sendForm.amount }}</span>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button style="width: 200px" type="primary" @click="buyNow">立即购买</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import orderApi from '@/api/schoolCompletePaymentIs.js';
  import request from '@/utils/request';
  import store from '@/store';
  export default {
    data() {
      return {
        overtime: true, // 订单是否过期
        oldContractNum: 0,
        token: store.getters.token,
        purchaseDialogVisible: false,
        sendForm: {
          contractNum: 1,
          amount: 2
        },
        sysUserInfo: localStorage.getItem('sysUserInfo')
      };
    },
    computed: {
      ...mapGetters(['setpayUrl', 'contractNum']),
      amount() {
        return this.sendForm.contractNum * 2;
      }
    },
    watch: {
      amount() {
        this.sendForm.amount = this.amount;
      }
    },
    methods: {
      // 立即购买
      buyNow() {
        let that = this;

        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let info = JSON.parse(this.sysUserInfo);
        // info.merchantCode
        let data = {
          merchantCode: info.merchantCode,
          ...this.sendForm
        };
        this.goPay(data).then((res) => {
          console.log(res.data, '>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
          this.purchaseDialogVisible = false;
          loading.close();

          this.oldContractNum = this.contractNum; // 保存旧的合同数量
          this.sourceOrderId = res.data.sourceOrderId; // 保存订单id
          const split = dxSource.split('##'); //["ZNYY", "BROWSER", "WEB"]
          res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
          let params = JSON.stringify(res.data);
          let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
          //需要编码两遍，避免出现+号等
          var encode = Base64.encode(Base64.encode(req));
          // this.setpayUrl +
          // window.open('http://192.168.40.165:8000/product?' + encode, '_blank');
          window.open(this.setpayUrl + 'product?' + encode, '_blank');
          // this.$store.commit('SET_CONTRACTNUM', this.sendForm.contractNum);

          // // 定时器轮询 订单支付回调 以刷新合同数量
          // that.st0 = setInterval(that.pollPay, 10000);
          // // 超时清除定时器
          // const timeoutId = setTimeout(() => {
          //   that.clearEsignInterval(that.st0);
          //   clearTimeout(timeoutId); // 清除超时定时器
          // }, 600000);

          //
          // 监听来自子窗口的消息
          window.addEventListener('message', (event) => {
            // console.log('父窗口收到:', event);
            // 验证消息来源
            if (event.data.type !== 'PAYMENT_SUCCESS' || event.origin + '/' !== this.setpayUrl) return;
            // 轮询合同数量, 直到刷新成功，超时则刷新失败
            that.overtime = false;
            that.getContract();
            const timeoutId = setTimeout(() => {
              that.overtime = true;
              clearTimeout(timeoutId); // 清除超时定时器
            }, 600000);
          });
        });
      },
      // 轮询订单支付回调
      async pollPay() {
        let that = this;
        let { data } = await orderApi.judgeOrderStatus({ sourceOrderId: this.sourceOrderId });

        if (data == 3) {
          // 成功 刷新合同数量
          that.clearEsignInterval(that.st0); // 清除定时器

          // 轮询合同数量, 直到刷新成功，超时则刷新失败
          this.overtime = false;
          that.getContract();
          const timeoutId = setTimeout(() => {
            this.overtime = true;
            clearTimeout(timeoutId); // 清除超时定时器
          }, 600000);
        }
      },
      // 轮询合同数量改变
      async getContract() {
        let that = this;
        // 超时则刷新失败
        if (this.overtime) {
          return;
        }
        let timeoutId = setTimeout(() => {
          // 刷新合同数
          store.dispatch('getContract').then((num) => {
            if (that.oldContractNum != num) {
              this.overtime = true;
              return;
            }
            console.log('🚀 ~ store.dispatch ~ that.oldContractNum != that.contractNum:', that.oldContractNum, num);
            // 否则继续轮询
            that.getContract();
          });
        }, 1000);
      },
      //清除轮询定时器
      clearEsignInterval(st) {
        clearInterval(st);
        st = null;
      },
      // 去支付
      goPay(data) {
        return request({
          url: '/znyy/merchant/contract/pay',
          method: 'GET',
          params: data
        });
      },
      open() {
        this.purchaseDialogVisible = true;
      },
      // 关闭弹窗
      closeDialog() {
        this.sendForm = {
          contractNum: 1,
          amount: 2
        };
        this.purchaseDialogVisible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .info {
    color: #999;
  }
</style>
