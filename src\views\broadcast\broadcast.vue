<template>
  <div class="app-container">
    <!-- 新增按钮 -->
    <el-row class="container-card" style="margin-bottom: 30px;">
      <el-col :span="6">
        <el-col :span="8" class="marginbottom" style="line-height: 36px;">录播标题</el-col>
        <el-col :span="15">
          <el-input id="title" v-model="dataQuery.title" name="id" placeholder="请输入录播标题" />
        </el-col>
      </el-col>
      <el-col :span="15" style="height: 1px;" />

      <el-col :span="3" style="text-align: right;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="fetchData()">搜索</el-button>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24" style="text-align: right;margin-bottom: 30px;">
        <el-button type="success" icon="el-icon-plus" size="mini" @click="clickAdd">新增录播视频</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="tableLoading" stripe :data="tableData" border style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="id" label="id" align="center" width="180" :show-overflow-tooltip="true" />
      <el-table-column prop="title" label="产品标题" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top">
            <el-button icon="el-icon-edit" type="warning" size="mini" @click="handleUpdate(scope.row.id)">编辑</el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDelete(scope.row.id)">删除
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="speaker" label="主讲人" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="url" label="视频链接" align="center" >
        <template slot-scope="scope">
          {{aliUrl+scope.row.url}}
        </template>
      </el-table-column>
      <el-table-column prop="date" label="日期" align="center" />
      <el-table-column prop="time" label="时间" align="center" />
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog
      :title="addOrUpdate?'新增':'修改'"
      :visible.sync="dialogVisible"
      width="70%"
      :close-on-click-modal="false"
      @close="close">
      <el-form
        :ref="addOrUpdate?'addBroadcastData':'updateBroadcast'"
        :rules="rules"
        :model="addOrUpdate?addBroadcastData:updateBroadcast"
        label-position="left"
        label-width="120px">

        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="录播信息" name="first">
            <el-row>
              <el-col :span="11">
                <el-form-item label="标题" prop="title">
                  <el-input v-if="addOrUpdate" v-model="addBroadcastData.title" />
                  <el-input v-if="!addOrUpdate" v-model="updateBroadcast.title" />
                </el-form-item>

                <el-form-item label="主讲人" prop="speaker">
                  <el-input v-if="addOrUpdate" v-model="addBroadcastData.speaker" />
                  <el-input v-if="!addOrUpdate" v-model="updateBroadcast.speaker" />
                </el-form-item>

                <el-form-item label="是否上架" prop="status">
                  <el-switch v-if="addOrUpdate" v-model="addBroadcastData.status" active-color="#13ce66" />
                  <el-switch v-if="!addOrUpdate" v-model="updateBroadcast.status" active-color="#13ce66" />
                </el-form-item>

                <el-form-item label="开始时间" prop="startTime">
                  <template>
                    <div class="block">
                      <el-date-picker
                        v-if="addOrUpdate" v-model="addBroadcastData.startTime"
                        type="datetime"
                        placeholder="选择日期时间"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss">
                      </el-date-picker>
                      <el-date-picker
                        v-if="!addOrUpdate" v-model="updateBroadcast.startTime"
                        type="datetime"
                        placeholder="选择日期时间"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss">
                      </el-date-picker>
                    </div>
                  </template>
                </el-form-item>

              </el-col>
            </el-row>

            <div>
              <el-row>
                <el-col :span="20">
                  <el-form-item label="录播视频" v-if="isRouterAlive">
                    <el-upload class="upload-demo" :limit="1" v-loading="uploadLoading" :on-exceed="justPictureNum" :file-list="!addOrUpdate? fileDetailList: fileDetailList.url"
                               :http-request="uploadDetailHttp" >
                      <el-button size="small" type="primary">点击上传</el-button>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div><el-col style="height: 20px;" /></div>
          </el-tab-pane>
        </el-tabs>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="warning" size="mini" @click="close">取消</el-button>

        <el-button v-if="addOrUpdate" v-loading.fullscreen.lock="fullscreenLoading" size="mini" type="success" @click="addProductFun('addBroadcastData')">新增</el-button>
        <el-button v-if="!addOrUpdate" v-loading.fullscreen.lock="fullscreenLoading" size="mini" type="primary" @click="updateBroadcastFun('updateBroadcast')">修改</el-button>
      </div>

    </el-dialog>

  </div>

</template>

<script>
import broadcastApi from '@/api/broadcast'
import {
  ossPrClient
} from '@/api/alibaba'
import Tinymce from '@/components/Tinymce'
import {
  pageParamNames
} from '@/utils/constants'
import { valid } from 'mockjs'


export default {
  components: {
    Tinymce
  },
  data() {
    return {
      tableLoading: false,
      dataQuery: {
        status:'-1',
        title: ''
      },
      groupClassObject: [],
      groupClass: {},


      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      addClaseeNumbers: {
        classHoursNumber: undefined,
        classHoursPrice: undefined
      },
      isRouterAlive:false,//局部刷新
      fullscreenLoading: false, // 加载
      innerVisible: false, // 增加课时的弹框
      classHoursTableShow: false, // 拼团列表显示
      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      activeName: 'first', // tab默认第一个
      addBroadcastData: '', // 新增录播
      updateBroadcast: '', // 修改数据
      rules: { // 表单提交规则
        title: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        speaker: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        startTime: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      },
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表
      uploadLoading: false, // 上传图片加载按钮
      isUploadSuccess: true ,// 是否上传成功
      fileDetailList:[]
    }
  },
  created() {
    ossPrClient()
    this.fetchData()
    this.updateBroadcast = {
      'id': '',
      'title': '',
      'speaker': '',
      'status': '',
      'startTime': '',
      'url':'',
      'createTime':''
    }
  },
  methods: {
    // 获取UUid
    getUUid: function() {
      const s = []
      const hexDigits = '0123456789abcdef'
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      const UUid = s.join('')
      return UUid
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function() {
        this.isRouterAlive = true;
      });
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    // 删除上传图片
    handleRemoveDetail(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7,17) == parseInt(file.uid/1000)){
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },
    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      broadcastApi.broadcastList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data.data[name])))
      })
    },

    // 删除记录
    handleDelete(id) {
      this.$confirm('您确定要删除此条记录？', '提示', confirm).then(() => {
        broadcastApi.deleteBroadcast(id).then(() => {
          this.$message.success('删除成功')
          this.fetchData()
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },

    // 点击新增按钮
    clickAdd() {
      this.reload();
      this.addBroadcastData = {
        'title': '',
        'speaker': '',
        'status': true,
        'startTime': '',
        'url':''
      }
      this.dialogVisible = true
      this.addOrUpdate = true
      this.fileList=[]
      if (this.fileListPending.length!== 0) {
        this.$refs.clearupload.clearFiles()
      }
      this.$nextTick(() => this.$refs['addBroadcastData'].clearValidate())
    },

    // 删除上传图片
    handleRemove(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7,17) == parseInt(file.uid/1000)){
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },
    // 上传图片预览
    // handlePictureCardPreview(file) {
    // this.dialogImageUrl = file.url
    // this.dialogUploadVisible = true
    // },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`)
    },
    // 上传图片请求
    uploadPrHttp({ file }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.fileList.push({ 'uid': file.uid, 'url': url })
            } else { // 新增上传图片
              that.fileList.push({ name })
              that.addBroadcastData.imageDetailFilePath = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
      })
    },

    // 新增录播提交后台
    addProductFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (that.addBroadcastData.url===null||that.addBroadcastData.url.length===0){
          alert("视频一定得上传哦")
          return false
        }
        if (valid) {
          broadcastApi.addBroadcast(that.addBroadcastData).then(res=>{
            that.dialogVisible = false
            that.$nextTick(() => that.fetchData())
            that.$message.success('新增成功')
          },s=>{
            if(s==='error'){
              that.$message.error("错误信息");
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    // 编辑修改产品
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate = false
      that.isRouterAlive = true;
      broadcastApi.queryBroadcast(id).then(res => {
        console.log(res)
        that.updateBroadcast = res.data.data
        if (that.updateBroadcast.url.length > 0) {
          that.fileDetailList = [{
            url: that.aliUrl + that.updateBroadcast.url
          }]
        }
      })
    },

    // 修改录播提交
    updateBroadcastFun(ele) {
      const that = this
      // that.updateBroadcast.detail = that.$refs.editor.value
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          that.fullscreenLoading = true
          if (that.fileDetailList.length !== 0) {
            const a = that.fileDetailList[0].url.split('manage/')
            that.updateBroadcast.imageDetailFilePath = 'manage/' + a[a.length - 1]
            that.updateBroadcast.groupClassObjectVos = that.groupClassObject
            const loading = this.$loading({
              lock: true,
              text: '修改信息提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            that.updateBroadcast.imageListFilePaths = null
            broadcastApi.updateBroadcastById(that.updateBroadcast).then(() => {
              that.dialogVisible = false
              loading.close()
              that.$nextTick(() => that.fetchData())
              that.fullscreenLoading = false
              that.$message.success('修改成功')
              that.groupClassObject = []
            }).catch(() => {
              that.fullscreenLoading = false
              that.$message.error('修改失败')
              loading.close()
            })
          } else {
            that.fullscreenLoading = false
            that.$message.warning('视频一定得上传哦')
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //上传录频
    uploadDetailHttp({
      file
    }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      console.log(fileName+"wyy")
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          console.log(name);
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.updateBroadcast.url = name
              that.fileDetailList=[];
              that.fileDetailList.push({
                'uid': file.uid,
                'url': url
              })
            } else { // 新增上传图片
              that.addBroadcastData.url = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          //  that.$message.error('上传图片失败请检查网络或者刷新页面')
          that.uploadLoading = false
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
      })
    },

    // 取消课时
    // cancelGroup(classHoursNumber) {
    //   var index = this.groupClassObject.length - 1
    //   for (var i = 0; i < this.groupClassObject.length; i++) {
    //     const v = this.groupClassObject[i]
    //     if (v.classHoursNumber === classHoursNumber) {
    //       index = i
    //       this.groupClassObject.splice(index, 1)
    //       this.$message.success('取消成功')
    //       return
    //     }
    //   }
    // },
    // base64转blob
    toBlob(urlData, fileType) {
      const bytes = window.atob(urlData)
      let n = bytes.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bytes.charCodeAt(n)
      }
      return new Blob([u8arr], {
        type: fileType
      })
    },

    // 关闭弹窗
    close() {
      this.groupClassObject = []
      this.classHoursTableShow = false
      this.dialogVisible = false
    }

  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px ;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
}
</style>
