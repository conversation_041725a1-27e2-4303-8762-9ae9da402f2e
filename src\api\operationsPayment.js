/**
 * 托管中心列表相关接口
 */
import request from '@/utils/request';

export default {
  //俱乐部完款
  operationsPayment(params) {
    return request({
      url: '/znyy/operations/v2/operationsPayment',
      method: 'POST',
      params
    });
  },
  //查询学习系统合同状态
  operationsPaymentQuery(params) {
    return request({
      url: '/znyy/operations/v2/operationsPaymentQuery',
      method: 'GET',
      params
    });
  },
  //查询定金完款的数量
  queryDepositDetail(params) {
    return request({
      url: '/znyy/operations/v2/queryDepositDetail',
      method: 'GET',
      params
    });
  },
  QRlink(params) {
    return request({
      url: '/znyy/sign/contract/qr-link',
      method: 'GET',
      params
    });
  },
  judgeOrderStatus(params) {
    return request({
      url: '/znyy/operations/v2/judgeOrderStatus',
      method: 'GET',
      params
    });
  },
  amountConfig(params) {
    return request({
      url: '/znyy/V2/merchant/amountConfig',
      method: 'GET',
      params
    });
  },
  judgeOperationsNum(params) {
    return request({
      url: '/znyy/operations/v2/judgeOperationsNum',
      method: 'GET',
      params
    });
  },
  compensationSignStatus(data) {
    return request({
      url: '/znyy/sign/contract/compensation-sign-status',
      method: 'POST',
      data
    });
  },
  /**
   * 编辑所属地
   */
  updateProvinceCity(data) {
    return request({
      url: '/znyy/operations/v2/updateProvinceCity?id=' + data.id + '&provinceCity=' + data.provinceCity,
      method: 'PUT'
    });
  },
  /**
   * 开通俱乐部
   */
  openOperationApi(data) {
    return request({
      url: '/znyy/operations/v2/openClubManual',
      method: 'POST',
      params: data
    });
  }
};
