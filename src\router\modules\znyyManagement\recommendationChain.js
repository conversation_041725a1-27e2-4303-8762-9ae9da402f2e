// 完整推荐链-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/recommendationChain',
  component: Layout,
  redirect: '/area/recommendationChain/recommendationChain',
  meta: {
    perm: 'm:recommendation:recommendationChain',
    title: '完整推荐链',
    icon: 'recommendationChain'
  },
  children: [
    {
      path: 'recommendationChain',
      component: () => import('@/views/areas/recommendationChain/recommendationChain'),
      name: 'recommendationChain',
      meta: {
        perm: 'm:recommendation:recommendationChain',
        title: '完整推荐链',
        icon: 'recommendationChain'
      }
    }
  ]
};
