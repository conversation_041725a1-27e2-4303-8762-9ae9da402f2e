import request from '@/utils/request'

export default {
  //分页
  list(data) {
    return request({
      url: '/activiti/onlineRule/list',
      method: 'GET',
      params: data
    })
  },
  delete(data) {
    return request({
      url: '/activiti/onlineRule/delete',
      method: 'DELETE',
      params: data
    })
  },
  add(data) {
    return request({
      url: '/activiti/onlineRule/saveNew',
      method: 'POST',
      data
    })
  },
  update(data) {
    return request({
      url: '/activiti/onlineRule/update',
      method: 'POST',
      data
    })
  },
}
