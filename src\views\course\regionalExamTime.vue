<template>
  <div class="regional-exam-time">
    <!-- 筛选表单 -->
    <el-form :inline="true" :model="queryData" class="filter-form" size="small">
      <el-form-item label="城市编号">
        <el-input v-model="queryData.cityCode" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="省份城市">
        <el-cascader v-model="queryCityName" :options="cityOptions" :props="{ checkStrictly: true, value: 'label' }" @change="changeCityName1" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">搜索</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 添加按钮 -->
    <el-button type="primary" icon="el-icon-plus" @click="onAdd" style="margin-bottom: 16px">添加</el-button>

    <!-- 数据表格 -->
    <el-table :data="tableData" border style="width: 100%" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }" v-loading="tableLoading">
      <el-table-column v-for="(item, index) in tableHeadList" :key="index" :prop="item.value" :label="item.label" header-align="center">
        <template slot-scope="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="primary" size="mini" @click="onEdit(row)">编辑</el-button>
            <el-button type="danger" size="mini" @click="onDelete(row)">删除</el-button>
          </div>
          <div v-else-if="item.value.includes('Time')">
            <el-tooltip class="item" effect="dark" :content="row[item.value]" placement="top">
              <span class="ellipsis-text">{{ getSome(row[item.value]) }}</span>
            </el-tooltip>
          </div>
          <span v-else>{{ row[item.value] ? row[item.value] : '-' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination" style="margin-top: 16px; text-align: center">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="queryData.pageSize"
        :current-page="queryData.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :width="dialogWidth" @close="onDialogClose" :close-on-click-modal="false" class="lesson-plan-dialog">
      <el-form :model="formData" ref="formRef" label-width="120px">
        <el-form-item label="区域" prop="formCityName">
          <el-cascader v-if="dialogVisible" v-model="formCityName" :options="cityOptions" :props="{ checkStrictly: true, value: 'label' }" />
        </el-form-item>
        <el-form-item label="中考时间" prop="zhongkaoTime">
          <div class="date-list">
            <MonthDayInput :dialogWidth="gradWidth" v-model="formData.zhongkaoTime" key="zhongkaoTime"></MonthDayInput>
          </div>
        </el-form-item>
        <el-form-item label="高考时间" prop="gaokaoTime">
          <div class="date-list">
            <MonthDayInput :dialogWidth="gradWidth" v-model="formData.gaokaoTime" key="gaokaoTime"></MonthDayInput>
          </div>
        </el-form-item>
        <el-form-item label="雅思时间" prop="ieltsTime">
          <div class="date-list">
            <MonthDayInput :dialogWidth="gradWidth" v-model="formData.ieltsTime" key="ieltsTime"></MonthDayInput>
          </div>
        </el-form-item>
        <el-form-item label="托福时间" prop="toeflTime">
          <div class="date-list">
            <MonthDayInput :dialogWidth="gradWidth" v-model="formData.toeflTime" key="toeflTime"></MonthDayInput>
          </div>
        </el-form-item>
        <el-form-item label="KET时间" prop="ketTime">
          <div class="date-list">
            <MonthDayInput :dialogWidth="gradWidth" v-model="formData.ketTime" key="ketTime"></MonthDayInput>
          </div>
        </el-form-item>
        <el-form-item label="PET时间" prop="petTime">
          <div class="date-list">
            <MonthDayInput :dialogWidth="gradWidth" v-model="formData.petTime" key="petTime"></MonthDayInput>
          </div>
        </el-form-item>
        <el-form-item label="FCE时间" prop="fceTime">
          <div class="date-list">
            <MonthDayInput :dialogWidth="gradWidth" v-model="formData.fceTime" key="fceTime"></MonthDayInput>
          </div>
        </el-form-item>
      </el-form>
      <div style="text-align: center; margin-top: 30px">
        <el-button @click="onDialogClose">取消</el-button>
        <el-button type="primary" @click="onDialogConfirm" :loading="submitLoading">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import areaData from '@/utils/miniCity.json';
  import { getRegionalExamTimeList, addRegionalExamTime, editRegionalExamTime, deleteRegionalExamTime } from '@/api/course/regionalExamTime';
  import MonthDayInput from './components/MonthDayInputList.vue';
  import { mapGetters } from 'vuex';

  export default {
    name: 'RegionalExamTime',
    components: {
      MonthDayInput
    },
    data() {
      return {
        submitLoading: false,
        tableLoading: false,
        tableHeadList: [
          {
            label: '城市编号',
            value: 'cityCode'
          },
          {
            label: '区域',
            value: 'cityName'
          },
          {
            label: '中考时间',
            value: 'zhongkaoTime'
          },
          {
            label: '高考时间',
            value: 'gaokaoTime'
          },
          {
            label: 'KET时间',
            value: 'ketTime'
          },
          {
            label: 'PET时间',
            value: 'petTime'
          },
          {
            label: 'FCE时间',
            value: 'fceTime'
          },
          {
            label: '雅思时间',
            value: 'ieltsTime'
          },
          {
            label: '托福时间',
            value: 'toeflTime'
          },
          {
            label: '操作',
            value: 'operate'
          }
        ],
        queryCityName: [],
        formCityName: [],
        queryData: {
          pageNum: 1,
          pageSize: 10,
          cityCode: '',
          cityName: ''
        },
        tableData: [],
        total: 14,
        deleteDialogVisible: false,
        cityOptions: areaData,
        deleteRow: null,
        dialogVisible: false,
        dialogTitle: '添加',
        formData: {
          cityName: '',
          province: '',
          city: '',
          area: '',
          zhongkaoTime: [''],
          gaokaoTime: [''],
          ieltsTime: [''],
          toeflTime: [''],
          ketTime: [''],
          petTime: [''],
          fceTime: ['']
        },
        gradWidth: '4',
        dialogWidth: '45%'
      };
    },
    mounted() {
      this.initData();
    },
    computed: {
      ...mapGetters(['getClientWidth'])
    },
    watch: {
      getClientWidth: {
        handler(val) {
          // console.log(val);
          // 根据屏幕宽度动态计算弹窗宽度
          if (val < 460) {
            this.gradWidth = '1';
            this.dialogWidth = '100%';
          } else if (val < 780) {
            this.gradWidth = '2';
            this.dialogWidth = '80%';
          } else if (val < 1280) {
            this.gradWidth = '3';
            this.dialogWidth = '65%';
          } else {
            this.gradWidth = '4';
            this.dialogWidth = '50%';
          }
        },
        immediate: true
      },
      queryCityName: {
        handler(val) {
          if (val && val.length > 0) {
            this.queryData.cityName = val.join('');
          } else {
            this.queryData.cityName = '';
          }
        },
        deep: true,
        immediate: true
      },
      formCityName: {
        handler(val) {
          if (val) {
            this.formData.cityName = val.join('');
            this.formData.province = val.length > 0 ? val[0] : '';
            this.formData.city = val.length > 1 ? val[1] : '';
            this.formData.area = val.length > 2 ? val[2] : '';
          } else {
            this.formData.cityName = '';
            this.formData.province = '';
            this.formData.city = '';
            this.formData.area = '';
          }
        },
        deep: true,
        immediate: true
      }
    },
    methods: {
      getSome(str) {
        if (str) {
          let arr = str.split('-');
          if (arr && arr.length > 0) {
            return arr[0];
          }
        } else {
          return '-';
        }
      },
      async initData() {
        this.tableLoading = true;
        try {
          let { data } = await getRegionalExamTimeList(this.queryData);
          console.log(data);
          this.total = +data.totalItems;
          this.tableData = data.data;
          this.tableLoading = false;
        } catch (error) {
          this.tableLoading = false;
        } finally {
          this.tableLoading = false;
        }
      },
      onSearch() {
        // 搜索逻辑
        this.queryData.pageNum = 1;
        this.initData();
      },
      changeCityName1(e) {
        console.log(e.join('/'));
      },
      onReset() {
        this.queryCityName = [];
        this.queryData = {
          pageNum: 1,
          pageSize: 10,
          cityCode: '',
          cityName: ''
        };
        this.initData();
      },
      onAdd() {
        this.dialogTitle = '添加区域考试时间';
        this.editIndex = null;
        this.dialogVisible = true;
      },
      splitCompactAddress(address) {
        const result = [];

        // 特殊省级名称列表
        const specialProvinces = ['香港特别行政区', '澳门特别行政区'];

        // 特殊市级名称（没有“市”字结尾）
        const specialCities = ['省直辖县级行政区划', '香港特别行政区', '澳门特别行政区'];

        // 先匹配特殊省份
        for (const prov of specialProvinces) {
          if (address.startsWith(prov)) {
            result.push(prov);
            address = address.slice(prov.length);
            break;
          }
        }

        // 如果省未匹配，再匹配普通省份
        if (result.length === 0) {
          const provinceMatch = address.match(/^(.*?(省|自治区|特别行政区))/);
          if (provinceMatch) {
            const province = provinceMatch[1];
            result.push(province);
            address = address.slice(province.length);
          } else {
            const directMatch = address.match(/^(北京|天津|上海|重庆)市/);
            if (directMatch) {
              const province = directMatch[0];
              result.push(province);
              address = address.slice(province.length);
            }
          }
        }

        // 匹配特殊市级名
        for (const city of specialCities) {
          if (address.startsWith(city)) {
            result.push(city);
            address = address.slice(city.length);
            break;
          }
        }

        // 如果市级没被匹配，再匹配普通市级
        if (result.length === 1) {
          const cityMatch = address.match(/^(.*?市)/);
          if (cityMatch) {
            const city = cityMatch[1];
            result.push(city);
            address = address.slice(city.length);
          }
        }

        // 区/县级
        if (address) {
          result.push(address);
        }

        return result;
      },
      splitAddress(address) {
        const result = [];
        const directCities = ['北京市', '天津市', '上海市', '重庆市'];

        // 处理直辖市
        for (const city of directCities) {
          if (address.startsWith(city)) {
            result.push(city); // 直辖市做为省级
            let rest = address.slice(city.length);

            if (rest) {
              console.log(rest, '============');
              const cityLevelMatch = rest.match(/^(市|县|区)/);
              if (cityLevelMatch) {
                result.push(cityLevelMatch[1]);
                rest = rest.slice(cityLevelMatch[1].length);
              }

              if (rest) result.push(rest);
            }

            return result;
          }
        }

        // 普通省份处理
        const provinceMatch = address.match(/^(.*?(省|自治区))/);
        if (provinceMatch) {
          result.push(provinceMatch[1]);
          address = address.slice(provinceMatch[1].length);
        }

        const cityMatch = address.match(/^(.*?市)/);
        if (cityMatch) {
          result.push(cityMatch[1]);
          address = address.slice(cityMatch[1].length);
        }

        if (address) {
          result.push(address);
        }

        return result;
      },
      onEdit(row) {
        console.log(row);
        let copyData = JSON.parse(JSON.stringify(row));
        // this.formCityName = copyData.cityName.split('');
        this.formCityName = this.splitCompactAddress(copyData.cityName);
        for (const key in copyData) {
          if (key.includes('Time') && key !== 'createTime' && key !== 'updateTime') {
            copyData[key] = copyData[key].split('-');
          }
        }
        this.dialogTitle = '编辑区域考试时间';
        this.formData = { ...copyData };
        this.dialogVisible = true;
      },
      onDelete(row) {
        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await deleteRegionalExamTime({ id: row.id });
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.onReset();
            } catch (error) {
              console.log(error);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      handleSizeChange(size) {
        this.queryData.pageSize = size;
        this.queryData.pageNum = 1;
        this.initData();
        // 重新请求数据
      },
      handleCurrentChange(page) {
        this.queryData.pageNum = page;
        this.initData();
        // 重新请求数据
      },
      onDialogClose() {
        this.submitLoading = false;
        this.formCityName = [];
        this.formData = {
          cityName: '',
          zhongkaoTime: [''],
          gaokaoTime: [''],
          ieltsTime: [''],
          toeflTime: [''],
          ketTime: [''],
          petTime: [''],
          fceTime: ['']
        };
        this.$refs.formRef && this.$refs.formRef.resetFields();
        this.dialogVisible = false;
      },
      isValidMonthDayArray(arr) {
        const seen = new Set();

        // 每月的最大天数
        const maxDays = {
          1: 31,
          2: 29, // 不区分闰年，统一允许到29
          3: 31,
          4: 30,
          5: 31,
          6: 30,
          7: 31,
          8: 31,
          9: 30,
          10: 31,
          11: 30,
          12: 31
        };

        for (const val of arr) {
          if (seen.has(val)) {
            return false; // 重复
          }
          seen.add(val);

          if (!/^\d{1,2}\.\d{1,2}$/.test(val)) {
            return false; // 格式错误
          }

          const [mStr, dStr] = val.split('.');
          const month = parseInt(mStr, 10);
          const day = parseInt(dStr, 10);

          if (!maxDays[month] || day < 1 || day > maxDays[month]) {
            return false; // 月份非法或日期超出范围
          }
        }

        return true; // 所有值合法且无重复
      },
      onDialogConfirm() {
        this.$refs.formRef.validate(async (valid) => {
          if (valid) {
            console.log(this.formData);
            if (!this.formData.cityName) {
              return this.$message.error('请选择区域');
            }

            // 检查是否至少有一个时间字段有值
            const timeFields = ['zhongkaoTime', 'gaokaoTime', 'ieltsTime', 'toeflTime', 'ketTime', 'petTime', 'fceTime'];

            // 检查是否至少有一个时间字段包含有效值
            const hasAnyTimeValue = timeFields.some((field) => this.formData[field] && this.formData[field].filter(Boolean).length > 0);

            if (!hasAnyTimeValue) {
              return this.$message.error('请至少填写一个考试时间');
            }

            let obj = JSON.parse(JSON.stringify(this.formData));
            for (const key in obj) {
              if (key.includes('Time') && key !== 'createTime' && key !== 'updateTime') {
                if (obj[key] && obj[key].length > 0) {
                  // 只有当数组中有非空值时才进行验证
                  const filteredValues = obj[key].filter(Boolean);
                  if (filteredValues.length > 0) {
                    if (this.isValidMonthDayArray(filteredValues)) {
                      obj[key] = filteredValues.join('-');
                    } else {
                      return this.$message.error('请检查日期');
                    }
                  } else {
                    // 如果该字段没有有效值，设置为空字符串
                    obj[key] = '';
                  }
                }
              }
            }

            try {
              this.submitLoading = true;
              this.formData.id ? await editRegionalExamTime(obj) : await addRegionalExamTime(obj);
              this.onReset();
              this.$message({
                type: 'success',
                message: '操作成功'
              });
              this.dialogVisible = false;
            } catch (error) {
              console.log(error);
            } finally {
              this.submitLoading = false;
            }
          }
        });
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      }
    }
  };
</script>

<style lang="less" scoped>
  .regional-exam-time {
    padding: 24px;
    background: #fff;
  }
  .filter-form {
    margin-bottom: 16px;
  }
  .date-list {
    max-height: 130px;
    overflow-y: auto;
  }
  .ellipsis-text {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .lesson-plan-list {
    padding: 20px;
  }
  .filter-form {
    margin-bottom: 16px;
  }
  .input-list {
    max-height: 150px;
    overflow-y: auto;
  }

  /* 弹窗响应式样式 */
  /deep/ .lesson-plan-dialog .el-dialog {
    margin-top: 5vh !important;
    max-height: 90vh;
    overflow-y: auto;
  }

  /* 移动端适配 */
  @media screen and (max-width: 768px) {
    .filter-form .el-form-item {
      margin-bottom: 10px;
    }

    /deep/ .lesson-plan-dialog .el-dialog {
      margin: 3vh auto !important;
      max-height: 94vh;
    }

    /deep/ .lesson-plan-dialog .el-dialog__body {
      padding: 15px !important;
    }

    /deep/ .el-form-item__label {
      width: 100px !important;
    }

    .input-list {
      width: 100%;
    }
  }

  /* 平板适配 */
  @media screen and (min-width: 769px) and (max-width: 1200px) {
    /deep/ .lesson-plan-dialog .el-dialog {
      margin-top: 8vh !important;
    }

    .input-list {
      width: 90%;
    }
  }
</style>
