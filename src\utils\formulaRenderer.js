// 公式/文本混合渲染工具（精简版）
// 流程：定界符分段 -> 纯中文短文本直接返回 -> HTML 富文本内局部解析 -> 评分判定整体公式或纯文本
import katex from 'katex';
// 重要：未引入 KaTeX 的默认样式时，katex 会同时显示 MathML 与 HTML 两套结构，
// 简单表达式会出现视觉上的“重复”(例如 "$(0,1)$" -> (0,1)(0,1))。
// 引入官方样式以隐藏无障碍用的 MathML 可视输出，避免重复。
// 样式请在运行入口 (如 main.js) 全局引入：import 'katex/dist/katex.min.css'
// 避免在此工具文件直接引入导致 Jest 转换器无法解析 CSS 而报 SyntaxError。

export function escapeHtml(str = '') {
  return str.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
}

const CHINESE_TEXT_ONLY = /^[\u4E00-\u9FFF，。、“”‘’：《》：（）()［］【】…·—\s]+$/;
const delimiterDetection = /\$[^$]+\$|\\\(|\\\)|\\\[|\\\]|\$\$[\s\S]+?\$\$/; // 公式定界符
// 分式或幂的结构提示（粗略）
const STRUCT_HINT = /(\d+\/\d+|[A-Za-z]\d+|\d+[A-Za-z])/g;
const HTML_TAG_PATTERN = /<[^>]+>/; // 检测是否包含 HTML 标签

// 数学命令正则（不使用全局 g，避免 test 状态副作用）
const CORE_CMD_RE = /\\(frac|sqrt|sum|int|lim|log|ln|sin|cos|tan|alpha|beta|gamma|theta|pi|rightarrow|leftrightarrow)\b/;

function mathScore(text) {
  let score = 0;
  if (CORE_CMD_RE.test(text)) score += 1; // 含核心命令
  if (/[_^]/.test(text)) score += 1; // 上下标
  if (/[A-Za-z0-9]\s*=\s*[A-Za-z0-9]/.test(text)) score += 1; // 等号结构
  // 运算符统计：排除英文连字符场景 （字母-字母 或 - 后跟空格形成语句断开的情况） 与 URL/路径中的斜杠
  const operatorMatches = (text.match(/[+\-*/]/g) || []).filter((op, idx) => {
    if (op === '-') {
      const prev = text[idx - 1];
      const next = text[idx + 1];
      // 若两侧都是字母数字，视作英文连字符而非减号，例如 eight-year-old
      if (/[A-Za-z0-9]/.test(prev) && /[A-Za-z0-9]/.test(next)) return false;
    }
    if (op === '/') {
      // 过滤可能的 URL 或文件路径：出现 'http', 'https', 'www', '.com' 等上下文时不计作除号
      const slice = text.slice(Math.max(0, idx - 10), idx + 10);
      if (/(http|https|www|\.com|\.cn|\.org)/.test(slice)) return false;
    }
    return true;
  });
  if (operatorMatches.length >= 2) score += 1; // 至少两个有效数学运算符
  if (STRUCT_HINT.test(text)) score += 0.5; // 结构提示
  return score;
}

// 判断是否很可能是数学公式（不含定界符的启发式，用于其它地方按需调用）
export function isLikelyFormula(text) {
  if (!text) return false;
  if (delimiterDetection.test(text)) return true;
  // 英文阅读理解类超长段落直接视为非公式
  const len = text.length;
  const latin = (text.match(/[A-Za-z]/g) || []).length;
  const space = (text.match(/\s/g) || []).length;
  const englishRatio = (latin + space) / (len || 1);
  if (len > 160 && englishRatio > 0.65) return false;
  const score = mathScore(text);
  return score >= 2; // 提高阈值：至少 2 项命中才认为是公式
}

export function renderFormulaAuto(text) {
  if (!text) return '';
  // 定界符混合模式优先
  if (delimiterDetection.test(text)) return renderMixed(text);
  if (CHINESE_TEXT_ONLY.test(text)) return escapeHtml(text);
  if (HTML_TAG_PATTERN.test(text)) return renderHtmlWithMath(text);
  const len = text.length;
  const latin = (text.match(/[A-Za-z]/g) || []).length;
  const space = (text.match(/\s/g) || []).length;
  const englishRatio = (latin + space) / (len || 1);
  // 不提升阈值：保持原 score <1 直接文本，但先过滤英文阅读理解长段落
  if (len > 160 && englishRatio > 0.65) return escapeHtml(text);
  const score = mathScore(text);
  if (score < 1) return escapeHtml(text);
  // 针对长中文说明（含少量等号/数字），避免整体误判为公式：中文比例高且数学命中仅来自等号结构
  const chineseChars = (text.match(/[\u4E00-\u9FFF]/g) || []).length;
  const chineseRatio = chineseChars / len;
  // 新增：若为中文主导的说明性语句，且无核心数学命令/上下标/分式，只出现若干 "变量=数字" 形式，则视为普通文本
  const hasCoreCmd = CORE_CMD_RE.test(text);
  const hasSubSup = /[_^]/.test(text);
  const hasFracPattern = /\\frac|\\sqrt/.test(text);
  const equalsPairs = (text.match(/[A-Za-z][A-Za-z0-9]*\s*=\s*[0-9]+/g) || []).length;
  if (chineseRatio > 0.35 && !hasCoreCmd && !hasSubSup && !hasFracPattern) {
    return escapeHtml(text);
  }
  // 若仅有 score==1（例如只命中等号结构或单个运算符），且没有定界符，则视为普通文本
  if (!delimiterDetection.test(text) && score === 1) return escapeHtml(text);
  // 无定界符且缺少强数学特征（核心命令/上下标/分式/至少两个 + - * /）时仍按普通文本处理，防止几何叙述整段被包裹
  if (!delimiterDetection.test(text)) {
    const strongOps = (text.match(/[+\-*/]/g) || []).filter((op, idx) => {
      if (op === '-') {
        const prev = text[idx - 1];
        const next = text[idx + 1];
        if (/[A-Za-z0-9]/.test(prev) && /[A-Za-z0-9]/.test(next)) return false;
      }
      if (op === '/') {
        const slice = text.slice(Math.max(0, idx - 10), idx + 10);
        if (/(http|https|www|\.com|\.cn|\.org)/.test(slice)) return false;
      }
      return true;
    });
    const hasStrongFeature = hasCoreCmd || hasSubSup || hasFracPattern || strongOps.length >= 2;
    if (!hasStrongFeature) return escapeHtml(text);
  }
  if (len > 30 && chineseRatio > 0.4 && score <= 1.5) return escapeHtml(text);
  try {
    const rendered = katex.renderToString(text, { throwOnError: false, displayMode: false, strict: false, trust: true });
    if (rendered.includes('ParseError')) return escapeHtml(text);
    return rendered;
  } catch (e) {
    return escapeHtml(text);
  }
}

export function renderMixed(raw = '') {
  const patterns = [
    { r: /\$\$([\s\S]+?)\$\$/g, d: true },
    { r: /\$([^$]+?)\$/g, d: false },
    { r: /\\\[([\s\S]+?)\\\]/g, d: true },
    { r: /\\\(([\s\S]+?)\\\)/g, d: false }
  ];
  const matches = [];
  for (const p of patterns) {
    let m;
    while ((m = p.r.exec(raw)) !== null) {
      matches.push({ start: m.index, end: p.r.lastIndex, body: m[1], display: p.d, raw: m[0] });
    }
  }
  if (!matches.length) return escapeHtml(raw);
  matches.sort((a, b) => a.start - b.start);
  let cursor = 0,
    html = '';
  for (const seg of matches) {
    if (cursor < seg.start) html += escapeHtml(raw.slice(cursor, seg.start));
    try {
      // 规范化：很多来源（例如经过 JSON / 字符串转义）会把 "\\frac" 写成两个反斜杠，
      // KaTeX 解析时会把前两个反斜杠视为换行命令，导致意外换行。这里在渲染前把常见命令前的重复反斜杠压缩成单个。
      let normalized = seg.body
        .trim()
        // 针对命令（\frac \sqrt ...）双反斜杠 -> 单反斜杠
        .replace(/\\\\(frac|sqrt|sum|int|lim|log|ln|sin|cos|tan|alpha|beta|gamma|theta|pi|rightarrow|leftrightarrow)\b/g, '\\$1')
        // 针对多余的三连、四连（保守地把奇数还原成单，偶数保留一个）
        .replace(/\\{2,}/g, (m) => (m.length % 2 === 0 ? '\\' : '\\'));
      const h = katex.renderToString(normalized, { throwOnError: false, displayMode: seg.display, trust: true, strict: false });
      if (h.includes('ParseError')) html += escapeHtml(seg.raw);
      else html += h;
    } catch (e) {
      html += escapeHtml(seg.raw);
    }
    cursor = seg.end;
  }
  if (cursor < raw.length) html += escapeHtml(raw.slice(cursor));
  return html;
}

// 富文本（含标签）中穿插公式：保持标签原样，仅解析文本节点里的公式定界符；无需整体 KaTeX
export function renderHtmlWithMath(raw = '') {
  raw = sanitizeHtml(raw);
  return raw
    .split(/(<[^>]+>)/g)
    .filter(Boolean)
    .map((seg) => (seg[0] === '<' ? seg : delimiterDetection.test(seg) ? renderMixed(seg) : escapeHtml(seg)))
    .join('');
}

// 简易 HTML 清洗：白名单标签 + 去除事件属性 / script / style
// 说明：若将来需要支持 img / a，可在 ALLOWED_TAGS 中加入并在下面保留安全属性
export function sanitizeHtml(raw = '') {
  if (!raw) return '';
  const TAGS = new Set(['p', 'br', 'span', 'sub', 'sup', 'em', 'strong', 'b', 'i', 'u']);
  return raw
    .replace(/<script[\s\S]*?<\/script>/gi, '')
    .replace(/<style[\s\S]*?<\/style>/gi, '')
    .replace(/<!DOCTYPE[\s\S]*?>/gi, '')
    .replace(/<!--[\s\S]*?-->/g, '')
    .replace(/<\/?([a-zA-Z0-9:-]+)([^>]*)>/g, (m, tag) => {
      const t = tag.toLowerCase();
      if (!TAGS.has(t)) return escapeHtml(m);
      return m[1] === '/' ? `</${t}>` : `<${t}>`; // 丢弃所有属性
    });
}

export default { renderFormulaAuto, renderMixed, renderHtmlWithMath, sanitizeHtml, escapeHtml, isLikelyFormula };
