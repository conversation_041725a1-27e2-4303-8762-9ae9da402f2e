// 学员管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/student',
  component: Layout,
  redirect: '/student/studentList',
  meta: {
    perm: 'm:student',
    title: '学员管理',
    icon: 'student'
  },
  children: [
    {
      path: 'mathsStudentList',
      component: () => import('@/views/maths/index'),
      name: 'mathsStudentList',
      meta: {
        perm: 'm:student:mathsStudentList',
        title: '学员管理'
      }
    },
    {
      path: 'studentList',
      component: () => import('@/views/student/studentList'),
      name: 'studentList',
      meta: {
        perm: 'm:student:studentList',
        title: '学员列表'
      }
    },
    {
      path: 'areasStudentCourseList',
      component: () => import('@/views/areas/school/areasStudentCourseList'),
      name: 'areasStudentCourseList',
      meta: {
        perm: 'm:student:areasStudentCourseList',
        title: '学员列表'
      }
    },
    {
      path: 'xueyuanxinxi',
      hidden: true,
      component: () => import('@/views/student/xueyuanxinxi'),
      name: '学员信息表',
      meta: {
        title: '学员信息'
      }
    },
    {
      path: 'shikejilu',
      hidden: true,
      component: () => import('@/views/student/shikejilu'),
      name: '咨询记录表',
      meta: {
        title: '咨询记录表'
      }
    },
    {
      path: 'areasOpenCourse',
      hidden: true,
      component: () => import('@/views/areas/school/areasOpenCourse'),
      name: 'areasOpenCourse',
      meta: {
        perm: 'm:student:areasOpenCourse',
        title: '开通课程'
      }
    },
    {
      path: 'areasOpenSuperReadCourse',
      hidden: true,
      component: () => import('@/views/areas/school/areasOpenSuperReadCourse'),
      name: 'areasOpenSuperReadCourse',
      meta: {
        perm: 'm:student:areasOpenSuperReadCourse',
        title: '开通超级阅读课程'
      }
    },
    {
      path: 'areasOpenListenCourse',
      hidden: true,
      component: () => import('@/views/areas/school/areasOpenListenCourse'),
      name: 'areasOpenListenCourse',
      meta: {
        perm: 'm:student:areasOpenListenCourse',
        title: '开通全能听力课程'
      }
    },
    {
      path: 'studentExperienceEdit',
      hidden: true,
      component: () => import('@/views/student/studentExperienceEdit'),
      name: 'studentExperienceEdit',
      meta: {
        title: '学员试课报告编辑'
      }
    },
    {
      path: 'trialClassOrderList',
      component: () => import('@/views/student/trialClassOrderList'),
      name: 'trialClassOrderList',
      meta: {
        perm: 'm:student:trialClassOrderList',
        title: '待完善上课信息表'
      }
    },
    {
      path: 'jiaofufangshi',
      component: () => import('@/views/student/jiaofufangshi'),
      name: 'jiaofufangshi',
      meta: {
        noCache: true,
        // perm: 'm:student:jiaofufangshi',
        title: '集中交付学员'
      }
    },
    {
      path: 'deliverClassFlowList',
      component: () => import('@/views/student/deliverClassFlowList'),
      name: 'deliverClassFlowList',
      meta: {
        noCache: true,
        perm: 'm:student:deliverClassFlowList',
        title: '交付学时充值流水'
      }
    },
    {
      path: 'areasStudentCourseRecord',
      component: () => import('@/views/areas/school/areasStudentCourseRecord'),
      name: 'areasStudentCourseRecord',
      meta: {
        perm: 'm:student:areasStudentCourseRecord',
        title: '学员课程记录'
      }
    },
    {
      path: 'areasStudentTestResultList',
      component: () => import('@/views/areas/school/areasStudentTestResultList'),
      name: 'areasStudentTestResultList',
      meta: {
        perm: 'm:student:areasStudentTestResultList',
        title: '学员词汇测试'
      }
    },
    {
      path: 'studentWordsTest',
      hidden: true,
      component: () => import('@/views/student/studentWordsTest'),
      name: 'studentWordsTest',
      meta: {
        perm: 'm:student:studentWordsTest',
        title: '学员词汇测试'
      }
    },
    {
      path: 'areaStudentWordReviewPrint',
      component: () => import('@/views/areas/school/areaStudentWordReviewPrint'),
      name: 'areaStudentWordReviewPrint',
      meta: {
        perm: 'm:student:areaStudentWordReviewPrint',
        title: '21天抗遗忘复习计划'
      }
    },
    {
      path: 'studentWordReviewPrint',
      hidden: true,
      component: () => import('@/views/student/studentWordReviewPrint'),
      name: 'studentWordReviewPrint',
      meta: {
        //perm:'m:student:studentWordReviewPrint',
        title: '21天抗遗忘打印'
      }
    },
    {
      path: 'studentWordReviewList',
      hidden: true,
      component: () => import('@/views/student/studentWordReviewList'),
      name: 'studentWordReviewList',
      meta: {
        title: '21天抗遗忘记录'
      }
    },
    {
      path: 'studentWordView',
      hidden: true,
      component: () => import('@/views/student/studentWordViewList'),
      name: 'studentWordViewList',
      meta: {
        title: '查看详情'
      }
    },
    {
      path: 'areasStudentWordPrintList',
      component: () => import('@/views/areas/school/areasStudentWordPrintList'),
      name: 'areasStudentWordPrintList',
      meta: {
        perm: 'm:student:areasStudentWordPrintList',
        title: '学员测验打印'
      }
    },
    {
      path: 'studentTestPrint',
      hidden: true,
      component: () => import('@/views/student/studentTestPrint'),
      name: 'studentTestPrint',
      meta: {
        perm: 'm:student:studentTestPrint',
        title: '学员测验打印'
      }
    },
    {
      path: 'studentDayPrint',
      hidden: true,
      component: () => import('@/views/student/studentDayPrint'),
      name: 'studentDayPrint',
      meta: {
        title: '学员测验打印'
      }
    },
    {
      path: 'studentGraduation',
      hidden: true,
      component: () => import('@/views/student/studentGraduation'),
      name: 'studentGraduation',
      meta: {
        title: '学员测验打印'
      }
    },
    {
      path: 'studentTestPrintReading',
      hidden: true,
      component: () => import('@/views/student/studentTestPrintReading'),
      name: 'studentTestPrintReading',
      meta: {
        perm: 'm:student:studentTestPrintReading',
        title: '学员测验打印-阅读理解'
      }
    },
    {
      path: 'studentTestPrintReport',
      hidden: true,
      component: () => import('@/views/student/studentTestPrintReport'),
      name: 'studentTestPrintReport',
      meta: {
        perm: 'm:student:studentTestPrintReport',
        title: '学员测验打印-结业报告'
      }
    },
    {
      path: 'areasStudentExperience',
      component: () => import('@/views/areas/school/areasStudentExperienceList'),
      name: 'areasStudentExperience',
      meta: {
        perm: 'm:student:areasStudentExperienceList',
        title: '学员试课报告'
      }
    },
    {
      path: 'areasStudentForget',
      component: () => import('@/views/areas/school/areasStudentForgetList'),
      name: 'areasStudentForget',
      meta: {
        perm: 'm:student:areasStudentForgetList',
        title: '21天抗遗忘报告'
      }
    },
    {
      path: 'studentForget',
      hidden: true,
      component: () => import('@/views/student/studentForget'),
      name: 'studentForget',
      meta: {
        // perm: 'b:znyy:student:studentForget',
        title: '21天抗遗忘报告打印详情'
      }
    },
    {
      path: 'areasStudentCourseFlow',
      component: () => import('@/views/areas/school/areasStudentCourseFlow'),
      name: 'areasStudentCourseFlow',
      meta: {
        perm: 'm:student:areasStudentCourseFlow',
        title: '学员销课记录'
      }
    },
    {
      path: 'studentCourseRecord',
      component: () => import('@/views/student/studentCourseRecord'),
      name: 'studentCourseRecord',
      meta: {
        perm: 'm:student:studentCourseRecord',
        title: '学员课程记录'
      }
    },
    {
      path: 'studentCourseProgress',
      component: () => import('@/views/student/studentCourseProgress'),
      name: 'studentCourseProgress',
      meta: {
        perm: 'm:student:studentCourseProgress',
        title: '学员课程进度'
      }
    },

    {
      path: 'wordBookView',
      component: () => import('@/views/student/wordBookView'),
      name: 'wordBookView',
      meta: {
        perm: 'm:student:wordBookView',
        title: '单词本查看'
      }
    }
  ]
};
