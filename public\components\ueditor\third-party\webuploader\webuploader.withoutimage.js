!function(n){function s(e,t){var n,i,r;if("string"==typeof e)return o(e);for(n=[],i=e.length,r=0;r<i;r++)n.push(o(e[r]));return t.apply(null,n)}var e,b,t,i,a={},r=function(e,t,n){var i,r={exports:t};"function"==typeof t&&(n.length||(n=[s,r.exports,r]),void 0!==(i=t.apply(null,n))&&(r.exports=i)),a[e]=r.exports},o=function(e){var t=a[e]||n[e];if(!t)throw new Error("`"+e+"` is undefined");return t},u=(b=n,i=s,(t=function(e,t,n){2===arguments.length&&(n=t,t=null),s(t||[],function(){r(e,n,arguments)})})("dollar-third",[],function(){return b.j<PERSON><PERSON><PERSON>||<PERSON><PERSON>}),t("dollar",["dollar-third"],function(e){return e}),t("promise-third",["dollar"],function(e){return{Deferred:e.Deferred,when:e.when,isPromise:function(e){return e&&"function"==typeof e.then}}}),t("promise",["promise-third"],function(e){return e}),t("base",["dollar","promise"],function(o,e){function t(){}var i,n,r,s,a,u,c,l,d,p,h,f,g,m,v=Function.call;function _(e,t){return function(){return e.apply(t,arguments)}}return{version:"0.1.2",$:o,Deferred:e.Deferred,isPromise:e.isPromise,when:e.when,browser:(c=navigator.userAgent,l={},d=c.match(/WebKit\/([\d.]+)/),p=c.match(/Chrome\/([\d.]+)/)||c.match(/CriOS\/([\d.]+)/),h=c.match(/MSIE\s([\d\.]+)/)||c.match(/(?:trident)(?:.*rv:([\w.]+))?/i),f=c.match(/Firefox\/([\d.]+)/),g=c.match(/Safari\/([\d.]+)/),m=c.match(/OPR\/([\d.]+)/),d&&(l.webkit=parseFloat(d[1])),p&&(l.chrome=parseFloat(p[1])),h&&(l.ie=parseFloat(h[1])),f&&(l.firefox=parseFloat(f[1])),g&&(l.safari=parseFloat(g[1])),m&&(l.opera=parseFloat(m[1])),l),os:(r=navigator.userAgent,s={},a=r.match(/(?:Android);?[\s\/]+([\d.]+)?/),u=r.match(/(?:iPad|iPod|iPhone).*OS\s([\d_]+)/),a&&(s.android=parseFloat(a[1])),u&&(s.ios=parseFloat(u[1].replace(/_/g,"."))),s),inherits:function(e,t,n){var i,r,s;return"function"==typeof t?(i=t,t=null):i=t&&t.hasOwnProperty("constructor")?t.constructor:function(){return e.apply(this,arguments)},o.extend(!0,i,e,n||{}),i.__super__=e.prototype,i.prototype=(r=e.prototype,Object.create?Object.create(r):((s=function(){}).prototype=r,new s)),t&&o.extend(!0,i.prototype,t),i},noop:t,bindFn:_,log:b.console?_(console.log,console):t,nextTick:function(e){setTimeout(e,1)},slice:(n=[].slice,function(){return v.apply(n,arguments)}),guid:(i=0,function(e){for(var t=(+new Date).toString(32),n=0;n<5;n++)t+=Math.floor(65535*Math.random()).toString(32);return(e||"wu_")+t+(i++).toString(32)}),formatSize:function(e,t,n){var i;for(n=n||["B","K","M","G","TB"];(i=n.shift())&&1024<e;)e/=1024;return("B"===i?e:e.toFixed(t||2))+i}}}),t("mediator",["base"],function(e){var t,r=e.$,s=[].slice,o=/\s+/;function a(e,t,n,i){return r.grep(e,function(e){return e&&(!t||e.e===t)&&(!n||e.cb===n||e.cb._cb===n)&&(!i||e.ctx===i)})}function u(e,n,i){r.each((e||"").split(o),function(e,t){i(t,n)})}function c(e,t){for(var n,i=!1,r=-1,s=e.length;++r<s;)if(!1===(n=e[r]).cb.apply(n.ctx2,t)){i=!0;break}return!i}return t={on:function(e,t,i){var r,s=this;return t&&(r=this._events||(this._events=[]),u(e,t,function(e,t){var n={e:e};n.cb=t,n.ctx=i,n.ctx2=i||s,n.id=r.length,r.push(n)})),this},once:function(e,t,i){var r=this;return t&&u(e,t,function(e,t){var n=function(){return r.off(e,n),t.apply(i||r,arguments)};n._cb=t,r.on(e,n,i)}),r},off:function(e,t,n){var i=this._events;return i&&(e||t||n?u(e,t,function(e,t){r.each(a(i,e,t,n),function(){delete i[this.id]})}):this._events=[]),this},trigger:function(e){var t,n,i;return this._events&&e?(t=s.call(arguments,1),n=a(this._events,e),i=a(this._events,"all"),c(n,t)&&c(i,arguments)):this}},r.extend({installTo:function(e){return r.extend(e,t)}},t)}),t("uploader",["base","mediator"],function(e,r){var s=e.$;function n(e){this.options=s.extend(!0,{},n.options,e),this._init(this.options)}return n.options={},r.installTo(n.prototype),s.each({upload:"start-upload",stop:"stop-upload",getFile:"get-file",getFiles:"get-files",addFile:"add-file",addFiles:"add-file",sort:"sort-files",removeFile:"remove-file",skipFile:"skip-file",retry:"retry",isInProgress:"is-in-progress",makeThumb:"make-thumb",getDimension:"get-dimension",addButton:"add-btn",getRuntimeType:"get-runtime-type",refresh:"refresh",disable:"disable",enable:"enable",reset:"reset"},function(e,t){n.prototype[e]=function(){return this.request(t,arguments)}}),s.extend(n.prototype,{state:"pending",_init:function(e){var t=this;t.request("init",e,function(){t.state="ready",t.trigger("ready")})},option:function(e,t){var n=this.options;if(!(1<arguments.length))return e?n[e]:n;s.isPlainObject(t)&&s.isPlainObject(n[e])?s.extend(n[e],t):n[e]=t},getStats:function(){var e=this.request("get-stats");return{successNum:e.numOfSuccess,cancelNum:e.numOfCancel,invalidNum:e.numOfInvalid,uploadFailNum:e.numOfUploadFailed,queueNum:e.numOfQueue}},trigger:function(e){var t=[].slice.call(arguments,1),n=this.options,i="on"+e.substring(0,1).toUpperCase()+e.substring(1);return!(!1===r.trigger.apply(this,arguments)||s.isFunction(n[i])&&!1===n[i].apply(this,t)||s.isFunction(this[i])&&!1===this[i].apply(this,t)||!1===r.trigger.apply(r,[this,e].concat(t)))},request:e.noop}),e.create=n.create=function(e){return new n(e)},e.Uploader=n}),t("runtime/runtime",["base","mediator"],function(t,e){function i(e){for(var t in e)if(e.hasOwnProperty(t))return t;return null}var r=t.$,s={};function o(e){this.options=r.extend({container:document.body},e),this.uid=t.guid("rt_")}return r.extend(o.prototype,{getContainer:function(){var e,t,n=this.options;return this._container?this._container:(e=r(n.container||document.body),(t=r(document.createElement("div"))).attr("id","rt_"+this.uid),t.css({position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.append(t),e.addClass("webuploader-container"),this._container=t)},init:t.noop,exec:t.noop,destroy:function(){this._container&&this._container.parentNode.removeChild(this.__container),this.off()}}),o.orders="html5,flash",o.addRuntime=function(e,t){s[e]=t},o.hasRuntime=function(e){return!!(e?s[e]:i(s))},o.create=function(e,t){var n;if(t=t||o.orders,r.each(t.split(/\s*,\s*/g),function(){if(s[this])return n=this,!1}),!(n=n||i(s)))throw new Error("Runtime Error");return new s[n](e)},e.installTo(o.prototype),o}),t("runtime/client",["base","mediator","runtime/runtime"],function(s,e,o){var a,i;function t(t,n){var i,e,r=s.Deferred();this.uid=s.guid("client_"),this.runtimeReady=function(e){return r.done(e)},this.connectRuntime=function(e,t){if(i)throw new Error("already connected!");return r.done(t),"string"==typeof e&&a.get(e)&&(i=a.get(e)),(i=i||a.get(null,n))?(s.$.extend(i.options,e),i.__promise.then(r.resolve),i.__client++):((i=o.create(e,e.runtimeOrder)).__promise=r.promise(),i.once("ready",r.resolve),i.init(),a.add(i),i.__client=1),n&&(i.__standalone=n),i},this.getRuntime=function(){return i},this.disconnectRuntime=function(){i&&(i.__client--,i.__client<=0&&(a.remove(i),delete i.__promise,i.destroy()),i=null)},this.exec=function(){if(i){var e=s.slice(arguments);return t&&e.unshift(t),i.exec.apply(this,e)}},this.getRuid=function(){return i&&i.uid},this.destroy=(e=this.destroy,function(){e&&e.apply(this,arguments),this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()})}return i={},a={add:function(e){i[e.uid]=e},get:function(e,t){var n;if(e)return i[e];for(n in i)if(!t||!i[n].__standalone)return i[n];return null},remove:function(e){delete i[e.uid]}},e.installTo(t.prototype),t}),t("lib/dnd",["base","mediator","runtime/client"],function(e,t,n){var i=e.$;function r(e){(e=this.options=i.extend({},r.options,e)).container=i(e.container),e.container.length&&n.call(this,"DragAndDrop")}return r.options={accept:null,disableGlobalDnd:!1},e.inherits(n,{constructor:r,init:function(){var e=this;e.connectRuntime(e.options,function(){e.exec("init"),e.trigger("ready")})},destroy:function(){this.disconnectRuntime()}}),t.installTo(r.prototype),r}),t("widgets/widget",["base","uploader"],function(d,e){var p=d.$,t=e.prototype._init,h={},r=[];function s(e){this.owner=e,this.options=e.options}return p.extend(s.prototype,{init:d.noop,invoke:function(e,t){var n=this.responseMap;return n&&e in n&&n[e]in this&&p.isFunction(this[n[e]])?this[n[e]].apply(this,t):h},request:function(){return this.owner.request.apply(this.owner,arguments)}}),p.extend(e.prototype,{_init:function(){var n=this,i=n._widgets=[];return p.each(r,function(e,t){i.push(new t(n))}),t.apply(n,arguments)},request:function(e,t,n){var i,r,s,o=0,a=this._widgets,u=a.length,c=[],l=[];for(t=function(e){if(e){var t=e.length,n=p.type(e);return 1===e.nodeType&&t||"array"===n||"function"!==n&&"string"!==n&&(0===t||"number"==typeof t&&0<t&&t-1 in e)}}(t)?t:[t];o<u;o++)(i=a[o].invoke(e,t))!==h&&(d.isPromise(i)?l.push(i):c.push(i));return n||l.length?(r=d.when.apply(d,l))[s=r.pipe?"pipe":"then"](function(){var e=d.Deferred(),t=arguments;return setTimeout(function(){e.resolve.apply(e,t)},1),e.promise()})[s](n||d.noop):c[0]}}),e.register=s.register=function(e,t){var n,i={init:"init"};return 1===arguments.length?(t=e).responseMap=i:t.responseMap=p.extend(i,e),n=d.inherits(s,t),r.push(n),n},s}),t("widgets/filednd",["base","uploader","lib/dnd","widgets/widget"],function(s,e,o){var a=s.$;return e.options.dnd="",e.register({init:function(e){if(e.dnd&&"html5"===this.request("predict-runtime-type")){var t,n=this,i=s.Deferred(),r=a.extend({},{disableGlobalDnd:e.disableGlobalDnd,container:e.dnd,accept:e.accept});return(t=new o(r)).once("ready",i.resolve),t.on("drop",function(e){n.request("add-file",[e])}),t.on("accept",function(e){return n.owner.trigger("dndAccept",e)}),t.init(),i.promise()}}})}),t("lib/filepaste",["base","mediator","runtime/client"],function(e,t,n){var i=e.$;function r(e){(e=this.options=i.extend({},e)).container=i(e.container||document.body),n.call(this,"FilePaste")}return e.inherits(n,{constructor:r,init:function(){var e=this;e.connectRuntime(e.options,function(){e.exec("init"),e.trigger("ready")})},destroy:function(){this.exec("destroy"),this.disconnectRuntime(),this.off()}}),t.installTo(r.prototype),r}),t("widgets/filepaste",["base","uploader","lib/filepaste","widgets/widget"],function(s,e,o){var a=s.$;return e.register({init:function(e){if(e.paste&&"html5"===this.request("predict-runtime-type")){var t,n=this,i=s.Deferred(),r=a.extend({},{container:e.paste,accept:e.accept});return(t=new o(r)).once("ready",i.resolve),t.on("paste",function(e){n.owner.request("add-file",[e])}),t.init(),i.promise()}}})}),t("lib/blob",["base","runtime/client"],function(e,n){function t(e,t){this.source=t,this.ruid=e,n.call(this,"Blob"),this.uid=t.uid||this.uid,this.type=t.type||"",this.size=t.size||0,e&&this.connectRuntime(e)}return e.inherits(n,{constructor:t,slice:function(e,t){return this.exec("slice",e,t)},getSource:function(){return this.source}}),t}),t("lib/file",["base","lib/blob"],function(e,i){var r=1,s=/\.([^.]+)$/;return e.inherits(i,function(e,t){var n;i.apply(this,arguments),this.name=t.name||"untitled"+r++,!(n=s.exec(t.name)?RegExp.$1.toLowerCase():"")&&this.type&&(n=/\/(jpg|jpeg|png|gif|bmp)$/i.exec(this.type)?RegExp.$1.toLowerCase():"",this.name+="."+n),!this.type&&~"jpg,jpeg,png,gif,bmp".indexOf(n)&&(this.type="image/"+("jpg"===n?"jpeg":n)),this.ext=n,this.lastModifiedDate=t.lastModifiedDate||(new Date).toLocaleString()})}),t("lib/filepicker",["base","runtime/client","lib/file"],function(e,t,s){var o=e.$;function n(e){if((e=this.options=o.extend({},n.options,e)).container=o(e.id),!e.container.length)throw new Error("按钮指定错误");e.innerHTML=e.innerHTML||e.label||e.container.html()||"",e.button=o(e.button||document.createElement("div")),e.button.html(e.innerHTML),e.container.html(e.button),t.call(this,"FilePicker",!0)}return n.options={button:null,container:null,label:null,innerHTML:null,multiple:!0,accept:null,name:"file"},e.inherits(t,{constructor:n,init:function(){var n=this,i=n.options,r=i.button;r.addClass("webuploader-pick"),n.on("all",function(e){var t;switch(e){case"mouseenter":r.addClass("webuploader-pick-hover");break;case"mouseleave":r.removeClass("webuploader-pick-hover");break;case"change":t=n.exec("getFiles"),n.trigger("select",o.map(t,function(e){return(e=new s(n.getRuid(),e))._refer=i.container,e}),i.container)}}),n.connectRuntime(i,function(){n.refresh(),n.exec("init",i),n.trigger("ready")}),o(b).on("resize",function(){n.refresh()})},refresh:function(){var e=this.getRuntime().getContainer(),t=this.options.button,n=t.outerWidth?t.outerWidth():t.width(),i=t.outerHeight?t.outerHeight():t.height(),r=t.offset();n&&i&&e.css({bottom:"auto",right:"auto",width:n+"px",height:i+"px"}).offset(r)},enable:function(){this.options.button.removeClass("webuploader-pick-disable"),this.refresh()},disable:function(){var e=this.options.button;this.getRuntime().getContainer().css({top:"-99999px"}),e.addClass("webuploader-pick-disable")},destroy:function(){this.runtime&&(this.exec("destroy"),this.disconnectRuntime())}}),n}),t("widgets/filepicker",["base","uploader","lib/filepicker","widgets/widget"],function(a,e,u){var c=a.$;return c.extend(e.options,{pick:null,accept:null}),e.register({"add-btn":"addButton",refresh:"refresh",disable:"disable",enable:"enable"},{init:function(e){return this.pickers=[],e.pick&&this.addButton(e.pick)},refresh:function(){c.each(this.pickers,function(){this.refresh()})},addButton:function(e){var t,n,i,r=this,s=r.options,o=s.accept;if(e)return i=a.Deferred(),c.isPlainObject(e)||(e={id:e}),t=c.extend({},e,{accept:c.isPlainObject(o)?[o]:o,swf:s.swf,runtimeOrder:s.runtimeOrder}),(n=new u(t)).once("ready",i.resolve),n.on("select",function(e){r.owner.request("add-file",[e])}),n.init(),this.pickers.push(n),i.promise()},disable:function(){c.each(this.pickers,function(){this.disable()})},enable:function(){c.each(this.pickers,function(){this.enable()})}})}),t("file",["base","mediator"],function(e,t){var n=e.$,i="WU_FILE_",r=0,s=/\.([^.]+)$/,o={};function a(e){this.name=e.name||"Untitled",this.size=e.size||0,this.type=e.type||"application",this.lastModifiedDate=e.lastModifiedDate||+new Date,this.id=i+r++,this.ext=s.exec(this.name)?RegExp.$1:"",this.statusText="",o[this.id]=a.Status.INITED,this.source=e,this.loaded=0,this.on("error",function(e){this.setStatus(a.Status.ERROR,e)})}return n.extend(a.prototype,{setStatus:function(e,t){var n=o[this.id];void 0!==t&&(this.statusText=t),e!==n&&(o[this.id]=e,this.trigger("statuschange",e,n))},getStatus:function(){return o[this.id]},getSource:function(){return this.source},destory:function(){delete o[this.id]}}),t.installTo(a.prototype),a.Status={INITED:"inited",QUEUED:"queued",PROGRESS:"progress",ERROR:"error",COMPLETE:"complete",CANCELLED:"cancelled",INTERRUPT:"interrupt",INVALID:"invalid"},a}),t("queue",["base","mediator","file"],function(e,t,n){var s=e.$,r=n.Status;function i(){this.stats={numOfQueue:0,numOfSuccess:0,numOfCancel:0,numOfProgress:0,numOfUploadFailed:0,numOfInvalid:0},this._queue=[],this._map={}}return s.extend(i.prototype,{append:function(e){return this._queue.push(e),this._fileAdded(e),this},prepend:function(e){return this._queue.unshift(e),this._fileAdded(e),this},getFile:function(e){return"string"!=typeof e?e:this._map[e]},fetch:function(e){var t,n,i=this._queue.length;for(e=e||r.QUEUED,t=0;t<i;t++)if(e===(n=this._queue[t]).getStatus())return n;return null},sort:function(e){"function"==typeof e&&this._queue.sort(e)},getFiles:function(){for(var e,t=[].slice.call(arguments,0),n=[],i=0,r=this._queue.length;i<r;i++)e=this._queue[i],t.length&&!~s.inArray(e.getStatus(),t)||n.push(e);return n},_fileAdded:function(e){var n=this;this._map[e.id]||(this._map[e.id]=e).on("statuschange",function(e,t){n._onFileStatusChange(e,t)}),e.setStatus(r.QUEUED)},_onFileStatusChange:function(e,t){var n=this.stats;switch(t){case r.PROGRESS:n.numOfProgress--;break;case r.QUEUED:n.numOfQueue--;break;case r.ERROR:n.numOfUploadFailed--;break;case r.INVALID:n.numOfInvalid--}switch(e){case r.QUEUED:n.numOfQueue++;break;case r.PROGRESS:n.numOfProgress++;break;case r.ERROR:n.numOfUploadFailed++;break;case r.COMPLETE:n.numOfSuccess++;break;case r.CANCELLED:n.numOfCancel++;break;case r.INVALID:n.numOfInvalid++}}}),t.installTo(i.prototype),i}),t("widgets/queue",["base","uploader","queue","file","lib/file","runtime/client","widgets/widget"],function(c,e,l,t,n,d){var p=c.$,i=/\.\w+$/,s=t.Status;return e.register({"sort-files":"sortFiles","add-file":"addFiles","get-file":"getFile","fetch-file":"fetchFile","get-stats":"getStats","get-files":"getFiles","remove-file":"removeFile",retry:"retry",reset:"reset","accept-file":"acceptFile"},{init:function(e){var t,n,i,r,s,o,a,u=this;if(p.isPlainObject(e.accept)&&(e.accept=[e.accept]),e.accept){for(s=[],i=0,n=e.accept.length;i<n;i++)(r=e.accept[i].extensions)&&s.push(r);s.length&&(o="\\."+s.join(",").replace(/,/g,"$|\\.").replace(/\*/g,".*")+"$"),u.accept=new RegExp(o,"i")}if(u.queue=new l,u.stats=u.queue.stats,"html5"===this.request("predict-runtime-type"))return t=c.Deferred(),(a=new d("Placeholder")).connectRuntime({runtimeOrder:"html5"},function(){u._ruid=a.getRuid(),t.resolve()}),t.promise()},_wrapFile:function(e){if(!(e instanceof t)){if(!(e instanceof n)){if(!this._ruid)throw new Error("Can't add external files.");e=new n(this._ruid,e)}e=new t(e)}return e},acceptFile:function(e){return!(!e||e.size<6||this.accept&&i.exec(e.name)&&!this.accept.test(e.name))},_addFile:function(e){var t=this;if(e=t._wrapFile(e),t.owner.trigger("beforeFileQueued",e)){if(t.acceptFile(e))return t.queue.append(e),t.owner.trigger("fileQueued",e),e;t.owner.trigger("error","Q_TYPE_DENIED",e)}},getFile:function(e){return this.queue.getFile(e)},addFiles:function(e){var t=this;e.length||(e=[e]),e=p.map(e,function(e){return t._addFile(e)}),t.owner.trigger("filesQueued",e),t.options.auto&&t.request("start-upload")},getStats:function(){return this.stats},removeFile:function(e){(e=e.id?e:this.queue.getFile(e)).setStatus(s.CANCELLED),this.owner.trigger("fileDequeued",e)},getFiles:function(){return this.queue.getFiles.apply(this.queue,arguments)},fetchFile:function(){return this.queue.fetch.apply(this.queue,arguments)},retry:function(e,t){var n,i,r;if(e)return(e=e.id?e:this.queue.getFile(e)).setStatus(s.QUEUED),void(t||this.request("start-upload"));for(i=0,r=(n=this.queue.getFiles(s.ERROR)).length;i<r;i++)(e=n[i]).setStatus(s.QUEUED);this.request("start-upload")},sortFiles:function(){return this.queue.sort.apply(this.queue,arguments)},reset:function(){this.queue=new l,this.stats=this.queue.stats}})}),t("widgets/runtime",["uploader","runtime/runtime","widgets/widget"],function(e,r){return e.support=function(){return r.hasRuntime.apply(r,arguments)},e.register({"predict-runtime-type":"predictRuntmeType"},{init:function(){if(!this.predictRuntmeType())throw Error("Runtime Error")},predictRuntmeType:function(){var e,t,n=this.options.runtimeOrder||r.orders,i=this.type;if(!i)for(e=0,t=(n=n.split(/\s*,\s*/g)).length;e<t;e++)if(r.hasRuntime(n[e])){this.type=i=n[e];break}return i}})}),t("lib/transport",["base","runtime/client","mediator"],function(e,n,t){var i=e.$;function r(e){var t=this;e=t.options=i.extend(!0,{},r.options,e||{}),n.call(this,"Transport"),this._blob=null,this._formData=e.formData||{},this._headers=e.headers||{},this.on("progress",this._timeout),this.on("load error",function(){t.trigger("progress",1),clearTimeout(t._timer)})}return r.options={server:"",method:"POST",withCredentials:!1,fileVal:"file",timeout:12e4,formData:{},headers:{},sendAsBinary:!1},i.extend(r.prototype,{appendBlob:function(e,t,n){var i=this,r=i.options;i.getRuid()&&i.disconnectRuntime(),i.connectRuntime(t.ruid,function(){i.exec("init")}),i._blob=t,r.fileVal=e||r.fileVal,r.filename=n||r.filename},append:function(e,t){"object"==typeof e?i.extend(this._formData,e):this._formData[e]=t},setRequestHeader:function(e,t){"object"==typeof e?i.extend(this._headers,e):this._headers[e]=t},send:function(e){this.exec("send",e),this._timeout()},abort:function(){return clearTimeout(this._timer),this.exec("abort")},destroy:function(){this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()},getResponse:function(){return this.exec("getResponse")},getResponseAsJson:function(){return this.exec("getResponseAsJson")},getStatus:function(){return this.exec("getStatus")},_timeout:function(){var e=this,t=e.options.timeout;t&&(clearTimeout(e._timer),e._timer=setTimeout(function(){e.abort(),e.trigger("error","timeout")},t))}}),t.installTo(r.prototype),r}),t("widgets/upload",["base","uploader","file","lib/transport","widgets/widget"],function(l,e,t,d){var p=l.$,s=l.isPromise,h=t.Status;p.extend(e.options,{prepareNextFile:!1,chunked:!1,chunkSize:5242880,chunkRetry:2,threads:3,formData:null}),e.register({"start-upload":"start","stop-upload":"stop","skip-file":"skipFile","is-in-progress":"isInProgress"},{init:function(){var e=this.owner;this.runing=!1,this.pool=[],this.pending=[],this.remaning=0,this.__tick=l.bindFn(this._tick,this),e.on("uploadComplete",function(e){e.blocks&&p.each(e.blocks,function(e,t){t.transport&&(t.transport.abort(),t.transport.destroy()),delete t.transport}),delete e.blocks,delete e.remaning})},start:function(){var i=this;p.each(i.request("get-files",h.INVALID),function(){i.request("remove-file",this)}),i.runing||(i.runing=!0,p.each(i.pool,function(e,t){var n=t.file;n.getStatus()===h.INTERRUPT&&(n.setStatus(h.PROGRESS),i._trigged=!1,t.transport&&t.transport.send())}),i._trigged=!1,i.owner.trigger("startUpload"),l.nextTick(i.__tick))},stop:function(e){!1!==this.runing&&(this.runing=!1,e&&p.each(this.pool,function(e,t){t.transport&&t.transport.abort(),t.file.setStatus(h.INTERRUPT)}),this.owner.trigger("stopUpload"))},isInProgress:function(){return!!this.runing},getStats:function(){return this.request("get-stats")},skipFile:function(e,t){(e=this.request("get-file",e)).setStatus(t||h.COMPLETE),e.skipped=!0,e.blocks&&p.each(e.blocks,function(e,t){var n=t.transport;n&&(n.abort(),n.destroy(),delete t.transport)}),this.owner.trigger("uploadSkip",e)},_tick:function(){var e,t,n=this,i=n.options;if(n._promise)return n._promise.always(n.__tick);n.pool.length<i.threads&&(t=n._nextBlock())?(n._trigged=!1,e=function(e){n._promise=null,e&&e.file&&n._startSend(e),l.nextTick(n.__tick)},n._promise=s(t)?t.always(e):e(t)):n.remaning||n.getStats().numOfQueue||(n.runing=!1,n._trigged||l.nextTick(function(){n.owner.trigger("uploadFinished")}),n._trigged=!0)},_nextBlock:function(){var e,t,n=this,i=n._act,r=n.options;return i&&i.has()&&i.file.getStatus()===h.PROGRESS?(r.prepareNextFile&&!n.pending.length&&n._prepareNextFile(),i.fetch()):n.runing?(!n.pending.length&&n.getStats().numOfQueue&&n._prepareNextFile(),e=n.pending.shift(),t=function(e){return e?(i=function(e,t){for(var n,i=[],r=e.source.size,s=t?Math.ceil(r/t):1,o=0,a=0;a<s;)n=Math.min(t,r-o),i.push({file:e,start:o,end:t?o+n:r,total:r,chunks:s,chunk:a++}),o+=n;return e.blocks=i.concat(),e.remaning=i.length,{file:e,has:function(){return!!i.length},fetch:function(){return i.shift()}}}(e,r.chunked?r.chunkSize:0),(n._act=i).fetch()):null},s(e)?e[e.pipe?"pipe":"then"](t):t(e)):void 0},_prepareNextFile:function(){var t,n=this,i=n.request("fetch-file"),r=n.pending;i&&((t=n.request("before-send-file",i,function(){return i.getStatus()===h.QUEUED?(n.owner.trigger("uploadStart",i),i.setStatus(h.PROGRESS),i):n._finishFile(i)})).done(function(){var e=p.inArray(t,r);~e&&r.splice(e,1,i)}),t.fail(function(e){i.setStatus(h.ERROR,e),n.owner.trigger("uploadError",i,e),n.owner.trigger("uploadComplete",i)}),r.push(t))},_popBlock:function(e){var t=p.inArray(e,this.pool);this.pool.splice(t,1),e.file.remaning--,this.remaning--},_startSend:function(e){var t=this,n=e.file;t.pool.push(e),t.remaning++,e.blob=1===e.chunks?n.source:n.source.slice(e.start,e.end),t.request("before-send",e,function(){n.getStatus()===h.PROGRESS?t._doSend(e):(t._popBlock(e),l.nextTick(t.__tick))}).fail(function(){1===n.remaning?t._finishFile(n).always(function(){e.percentage=1,t._popBlock(e),t.owner.trigger("uploadComplete",n),l.nextTick(t.__tick)}):(e.percentage=1,t._popBlock(e),l.nextTick(t.__tick))})},_doSend:function(i){var n,r,t=this,s=t.owner,o=t.options,a=i.file,u=new d(o),e=p.extend({},o.formData),c=p.extend({},o.headers);(i.transport=u).on("destroy",function(){delete i.transport,t._popBlock(i),l.nextTick(t.__tick)}),u.on("progress",function(e){var t=0,n=0;t=i.percentage=e,1<i.chunks&&(p.each(a.blocks,function(e,t){n+=(t.percentage||0)*(t.end-t.start)}),t=n/a.size),s.trigger("uploadProgress",a,t||0)}),n=function(t){var e;return(r=u.getResponseAsJson()||{})._raw=u.getResponse(),e=function(e){t=e},s.trigger("uploadAccept",i,r,e)||(t=t||"server"),t},u.on("error",function(e,t){i.retried=i.retried||0,1<i.chunks&&~"http,abort".indexOf(e)&&i.retried<o.chunkRetry?(i.retried++,u.send()):(t||"server"!==e||(e=n(e)),a.setStatus(h.ERROR,e),s.trigger("uploadError",a,e),s.trigger("uploadComplete",a))}),u.on("load",function(){var e;(e=n())?u.trigger("error",e,!0):1===a.remaning?t._finishFile(a,r):u.destroy()}),e=p.extend(e,{id:a.id,name:a.name,type:a.type,lastModifiedDate:a.lastModifiedDate,size:a.size}),1<i.chunks&&p.extend(e,{chunks:i.chunks,chunk:i.chunk}),s.trigger("uploadBeforeSend",i,e,c),u.appendBlob(o.fileVal,i.blob,a.name),u.append(e),u.setRequestHeader(c),u.send()},_finishFile:function(t,e,n){var i=this.owner;return i.request("after-send-file",arguments,function(){t.setStatus(h.COMPLETE),i.trigger("uploadSuccess",t,e,n)}).fail(function(e){t.getStatus()===h.PROGRESS&&t.setStatus(h.ERROR,e),i.trigger("uploadError",t,e)}).always(function(){i.trigger("uploadComplete",t)})}})}),t("widgets/validator",["base","uploader","file","widgets/widget"],function(e,t,n){var i,r=e.$,s={};return i={addValidator:function(e,t){s[e]=t},removeValidator:function(e){delete s[e]}},t.register({init:function(){var e=this;r.each(s,function(){this.call(e.owner)})}}),i.addValidator("fileNumLimit",function(){var e=this,t=e.options,n=0,i=t.fileNumLimit>>0,r=!0;i&&(e.on("beforeFileQueued",function(e){return i<=n&&r&&(r=!1,this.trigger("error","Q_EXCEED_NUM_LIMIT",i,e),setTimeout(function(){r=!0},1)),!(i<=n)}),e.on("fileQueued",function(){n++}),e.on("fileDequeued",function(){n--}),e.on("uploadFinished",function(){n=0}))}),i.addValidator("fileSizeLimit",function(){var e=this,t=e.options,n=0,i=t.fileSizeLimit>>0,r=!0;i&&(e.on("beforeFileQueued",function(e){var t=n+e.size>i;return t&&r&&(r=!1,this.trigger("error","Q_EXCEED_SIZE_LIMIT",i,e),setTimeout(function(){r=!0},1)),!t}),e.on("fileQueued",function(e){n+=e.size}),e.on("fileDequeued",function(e){n-=e.size}),e.on("uploadFinished",function(){n=0}))}),i.addValidator("fileSingleSizeLimit",function(){var t=this.options.fileSingleSizeLimit;t&&this.on("beforeFileQueued",function(e){if(e.size>t)return e.setStatus(n.Status.INVALID,"exceed_size"),this.trigger("error","F_EXCEED_SIZE",e),!1})}),i.addValidator("duplicate",function(){var e=this.options,n={};e.duplicate||(this.on("beforeFileQueued",function(e){var t=e.__hash||(e.__hash=function(e){for(var t=0,n=0,i=e.length;n<i;n++)t=e.charCodeAt(n)+(t<<6)+(t<<16)-t;return t}(e.name+e.size+e.lastModifiedDate));if(n[t])return this.trigger("error","F_DUPLICATE",e),!1}),this.on("fileQueued",function(e){var t=e.__hash;t&&(n[t]=!0)}),this.on("fileDequeued",function(e){var t=e.__hash;t&&delete n[t]}))}),i}),t("runtime/compbase",[],function(){return function(e,t){this.owner=e,this.options=e.options,this.getRuntime=function(){return t},this.getRuid=function(){return t.uid},this.trigger=function(){return e.trigger.apply(e,arguments)}}}),t("runtime/html5/runtime",["base","runtime/runtime","runtime/compbase"],function(a,t,n){var u={};function e(){var s={},o=this,e=this.destory;t.apply(o,arguments),o.type="html5",o.exec=function(e,t){var n,i=this.uid,r=a.slice(arguments,2);if(u[e]&&(n=s[i]=s[i]||new u[e](this,o))[t])return n[t].apply(n,r)},o.destory=function(){return e&&e.apply(this,arguments)}}return a.inherits(t,{constructor:e,init:function(){var e=this;setTimeout(function(){e.trigger("ready")},1)}}),e.register=function(e,t){return u[e]=a.inherits(n,t)},b.Blob&&b.FileReader&&b.DataView&&t.addRuntime("html5",e),e}),t("runtime/html5/blob",["runtime/html5/runtime","lib/blob"],function(e,i){return e.register("Blob",{slice:function(e,t){var n=this.owner.source;return n=(n.slice||n.webkitSlice||n.mozSlice).call(n,e,t),new i(this.getRuid(),n)}})}),t("runtime/html5/dnd",["base","runtime/html5/runtime","lib/file"],function(p,e,r){var s=p.$,o="webuploader-dnd-";return e.register("DragAndDrop",{init:function(){var e=this.elem=this.options.container;this.dragEnterHandler=p.bindFn(this._dragEnterHandler,this),this.dragOverHandler=p.bindFn(this._dragOverHandler,this),this.dragLeaveHandler=p.bindFn(this._dragLeaveHandler,this),this.dropHandler=p.bindFn(this._dropHandler,this),this.dndOver=!1,e.on("dragenter",this.dragEnterHandler),e.on("dragover",this.dragOverHandler),e.on("dragleave",this.dragLeaveHandler),e.on("drop",this.dropHandler),this.options.disableGlobalDnd&&(s(document).on("dragover",this.dragOverHandler),s(document).on("drop",this.dropHandler))},_dragEnterHandler:function(e){var t,n=this,i=n._denied||!1;return e=e.originalEvent||e,n.dndOver||(n.dndOver=!0,(t=e.dataTransfer.items)&&t.length&&(n._denied=i=!n.trigger("accept",t)),n.elem.addClass(o+"over"),n.elem[i?"addClass":"removeClass"](o+"denied")),e.dataTransfer.dropEffect=i?"none":"copy",!1},_dragOverHandler:function(e){var t=this.elem.parent().get(0);return t&&!s.contains(t,e.currentTarget)||(clearTimeout(this._leaveTimer),this._dragEnterHandler.call(this,e)),!1},_dragLeaveHandler:function(){var e,t=this;return e=function(){t.dndOver=!1,t.elem.removeClass(o+"over "+o+"denied")},clearTimeout(t._leaveTimer),t._leaveTimer=setTimeout(e,100),!1},_dropHandler:function(e){var t=this,n=t.getRuid(),i=t.elem.parent().get(0);return i&&!s.contains(i,e.currentTarget)||(t._getTansferFiles(e,function(e){t.trigger("drop",s.map(e,function(e){return new r(n,e)}))}),t.dndOver=!1,t.elem.removeClass(o+"over")),!1},_getTansferFiles:function(e,t){var n,i,r,s,o,a,u,c,l=[],d=[];for(n=(r=(e=e.originalEvent||e).dataTransfer).items,i=r.files,c=!(!n||!n[0].webkitGetAsEntry),a=0,u=i.length;a<u;a++)s=i[a],o=n&&n[a],c&&o.webkitGetAsEntry().isDirectory?d.push(this._traverseDirectoryTree(o.webkitGetAsEntry(),l)):l.push(s);p.when.apply(p,d).done(function(){l.length&&t(l)})},_traverseDirectoryTree:function(e,s){var o=p.Deferred(),a=this;return e.isFile?e.file(function(e){s.push(e),o.resolve()}):e.isDirectory&&e.createReader().readEntries(function(e){var t,n=e.length,i=[],r=[];for(t=0;t<n;t++)i.push(a._traverseDirectoryTree(e[t],r));p.when.apply(p,i).then(function(){s.push.apply(s,r),o.resolve()},o.reject)}),o.promise()},destroy:function(){var e=this.elem;e.off("dragenter",this.dragEnterHandler),e.off("dragover",this.dragEnterHandler),e.off("dragleave",this.dragLeaveHandler),e.off("drop",this.dropHandler),this.options.disableGlobalDnd&&(s(document).off("dragover",this.dragOverHandler),s(document).off("drop",this.dropHandler))}})}),t("runtime/html5/filepaste",["base","runtime/html5/runtime","lib/file"],function(a,e,u){return e.register("FilePaste",{init:function(){var e,t,n,i,r=this.options,s=this.elem=r.container,o=".*";if(r.accept){for(e=[],t=0,n=r.accept.length;t<n;t++)(i=r.accept[t].mimeTypes)&&e.push(i);e.length&&(o=(o=e.join(",")).replace(/,/g,"|").replace(/\*/g,".*"))}this.accept=o=new RegExp(o,"i"),this.hander=a.bindFn(this._pasteHander,this),s.on("paste",this.hander)},_pasteHander:function(e){var t,n,i,r,s,o=[],a=this.getRuid();for(r=0,s=(t=(e=e.originalEvent||e).clipboardData.items).length;r<s;r++)"file"===(n=t[r]).kind&&(i=n.getAsFile())&&o.push(new u(a,i));o.length&&(e.preventDefault(),e.stopPropagation(),this.trigger("paste",o))},destroy:function(){this.elem.off("paste",this.hander)}})}),t("runtime/html5/filepicker",["base","runtime/html5/runtime"],function(e,t){var l=e.$;return t.register("FilePicker",{init:function(){var e,t,n,i,r=this.getRuntime().getContainer(),s=this,o=s.owner,a=s.options,u=l(document.createElement("label")),c=l(document.createElement("input"));if(c.attr("type","file"),c.attr("name",a.name),c.addClass("webuploader-element-invisible"),u.on("click",function(){c.trigger("click")}),u.css({opacity:0,width:"100%",height:"100%",display:"block",cursor:"pointer",background:"#ffffff"}),a.multiple&&c.attr("multiple","multiple"),a.accept&&0<a.accept.length){for(e=[],t=0,n=a.accept.length;t<n;t++)e.push(a.accept[t].mimeTypes);c.attr("accept",e.join(","))}r.append(c),r.append(u),i=function(e){o.trigger(e.type)},c.on("change",function(e){var t,n=arguments.callee;s.files=e.target.files,t=this.cloneNode(!0),this.parentNode.replaceChild(t,this),c.off(),c=l(t).on("change",n).on("mouseenter mouseleave",i),o.trigger("change")}),u.on("mouseenter mouseleave",i)},getFiles:function(){return this.files},destroy:function(){}})}),t("runtime/html5/transport",["base","runtime/html5/runtime"],function(u,e){var t=u.noop,c=u.$;return e.register("Transport",{init:function(){this._status=0,this._response=null},send:function(){var n,e,t,i=this.owner,r=this.options,s=this._initAjax(),o=i._blob,a=r.server;r.sendAsBinary?(a+=(/\?/.test(a)?"&":"?")+c.param(i._formData),e=o.getSource()):(n=new FormData,c.each(i._formData,function(e,t){n.append(e,t)}),n.append(r.fileVal,o.getSource(),r.filename||i._formData.name||"")),r.withCredentials&&"withCredentials"in s?(s.open(r.method,a,!0),s.withCredentials=!0):s.open(r.method,a),this._setRequestHeader(s,r.headers),e?(s.overrideMimeType("application/octet-stream"),u.os.android?((t=new FileReader).onload=function(){s.send(this.result),t=t.onload=null},t.readAsArrayBuffer(e)):s.send(e)):s.send(n)},getResponse:function(){return this._response},getResponseAsJson:function(){return this._parseJson(this._response)},getStatus:function(){return this._status},abort:function(){var e=this._xhr;e&&(e.upload.onprogress=t,e.onreadystatechange=t,e.abort(),this._xhr=e=null)},destroy:function(){this.abort()},_initAjax:function(){var n=this,e=new XMLHttpRequest;return!this.options.withCredentials||"withCredentials"in e||"undefined"==typeof XDomainRequest||(e=new XDomainRequest),e.upload.onprogress=function(e){var t=0;return e.lengthComputable&&(t=e.loaded/e.total),n.trigger("progress",t)},e.onreadystatechange=function(){if(4===e.readyState)return e.upload.onprogress=t,e.onreadystatechange=t,n._xhr=null,n._status=e.status,200<=e.status&&e.status<300?(n._response=e.responseText,n.trigger("load")):500<=e.status&&e.status<600?(n._response=e.responseText,n.trigger("error","server")):n.trigger("error",n._status?"http":"abort")},n._xhr=e},_setRequestHeader:function(n,e){c.each(e,function(e,t){n.setRequestHeader(e,t)})},_parseJson:function(e){var t;try{t=JSON.parse(e)}catch(e){t={}}return t}})}),t("runtime/flash/runtime",["base","runtime/runtime","runtime/compbase"],function(u,n,i){var r=u.$,c={};function e(){var s={},o={},e=this.destory,a=this,t=u.guid("webuploader_");n.apply(a,arguments),a.type="flash",a.exec=function(e,t){var n,i=this.uid,r=u.slice(arguments,2);return o[i]=this,c[e]&&(s[i]||(s[i]=new c[e](this,a)),(n=s[i])[t])?n[t].apply(n,r):a.flashExec.apply(this,arguments)},b[t]=function(){var e=arguments;setTimeout(function(){(function(e,t){var n,i,r=e.type||e;i=(n=r.split("::"))[0],"Ready"===(r=n[1])&&i===a.uid?a.trigger("ready"):o[i]&&o[i].trigger(r.toLowerCase(),e,t)}).apply(null,e)},1)},this.jsreciver=t,this.destory=function(){return e&&e.apply(this,arguments)},this.flashExec=function(e,t){var n=a.getFlash(),i=u.slice(arguments,2);return n.exec(this.uid,e,t,i)}}return u.inherits(n,{constructor:e,init:function(){var e,t=this.getContainer(),n=this.options;t.css({position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),e='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+n.swf+'" ',u.browser.ie&&(e+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),e+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+n.swf+'" /><param name="flashvars" value="uid='+this.uid+"&jsreciver="+this.jsreciver+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>',t.html(e)},getFlash:function(){return this._flash||(this._flash=r("#"+this.uid).get(0)),this._flash}}),e.register=function(e,t){return t=c[e]=u.inherits(i,r.extend({flashExec:function(){var e=this.owner;return this.getRuntime().flashExec.apply(e,arguments)}},t))},11.4<=function(){var t;try{t=(t=navigator.plugins["Shockwave Flash"]).description}catch(e){try{t=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(e){t="0.0"}}return t=t.match(/\d+/g),parseFloat(t[0]+"."+t[1],10)}()&&n.addRuntime("flash",e),e}),t("runtime/flash/filepicker",["base","runtime/flash/runtime"],function(e,t){var r=e.$;return t.register("FilePicker",{init:function(e){var t,n,i=r.extend({},e);for(t=i.accept&&i.accept.length,n=0;n<t;n++)i.accept[n].title||(i.accept[n].title="Files");delete i.button,delete i.container,this.flashExec("FilePicker","init",i)},destroy:function(){}})}),t("runtime/flash/transport",["base","runtime/flash/runtime","runtime/client"],function(e,t,r){var o=e.$;return t.register("Transport",{init:function(){this._status=0,this._response=null,this._responseJson=null},send:function(){var e,t=this.owner,n=this.options,i=this._initAjax(),r=t._blob,s=n.server;i.connectRuntime(r.ruid),n.sendAsBinary?(s+=(/\?/.test(s)?"&":"?")+o.param(t._formData),e=r.uid):(o.each(t._formData,function(e,t){i.exec("append",e,t)}),i.exec("appendBlob",n.fileVal,r.uid,n.filename||t._formData.name||"")),this._setRequestHeader(i,n.headers),i.exec("send",{method:n.method,url:s},e)},getStatus:function(){return this._status},getResponse:function(){return this._response},getResponseAsJson:function(){return this._responseJson},abort:function(){var e=this._xhr;e&&(e.exec("abort"),e.destroy(),this._xhr=e=null)},destroy:function(){this.abort()},_initAjax:function(){var n=this,i=new r("XMLHttpRequest");return i.on("uploadprogress progress",function(e){return n.trigger("progress",e.loaded/e.total)}),i.on("load",function(){var e=i.exec("getStatus"),t="";return i.off(),n._xhr=null,200<=e&&e<300?(n._response=i.exec("getResponse"),n._responseJson=i.exec("getResponseAsJson")):t=500<=e&&e<600?(n._response=i.exec("getResponse"),n._responseJson=i.exec("getResponseAsJson"),"server"):"http",i.destroy(),i=null,t?n.trigger("error",t):n.trigger("load")}),i.on("error",function(){i.off(),n._xhr=null,n.trigger("error","http")}),n._xhr=i},_setRequestHeader:function(n,e){o.each(e,function(e,t){n.exec("setRequestHeader",e,t)})}})}),t("preset/withoutimage",["base","widgets/filednd","widgets/filepaste","widgets/filepicker","widgets/queue","widgets/runtime","widgets/upload","widgets/validator","runtime/html5/blob","runtime/html5/dnd","runtime/html5/filepaste","runtime/html5/filepicker","runtime/html5/transport","runtime/flash/filepicker","runtime/flash/transport"],function(e){return e}),t("webuploader",["preset/withoutimage"],function(e){return e}),i("webuploader"));!function(e){var t,n,i,r,s,o;for(t in o=function(e){return e&&e.charAt(0).toUpperCase()+e.substr(1)},a)if(n=e,a.hasOwnProperty(t)){for(s=o((i=t.split("/")).pop());r=o(i.shift());)n[r]=n[r]||{},n=n[r];n[s]=a[t]}}(u),"object"==typeof module&&"object"==typeof module.exports?module.exports=u:"function"==typeof define&&define.amd?define([],u):(e=n.WebUploader,n.WebUploader=u,n.WebUploader.noConflict=function(){n.WebUploader=e})}(this);