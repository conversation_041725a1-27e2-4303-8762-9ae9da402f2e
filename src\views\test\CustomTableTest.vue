<template>
  <div class="dx-table-test">
    <h1>DxTable 组件测试</h1>

    <div class="test-section">
      <h2>基础表格</h2>
      <dx-table :data="tableData" style="width: 100%">
        <dx-table-column prop="date" label="日期" width="180"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="180"></dx-table-column>
        <dx-table-column prop="address" label="地址"></dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>带操作列的表格</h2>
      <dx-table :data="tableData" border style="width: 100%">
        <dx-table-column prop="date" label="日期" width="180"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="180"></dx-table-column>
        <dx-table-column prop="address" label="地址"></dx-table-column>
        <dx-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button @click="handleEdit(scope.row)" type="primary" size="small">编辑</el-button>
            <el-button @click="handleDelete(scope.row)" type="danger" size="small">删除</el-button>
          </template>
        </dx-table-column>
      </dx-table>
    </div>

    <div class="test-section">
      <h2>多选表格</h2>
      <dx-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
        <dx-table-column type="selection" width="55"></dx-table-column>
        <dx-table-column prop="date" label="日期" width="120"></dx-table-column>
        <dx-table-column prop="name" label="姓名" width="120"></dx-table-column>
        <dx-table-column prop="address" label="地址" show-overflow-tooltip></dx-table-column>
      </dx-table>
      <div style="margin-top: 20px">
        <el-button @click="toggleSelection([tableData[1], tableData[2]])">切换第二、第三行的选中状态</el-button>
        <el-button @click="toggleSelection()">取消选择</el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>排序表格</h2>
      <dx-table :data="tableData" style="width: 100%" :default-sort="{ prop: 'date', order: 'descending' }">
        <dx-table-column prop="date" label="日期" sortable width="180"></dx-table-column>
        <dx-table-column prop="name" label="姓名" sortable width="180"></dx-table-column>
        <dx-table-column prop="address" label="地址"></dx-table-column>
      </dx-table>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'DxTableTest',
    data() {
      return {
        tableData: [
          {
            date: '2016-05-02',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1518 弄'
          },
          {
            date: '2016-05-04',
            name: '张小刚',
            address: '上海市普陀区金沙江路 1517 弄'
          },
          {
            date: '2016-05-01',
            name: '李小红',
            address: '上海市普陀区金沙江路 1519 弄'
          },
          {
            date: '2016-05-03',
            name: '周小伟',
            address: '上海市普陀区金沙江路 1516 弄'
          }
        ],
        multipleSelection: []
      };
    },
    methods: {
      handleEdit(row) {
        console.log('编辑:', row);
        this.$message.success(`编辑 ${row.name}`);
      },
      handleDelete(row) {
        console.log('删除:', row);
        this.$message.warning(`删除 ${row.name}`);
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
        console.log('选择变化:', val);
      },
      toggleSelection(rows) {
        if (rows) {
          rows.forEach((row) => {
            this.$refs.multipleTable.toggleRowSelection(row);
          });
        } else {
          this.$refs.multipleTable.clearSelection();
        }
      }
    }
  };
</script>

<style scoped>
  .dx-table-test {
    padding: 20px;
  }

  .test-section {
    margin-bottom: 40px;
  }

  .test-section h2 {
    margin-bottom: 20px;
    color: #303133;
    font-size: 18px;
  }
</style>
