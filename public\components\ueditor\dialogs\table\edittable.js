!function(){function e(){(r=this).init()}var r,l=$G("J_title"),d=$G("J_titleCol"),i=$G("J_caption"),a=$G("J_sorttable"),c=$G("J_autoSizeContent"),m=$G("J_autoSizePage"),s=$G("J_tone"),u=$G("J_preview");e.prototype={init:function(){var e=new UE.ui.ColorPicker({editor:editor}),t=new UE.ui.Popup({editor:editor,content:e});l.checked=-1==editor.queryCommandState("inserttitle"),d.checked=-1==editor.queryCommandState("inserttitlecol"),i.checked=-1==editor.queryCommandState("insertcaption"),a.checked=1==editor.queryCommandState("enablesort");var o=editor.queryCommandState("enablesort"),n=editor.queryCommandState("disablesort");a.checked=!!(o<0&&0<=n),a.disabled=!!(o<0&&n<0),a.title=o<0&&n<0?lang.errorMsg:"",r.createTable(l.checked,d.checked,i.checked),r.setAutoSize(),r.setColor(r.getColor()),domUtils.on(l,"click",r.titleHanler),domUtils.on(d,"click",r.titleColHanler),domUtils.on(i,"click",r.captionHanler),domUtils.on(a,"click",r.sorttableHanler),domUtils.on(c,"click",r.autoSizeContentHanler),domUtils.on(m,"click",r.autoSizePageHanler),domUtils.on(s,"click",function(){t.showAnchor(s)}),domUtils.on(document,"mousedown",function(){t.hide()}),e.addListener("pickcolor",function(){r.setColor(arguments[1]),t.hide()}),e.addListener("picknocolor",function(){r.setColor(""),t.hide()})},createTable:function(e,t,o){var n=[];if(n.push("<table id='J_example'>"),o&&n.push("<caption>"+lang.captionName+"</caption>"),e){n.push("<tr>"),t&&n.push("<th>"+lang.titleName+"</th>");for(var i=0;i<5;i++)n.push("<th>"+lang.titleName+"</th>");n.push("</tr>")}for(var a=0;a<6;a++){n.push("<tr>"),t&&n.push("<th>"+lang.titleName+"</th>");for(var r=0;r<5;r++)n.push("<td>"+lang.cellsName+"</td>");n.push("</tr>")}n.push("</table>"),u.innerHTML=n.join(""),this.updateSortSpan()},titleHanler:function(){var e=$G("J_example"),t=document.createDocumentFragment(),o=domUtils.getComputedStyle(domUtils.getElementsByTagName(e,"td")[0],"border-color"),n=e.rows[0].children.length;if(l.checked){e.insertRow(0);for(var i,a=0;a<n;a++)(i=document.createElement("th")).innerHTML=lang.titleName,t.appendChild(i);e.rows[0].appendChild(t)}else domUtils.remove(e.rows[0]);r.setColor(o),r.updateSortSpan()},titleColHanler:function(){var e=$G("J_example"),t=domUtils.getComputedStyle(domUtils.getElementsByTagName(e,"td")[0],"border-color"),o=e.rows,n=o.length;if(d.checked)for(var i,a=0;a<n;a++)(i=document.createElement("th")).innerHTML=lang.titleName,o[a].insertBefore(i,o[a].children[0]);else for(a=0;a<n;a++)domUtils.remove(o[a].children[0]);r.setColor(t),r.updateSortSpan()},captionHanler:function(){var e=$G("J_example");if(i.checked){var t=document.createElement("caption");t.innerHTML=lang.captionName,e.insertBefore(t,e.firstChild)}else domUtils.remove(domUtils.getElementsByTagName(e,"caption")[0])},sorttableHanler:function(){r.updateSortSpan()},autoSizeContentHanler:function(){$G("J_example").removeAttribute("width")},autoSizePageHanler:function(){var e=$G("J_example"),t=e.getElementsByTagName(e,"td");utils.each(t,function(e){e.removeAttribute("width")}),e.setAttribute("width","100%")},updateSortSpan:function(){var e=$G("J_example"),t=e.rows[0],o=domUtils.getElementsByTagName(e,"span");utils.each(o,function(e){e.parentNode.removeChild(e)}),a.checked&&utils.each(t.cells,function(e,t){var o=document.createElement("span");o.innerHTML="^",e.appendChild(o)})},getColor:function(){var e=editor.selection.getStart(),t=domUtils.findParentByTagName(e,["td","th","caption"],!0);return t&&domUtils.getComputedStyle(t,"border-color")||"#DDDDDD"},setColor:function(t){var e=$G("J_example"),o=domUtils.getElementsByTagName(e,"td").concat(domUtils.getElementsByTagName(e,"th"),domUtils.getElementsByTagName(e,"caption"));s.value=t,utils.each(o,function(e){e.style.borderColor=t})},setAutoSize:function(){m.checked=!0,this.autoSizePageHanler()}},new e,dialog.onok=function(){editor.__hasEnterExecCommand=!0;var e={title:"inserttitle deletetitle",titleCol:"inserttitlecol deletetitlecol",caption:"insertcaption deletecaption",sorttable:"enablesort disablesort"};for(var t in editor.fireEvent("saveScene"),e){var o=e[t].split(" ");$G("J_"+t).checked?-1!=editor.queryCommandState(o[0])&&editor.execCommand(o[0]):-1!=editor.queryCommandState(o[1])&&editor.execCommand(o[1])}editor.execCommand("edittable",s.value),c.checked&&editor.execCommand("adaptbytext"),m.checked&&editor.execCommand("adaptbywindow"),editor.fireEvent("saveScene"),editor.__hasEnterExecCommand=!1}}();