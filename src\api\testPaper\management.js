// 数学相关接口
import request from '@/utils/request';

export default {
  // 课程管理--课程大类    ---  公共接口
  getKcdlForKnowledge(data) {
    return request({
      url: 'znyy/curriculum/math',
      method: 'GET',
      params: data
    });
  },
  // 课程管理--树    ---  公共接口
  selectTree(data) {
    return request({
      url: '/dsx/math/web/coursePeriodConfig/selectTree',
      method: 'GET',
      params: data
    });
  },
  // 课程管理--树 --新增/编辑章节/知识小点    ---  公共接口
  insertOrUpdateTree(data) {
    return request({
      url: '/dsx/math/web/coursePeriodConfig/insertOrUpdate',
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },
  // 课程管理--树 --删除章节/知识小点    ---  公共接口
  deleteAboutTree(data) {
    return request({
      url: '/dsx/math/web/coursePeriodConfig/delete',
      method: 'DELETE',
      params: data
    });
  },
  // 课程管理--知识点管理--查询分页
  searchForKnowledge(data) {
    return request({
      url: '/dsx/math/web/courseKnowledge/list',
      method: 'GET',
      params: data
    });
  },
  // 课程管理--知识点管理--保存修改知识点信息

  changeForKnowledge(data) {
    return request({
      url: '/dsx/math/web/courseKnowledge/saveOrUpdateKnowledge',
      method: 'POST',
      data
    });
  },
  // 课程管理--知识点管理--知识点删除
  deleteForKnowledge(data) {
    return request({
      url: '/dsx/math/web/courseKnowledge/delete-knowledge',
      method: 'DELETE',
      params: data
    });
  },
  // 课程管理--知识点管理--导入知识点数据
  importForKnowledge(data) {
    return request({
      url: '/dsx/math/web/courseKnowledge/importFile',
      method: 'POST',
      data
    });
  },
  // 学生管理--查询
  searchForStudent(data) {
    return request({
      url: '/dsx/math/web/courseStudentManage/list',
      method: 'GET',
      params: data
    });
  },
  // 学生管理--成绩详情
  searchForDetail(data) {
    return request({
      url: '/dsx/math/web/courseStudentManage/detail',
      method: 'GET',
      params: data
    });
  },
  // 班型题目配置--查询
  searchForClass(data) {
    return request({
      url: '/dsx/math/web/classTypeTopicConfig/selectConfig',
      method: 'GET',
      params: data
    });
  },
  // 班型题目配置--新增/编辑
  updateForClass(data) {
    return request({
      url: '/dsx/math/web/classTypeTopicConfig/insertOrUpdate',
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },
  // 课程分类配置--查询  课程分类列表
  searchForCourseList(data) {
    return request({
      url: '/dsx/math/web/coursePeriodConfig/listPage',
      method: 'GET',
      params: data
    });
  },
  // 课程分类配置--查询  关联版本列表
  searchForVersionListPage(data) {
    return request({
      url: '/dsx/math/web/basisVersion/versionListPage',
      method: 'GET',
      params: data
    });
  },
  // 课程分类配置--查询版本列表
  selectVersionInfo(data) {
    return request({
      url: '/dsx/math/web/basisConfig/selectVersionInfo',
      method: 'GET',
      params: data
    });
  },
  // 课程分类配置--查询学科列表
  selectSubjectInfo(data) {
    return request({
      url: '/dsx/math/web/basisConfig/selectSubjectInfo',
      method: 'GET',
      params: data
    });
  },
  // 课程分类配置--新增学科
  insertSubject(data) {
    return request({
      url: '/dsx/math/web/coursePeriodConfig/insertSubject',
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },
  // 课程分类配置--更新版本排序
  updateVersionSort(data) {
    return request({
      url: '/dsx/math/web/basisVersion/updateVersionSort',
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },
  // 课程分类配置--删除课程分类
  deleteCourse(data) {
    return request({
      url: '/dsx/math/web/coursePeriodConfig/delete',
      method: 'DELETE',
      params: data
    });
  },
  // 课程分类配置--删除关联版本
  deleteVersion(data) {
    return request({
      url: '/dsx/math/web/basisVersion/deleteVersion',
      method: 'DELETE',
      params: data
    });
  },
  // 课程分类配置--更新课程分类排序
  updateSort(data) {
    return request({
      url: '/dsx/math/web/coursePeriodConfig/updateSort',
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
