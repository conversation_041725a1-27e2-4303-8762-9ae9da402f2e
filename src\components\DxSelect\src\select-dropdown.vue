<template>
  <div class="dx-select-dropdown el-popper" :class="[{ 'is-multiple': $parent.multiple }, popperClass]" :style="{ minWidth: minWidth }">
    <slot></slot>
  </div>
</template>

<script type="text/babel">
  import Popper from 'element-ui/src/utils/vue-popper';

  export default {
    name: 'DxSelectDropdown',

    componentName: 'DxSelectDropdown',

    mixins: [Popper],

    props: {
      placement: {
        default: 'bottom-start'
      },

      boundariesPadding: {
        default: 0
      },

      popperOptions: {
        default() {
          return {
            gpuAcceleration: false
          };
        }
      },

      visibleArrow: {
        default: true
      },

      appendToBody: {
        type: Boolean,
        default: true
      }
    },

    data() {
      return {
        minWidth: ''
      };
    },

    computed: {
      popperClass() {
        return this.$parent.popperClass;
      }
    },

    watch: {
      '$parent.inputWidth'() {
        this.updateDropdownWidth();
      },
      '$parent.visible'(val) {
        if (val) {
          this.$nextTick(() => {
            this.updateDropdownWidth();
          });
        }
      }
    },

    methods: {
      updateDropdownWidth() {
        if (this.$parent && this.$parent.$el) {
          const parentWidth = this.$parent.$el.getBoundingClientRect().width;
          this.minWidth = parentWidth + 'px';
          // 确保下拉框宽度与父组件完全一致
          if (this.$el) {
            this.$el.style.width = parentWidth + 'px';
          }
        }
      }
    },

    mounted() {
      this.referenceElm = this.$parent.$refs.reference.$el;
      this.$parent.popperElm = this.popperElm = this.$el;
      this.$on('updatePopper', () => {
        if (this.$parent.visible) {
          this.updatePopper();
          this.updateDropdownWidth();
        }
      });
      this.$on('destroyPopper', this.destroyPopper);
      // 初始化时设置宽度
      this.$nextTick(() => {
        this.updateDropdownWidth();
      });
    }
  };
</script>
