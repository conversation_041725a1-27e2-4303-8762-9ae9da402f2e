<template>
  <div class="dx-select-dropdown el-popper" :class="[{ 'is-multiple': $parent.multiple }, popperClass]" :style="{ minWidth: minWidth }">
    <slot></slot>
  </div>
</template>

<script type="text/babel">
  import Popper from 'element-ui/src/utils/vue-popper';

  export default {
    name: 'DxSelectDropdown',

    componentName: 'DxSelectDropdown',

    mixins: [Popper],

    props: {
      placement: {
        default: 'bottom-start'
      },

      boundariesPadding: {
        default: 0
      },

      popperOptions: {
        default() {
          return {
            gpuAcceleration: false
          };
        }
      },

      visibleArrow: {
        default: true
      },

      appendToBody: {
        type: Boolean,
        default: true
      }
    },

    data() {
      return {
        minWidth: ''
      };
    },

    computed: {
      popperClass() {
        return this.$parent.popperClass;
      }
    },

    watch: {
      '$parent.inputWidth'() {
        this.updateDropdownWidth();
      },
      '$parent.visible'(val) {
        if (val) {
          this.$nextTick(() => {
            this.updateDropdownWidth();
          });
        }
      }
    },

    methods: {
      updateDropdownWidth() {
        if (this.$parent && this.$parent.$el) {
          const parentWidth = this.$parent.$el.getBoundingClientRect().width;
          this.minWidth = parentWidth + 'px';
          // 确保下拉框宽度与父组件完全一致
          if (this.$el) {
            this.$el.style.width = parentWidth + 'px';
            this.$el.style.maxWidth = parentWidth + 'px';
            this.$el.style.minWidth = parentWidth + 'px';

            // 设置滚动条容器宽度，但不破坏滚动功能
            const scrollbar = this.$el.querySelector('.el-scrollbar');
            if (scrollbar) {
              scrollbar.style.width = '100%';
              scrollbar.style.boxSizing = 'border-box';
            }

            const scrollbarWrap = this.$el.querySelector('.el-scrollbar__wrap');
            if (scrollbarWrap) {
              scrollbarWrap.style.width = '100%';
              scrollbarWrap.style.boxSizing = 'border-box';
              // 确保滚动功能正常，不设置 maxHeight（由CSS控制）
            }

            const scrollbarView = this.$el.querySelector('.el-scrollbar__view');
            if (scrollbarView) {
              scrollbarView.style.width = '100%';
              scrollbarView.style.boxSizing = 'border-box';
            }
          }
        }
      }
    },

    mounted() {
      this.referenceElm = this.$parent.$refs.reference.$el;
      this.$parent.popperElm = this.popperElm = this.$el;
      this.$on('updatePopper', () => {
        if (this.$parent.visible) {
          this.updatePopper();
          this.updateDropdownWidth();
        }
      });
      this.$on('destroyPopper', this.destroyPopper);
      // 初始化时设置宽度
      this.$nextTick(() => {
        this.updateDropdownWidth();
      });
    }
  };
</script>
