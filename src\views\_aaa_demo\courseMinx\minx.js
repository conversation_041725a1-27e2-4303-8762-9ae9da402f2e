import videoManageMentAPI from '@/api/mathApi/videoManageMentAPI';
export default {
  data() {
    return {};
  },
  created() {},

  methods: {
    // 获取版本id
    getVersionId() {
      videoManageMentAPI
        .getVersionIdAPI({
          curriculumId: this.selectOperate.curriculumId
        })
        .then((res) => {
          if (res.success) {
            this.versionOptions = res.data;
            this.versionId = this.versionOptions.length > 0 ? this.versionOptions[0].id : '';
            this.getTreeData();
          } else {
            this.versionOptions = [];
            this.versionId = '';
            // this.getTreeData();
          }
        });
    },
    // 获取树形数据
    getTreeData() {
      console.log(this.selectOperate.curriculumId, this.versionId, '-----');

      if (!this.selectOperate.curriculumId) return;
      if (!this.versionId) return;
      this.versionLoading = true;
      console.log(123);

      videoManageMentAPI
        .getTreeDataAPI({
          curriculumId: this.selectOperate.curriculumId,
          versionId: this.versionId,
          nodeLevel: 2
        })
        .then((res) => {
          if (res.success) {
            console.log(res.data);

            // if (res.data) {
            // 转换数据结构
            this.data = this.treeData = this.deepReplace(res.data);
            console.log(this.treeData);

            this.currentNodeKey = this.treeData[0].id;
            this.childrenId = this.treeData[0].id;
            // this.parentId = this.treeData[0].children[0].id;
            console.log(this.childrenId, this.parentId);
            // }

            // 自动执行一次搜索
            this.handleSearch();
          }
        })
        .finally(() => {
          this.versionLoading = false;
        });
    },
    // 重置
    reset() {
      this.$refs.SearchForm.resetFields();
      // 获取默认的课程大类
      const defaultCurriculum = this.courseTypeOptions[0];
      // const defaultCurriculum = this.courseTypeOptions.filter((i) => i.id === this.selectOperate.curriculumId)[0];
      console.log(defaultCurriculum, this.selectOperate.curriculumId);
      if (defaultCurriculum) {
        this.selectOperate.curriculumId = defaultCurriculum.id;
        // 存储到本地
        localStorage.setItem('curriculumId', defaultCurriculum.id);
      }
      // 重置其他字段
      this.selectOperate.courseName = '';
      this.selectOperate.courseType = '';
      // 1.7 新增的重置条件
      this.versionId = null;
      this.parentId = null;
      this.childrenId = null;

      // 1.7 新增的重置条件
      // 重置后自动执行一次搜索
      this.pageParams.pageNum = 1;
      this.currentNodeLevel = 1;
      this.selectOperate.videoName = ''; //视频页专属
      this.getVersionId();
      // this.handleSearch();
    },
    // 课程大类选择改变时
    handleCurriculumChange() {
      // 重新获取树形数据
      // this.getTreeData();
      this.getVersionId();
      // 重新获取列表数据
      this.getTableData(); //不可抽离
    }
  }
};
