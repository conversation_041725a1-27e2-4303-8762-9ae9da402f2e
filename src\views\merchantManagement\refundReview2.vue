<template>
    <div>
        <div element-loading-spinner="el-icon-loading" element-loading-text="加载中..." v-loading="loading">
            <el-card shadow="never" class="card">
                <el-form ref="form" :model="searchForm" :inline="true">
                    <el-form-item label="月份">
                        <el-date-picker v-model="searchForm.date" type="month" value-format="yyyy-MM" placeholder="选择月">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="俱乐部编号">
                        <el-input v-model="searchForm.merchantCode" placeholder="请输入俱乐部编号" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="俱乐部名称">
                        <el-input v-model="searchForm.merchantName" placeholder="请输入俱乐部名称" size="small"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit" size="small">查询</el-button>
                        <el-button size="small" @click="onreset">重置</el-button>
                    </el-form-item>
                </el-form>
                <div id="charts_one" style="min-height: 80vh">
                    <el-table :header-cell-style="{ background: '#eef1f6', color: '#606266' }" ref="multipleTable"
                        border :data="list">
                        <el-table-column align="center" property="merchantCode" label="俱乐部编号"
                            width="220"></el-table-column>
                        <el-table-column align="center" property="merchantName" label="俱乐部名称"
                            width="220"></el-table-column>
                        <el-table-column align="center" property="headUser" label="负责人" width="220"></el-table-column>
                        <el-table-column align="center" property="refundSum" label="退费门店总数"
                            width="220"></el-table-column>
                        <el-table-column align="center" property="noHandle" label="待处理退费门店数"
                            width="220"></el-table-column>
                        <el-table-column align="center" property="isHandle" label="已处理退费门店"
                            width="220"></el-table-column>

                        <el-table-column label="操作" fixed="right" align="center">
                            <template slot-scope="scope">
                                <div>
                                    <el-button size="mini" type="primary"
                                        @click="detail(scope.row.merchantCode)">明细</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <Page :total="page_data.total" :page.sync="page_data.page" :limit.sync="page_data.pageSize"
                        @pagination="index" />
                </div>

            </el-card>
        </div>

    </div>
</template>
<script>
import {
    getRefundSummaryPage
} from '@/api/refundReview'
import Page from '@/components/Pages/pages.vue';
export default {
    name: 'refundReview',
    components: {
        Page
    },
    data() {
        return {
            loading: false,
            searchForm: {
                date: '',
                merchantCode: '',
                merchantName: ''
            },
            page_data: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            list: []
        }
    },
    mounted() {
        this.getNowFormatDate()
    },
    methods: {
        inputDate() {
            console.log(value);

        },
        index() {
            this.loading = true;
            let param = { pageSize: this.page_data.pageSize, pageNum: this.page_data.page };
            Object.assign(param, this.searchForm);
            getRefundSummaryPage(param).then((res) => {
                console.log(res);
                this.list = res.data.data
                this.page_data.total = res.data.data ? res.data.totalItems * 1 : 0;
                this.loading = false;
            })
        },
        onSubmit() {
            this.index();
        },
        onreset() {
            (this.searchForm = {
                years: '',
                merchantCode: '',
                merchantName: ''
            }),
                (this.page_data = {
                    pageSize: 10,
                    page: 1,
                    total: 0
                });
            //   this.index();
            this.getNowFormatDate()
        },
        detail(id) {
            this.$router.push({
                path: '/merchantManagement/refundReviewDetail',
                query: { clubId: id }
            })

        },


        getNowFormatDate() {
            let date = new Date(),
                year = date.getFullYear(), //获取完整的年份(4位)
                month = date.getMonth() + 1, //获取当前月份(0-11,0代表1月)
                strDate = date.getDate() // 获取当前日(1-31)
            if (month < 10) month = `0${month}` // 如果月份是个位数，在前面补0
            this.searchForm.date = `${year}-${month}`
            this.index()
        }

    }
}
</script>
<style lang="scss"></style>