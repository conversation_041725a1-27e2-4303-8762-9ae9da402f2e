<template>
  <div>
    <el-radio-group v-model="basicClass" size="medium">
      <el-radio-button label="1">基础班</el-radio-button>
      <el-radio-button label="2">拔高班</el-radio-button>
    </el-radio-group>
    <div v-if="basicClass == 1" style="font-size: 14px">
      <div style="display: flex; justify-content: space-between; align-items: center; width: 700px; margin: 10px 0 10px 0">
        <div>合计：{{ basicClass == 1 ? basicsErrBookTotal : raiseErrBookTotal }} 题</div>
        <el-button type="primary" icon="el-icon-plus" @click="addForm" :disabled="basicList.length >= 5">添加</el-button>
      </div>
      <template v-for="(dataQuery, index) in basicList">
        <div :key="index + 'basicList'" class="form-item" v-if="dataQuery.deleted != 1">
          <el-form :model="dataQuery" ref="basicListDataQuery" :inline="false" style="width: fit-content">
            <div style="padding: 10px 0; display: flex; justify-content: space-between; width: 650px">
              <span>共{{ getListCount(dataQuery) }}题</span>
              <span v-if="index !== 0" style="cursor: pointer" @click="deleteListItem(dataQuery)">
                <i class="el-icon-delete"></i>
              </span>
            </div>
            <el-form-item v-if="index === 0">
              <div class="redStar">
                <span>*</span>
                <div>根据课堂所有学生最近</div>
                &nbsp;
                <!-- v-rules="dataQuery.cycleEndNum" -->
                <el-input
                  v-model="dataQuery.cycleEndNum"
                  @change="dataQuery.cycleEndNum > 400 ? (dataQuery.cycleEndNum = 400) : dataQuery.cycleEndNum"
                  type="number"
                  placeholder="请输入"
                  :max="400"
                  :min="1"
                  clearable
                  style="width: 100px"
                ></el-input>
                &nbsp;
                <div>日做错的题目为基础生成错题带刷数据</div>
              </div>
            </el-form-item>
            <el-form-item v-else>
              <div class="redStar">
                <span>*</span>
                <div>根据课堂所有学生最近</div>
                &nbsp;
                <el-input
                  v-model="dataQuery.cycleStartNum"
                  type="number"
                  placeholder="请输入"
                  @change="dataQuery.cycleStartNum > 400 ? (dataQuery.cycleStartNum = 400) : dataQuery.cycleStartNum"
                  :max="400"
                  :min="1"
                  clearable
                  style="width: 90px"
                ></el-input>
                &nbsp;日,至&nbsp;
                <el-input
                  v-model="dataQuery.cycleEndNum"
                  type="number"
                  @change="dataQuery.cycleEndNum > 400 ? (dataQuery.cycleEndNum = 400) : dataQuery.cycleEndNum"
                  placeholder="请输入"
                  :max="400"
                  :min="1"
                  clearable
                  style="width: 90px"
                ></el-input>
                &nbsp;
                <div>日做错的题目为基础生成错题带刷数据</div>
              </div>
            </el-form-item>
            <el-form-item label="出题规则：">
              <el-radio-group v-model="dataQuery.questionMethod">
                <div>
                  <span style="margin-right: 50px">
                    <el-radio :label="1">按题目错误次数</el-radio>
                  </span>
                  <span style="margin-right: 5px">
                    <el-radio :label="2">按题目错误人数占比</el-radio>
                  </span>
                  <el-input
                    type="number"
                    @change="dataQuery.percentageNumber > 100 ? (dataQuery.percentageNumber = 100) : dataQuery.percentageNumber"
                    :disabled="dataQuery.questionMethod === 2 ? false : true"
                    style="margin-right: 5px; width: 100px"
                    v-model="dataQuery.percentageNumber"
                    placeholder="请输入"
                    :max="100"
                    :min="1"
                    clearable
                  ></el-input>
                </div>
              </el-radio-group>
              <span>%</span>
              <span style="margin-left: 30px">
                <el-tooltip class="item" effect="dark" placement="top-start">
                  <i class="el-icon-warning-outline"></i>
                  <div slot="content">
                    一：按错题次数：
                    <br />
                    1、课上四个学生甲、乙、丙、丁；甲乙丙丁的单选错题分别为abc，cd，bcd，cdefg；
                    <br />
                    2、此时统计单选题目错误次数为a-1次，b-2次，c-4次，d-3次，e-1次，f-1次，g-1次；
                    <br />
                    3、将单选错题按错误次数排序：低难度：c、d、中难度：b、a、e、高难度：f、g；同样错题次数离当前时间近的排在前面 4、错题带刷单选题出题低、中、高难度分别，1、2、3题
                    <br />
                    5、最终会出c、b、a、f、g的变式题，同时补充两题高难度题目
                    <br />
                    6、此时总题数不足6题，最终变式无法满足总题数需求，则从所有错题对应的知识点、题型，不限制难度出题，补充完剩下的题目。 错题带刷-卷子变式题不能重复；
                    <br />
                    最多配置五个阶段，超出提示最多添加五个阶段 卷子排序按题型，难度从低到高 第一个阶段不能删除，默认七天（当天不算，往前推七天）； 错误人数占比默认50%，支持调整；
                    <br />
                    二：按题目错误人数：
                    <br />
                    1、课上四个学生甲、乙、丙、丁；甲乙丙丁的单选错题分别为abc，cd，bcd，cdefg；
                    <br />
                    2、此时统计单选题目错误次数为a-1人，b-2人，c-4人，d-3人，e-1人，f-1人，g-1人； 3、将单选错题按错误人数取题目，结果为c、d两题 中难度：c，高难度：d
                    <br />
                    4、错题带刷单选题配置低、中、高难度分别为2、1、1；
                    <br />
                    5、最终会出c、d的变式题，此时总题数不足4题，最终变式无法满足总题数需求，则从所有错题对应的知识点、题型，不限制难度出题，补充完剩下的题目。
                  </div>
                </el-tooltip>
              </span>
            </el-form-item>
            <el-form-item>
              <el-radio-group v-model="dataQuery.classTopicType" size="medium">
                <el-radio-button label="0">单选</el-radio-button>
                <el-radio-button label="1">填空</el-radio-button>
                <el-radio-button label="2">计算</el-radio-button>
                <el-radio-button label="3">解方程</el-radio-button>
                <el-radio-button label="4">证明题</el-radio-button>
                <el-radio-button label="5">几何综合题</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="classType[dataQuery.classTopicType] + '题总数：'">
              <el-select v-model="dataQuery.classTypes[dataQuery.classTopicType].questionTypesTotal" placeholder="请选择" style="width: 350px">
                <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="低难度题目数：">
              <el-select v-model="dataQuery.classTypes[dataQuery.classTopicType].lowDifficultyTopicNum" placeholder="请选择" style="width: 350px">
                <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="中难度题目数：">
              <el-select v-model="dataQuery.classTypes[dataQuery.classTopicType].middleDifficultyTopicNum" placeholder="请选择" style="width: 350px">
                <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="高难度题目数：">
              <el-select v-model="dataQuery.classTypes[dataQuery.classTopicType].highDifficultyTopicNum" placeholder="请选择" style="width: 350px">
                <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </div>
    <div v-else style="font-size: 14px">
      <div style="display: flex; justify-content: space-between; align-items: center; width: 700px; margin: 10px 0 10px 0">
        <div>合计：{{ basicClass == 1 ? basicsErrBookTotal : raiseErrBookTotal }} 题</div>
        <el-button type="primary" icon="el-icon-plus" @click="addForm" :disabled="raiseList.length >= 5">添加</el-button>
      </div>
      <template v-for="(dataQuery, index) in raiseList">
        <div :key="index + 'raiseList'" class="form-item" v-if="dataQuery.deleted != 1">
          <el-form :model="dataQuery" ref="basicListDataQuery" :inline="false" style="width: fit-content">
            <div style="padding: 10px 0; display: flex; justify-content: space-between; width: 650px">
              <span>共{{ getListCount(dataQuery) }}题</span>
              <span v-if="index !== 0" style="cursor: pointer" @click="deleteListItem(dataQuery)">
                <i class="el-icon-delete"></i>
              </span>
            </div>
            <el-form-item v-if="index === 0">
              <div class="redStar">
                <span>*</span>
                <div>根据课堂所有学生最近</div>
                &nbsp;
                <!-- v-rules="dataQuery.cycleEndNum" -->
                <el-input
                  v-model="dataQuery.cycleEndNum"
                  :max="400"
                  @change="dataQuery.cycleEndNum > 400 ? (dataQuery.cycleEndNum = 400) : dataQuery.cycleEndNum"
                  type="number"
                  placeholder="请输入"
                  clearable
                  style="width: 100px"
                ></el-input>
                &nbsp;
                <div>日做错的题目为基础生成错题带刷数据</div>
              </div>
            </el-form-item>
            <el-form-item v-else>
              <div class="redStar">
                <span>*</span>
                <div>根据课堂所有学生最近</div>
                &nbsp;
                <el-input
                  @change="dataQuery.cycleStartNum > 400 ? (dataQuery.cycleStartNum = 400) : dataQuery.cycleStartNum"
                  v-model="dataQuery.cycleStartNum"
                  :max="400"
                  type="number"
                  placeholder="请输入"
                  clearable
                  style="width: 90px"
                ></el-input>
                &nbsp;日,至&nbsp;
                <el-input
                  v-model="dataQuery.cycleEndNum"
                  @change="dataQuery.cycleEndNum > 400 ? (dataQuery.cycleEndNum = 400) : dataQuery.cycleEndNum"
                  :max="400"
                  type="number"
                  placeholder="请输入"
                  clearable
                  style="width: 90px"
                ></el-input>
                &nbsp;
                <div>日做错的题目为基础生成错题带刷数据</div>
              </div>
            </el-form-item>
            <el-form-item label="出题规则：">
              <el-radio-group v-model="dataQuery.questionMethod">
                <div>
                  <span style="margin-right: 50px">
                    <el-radio :label="1">按题目错误次数</el-radio>
                  </span>
                  <span style="margin-right: 5px">
                    <el-radio :label="2">按题目错误人数占比</el-radio>
                  </span>
                  <el-input
                    @change="dataQuery.percentageNumber > 100 ? (dataQuery.percentageNumber = 100) : dataQuery.percentageNumber"
                    :disabled="dataQuery.questionMethod === 2 ? false : true"
                    type="number"
                    style="margin-right: 5px; width: 100px"
                    v-model="dataQuery.percentageNumber"
                    placeholder="请输入"
                    :max="100"
                    :min="1"
                    clearable
                  ></el-input>
                </div>
              </el-radio-group>
              <span>%</span>
              <span style="margin-left: 30px">
                <el-tooltip class="item" effect="dark" placement="top-start">
                  <i class="el-icon-warning-outline"></i>
                  <div slot="content">
                    一：按错题次数：
                    <br />
                    1、课上四个学生甲、乙、丙、丁；甲乙丙丁的单选错题分别为abc，cd，bcd，cdefg；
                    <br />
                    2、此时统计单选题目错误次数为a-1次，b-2次，c-4次，d-3次，e-1次，f-1次，g-1次；
                    <br />
                    3、将单选错题按错误次数排序：低难度：c、d、中难度：b、a、e、高难度：f、g；同样错题次数离当前时间近的排在前面 4、错题带刷单选题出题低、中、高难度分别，1、2、3题
                    <br />
                    5、最终会出c、b、a、f、g的变式题，同时补充两题高难度题目
                    <br />
                    6、此时总题数不足6题，最终变式无法满足总题数需求，则从所有错题对应的知识点、题型，不限制难度出题，补充完剩下的题目。 错题带刷-卷子变式题不能重复；
                    <br />
                    最多配置五个阶段，超出提示最多添加五个阶段 卷子排序按题型，难度从低到高 第一个阶段不能删除，默认七天（当天不算，往前推七天）； 错误人数占比默认50%，支持调整；
                    <br />
                    二：按题目错误人数：
                    <br />
                    1、课上四个学生甲、乙、丙、丁；甲乙丙丁的单选错题分别为abc，cd，bcd，cdefg；
                    <br />
                    2、此时统计单选题目错误次数为a-1人，b-2人，c-4人，d-3人，e-1人，f-1人，g-1人； 3、将单选错题按错误人数取题目，结果为c、d两题 中难度：c，高难度：d
                    <br />
                    4、错题带刷单选题配置低、中、高难度分别为2、1、1；
                    <br />
                    5、最终会出c、d的变式题，此时总题数不足4题，最终变式无法满足总题数需求，则从所有错题对应的知识点、题型，不限制难度出题，补充完剩下的题目。
                  </div>
                </el-tooltip>
              </span>
            </el-form-item>
            <el-form-item>
              <el-radio-group v-model="dataQuery.classTopicType" size="medium">
                <el-radio-button label="0">单选</el-radio-button>
                <el-radio-button label="1">填空</el-radio-button>
                <el-radio-button label="2">计算</el-radio-button>
                <el-radio-button label="3">解方程</el-radio-button>
                <el-radio-button label="4">证明题</el-radio-button>
                <el-radio-button label="5">几何综合题</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="classType[dataQuery.classTopicType] + '题总数：'">
              <el-select v-model="dataQuery.classTypes[dataQuery.classTopicType].questionTypesTotal" placeholder="请选择" style="width: 350px">
                <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="低难度题目数：">
              <el-select v-model="dataQuery.classTypes[dataQuery.classTopicType].lowDifficultyTopicNum" placeholder="请选择" style="width: 350px">
                <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="中难度题目数：">
              <el-select v-model="dataQuery.classTypes[dataQuery.classTopicType].middleDifficultyTopicNum" placeholder="请选择" style="width: 350px">
                <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="高难度题目数：">
              <el-select v-model="dataQuery.classTypes[dataQuery.classTopicType].highDifficultyTopicNum" placeholder="请选择" style="width: 350px">
                <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      topicConfigId: {},
      curriculumId: {
        type: String,
        default: () => {
          return null;
        }
      }
    },
    directives: {
      // rules: {
      //   bind(el, o) {
      //     console.log(el, o);
      //     // 初始化判空
      //   },
      //   update(el, o) {
      //     console.log(el, o.value);
      //     if (o.value) {
      //       console.log('有');
      //     } else {
      //       const input = div.querySelector('.el-input__inner');
      //       console.log(input, '没有');
      //     }
      //     // 外部数据变化时再次判空
      //   },
      //   unbind(el, o) {
      //     console.log(el, o);
      //     // 清理事件
      //   }
      // }
    },
    data() {
      return {
        basicsErrBookTotal: 0,
        raiseErrBookTotal: 0,
        classType: {
          0: '单选',
          1: '填空',
          2: '计算',
          3: '解方程',
          4: '证明题',
          5: '几何综合题'
        },
        rules: {
          cycleEndNum: [{ required: true, message: '不能为空', trigger: 'change' }]
        },
        basicClass: 1,
        basicList: [
          {
            percentageNumber: 50,
            classTopicType: 0,
            questionMethod: 1,
            cycleStartNum: 0,
            cycleEndNum: 7,
            index: 0,
            deleted: 0,
            classTypes: this.createDefaultClassTypes()
          }
        ],
        raiseList: [
          {
            percentageNumber: 50,
            classTopicType: 0,
            questionMethod: 1,
            cycleStartNum: 0,
            cycleEndNum: 7,
            index: 0,
            deleted: 0,
            classTypes: this.createDefaultClassTypes()
          }
        ],
        errgradeList: Array.from({ length: 41 }, (_, index) => ({
          id: index,
          name: index.toString()
        }))
      };
    },
    methods: {
      getListCount(list) {
        console.log(list.classTypes);
        if (!list.classTypes) return 0;
        return Object.values(list.classTypes).reduce((acc, cur) => {
          return acc + cur.questionTypesTotal;
        }, 0);
      },
      loading() {
        return this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        // return loading.close();
      },

      totalize(l) {
        let list = l.ruleConfigList;
        if (!list) return;
        this.basicsErrBookTotal = list.reduce((acc, cur) => {
          return cur.classType === 0 ? acc + cur.questionTypesTotal : acc;
        }, 0);

        this.raiseErrBookTotal = list.reduce((acc, cur) => {
          return cur.classType === 1 ? acc + cur.questionTypesTotal : acc;
        }, 0);
        this.$forceUpdate();
      },
      toggleEmptyClass(el) {
        console.log(el);
      },
      createDefaultClassTypes() {
        const init = () => ({
          highDifficultyTopicNum: 0,
          middleDifficultyTopicNum: 0,
          lowDifficultyTopicNum: 0,
          questionTypesTotal: 0,
          id: null
        });
        return { 0: init(), 1: init(), 2: init(), 3: init(), 4: init(), 5: init() };
      },
      deleteListItem(i) {
        console.log(i);

        // 获取对象的所有值，并检查每个 classType 的 id 是否存在
        const classTypesValues = i.classTypes ? Object.values(i.classTypes) : [];
        console.log(classTypesValues);

        const isAllTypesValid = classTypesValues.every((type) => type?.id != null);
        console.log(isAllTypesValid);
        console.log(this.basicClass);

        if (this.basicClass == 1) {
          this.basicList = this.basicList.filter((e) => {
            if (e.index != i.index) return true;
            if (isAllTypesValid) {
              e.deleted = 1;
              return true;
            } else {
              return false;
            }
          });
        } else {
          console.log(this.raiseList);

          this.raiseList = this.raiseList.filter((e) => {
            console.log(e);
            if (e.index != i.index) return true;
            if (isAllTypesValid) {
              e.deleted = 1;
              return true;
            } else {
              return false;
            }
          });
        }
      },
      addForm() {
        let obj = {
          month: null,
          classTopicType: 0,
          questionMethod: 1,
          cycleStartNum: null,
          cycleEndNum: null,
          index: this.basicClass == 1 ? this.basicList.length : this.raiseList.length,
          deleted: 0,
          classTypes: this.createDefaultClassTypes()
        };
        if (this.basicClass == 1) {
          if (this.basicList.length >= 5) return this.$message.warning('基础班最多5个出题规则');
          this.basicList.push(obj);
        } else {
          if (this.raiseList.length >= 5) return this.$message.warning('拔高班最多5个出题规则');
          this.raiseList.push(obj);
        }
      },
      judgeData() {
        // 检查必填项
        let prevItem1 = null; // 用来记录前一项
        for (const item of this.basicList) {
          // 跳过已标记删除的项
          if (item && item.deleted == 1) continue;
          // 1. 原有逻辑：必填项校验
          if ([item.cycleStartNum, item.cycleEndNum].some((v) => v == null || v === '')) {
            return '基础班有必填项未写';
          }
          // 2. 原有逻辑：百分比校验
          if (item.questionMethod == 2 && !item.percentageNumber) {
            return '基础班百分比未写';
          }
          if (item.questionMethod == 2 && item.percentageNumber == 0) {
            return '基础班百分比不能为 0';
          }
          // 3. 原有逻辑：题目总数校验
          if (item.classTypes) {
            for (const k of Object.values(item.classTypes)) {
              const sum = (k.highDifficultyTopicNum || 0) + (k.middleDifficultyTopicNum || 0) + (k.lowDifficultyTopicNum || 0);
              if (sum !== (k.questionTypesTotal || 0)) {
                return '基础班题目总数跟单选题总数对不上';
              }
            }
          }
          // 4. 原有逻辑：开始/结束日期不能相同
          console.log(Number(item.cycleStartNum), Number(item.cycleEndNum));

          if (Number(item.cycleStartNum) == Number(item.cycleEndNum)) {
            return '基础班开始日期不能与结束日期相同！最大日期400天';
          }
          // 5. 新增逻辑：后一项的开始日期必须大于前一项的结束日期
          if (prevItem1 && Number(item.cycleStartNum) <= Number(prevItem1.cycleEndNum)) {
            return '基础班周期的开始日期必须大于上一周期的结束日期';
          }
          prevItem1 = item; // 记录当前项，供下一轮比对
        }
        let prevItem2 = null; // 用来记录前一项
        // console.log(this.raiseList);

        for (const item of this.raiseList) {
          // 跳过已标记删除的项
          if (item && item.deleted == 1) continue;
          // 1. 原有逻辑：必填项校验
          if ([item.cycleStartNum, item.cycleEndNum].some((v) => v == null || v === '')) {
            return '拔高班有必填项未写';
          }
          // 2. 原有逻辑：百分比校验
          if (item.questionMethod == 2 && !item.percentageNumber) {
            return '拔高班百分比未写';
          }
          if (item.questionMethod == 2 && item.percentageNumber == 0) {
            return '拔高班百分比不能为 0';
          }
          // 3. 原有逻辑：题目总数校验
          if (item.classTypes) {
            for (const k of Object.values(item.classTypes)) {
              const sum = (k.highDifficultyTopicNum || 0) + (k.middleDifficultyTopicNum || 0) + (k.lowDifficultyTopicNum || 0);
              if (sum !== (k.questionTypesTotal || 0)) {
                return '拔高班题目总数跟单选题总数对不上';
              }
            }
          }
          // 4. 原有逻辑：开始/结束日期不能相同
          if (Number(item.cycleStartNum) == Number(item.cycleEndNum)) {
            return '拔高班开始日期不能与结束日期相同！最大日期400天';
          }
          // 5. 新增逻辑：后一项的开始日期必须大于前一项的结束日期

          if (prevItem2 && Number(item.cycleStartNum) <= Number(prevItem2.cycleEndNum)) {
            return '拔高班周期的开始日期必须大于上一周期的结束日期';
          }
          prevItem2 = item; // 记录当前项，供下一轮比对
          console.log(prevItem2);
        }
        return null; // 所有验证通过
      },
      getList() {
        // 先进行表单校验；如果有不通过则直接提示并中断
        const judge = this.judgeData();
        if (judge) return this.$message.warning(judge);
        const buildFromList = (list, classType) => {
          console.log(list, classType);
          const result = [];
          list.forEach((item) => {
            // 跳过被标记删除的项
            // if (!item || item.deleted == 1) return;
            const { classTypes, classTopicType, ...rest } = item;
            Object.keys(classTypes || {}).forEach((key) => {
              const typeEntry = classTypes[key] || {};
              result.push({
                topicConfigId: this.topicConfigId,
                curriculumId: this.curriculumId,
                ...rest,
                classType: classType, // 0: 基础班，1: 拔高班
                classTopicType: key, // 题型（0~5）
                wrongRuleNumber: item.index + 1, // 规则编号（从 1 开始）
                ...typeEntry
              });
            });
          });
          return result;
        };
        // 拼装基础班与拔高班两类配置
        const wrongRuleConfigList = [...buildFromList(this.basicList, 0), ...buildFromList(this.raiseList, 1)];
        console.log(wrongRuleConfigList, '--------');
        return wrongRuleConfigList;
      },
      assignmentData(v) {
        this.basicsErrBookTotal = 0;
        this.raiseErrBookTotal = 0;
        this.totalize(v);

        const categorizedData = { basic: {}, advanced: {} };
        console.log(v.ruleConfigList);
        if (v.ruleConfigList && v.ruleConfigList.length > 0) {
          // 遍历规则配置列表，将规则按照类型分类
          v.ruleConfigList.forEach((rule) => {
            const target = rule.classType === 0 ? 'basic' : 'advanced';
            const index = rule.wrongRuleNumber - 1; // 规则编号作为索引
            // 初始化数组并存储规则
            categorizedData[target][index] = categorizedData[target][index] || [];
            categorizedData[target][index].push(rule);
          });
          console.log(categorizedData.basic, categorizedData.advanced);
          // 生成基础班和拔高班的列表
          this.basicList = this.createClassList(categorizedData.basic, 'basic');
          this.raiseList = this.createClassList(categorizedData.advanced, 'advanced');
        } else {
          console.log(2);
          this.basicList = [this.defaultClassObject()];
          this.raiseList = [this.defaultClassObject()];
        }
        console.log(this.basicList);
      },
      createClassList(data, q) {
        // 若无数据，返回默认结构
        if (Object.keys(data).length === 0) return [this.defaultClassObject(q)];
        return Object.keys(data).map((key) => {
          const firstItem = data[key][0];
          const classTypes = this.createDefaultClassTypes();
          // 填充 classTypes 数据
          data[key].forEach((rule, index) => {
            classTypes[index] = {
              id: rule.id,
              highDifficultyTopicNum: rule.highDifficultyTopicNum,
              lowDifficultyTopicNum: rule.lowDifficultyTopicNum,
              middleDifficultyTopicNum: rule.middleDifficultyTopicNum,
              questionTypesTotal: rule.questionTypesTotal
            };
          });
          // 返回构建好的类对象
          return {
            deleted: firstItem.deleted,
            percentageNumber: firstItem.percentageNumber,
            classTopicType: firstItem.classTopicType,
            questionMethod: firstItem.questionMethod,
            cycleStartNum: firstItem.cycleStartNum,
            cycleEndNum: firstItem.cycleEndNum,
            index: firstItem.wrongRuleNumber - 1,
            classTypes
          };
        });
      },
      defaultClassObject(q) {
        // 返回默认的类对象结构
        return {
          percentageNumber: 50,
          classTopicType: 0,
          questionMethod: 1,
          cycleStartNum: 0,
          cycleEndNum: 7,
          index: 0,
          deleted: 0,
          classTypes: this.createDefaultClassTypes()
        };
      }
    }
  };
</script>

<style lang="scss" scoped>
  .form-item {
    width: 700px;
    display: flex;
    padding: 10px 30px;
    // border: 1px solid #ccc;
    margin-bottom: 12px;
    box-shadow: black 2px 1px 12px -10px;
    // justify-content: center;
  }
  .redStar {
    display: flex;
    align-items: center;
    width: 100%;
    span {
      color: red;
      font-size: 20px;
    }
    .el-form {
      margin-top: 20px;
    }
  }
</style>
