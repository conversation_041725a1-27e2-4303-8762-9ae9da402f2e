var CodeMirror=function(){function Ce(t,e){var L={},n=Ce.defaults;for(var r in n)n.hasOwnProperty(r)&&(L[r]=(e&&e.hasOwnProperty(r)?e:n)[r]);var m=L.document,s=m.createElement("div");s.className="CodeMirror"+(L.lineWrapping?" CodeMirror-wrap":""),s.innerHTML='<div style="overflow: hidden; position: relative; width: 3px; height: 0px;"><textarea style="position: absolute; padding: 0; width: 1px;" wrap="off" autocorrect="off" autocapitalize="off"></textarea></div><div class="CodeMirror-scroll" tabindex="-1"><div style="position: relative"><div style="position: relative"><div class="CodeMirror-gutter"><div class="CodeMirror-gutter-text"></div></div><div class="CodeMirror-lines"><div style="position: relative"><div style="position: absolute; width: 100%; height: 0; overflow: hidden; visibility: hidden"></div><pre class="CodeMirror-cursor">&#160;</pre><div></div></div></div></div></div></div>',t.appendChild?t.appendChild(s):t(s);var l=s.firstChild,a=l.firstChild,A=s.lastChild,z=A.firstChild,d=z.firstChild,p=d.firstChild,u=p.firstChild,g=p.nextSibling.firstChild,f=g.firstChild,o=f.nextSibling,v=o.nextSibling;Vt(),/AppleWebKit/.test(navigator.userAgent)&&/Mobile\/\w+/.test(navigator.userAgent)&&(a.style.width="0px"),_e||(g.draggable=!0),null!=L.tabindex&&(a.tabIndex=L.tabindex),L.gutter||L.lineNumbers||(p.style.display="none");try{Yt("x")}catch(t){throw t.message.match(/runtime/i)&&(t=new Error("A CodeMirror inside a P-style element does not work in Internet Explorer. (innerHTML bug)")),t}var i,T,E,c,h=new Fe,x=new Fe,W=new De([new Ne([new We("")])]);Ot();var y,k,C,w,M,b,N,D,S,H,I,O,R,P,U={from:{line:0,ch:0},to:{line:0,ch:0},inverted:!1},B=!1,V=0,K=0,F=0,j=0,G="",Y=Ut();ye(function(){Q(L.value||""),M=!1})();var _,q=new Re;Ke(A,"mousedown",ye(function(e){St(e.shiftKey);for(var t=Ve(e);t!=s;t=t.parentNode)if(t.parentNode==z&&t!=d)return;for(t=Ve(e);t!=s;t=t.parentNode)if(t.parentNode==u)return L.onGutterClick&&L.onGutterClick($,Qe(u.childNodes,t)+K,e),Ue(e);var i=ae(e);switch(function(t){{if(t.which)return t.which;if(1&t.button)return 1;if(2&t.button)return 3;if(4&t.button)return 2}}(e)){case 3:return void(Ge&&!we&&se(e));case 2:return void(i&&Tt(i.line,i.ch,!0))}if(!i)return void(Ve(e)==A&&Ue(e));c||nt();var n=+new Date;{if(C&&C.time>n-400&&Xe(C.pos,i))return Ue(e),setTimeout(xt,20),function(t){Lt({line:t,ch:0},{line:t,ch:Z(t).text.length})}(i.line);if(k&&k.time>n-400&&Xe(k.pos,i))return C={time:n,pos:i},Ue(e),Ht(i);k={time:n,pos:i}}var o,l=i;if(je&&!Xe(U.from,U.to)&&!$e(i,U.from)&&!$e(U.to,i)){_e&&(g.draggable=!0);var r=Ke(m,"mouseup",ye(function(t){_e&&(g.draggable=!1),w=!1,r(),Math.abs(e.clientX-t.clientX)+Math.abs(e.clientY-t.clientY)<10&&(Ue(t),Tt(i.line,i.ch,!0),xt())}),!0);return void(w=!0)}Ue(e),Tt(i.line,i.ch,!0);var a=Ke(m,"mousemove",ye(function(t){clearTimeout(o),Ue(t),function t(e){var n=ae(e,!0);if(n&&!Xe(n,l)){c||nt(),Lt(i,l=n),M=!1;var r=Ct();(n.line>=r.to||n.line<r.from)&&(o=setTimeout(ye(function(){t(e)}),150))}}(t)}),!0);r=Ke(m,"mouseup",ye(function(t){clearTimeout(o);var e=ae(t);e&&Lt(i,e),Ue(t),xt(),M=!0,a(),r()}),!0)})),Ke(A,"dblclick",ye(function(t){for(var e=Ve(t);e!=s;e=e.parentNode)if(e.parentNode==u)return Ue(t);var n=ae(t);if(!n)return;C={time:+new Date,pos:n},Ue(t),Ht(n)})),Ke(g,"dragstart",function(t){var e=ct();Je(e),t.dataTransfer.setDragImage(Ze,0,0),t.dataTransfer.setData("Text",e)}),Ke(g,"selectstart",Ue),Ge||Ke(A,"contextmenu",se),Ke(A,"scroll",function(){wt([]),L.fixedGutter&&(p.style.left=A.scrollLeft+"px"),L.onScroll&&L.onScroll($)}),Ke(window,"resize",function(){wt(!0)}),Ke(a,"keyup",ye(function(t){if(L.onKeyEvent&&L.onKeyEvent($,Pe(t)))return;16==t.keyCode&&(y=null)})),Ke(a,"input",pt),Ke(a,"keydown",ye(function(t){c||nt();var e=t.keyCode;Ye&&27==e&&(t.returnValue=!1);if(St(16==e||t.shiftKey),L.onKeyEvent&&L.onKeyEvent($,Pe(t)))return;var n=tt(t);window.opera&&(et=n?t.keyCode:null,!n&&(we?t.metaKey:t.ctrlKey)&&88==t.keyCode&&st(""))})),Ke(a,"keypress",ye(function(t){if(window.opera&&t.keyCode==et)return et=null,void Ue(t);if(L.onKeyEvent&&L.onKeyEvent($,Pe(t)))return;if(window.opera&&!t.which&&tt(t))return;if(L.electricChars&&T.electricChars){var e=String.fromCharCode(null==t.charCode?t.keyCode:t.charCode);-1<T.electricChars.indexOf(e)&&setTimeout(ye(function(){It(U.to.line,"smart")}),75)}pt()})),Ke(a,"focus",nt),Ke(a,"blur",rt),Ke(A,"dragenter",Be),Ke(A,"dragover",Be),Ke(A,"drop",ye(function(t){t.preventDefault();var r=ae(t,!0),e=t.dataTransfer.files;if(!r||L.readOnly)return;if(e&&e.length&&window.FileReader&&window.File){function n(t,e){var n=new FileReader;n.onload=function(){o[e]=n.result,++l==i&&(r=Wt(r),ye(function(){var t=at(o.join(""),r,r);Lt(r,t)})())},n.readAsText(t)}for(var i=e.length,o=Array(i),l=0,a=0;a<i;++a)n(e[a],a)}else try{if(o=t.dataTransfer.getData("Text")){var s=at(o,r,r),u=U.from,f=U.to;Lt(r,s),w&&at("",u,f),xt()}}catch(t){}})),Ke(A,"paste",function(){xt(),pt()}),Ke(a,"paste",pt),Ke(a,"cut",ye(function(){st("")}));try{_=m.activeElement==a}catch(t){}function X(t){return 0<=t&&t<W.size}_?setTimeout(nt,20):rt();var $=s.CodeMirror={getValue:function(t){var e=[];return W.iter(0,W.size,function(t){e.push(t.text)}),e.join("\n")},setValue:ye(Q),getSelection:ct,replaceSelection:ye(st),focus:function(){xt(),nt(),pt()},setOption:function(t,e){var n=L[t];L[t]=e,"mode"==t||"indentUnit"==t?Ot():"readOnly"==t&&e?(rt(),a.blur()):"theme"==t?Vt():"lineWrapping"==t&&n!=e?ye(Pt)():"tabSize"==t&&ye(Bt)(),"lineNumbers"!=t&&"gutter"!=t&&"firstLineNumber"!=t&&"theme"!=t||ye(Rt)()},getOption:function(t){return L[t]},undo:ye(function(){ot(q.done,q.undone)}),redo:ye(function(){ot(q.undone,q.done)}),indentLine:ye(function(t,e){X(t)&&It(t,null==e?"smart":e?"add":"subtract")}),indentSelection:ye(function(t){if(Xe(U.from,U.to))return It(U.from.line,t);for(var e=U.to.line-(U.to.ch?0:1),n=U.from.line;n<=e;++n)It(n,t)}),historySize:function(){return{undo:q.done.length,redo:q.undone.length}},clearHistory:function(){q=new Re},matchBrackets:ye(function(){ce(!0)}),getTokenAt:ye(function(t){return Z((t=Wt(t)).line).getTokenAt(T,de(t.line),t.ch)}),getStateAfter:function(t){return de((t=Et(null==t?W.size-1:t))+1)},cursorCoords:function(t){return null==t&&(t=U.inverted),te(t?U.from:U.to)},charCoords:function(t){return te(Wt(t))},coordsChar:function(t){var e=qe(g);return Qt(t.x-e.left,t.y-e.top)},markText:ye(Ft),setBookmark:function(t){var e=new Ee((t=Wt(t)).ch);return Z(t.line).addMark(e),e},setMarker:ye(function(t,e,n){"number"==typeof t&&(t=Z(Et(t)));return t.gutterMarker={text:e,style:n},I=!0,t}),clearMarker:ye(function(t){"number"==typeof t&&(t=Z(Et(t)));t.gutterMarker=null,I=!0}),setLineClass:ye(function(t,e){return jt(t,function(t){return t.className!=e&&(t.className=e,1)})}),hideLine:ye(function(t){return Gt(t,!0)}),showLine:ye(function(t){return Gt(t,!1)}),onDeleteLine:function(t,e){if("number"==typeof t){if(!X(t))return null;t=Z(t)}return(t.handlers||(t.handlers=[])).push(e),t},lineInfo:function(t){if("number"==typeof t){if(!X(t))return null;var e=t;if(!(t=Z(t)))return null}else{if(null==(e=He(t)))return null}var n=t.gutterMarker;return{line:e,handle:t,text:t.text,markerText:n&&n.text,markerClass:n&&n.style,lineClass:t.className}},addWidget:function(t,e,n,r,i){var o=(t=Jt(Wt(t))).yBot,l=t.x;if(e.style.position="absolute",z.appendChild(e),"over"==r)o=t.y;else if("near"==r){var a=Math.max(A.offsetHeight,W.height*ee()),s=Math.max(z.clientWidth,g.clientWidth)-le();t.yBot+e.offsetHeight>a&&t.y>e.offsetHeight&&(o=t.y-e.offsetHeight),l+e.offsetWidth>s&&(l=s-e.offsetWidth)}e.style.top=o+oe()+"px",e.style.left=e.style.right="","right"==i?(l=z.clientWidth-e.offsetWidth,e.style.right="0px"):("left"==i?l=0:"middle"==i&&(l=(z.clientWidth-e.offsetWidth)/2),e.style.left=l+le()+"px"),n&&kt(l,o,l+e.offsetWidth,o+e.offsetHeight)},lineCount:function(){return W.size},clipPos:Wt,getCursor:function(t){return null==t&&(t=U.inverted),{line:(e=t?U.from:U.to).line,ch:e.ch};var e},somethingSelected:function(){return!Xe(U.from,U.to)},setCursor:ye(function(t,e,n){null==e&&"number"==typeof t.line?Tt(t.line,t.ch,n):Tt(t,e,n)}),setSelection:ye(function(t,e,n){(n?Lt:At)(Wt(t),Wt(e||t))}),getLine:function(t){if(X(t))return Z(t).text},getLineHandle:function(t){if(X(t))return Z(t)},setLine:ye(function(t,e){X(t)&&at(e,{line:t,ch:0},{line:t,ch:Z(t).text.length})}),removeLine:ye(function(t){X(t)&&at("",{line:t,ch:0},Wt({line:t+1,ch:0}))}),replaceRange:ye(at),getRange:function(t,e){return ft(Wt(t),Wt(e))},execCommand:function(t){return be[t]($)},moveH:ye(function(t,e){var n=t<0?U.from:U.to;(y||Xe(U.from,U.to))&&(n=Nt(t,e));Tt(n.line,n.ch,!0)}),deleteH:ye(function(t,e){Xe(U.from,U.to)?t<0?at("",Nt(t,e),U.to):at("",U.from,Nt(t,e)):at("",U.from,U.to);b=!0}),moveV:ye(function(t,e){var n=0,r=Jt(U.inverted?U.from:U.to,!0);null!=Dt&&(r.x=Dt);"page"==e?n=A.clientHeight:"line"==e&&(n=ee());var i=Qt(r.x,r.y+n*t+2);Tt(i.line,i.ch,!0),Dt=r.x}),toggleOverwrite:function(){B=!B},posFromIndex:function(n){var r,i=0;return W.iter(0,W.size,function(t){var e=t.text.length+1;if(n<e)return r=n,!0;n-=e,++i}),Wt({line:i,ch:r})},indexFromPos:function(t){if(t.line<0||t.ch<0)return 0;var e=t.ch;return W.iter(0,t.line,function(t){e+=t.text.length+1}),e},operation:function(t){return ye(t)()},refresh:function(){wt(!0)},getInputField:function(){return a},getWrapperElement:function(){return s},getScrollerElement:function(){return A},getGutterElement:function(){return p}};function Z(t){return function(t,e){for(;!t.lines;)for(var n=0;;++n){var r=t.children[n],i=r.chunkSize();if(e<i){t=r;break}e-=i}return t.lines[e]}(W,t)}function J(t,e){I=!0;for(var n=e-t.height,r=t;r;r=r.parent)r.height+=n}function Q(t){var e={line:0,ch:0};it(e,{line:W.size-1,ch:Z(W.size-1).text.length},en(t),e,e),M=!0}function tt(t){var e,n,r,i=rn[t.keyCode],o=Se[L.keyMap].auto;if(null==i||t.altGraphKey)return o&&(L.keyMap=o),null;if(t.altKey&&(i="Alt-"+i),t.ctrlKey&&(i="Ctrl-"+i),t.metaKey&&(i="Cmd-"+i),t.shiftKey&&(e=Le("Shift-"+i,L.extraKeys,L.keyMap))?n=!0:e=Le(i,L.extraKeys,L.keyMap),"string"==typeof e&&(e=be.propertyIsEnumerable(e)?be[e]:null),o&&(e||"Ctrl"!=(r=rn[t.keyCode])&&"Alt"!=r&&"Shift"!=r&&"Mod"!=r)&&(L.keyMap=o),!e)return!1;if(n){var l=y;y=null,e($),y=l}else e($);return Ue(t),!0}var et=null;function nt(){L.readOnly||(c||(L.onFocus&&L.onFocus($),c=!0,-1==s.className.search(/\bCodeMirror-focused\b/)&&(s.className+=" CodeMirror-focused"),H||vt(!0)),dt(),ue())}function rt(){c&&(L.onBlur&&L.onBlur($),c=!1,s.className=s.className.replace(" CodeMirror-focused","")),clearInterval(i),setTimeout(function(){c||(y=null)},150)}function it(t,e,n,r,i){if(q){var o=[];for(W.iter(t.line,e.line+1,function(t){o.push(t.text)}),q.addChange(t.line,n.length,o);q.done.length>L.undoDepth;)q.done.shift()}lt(t,e,n,r,i)}function ot(t,e){var n=t.pop();if(n){var r=[],i=n.start+n.added;W.iter(n.start,i,function(t){r.push(t.text)}),e.push({start:n.start,added:n.old.length,old:r});var o=Wt({line:n.start+n.old.length-1,ch:function(t,e){if(!e)return t?t.length:0;if(!t)return e.length;for(var n=t.length,r=e.length;0<=n&&0<=r&&t.charAt(n)==e.charAt(r);--n,--r);return r+1}(r[r.length-1],n.old[n.old.length-1])});lt({line:n.start,ch:0},{line:i-1,ch:Z(i-1).text.length},n.old,o,o),M=!0}}function lt(t,e,n,r,i){var o=!1,l=G.length;L.lineWrapping||W.iter(t.line,e.line,function(t){if(t.text.length==l)return o=!0}),(t.line!=e.line||1<n.length)&&(I=!0);var a=e.line-t.line,s=Z(t.line),u=Z(e.line);if(0==t.ch&&0==e.ch&&""==n[n.length-1]){var f=[],c=null;t.line?(c=Z(t.line-1)).fixMarkEnds(u):u.fixMarkStarts();for(var h=0,d=n.length-1;h<d;++h)f.push(We.inheritMarks(n[h],c));a&&W.remove(t.line,a,O),f.length&&W.insert(t.line,f)}else if(s==u)if(1==n.length)s.replace(t.ch,e.ch,n[0]);else{u=s.split(e.ch,n[n.length-1]),s.replace(t.ch,null,n[0]),s.fixMarkEnds(u);for(f=[],h=1,d=n.length-1;h<d;++h)f.push(We.inheritMarks(n[h],s));f.push(u),W.insert(t.line+1,f)}else if(1==n.length)s.replace(t.ch,null,n[0]),u.replace(null,e.ch,""),s.append(u),W.remove(t.line+1,a,O);else{f=[];s.replace(t.ch,null,n[0]),u.replace(null,e.ch,n[n.length-1]),s.fixMarkEnds(u);for(h=1,d=n.length-1;h<d;++h)f.push(We.inheritMarks(n[h],s));1<a&&W.remove(t.line+1,a-1,O),W.insert(t.line+1,f)}if(L.lineWrapping){var p=A.clientWidth/ie()-3;W.iter(t.line,t.line+n.length,function(t){if(!t.hidden){var e=Math.ceil(t.text.length/p)||1;e!=t.height&&J(t,e)}})}else W.iter(t.line,h+n.length,function(t){var e=t.text;e.length>l&&(l=(G=e).length,P=null,o=!1)}),o&&(l=0,G="",P=null,W.iter(0,W.size,function(t){var e=t.text;e.length>l&&(l=e.length,G=e)}));for(var m=[],g=n.length-a-1,v=(h=0,E.length);h<v;++h){var x=E[h];x<t.line?m.push(x):x>e.line&&m.push(x+g)}var y,k,C,w=t.line+Math.min(n.length,500);y=t.line,k=w,C=de(y),W.iter(y,k,function(t){t.highlight(T,C,L.tabSize),t.stateAfter=Ae(T,C)}),m.push(w),E=m,me(100),N.push({from:t.line,to:e.line+1,diff:g});var M={from:t,to:e,text:n};if(D){for(var b=D;b.next;b=b.next);b.next=M}else D=M;function S(t){return t<=Math.min(e.line,e.line+g)?t:t+g}At(r,i,S(U.from.line),S(U.to.line)),z.style.height=W.height*ee()+2*oe()+"px"}function at(r,i,o){function e(t){if($e(t,i))return t;if(!$e(o,t))return l;var e=t.line+r.length-(o.line-i.line)-1,n=t.ch;return t.line==o.line&&(n+=r[r.length-1].length-(o.ch-(o.line==i.line?i.ch:0))),{line:e,ch:n}}var l;return i=Wt(i),o=o?Wt(o):i,ut(r=en(r),i,o,function(t){return l=t,{from:e(U.from),to:e(U.to)}}),l}function st(t,e){ut(en(t),U.from,U.to,function(t){return"end"==e?{from:t,to:t}:"start"==e?{from:U.from,to:U.from}:{from:U.from,to:t}})}function ut(t,e,n,r){var i=1==t.length?t[0].length+e.ch:t[t.length-1].length,o=r({line:e.line+t.length-1,ch:i});it(e,n,t,o.from,o.to)}function ft(t,e){var n=t.line,r=e.line;if(n==r)return Z(n).text.slice(t.ch,e.ch);var i=[Z(n).text.slice(t.ch)];return W.iter(n+1,r,function(t){i.push(t.text)}),i.push(Z(r).text.slice(0,e.ch)),i.join("\n")}function ct(){return ft(U.from,U.to)}var ht=!1;function dt(){ht||h.set(L.pollInterval,function(){ge(),gt(),c&&dt(),ve()})}function pt(){var e=!1;ht=!0,h.set(20,function t(){ge(),gt()||e?(ht=!1,dt()):(e=!0,h.set(60,t)),ve()})}var mt="";function gt(){if(H||!c||nn(a))return!1;var t=a.value;if(t==mt)return!1;y=null;for(var e=0,n=Math.min(mt.length,t.length);e<n&&mt[e]==t[e];)++e;return e<mt.length?U.from={line:U.from.line,ch:U.from.ch-(mt.length-e)}:B&&Xe(U.from,U.to)&&(U.to={line:U.to.line,ch:Math.min(Z(U.to.line).text.length,U.to.ch+(t.length-e))}),st(t.slice(e),"end"),mt=t,!0}function vt(t){Xe(U.from,U.to)?t&&(mt=a.value=""):(mt="",a.value=ct(),a.select())}function xt(){L.readOnly||a.focus()}function yt(){var t=Jt(U.inverted?U.from:U.to),e=L.lineWrapping?Math.min(t.x,g.offsetWidth):t.x;return kt(e,t.y,e,t.yBot)}function kt(t,e,n,r){var i=le(),o=oe(),l=ee();e+=o,r+=o,t+=i,n+=i;var a=A.clientHeight,s=A.scrollTop,u=!1,f=!0;e<s?(A.scrollTop=Math.max(0,e-2*l),u=!0):s+a<r&&(A.scrollTop=r+l-a,u=!0);var c=A.clientWidth,h=A.scrollLeft,d=L.fixedGutter?p.clientWidth:0;return t<h+d?(t<50&&(t=0),A.scrollLeft=Math.max(0,t-10-d),u=!0):c+h-3<n&&(A.scrollLeft=n+10-c,u=!0,n>z.clientWidth&&(f=!1)),u&&L.onScroll&&L.onScroll($),f}function Ct(){var t=ee(),e=A.scrollTop-oe(),n=Math.max(0,Math.floor(e/t)),r=Math.ceil((e+A.clientHeight)/t);return{from:Ie(W,n),to:Ie(W,r)}}function wt(t,e){if(A.clientWidth){var n=Ct();if(!(!0!==t&&0==t.length&&n.from>=K&&n.to<=F)){var r=Math.max(n.from-100,0),i=Math.min(W.size,n.to+100);K<r&&r-K<20&&(r=K),i<F&&F-i<20&&(i=Math.min(W.size,F));for(var o=!0===t?[]:function(t,e){for(var n=0,r=e.length||0;n<r;++n){for(var i=e[n],o=[],l=i.diff||0,a=0,s=t.length;a<s;++a){var u=t[a];i.to<=u.from&&i.diff?o.push({from:u.from+l,to:u.to+l,domStart:u.domStart}):i.to<=u.from||i.from>=u.to?o.push(u):(i.from>u.from&&o.push({from:u.from,to:i.from,domStart:u.domStart}),i.to<u.to&&o.push({from:i.to+l,to:u.to+l,domStart:u.domStart+(i.to-u.from)}))}t=o}return t}([{from:K,to:F,domStart:0}],t),l=0,a=0;a<o.length;++a){var s=o[a];s.from<r&&(s.domStart+=r-s.from,s.from=r),s.to>i&&(s.to=i),s.from>=s.to?o.splice(a--,1):l+=s.to-s.from}if(l!=i-r){o.sort(function(t,e){return t.domStart-e.domStart});var u=ee(),f=p.style.display;v.style.display=p.style.display="none",function(t,e,r){if(r.length){function n(t){var e=t.nextSibling;return t.parentNode.removeChild(t),e}for(var i=0,o=v.firstChild,l=0;l<r.length;++l){for(var a=r[l];a.domStart>i;)o=n(o),i++;for(var s=0,u=a.to-a.from;s<u;++s)o=o.nextSibling,i++}for(;o;)o=n(o)}else v.innerHTML="";var f=r.shift(),c=(o=v.firstChild,s=t,U.from.line),h=U.to.line,d=c<t&&t<=h,p=m.createElement("div");W.iter(t,e,function(t){var e=null,n=null;d?(e=0,h==s&&(d=!1,n=U.to.ch)):c==s&&(h==s?(e=U.from.ch,n=U.to.ch):(d=!0,e=U.from.ch)),f&&f.to==s&&(f=r.shift()),!f||f.from>s?(t.hidden?p.innerHTML="<pre></pre>":p.innerHTML=t.getHTML(e,n,!0,Y),v.insertBefore(p.firstChild,o)):o=o.nextSibling,++s})}(r,i,o),v.style.display="";var c=r!=K||i!=F||j!=A.clientHeight+u;if(c&&(j=A.clientHeight+u),F=i,V=Oe(W,K=r),d.style.top=V*u+"px",z.style.height=W.height*u+2*oe()+"px",v.childNodes.length!=F-K)throw new Error("BAD PATCH! "+JSON.stringify(o)+" size="+(F-K)+" nodes="+v.childNodes.length);if(L.lineWrapping){P=A.clientWidth;var h=v.firstChild;W.iter(K,F,function(t){if(!t.hidden){var e=Math.round(h.offsetHeight/u)||1;t.height!=e&&(J(t,e),I=!0)}h=h.nextSibling})}else null==P&&(P=Yt(G)),P>A.clientWidth?(g.style.width=P+"px",z.style.width="",z.style.width=A.scrollWidth+"px"):g.style.width=z.style.width="";return p.style.display=f,(c||I)&&Mt(),bt(),!e&&L.onUpdate&&L.onUpdate($),!0}}}else K=F=V=0}function Mt(){if(L.gutter||L.lineNumbers){var t=d.offsetHeight,e=A.clientHeight;p.style.height=(t-e<2?e:t)+"px";var i=[],o=K;W.iter(K,Math.max(F,K+1),function(t){if(t.hidden)i.push("<pre></pre>");else{var e=t.gutterMarker,n=L.lineNumbers?o+L.firstLineNumber:null;e&&e.text?n=e.text.replace("%N%",null!=n?n:""):null==n&&(n=" "),i.push(e&&e.style?'<pre class="'+e.style+'">':"<pre>",n);for(var r=1;r<t.height;++r)i.push("<br/>&#160;");i.push("</pre>")}++o}),p.style.display="none",u.innerHTML=i.join("");for(var n,r=String(W.size).length,l=u.firstChild,a=(n=l).textContent||n.innerText||n.nodeValue||"",s="";a.length+s.length<r;)s+=" ";s&&l.insertBefore(m.createTextNode(s),l.firstChild),p.style.display="",g.style.marginLeft=p.offsetWidth+"px",I=!1}}function bt(){var t=U.inverted?U.from:U.to,e=(ee(),Jt(t,!0)),n=qe(s),r=qe(v);l.style.top=e.y+r.top-n.top+"px",l.style.left=e.x+r.left-n.left+"px",Xe(U.from,U.to)?(o.style.top=e.y+"px",o.style.left=(L.lineWrapping?Math.min(e.x,g.offsetWidth):e.x)+"px",o.style.display=""):o.style.display="none"}function St(t){y=t?y||(U.inverted?U.to:U.from):null}function Lt(t,e){var n=y&&Wt(y);n&&($e(n,t)?t=n:$e(e,n)&&(e=n)),At(t,e),b=!0}function At(t,e,n,r){if((Dt=null)==n&&(n=U.from.line,r=U.to.line),!Xe(U.from,t)||!Xe(U.to,e)){if($e(e,t)){var i=e;e=t,t=i}t.line!=n&&(t=zt(t,n,U.from.ch)),e.line!=r&&(e=zt(e,r,U.to.ch)),Xe(t,e)||Xe(t,U.to)?U.inverted=!1:Xe(e,U.from)&&(U.inverted=!0),Xe(t,e)?Xe(U.from,U.to)||N.push({from:n,to:r+1}):Xe(U.from,U.to)?N.push({from:t.line,to:e.line+1}):(Xe(t,U.from)||(t.line<n?N.push({from:t.line,to:Math.min(e.line,n)+1}):N.push({from:n,to:Math.min(r,t.line)+1})),Xe(e,U.to)||(e.line<r?N.push({from:Math.max(n,t.line),to:r+1}):N.push({from:Math.max(t.line,r),to:e.line+1}))),U.from=t,U.to=e,S=!0}}function zt(o,t,l){function e(t){for(var e=o.line+t,n=1==t?W.size:-1;e!=n;){var r=Z(e);if(!r.hidden){var i=o.ch;return(l<i||i>r.text.length)&&(i=r.text.length),{line:e,ch:i}}e+=t}}return Z(o.line).hidden?o.line>=t?e(1)||e(-1):e(-1)||e(1):o}function Tt(t,e,n){var r=Wt({line:t,ch:e||0});(n?Lt:At)(r,r)}function Et(t){return Math.max(0,Math.min(t,W.size-1))}function Wt(t){if(t.line<0)return{line:0,ch:0};if(t.line>=W.size)return{line:W.size-1,ch:Z(W.size-1).text.length};var e=t.ch,n=Z(t.line).text.length;return null==e||n<e?{line:t.line,ch:n}:e<0?{line:t.line,ch:0}:t}function Nt(r,t){var e=U.inverted?U.from:U.to,i=e.line,n=e.ch,o=Z(i);function l(t){if(n==(r<0?0:o.text.length)){if(t||!function(){for(var t=i+r,e=r<0?-1:W.size;t!=e;t+=r){var n=Z(t);if(!n.hidden)return i=t,o=n,1}}())return;n=r<0?o.text.length:0}else n+=r;return 1}if("char"==t)l();else if("column"==t)l(!0);else if("word"==t)for(var a=!1;!(r<0)||l();){if(tn(o.text.charAt(n)))a=!0;else if(a){r<0&&(r=1,l());break}if(0<r&&!l())break}return{line:i,ch:n}}var Dt=null;function Ht(t){for(var e=Z(t.line).text,n=t.ch,r=t.ch;0<n&&tn(e.charAt(n-1));)--n;for(;r<e.length&&tn(e.charAt(r));)++r;Lt({line:t.line,ch:n},{line:t.line,ch:r})}function It(t,e){if("smart"==(e=e||"add"))if(T.indent)var n=de(t);else e="prev";var r,i=Z(t),o=i.indentation(L.tabSize),l=i.text.match(/^\s*/)[0];if("prev"==e?r=t?Z(t-1).indentation(L.tabSize):0:"smart"==e?r=T.indent(n,i.text.slice(l.length),i.text):"add"==e?r=o+L.indentUnit:"subtract"==e&&(r=o-L.indentUnit),(r=Math.max(0,r))-o){u="";var a=0;if(L.indentWithTabs)for(var s=Math.floor(r/L.tabSize);s;--s)a+=L.tabSize,u+="\t";for(;a<r;)++a,u+=" "}else{if(U.from.line!=t&&U.to.line!=t)return;var u=l}at(u,{line:t,ch:0},{line:t,ch:l.length})}function Ot(){T=Ce.getMode(L,L.mode),W.iter(0,W.size,function(t){t.stateAfter=null}),E=[0],me()}function Rt(){var t=L.gutter||L.lineNumbers;p.style.display=t?"":"none",t?I=!0:v.parentNode.style.marginLeft=0}function Pt(t,e){if(L.lineWrapping){s.className+=" CodeMirror-wrap";var n=A.clientWidth/ie()-3;W.iter(0,W.size,function(t){if(!t.hidden){var e=Math.ceil(t.text.length/n)||1;1!=e&&J(t,e)}}),g.style.width=z.style.width=""}else s.className=s.className.replace(" CodeMirror-wrap",""),P=null,G="",W.iter(0,W.size,function(t){1==t.height||t.hidden||J(t,1),t.text.length>G.length&&(G=t.text)});N.push({from:0,to:W.size})}function Ut(){for(var t='<span class="cm-tab">',e=0;e<L.tabSize;++e)t+=" ";return t+"</span>"}function Bt(){Y=Ut(),wt(!0)}function Vt(){A.className=A.className.replace(/\s*cm-s-\w+/g,"")+L.theme.replace(/(^|\s)\s*/g," cm-s-")}function Kt(){this.set=[]}function Ft(t,e,n){t=Wt(t),e=Wt(e);var i=new Kt;function r(t,e,n,r){Z(t).addMark(new Te(e,n,r,i.set))}if(t.line==e.line)r(t.line,t.ch,e.ch,n);else{r(t.line,t.ch,null,n);for(var o=t.line+1,l=e.line;o<l;++o)r(o,null,null,n);r(e.line,null,e.ch,n)}return N.push({from:t.line,to:e.line+1}),i}function jt(t,e){var n=t,r=t;return"number"==typeof t?r=Z(Et(t)):n=He(t),null!=n&&e(r,n)?(N.push({from:n,to:n+1}),r):null}function Gt(t,n){return jt(t,function(t,e){return t.hidden!=n&&(t.hidden=n,J(t,n?0:1),!n||U.from.line!=e&&U.to.line!=e||At(zt(U.from,U.from.line,U.from.ch),zt(U.to,U.to.line,U.to.ch)),I=!0)})}function Yt(t){return f.innerHTML="<pre><span>x</span></pre>",f.firstChild.firstChild.firstChild.nodeValue=t,f.firstChild.firstChild.offsetWidth||10}Kt.prototype.clear=ye(function(){for(var t=1/0,e=-1/0,n=0,r=this.set.length;n<r;++n){var i=this.set[n],o=i.marked;if(o&&i.parent){var l=He(i);t=Math.min(t,l),e=Math.max(e,l);for(var a=0;a<o.length;++a)o[a].set==this.set&&o.splice(a--,1)}}t!=1/0&&N.push({from:t,to:e+1})}),Kt.prototype.find=function(){for(var t,e,n=0,r=this.set.length;n<r;++n)for(var i=this.set[n],o=i.marked,l=0;l<o.length;++l){var a=o[l];if(a.set==this.set&&(null!=a.from||null!=a.to)){var s=He(i);null!=s&&(null!=a.from&&(t={line:s,ch:a.from}),null!=a.to&&(e={line:s,ch:a.to}))}}return{from:t,to:e}};var _t,qt,Xt,$t=Math.floor(16777215*Math.random()).toString(16);function Zt(t,e){var n="";if(L.lineWrapping){var r=t.text.indexOf(" ",e+2);n=Je(t.text.slice(e+1,r<0?t.text.length:r+(Ye?5:0)))}f.innerHTML="<pre>"+t.getHTML(null,null,!1,Y,e)+'<span id="CodeMirror-temp-'+$t+'">'+Je(t.text.charAt(e)||" ")+"</span>"+n+"</pre>";var i=document.getElementById("CodeMirror-temp-"+$t),o=i.offsetTop,l=i.offsetLeft;if(Ye&&e&&0==o&&0==l){var a=document.createElement("span");a.innerHTML="x",i.parentNode.insertBefore(a,i.nextSibling),o=a.offsetTop}return{top:o,left:l}}function Jt(t,e){var n,r=ee(),i=r*(Oe(W,t.line)-(e?V:0));if(0==t.ch)n=0;else{var o=Zt(Z(t.line),t.ch);n=o.left,L.lineWrapping&&(i+=Math.max(0,o.top))}return{x:n,y:i,yBot:i+r}}function Qt(t,e){e<0&&(e=0);var r=ee(),n=ie(),i=V+Math.floor(e/r),o=Ie(W,i);if(o>=W.size)return{line:W.size-1,ch:Z(W.size-1).text.length};var l=Z(o),a=l.text,s=L.lineWrapping,u=s?i-Oe(W,o):0;if(t<=0&&0==u)return{line:o,ch:0};function f(t){var e=Zt(l,t);if(s){var n=Math.round(e.top/r);return Math.max(0,e.left+(n-u)*A.clientWidth)}return e.left}for(var c,h=0,d=0,p=a.length,m=Math.min(p,Math.ceil((t+u*A.clientWidth*.9)/n));;){var g=f(m);if(!(g<=t&&m<p)){c=g,p=m;break}m=Math.min(p,Math.ceil(1.2*m))}if(c<t)return{line:o,ch:p};for((g=f(m=Math.floor(.8*p)))<t&&(h=m,d=g);;){if(p-h<=1)return{line:o,ch:t-d<c-t?h:p};var v=Math.ceil((h+p)/2),x=f(v);t<x?(p=v,c=x):(h=v,d=x)}}function te(t){var e=Jt(t,!0),n=qe(g);return{x:n.left+e.x,y:n.top+e.y,yBot:n.top+e.yBot}}function ee(){if(null==Xt){Xt="<pre>";for(var t=0;t<49;++t)Xt+="x<br/>";Xt+="x</pre>"}var e=v.clientHeight;return e==qt||(qt=e,f.innerHTML=Xt,_t=f.firstChild.offsetHeight/50||1,f.innerHTML=""),_t}var ne,re=0;function ie(){return A.clientWidth==re?ne:(re=A.clientWidth,ne=Yt("x"))}function oe(){return g.offsetTop}function le(){return g.offsetLeft}function ae(t,e){var n,r,i=qe(A,!0);try{n=t.clientX,r=t.clientY}catch(t){return null}if(!e&&(n-i.left>A.clientWidth||r-i.top>A.clientHeight))return null;var o=qe(g,!0);return Qt(n-o.left,r-o.top)}function se(t){var e=ae(t);if(e&&!window.opera){!Xe(U.from,U.to)&&!$e(e,U.from)&&$e(e,U.to)||ye(Tt)(e.line,e.ch);var n=a.style.cssText;l.style.position="absolute",a.style.cssText="position: fixed; width: 30px; height: 30px; top: "+(t.clientY-5)+"px; left: "+(t.clientX-5)+"px; z-index: 1000; background: white; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",H=!0;var r=a.value=ct();if(xt(),a.select(),Ge){Be(t);var i=Ke(window,"mouseup",function(){i(),setTimeout(o,20)},!0)}else setTimeout(o,50)}function o(){var t=en(a.value).join("\n");t!=r&&ye(st)(t,"end"),l.style.position="relative",a.style.cssText=n,vt(!(H=!1)),dt()}}function ue(){clearInterval(i);var t=!0;o.style.visibility="",i=setInterval(function(){o.style.visibility=(t=!t)?"":"hidden"},650)}var fe={"(":")>",")":"(<","[":"]>","]":"[<","{":"}>","}":"{<"};function ce(t){var e=U.inverted?U.from:U.to,n=Z(e.line),r=e.ch-1,i=0<=r&&fe[n.text.charAt(r)]||fe[n.text.charAt(++r)];if(i){i.charAt(0);for(var h=">"==i.charAt(1),d=h?1:-1,o=n.styles,l=r+1,a=0,s=o.length;a<s;a+=2)if((l-=o[a].length)<=0){var p=o[a+1];break}var m=[n.text.charAt(r)],g=/[(){}[\]]/;for(a=e.line,s=h?Math.min(a+100,W.size):Math.max(-1,a-100);a!=s;a+=d){n=Z(a);var u=a==e.line,f=y(n,u&&h?r+1:0,u&&!h?r:n.text.length);if(f)break}p=(f=f||{pos:null,match:!1}).match?"CodeMirror-matchingbracket":"CodeMirror-nonmatchingbracket";var c=Ft({line:e.line,ch:r},{line:e.line,ch:r+1},p),v=null!=f.pos&&Ft({line:a,ch:f.pos},{line:a,ch:f.pos+1},p),x=ye(function(){c.clear(),v&&v.clear()});t?setTimeout(x,800):R=x}function y(t,e,n){if(t.text)for(var r,i=t.styles,o=h?0:t.text.length-1,l=h?0:i.length-2,a=h?i.length:-2;l!=a;l+=2*d){var s=i[l];if(null==i[l+1]||i[l+1]==p){for(var u=h?0:s.length-1,f=h?s.length:-1;u!=f;u+=d,o+=d)if(e<=o&&o<n&&g.test(r=s.charAt(u))){var c=fe[r];if(">"==c.charAt(1)==h)m.push(r);else{if(m.pop()!=c.charAt(0))return{pos:o,match:!1};if(!m.length)return{pos:o,match:!0}}}}else o+=d*s.length}}}function he(t){for(var e,n,r=t,i=t-40;i<r;--r){if(0==r)return 0;var o=Z(r-1);if(o.stateAfter)return r;var l=o.indentation(L.tabSize);(null==n||l<e)&&(n=r-1,e=l)}return n}function de(t){var e=he(t),n=e&&Z(e-1).stateAfter;return n=n?Ae(T,n):ze(T),W.iter(e,t,function(t){t.highlight(T,n,L.tabSize),t.stateAfter=Ae(T,n)}),e<t&&N.push({from:e,to:t}),t<W.size&&!Z(t).stateAfter&&E.push(t),n}function pe(){for(var r=+new Date+L.workTime,t=E.length;E.length;){if(Z(K).stateAfter)i=E.pop();else var i=K;if(!(i>=W.size)){var e=he(i),o=e&&Z(e-1).stateAfter;o=o?Ae(T,o):ze(T);var l=0,a=T.compareStates,s=!1,u=e,f=!1;if(W.iter(u,W.size,function(t){var e=t.stateAfter;if(+new Date>r)return E.push(u),me(L.workDelay),s&&N.push({from:i,to:u+1}),f=!0;var n=t.highlight(T,o,L.tabSize);if(n&&(s=!0),t.stateAfter=Ae(T,o),a){if(e&&a(e,o))return!0}else if(!1===n&&e){if(3<++l&&(!T.indent||T.indent(e,"")==T.indent(o,"")))return!0}else l=0;++u}),f)return;s&&N.push({from:i,to:u+1})}}t&&L.onHighlightComplete&&L.onHighlightComplete($)}function me(t){E.length&&x.set(t,ye(pe))}function ge(){M=b=D=null,S=!(N=[]),O=[]}function ve(){var t,e=!1;S&&(e=!yt()),N.length?t=wt(N,!0):(S&&bt(),I&&Mt()),e&&yt(),S&&(function(){if(o.getBoundingClientRect){var t=o.getBoundingClientRect();if(!Ye||t.top!=t.bottom){var e=window.innerHeight||Math.max(document.body.offsetHeight,document.documentElement.offsetHeight);(t.top<0||t.bottom>e)&&o.scrollIntoView()}}}(),ue()),c&&!H&&(!0===M||!1!==M&&S)&&vt(b),S&&L.matchBrackets&&setTimeout(ye(function(){R&&(R(),R=null),Xe(U.from,U.to)&&ce(!1)}),20);var n=D,r=O;S&&L.onCursorActivity&&L.onCursorActivity($),n&&L.onChange&&$&&L.onChange($,n);for(var i=0;i<r.length;++i)r[i]($);t&&L.onUpdate&&L.onUpdate($)}var xe=0;function ye(e){return function(){xe++||ge();try{var t=e.apply(this,arguments)}finally{--xe||ve()}return t}}for(var ke in Me)Me.propertyIsEnumerable(ke)&&!$.propertyIsEnumerable(ke)&&($[ke]=Me[ke]);return $}Ce.defaults={value:"",mode:null,theme:"default",indentUnit:2,indentWithTabs:!1,tabSize:4,keyMap:"default",extraKeys:null,electricChars:!0,onKeyEvent:null,lineWrapping:!1,lineNumbers:!1,gutter:!1,fixedGutter:!1,firstLineNumber:1,readOnly:!1,onChange:null,onCursorActivity:null,onGutterClick:null,onHighlightComplete:null,onUpdate:null,onFocus:null,onBlur:null,onScroll:null,matchBrackets:!1,workTime:100,workDelay:200,pollInterval:100,undoDepth:40,tabindex:null,document:window.document};var we=/Mac/.test(navigator.platform),o=(/Win/.test(navigator.platform),{}),l={};Ce.defineMode=function(t,e){Ce.defaults.mode||"null"==t||(Ce.defaults.mode=t),o[t]=e},Ce.defineMIME=function(t,e){l[t]=e},Ce.getMode=function(t,e){if("string"==typeof e&&l.hasOwnProperty(e)&&(e=l[e]),"string"==typeof e)var n=e,r={};else if(null!=e)n=e.name,r=e;var i=o[n];return i?i(t,r||{}):(window.console&&console.warn("No mode "+n+" found, falling back to plain text."),Ce.getMode(t,"text/plain"))},Ce.listModes=function(){var t=[];for(var e in o)o.propertyIsEnumerable(e)&&t.push(e);return t},Ce.listMIMEs=function(){var t=[];for(var e in l)l.propertyIsEnumerable(e)&&t.push({mime:e,mode:l[e]});return t};var Me=Ce.extensions={};Ce.defineExtension=function(t,e){Me[t]=e};var be=Ce.commands={selectAll:function(t){t.setSelection({line:0,ch:0},{line:t.lineCount()-1})},killLine:function(t){var e=t.getCursor(!0),n=t.getCursor(!1),r=!Xe(e,n);r||t.getLine(e.line).length!=e.ch?t.replaceRange("",e,r?n:{line:e.line}):t.replaceRange("",e,{line:e.line+1,ch:0})},deleteLine:function(t){var e=t.getCursor().line;t.replaceRange("",{line:e,ch:0},{line:e})},undo:function(t){t.undo()},redo:function(t){t.redo()},goDocStart:function(t){t.setCursor(0,0,!0)},goDocEnd:function(t){t.setSelection({line:t.lineCount()-1},null,!0)},goLineStart:function(t){t.setCursor(t.getCursor().line,0,!0)},goLineStartSmart:function(t){var e=t.getCursor(),n=t.getLine(e.line),r=Math.max(0,n.search(/\S/));t.setCursor(e.line,e.ch<=r&&e.ch?0:r,!0)},goLineEnd:function(t){t.setSelection({line:t.getCursor().line},null,!0)},goLineUp:function(t){t.moveV(-1,"line")},goLineDown:function(t){t.moveV(1,"line")},goPageUp:function(t){t.moveV(-1,"page")},goPageDown:function(t){t.moveV(1,"page")},goCharLeft:function(t){t.moveH(-1,"char")},goCharRight:function(t){t.moveH(1,"char")},goColumnLeft:function(t){t.moveH(-1,"column")},goColumnRight:function(t){t.moveH(1,"column")},goWordLeft:function(t){t.moveH(-1,"word")},goWordRight:function(t){t.moveH(1,"word")},delCharLeft:function(t){t.deleteH(-1,"char")},delCharRight:function(t){t.deleteH(1,"char")},delWordLeft:function(t){t.deleteH(-1,"word")},delWordRight:function(t){t.deleteH(1,"word")},indentAuto:function(t){t.indentSelection("smart")},indentMore:function(t){t.indentSelection("add")},indentLess:function(t){t.indentSelection("subtract")},insertTab:function(t){t.replaceSelection("\t","end")},transposeChars:function(t){var e=t.getCursor(),n=t.getLine(e.line);0<e.ch&&e.ch<n.length-1&&t.replaceRange(n.charAt(e.ch)+n.charAt(e.ch-1),{line:e.line,ch:e.ch-1},{line:e.line,ch:e.ch+1})},newlineAndIndent:function(t){t.replaceSelection("\n","end"),t.indentLine(t.getCursor().line)},toggleOverwrite:function(t){t.toggleOverwrite()}},Se=Ce.keyMap={};function Le(t,e,n){function l(t,e,n){var r=e[t];if(null!=r)return r;if(null==n&&(n=e.fallthrough),null==n)return e.catchall;if("string"==typeof n)return l(t,Se[n]);for(var i=0,o=n.length;i<o;++i)if(null!=(r=l(t,Se[n[i]])))return r;return null}return e?l(t,e,n):l(t,Se[n])}function Ae(t,e){if(!0===e)return e;if(t.copyState)return t.copyState(e);var n={};for(var r in e){var i=e[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function ze(t,e,n){return!t.startState||t.startState(e,n)}function c(t,e){this.pos=this.start=0,this.string=t,this.tabSize=e||8}function Te(t,e,n,r){this.from=t,this.to=e,this.style=n,this.set=r}function Ee(t){this.from=t,this.to=t,this.line=null}function We(t,e){this.styles=e||[t,null],this.text=t,this.height=1,this.marked=this.gutterMarker=this.className=this.handlers=null,this.stateAfter=this.parent=this.hidden=null}function u(t,e,n,r){for(var i=0,o=0,l=0;o<e;i+=2){var a=n[i],s=o+a.length;0==l?(t<s&&r.push(a.slice(t-o,Math.min(a.length,e-o)),n[i+1]),t<=s&&(l=1)):1==l&&(e<s?r.push(a.slice(0,e-o),n[i+1]):r.push(a,n[i+1])),o=s}}function Ne(t){this.lines=t,this.parent=null;for(var e=0,n=t.length,r=0;e<n;++e)t[e].parent=this,r+=t[e].height;this.height=r}function De(t){for(var e=0,n=0,r=0,i=(this.children=t).length;r<i;++r){var o=t[r];e+=o.chunkSize(),n+=o.height,o.parent=this}this.size=e,this.height=n,this.parent=null}function He(t){if(null==t.parent)return null;for(var e=t.parent,n=Qe(e.lines,t),r=e.parent;r;r=(e=r).parent){var i=0;for(r.children.length;r.children[i]!=e;++i)n+=r.children[i].chunkSize()}return n}function Ie(t,e){var n=0;t:do{for(var r=0,i=t.children.length;r<i;++r){var o=t.children[r],l=o.height;if(e<l){t=o;continue t}e-=l,n+=o.chunkSize()}return n}while(!t.lines);for(r=0,i=t.lines.length;r<i;++r){var a=t.lines[r].height;if(e<a)break;e-=a}return n+r}function Oe(t,e){var n=0;t:do{for(var r=0,i=t.children.length;r<i;++r){var o=t.children[r],l=o.chunkSize();if(e<l){t=o;continue t}e-=l,n+=o.height}return n}while(!t.lines);for(r=0;r<e;++r)n+=t.lines[r].height;return n}function Re(){this.time=0,this.done=[],this.undone=[]}function e(){Be(this)}function Pe(t){return t.stop||(t.stop=e),t}function Ue(t){t.preventDefault?t.preventDefault():t.returnValue=!1}function n(t){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0}function Be(t){Ue(t),n(t)}function Ve(t){return t.target||t.srcElement}function Ke(t,e,n,r){if("function"==typeof t.addEventListener){if(t.addEventListener(e,n,!1),r)return function(){t.removeEventListener(e,n,!1)}}else{function i(t){n(t||window.event)}if(t.attachEvent("on"+e,i),r)return function(){t.detachEvent("on"+e,i)}}}function Fe(){this.id=null}Se.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharRight",Backspace:"delCharLeft",Tab:"indentMore","Shift-Tab":"indentLess",Enter:"newlineAndIndent",Insert:"toggleOverwrite"},Se.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Alt-Up":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Down":"goDocEnd","Ctrl-Left":"goWordLeft","Ctrl-Right":"goWordRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delWordLeft","Ctrl-Delete":"delWordRight","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll",fallthrough:"basic"},Se.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goWordLeft","Alt-Right":"goWordRight","Cmd-Left":"goLineStart","Cmd-Right":"goLineEnd","Alt-Backspace":"delWordLeft","Ctrl-Alt-Backspace":"delWordRight","Alt-Delete":"delWordRight","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll",fallthrough:["basic","emacsy"]},Se.default=we?Se.macDefault:Se.pcDefault,Se.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageUp","Shift-Ctrl-V":"goPageDown","Ctrl-D":"delCharRight","Ctrl-H":"delCharLeft","Alt-D":"delWordRight","Alt-Backspace":"delWordLeft","Ctrl-K":"killLine","Ctrl-T":"transposeChars"},Ce.fromTextArea=function(e,t){function n(){e.value=o.getValue()}if((t=t||{}).value=e.value,!t.tabindex&&e.tabindex&&(t.tabindex=e.tabindex),e.form){var r=Ke(e.form,"submit",n,!0);if("function"==typeof e.form.submit){var i=e.form.submit;e.form.submit=function t(){n(),e.form.submit=i,e.form.submit(),e.form.submit=t}}}e.style.display="none";var o=Ce(function(t){e.parentNode.insertBefore(t,e.nextSibling)},t);return o.save=n,o.getTextArea=function(){return e},o.toTextArea=function(){n(),e.parentNode.removeChild(o.getWrapperElement()),e.style.display="",e.form&&(r(),"function"==typeof e.form.submit&&(e.form.submit=i))},o},Ce.copyState=Ae,Ce.startState=ze,c.prototype={eol:function(){return this.pos>=this.string.length},sol:function(){return 0==this.pos},peek:function(){return this.string.charAt(this.pos)},next:function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},eat:function(t){var e=this.string.charAt(this.pos);if("string"==typeof t)var n=e==t;else n=e&&(t.test?t.test(e):t(e));if(n)return++this.pos,e},eatWhile:function(t){for(var e=this.pos;this.eat(t););return this.pos>e},eatSpace:function(){for(var t=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t},skipToEnd:function(){this.pos=this.string.length},skipTo:function(t){var e=this.string.indexOf(t,this.pos);if(-1<e)return this.pos=e,!0},backUp:function(t){this.pos-=t},column:function(){return r(this.string,this.start,this.tabSize)},indentation:function(){return r(this.string,null,this.tabSize)},match:function(t,e,n){if("string"!=typeof t){var r=this.string.slice(this.pos).match(t);return r&&!1!==e&&(this.pos+=r[0].length),r}function i(t){return n?t.toLowerCase():t}if(i(this.string).indexOf(i(t),this.pos)==this.pos)return!1!==e&&(this.pos+=t.length),!0},current:function(){return this.string.slice(this.start,this.pos)}},Ce.StringStream=c,Te.prototype={attach:function(t){this.set.push(t)},detach:function(t){var e=Qe(this.set,t);-1<e&&this.set.splice(e,1)},split:function(t,e){return this.to<=t&&null!=this.to?null:new Te(this.from<t||null==this.from?null:this.from-t+e,null==this.to?null:this.to-t+e,this.style,this.set)},dup:function(){return new Te(null,null,this.style,this.set)},clipTo:function(t,e,n,r,i){null!=this.from&&this.from>=e&&(this.from=Math.max(r,this.from)+i),null!=this.to&&this.to>e&&(this.to=r<this.to?this.to+i:e),t&&r>this.from&&(r<this.to||null==this.to)&&(this.from=null),n&&(e<this.to||null==this.to)&&(e>this.from||null==this.from)&&(this.to=null)},isDead:function(){return null!=this.from&&null!=this.to&&this.from>=this.to},sameSet:function(t){return this.set==t.set}},Ee.prototype={attach:function(t){this.line=t},detach:function(t){this.line==t&&(this.line=null)},split:function(t,e){if(t<this.from)return this.from=this.to=this.from-t+e,this},isDead:function(){return this.from>this.to},clipTo:function(t,e,n,r,i){(t||e<this.from)&&(n||r>this.to)?(this.from=0,this.to=-1):this.from>e&&(this.from=this.to=Math.max(r,this.from)+i)},sameSet:function(t){return!1},find:function(){return this.line&&this.line.parent?{line:He(this.line),ch:this.from}:null},clear:function(){if(this.line){var t=Qe(this.line.marked,this);-1!=t&&this.line.marked.splice(t,1),this.line=null}}},We.inheritMarks=function(t,e){var n=new We(t),r=e&&e.marked;if(r)for(var i=0;i<r.length;++i)if(null==r[i].to&&r[i].style){var o=n.marked||(n.marked=[]),l=r[i].dup();o.push(l),l.attach(n)}return n},We.prototype={replace:function(t,e,n){var r=[],i=this.marked,o=null==e?this.text.length:e;if(u(0,t,this.styles,r),n&&r.push(n,null),u(o,this.text.length,this.styles,r),this.styles=r,this.text=this.text.slice(0,t)+n+this.text.slice(o),this.stateAfter=null,i)for(var l=n.length-(o-t),a=0,s=i[a];a<i.length;++a)s.clipTo(null==t,t||0,null==e,o,l),s.isDead()&&(s.detach(this),i.splice(a--,1))},split:function(t,e){var n=[e,null],r=this.marked;u(t,this.text.length,this.styles,n);var i=new We(e+this.text.slice(t),n);if(r)for(var o=0;o<r.length;++o){var l=r[o].split(t,e.length);l&&(i.marked||(i.marked=[]),i.marked.push(l),l.attach(i))}return i},append:function(t){var e=this.text.length,n=t.marked,r=this.marked;if(this.text+=t.text,u(0,t.text.length,t.styles,this.styles),r)for(var i=0;i<r.length;++i)null==r[i].to&&(r[i].to=e);if(n&&n.length){r||(this.marked=r=[]);t:for(i=0;i<n.length;++i){var o=n[i];if(!o.from)for(var l=0;l<r.length;++l){var a=r[l];if(a.to==e&&a.sameSet(o)){a.to=null==o.to?null:o.to+e,a.isDead()&&(a.detach(this),n.splice(i--,1));continue t}}r.push(o),o.attach(this),o.from+=e,null!=o.to&&(o.to+=e)}}},fixMarkEnds:function(t){var e=this.marked,n=t.marked;if(e)for(var r=0;r<e.length;++r){var i=e[r],o=null==i.to;if(o&&n)for(var l=0;l<n.length;++l)if(n[l].sameSet(i)){o=!1;break}o&&(i.to=this.text.length)}},fixMarkStarts:function(){var t=this.marked;if(t)for(var e=0;e<t.length;++e)null==t[e].from&&(t[e].from=0)},addMark:function(t){t.attach(this),null==this.marked&&(this.marked=[]),this.marked.push(t),this.marked.sort(function(t,e){return(t.from||0)-(e.from||0)})},highlight:function(t,e,n){var r,i=new c(this.text,n),o=this.styles,l=0,a=!1,s=o[0];for(""==this.text&&t.blankLine&&t.blankLine(e);!i.eol();){var u=t.token(i,e),f=this.text.slice(i.start,i.pos);if(i.start=i.pos,l&&o[l-1]==u?o[l-2]+=f:f&&(!a&&(o[l+1]!=u||l&&o[l-2]!=r)&&(a=!0),o[l++]=f,o[l++]=u,r=s,s=o[l]),5e3<i.pos){o[l++]=this.text.slice(i.pos),o[l++]=null;break}}return o.length!=l&&(o.length=l,a=!0),l&&o[l-2]!=r&&(a=!0),a||o.length<5&&this.text.length<10&&null},getTokenAt:function(t,e,n){for(var r=new c(this.text);r.pos<n&&!r.eol();){r.start=r.pos;var i=t.token(r,e)}return{start:r.start,end:r.pos,string:r.current(),className:i||null,state:e}},indentation:function(t){return r(this.text,null,t)},getHTML:function(t,e,n,r,i){var o=[],l=!0;function a(t,e){t&&(l&&Ye&&" "==t.charAt(0)&&(t=" "+t.slice(1)),l=!1,e?o.push('<span class="',e,'">',Je(t).replace(/\t/g,r),"</span>"):o.push(Je(t).replace(/\t/g,r)))}n&&o.push(this.className?'<pre class="'+this.className+'">':"<pre>");var s=this.styles,u=this.text,f=this.marked;t==e&&(t=null);var c=u.length;if(null!=i&&(c=Math.min(i,c)),u||null!=i)if(f||null!=t){var h=0,d=(C=0,""),p=-1,m=null;function g(){f&&(m=(p+=1)<f.length?f[p]:null)}for(g();h<c;){var v=c,x="";for(null!=t&&(h<t?v=t:(null==e||h<e)&&(x=" CodeMirror-selected",null!=e&&(v=Math.min(v,e))));m&&null!=m.to&&m.to<=h;)g();for(m&&(m.from>h?v=Math.min(v,m.from):(x+=" "+m.style,null!=m.to&&(v=Math.min(v,m.to))));;){var y=h+d.length,k=b;if(x&&(k=b?b+x:x),a(v<y?d.slice(0,v-h):d,k),v<=y){d=d.slice(v-h),h=v;break}h=y,d=s[C++],b="cm-"+s[C++]}}null!=t&&null==e&&a(" ","CodeMirror-selected")}else for(var C=0,w=0;w<c;C+=2){var M=s[C],b=s[C+1],S=M.length;c<w+S&&(M=M.slice(0,c-w)),w+=S,a(M,b&&"cm-"+b)}else a(" ",null!=t&&null==e?"CodeMirror-selected":null);return n&&o.push("</pre>"),o.join("")},cleanUp:function(){if(this.parent=null,this.marked)for(var t=0,e=this.marked.length;t<e;++t)this.marked[t].detach(this)}},Ne.prototype={chunkSize:function(){return this.lines.length},remove:function(t,e,n){for(var r=t,i=t+e;r<i;++r){var o=this.lines[r];if(this.height-=o.height,o.cleanUp(),o.handlers)for(var l=0;l<o.handlers.length;++l)n.push(o.handlers[l])}this.lines.splice(t,e)},collapse:function(t){t.splice.apply(t,[t.length,0].concat(this.lines))},insertHeight:function(t,e,n){this.height+=n,this.lines.splice.apply(this.lines,[t,0].concat(e));for(var r=0,i=e.length;r<i;++r)e[r].parent=this},iterN:function(t,e,n){for(var r=t+e;t<r;++t)if(n(this.lines[t]))return!0}},De.prototype={chunkSize:function(){return this.size},remove:function(t,e,n){this.size-=e;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(t<o){var l=Math.min(e,o-t),a=i.height;if(i.remove(t,l,n),this.height-=a-i.height,o==l&&(this.children.splice(r--,1),i.parent=null),0==(e-=l))break;t=0}else t-=o}if(this.size-e<25){var s=[];this.collapse(s),this.children=[new Ne(s)]}},collapse:function(t){for(var e=0,n=this.children.length;e<n;++e)this.children[e].collapse(t)},insert:function(t,e){for(var n=0,r=0,i=e.length;r<i;++r)n+=e[r].height;this.insertHeight(t,e,n)},insertHeight:function(t,e,n){this.size+=e.length,this.height+=n;for(var r=0,i=this.children.length;r<i;++r){var o=this.children[r],l=o.chunkSize();if(t<=l){if(o.insertHeight(t,e,n),o.lines&&50<o.lines.length){for(;50<o.lines.length;){var a=new Ne(o.lines.splice(o.lines.length-25,25));o.height-=a.height,this.children.splice(r+1,0,a),a.parent=this}this.maybeSpill()}break}t-=l}},maybeSpill:function(){if(!(this.children.length<=10)){var t=this;do{var e=new De(t.children.splice(t.children.length-5,5));if(t.parent){t.size-=e.size,t.height-=e.height;var n=Qe(t.parent.children,t);t.parent.children.splice(n+1,0,e)}else{var r=new De(t.children);(r.parent=t).children=[r,e],t=r}e.parent=t.parent}while(10<t.children.length);t.parent.maybeSpill()}},iter:function(t,e,n){this.iterN(t,e-t,n)},iterN:function(t,e,n){for(var r=0,i=this.children.length;r<i;++r){var o=this.children[r],l=o.chunkSize();if(t<l){var a=Math.min(e,l-t);if(o.iterN(t,a,n))return!0;if(0==(e-=a))break;t=0}else t-=l}}},Re.prototype={addChange:function(t,e,n){this.undone.length=0;var r=+new Date,i=this.done[this.done.length-1];if(400<r-this.time||!i||i.start>t+e||i.start+i.added<t-i.added+i.old.length)this.done.push({start:t,added:e,old:n});else{var o=0;if(t<i.start){for(var l=i.start-t-1;0<=l;--l)i.old.unshift(n[l]);i.added+=i.start-t,i.start=t}else i.start<t&&(e+=o=t-i.start);l=i.added-o;for(var a=n.length;l<a;++l)i.old.push(n[l]);i.added<e&&(i.added=e)}this.time=r}},Ce.e_stop=Be,Ce.e_preventDefault=Ue,Ce.e_stopPropagation=n,Ce.connect=Ke,Fe.prototype={set:function(t,e){clearTimeout(this.id),this.id=setTimeout(e,t)}};var t,je=!/MSIE [1-8]\b/.test(navigator.userAgent)&&"draggable"in document.createElement("div"),Ge=/gecko\/\d{7}/i.test(navigator.userAgent),Ye=/MSIE \d/.test(navigator.userAgent),_e=/WebKit\//.test(navigator.userAgent);function r(t,e,n){null==e&&-1==(e=t.search(/[^\s\u00a0]/))&&(e=t.length);for(var r=0,i=0;r<e;++r)"\t"==t.charAt(r)?i+=n-i%n:++i;return i}function qe(t,e){for(var n,r=t.ownerDocument.body,i=0,o=0,l=!1,a=t;a;a=a.offsetParent){var s=a.offsetLeft,u=a.offsetTop;a==r?(i+=Math.abs(s),o+=Math.abs(u)):(i+=s,o+=u),e&&"fixed"==((n=a).currentStyle?n.currentStyle:window.getComputedStyle(n,null)).position&&(l=!0)}var f=e&&!l?null:r;for(a=t.parentNode;a!=f;a=a.parentNode)null!=a.scrollLeft&&(i-=a.scrollLeft,o-=a.scrollTop);return{left:i,top:o}}function Xe(t,e){return t.line==e.line&&t.ch==e.ch}function $e(t,e){return t.line<e.line||t.line==e.line&&t.ch<e.ch}(t=document.createElement("textarea")).value="foo\nbar",t.value.indexOf("\r"),null!=document.documentElement.getBoundingClientRect&&(qe=function(t,e){try{var n=t.getBoundingClientRect();n={top:n.top,left:n.left}}catch(t){n={top:0,left:0}}if(!e)if(null==window.pageYOffset){var r=document.documentElement||document.body.parentNode;null==r.scrollTop&&(r=document.body),n.top+=r.scrollTop,n.left+=r.scrollLeft}else n.top+=window.pageYOffset,n.left+=window.pageXOffset;return n});var Ze=document.createElement("pre");function Je(t){return Ze.textContent=t,Ze.innerHTML}function Qe(t,e){if(t.indexOf)return t.indexOf(e);for(var n=0,r=t.length;n<r;++n)if(t[n]==e)return n;return-1}function tn(t){return/\w/.test(t)||t.toUpperCase()!=t.toLowerCase()}"\na"==Je("a")?Je=function(t){return Ze.textContent=t,Ze.innerHTML.slice(1)}:"\t"!=Je("\t")&&(Je=function(t){return Ze.innerHTML="",Ze.appendChild(document.createTextNode(t)),Ze.innerHTML}),Ce.htmlEscape=Je;var en=3!="\n\nb".split(/\n/).length?function(t){for(var e,n=0,r=[];-1<(e=t.indexOf("\n",n));)r.push(t.slice(n,"\r"==t.charAt(e-1)?e-1:e)),n=e+1;return r.push(t.slice(n)),r}:function(t){return t.split(/\r?\n/)};Ce.splitLines=en;var nn=window.getSelection?function(t){try{return t.selectionStart!=t.selectionEnd}catch(t){return!1}}:function(t){try{var e=t.ownerDocument.selection.createRange()}catch(t){}return!(!e||e.parentElement()!=t)&&0!=e.compareEndPoints("StartToEnd",e)};Ce.defineMode("null",function(){return{token:function(t){t.skipToEnd()}}}),Ce.defineMIME("text/plain","null");var rn={3:"Enter",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",91:"Mod",92:"Mod",93:"Mod",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63276:"PageUp",63277:"PageDown",63275:"End",63273:"Home",63234:"Left",63232:"Up",63235:"Right",63233:"Down",63302:"Insert",63272:"Delete"};return Ce.keyNames=rn,function(){for(var t=0;t<10;t++)rn[t+48]=String(t);for(t=65;t<=90;t++)rn[t]=String.fromCharCode(t);for(t=1;t<=12;t++)rn[t+111]=rn[t+63235]="F"+t}(),Ce}();CodeMirror.defineMode("xml",function(t,e){var o,l,a,r,i=t.indentUnit,s=e.htmlMode?{autoSelfClosers:{br:!0,img:!0,hr:!0,link:!0,input:!0,meta:!0,col:!0,frame:!0,base:!0,area:!0},doNotIndent:{pre:!0},allowUnquoted:!0}:{autoSelfClosers:{},doNotIndent:{},allowUnquoted:!1},u=e.alignCDATA;function f(e,n){function t(t){return(n.tokenize=t)(e,n)}var r,i=e.next();if("<"!=i)return"&"==i?(e.eatWhile(/[^;]/),e.eat(";"),"atom"):(e.eatWhile(/[^&<]/),null);if(e.eat("!"))return e.eat("[")?e.match("CDATA[")?t(h("atom","]]>")):null:e.match("--")?t(h("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),t(function r(i){return function(t,e){for(var n;null!=(n=t.next());){if("<"==n)return e.tokenize=r(i+1),e.tokenize(t,e);if(">"==n){if(1!=i)return e.tokenize=r(i-1),e.tokenize(t,e);e.tokenize=f;break}}return"meta"}}(1))):null;if(e.eat("?"))return e.eatWhile(/[\w\._\-]/),n.tokenize=h("meta","?>"),"meta";for(l=e.eat("/")?"closeTag":"openTag",e.eatSpace(),o="";r=e.eat(/[^\s\u00a0=<>\"\'\/?]/);)o+=r;return n.tokenize=c,"tag"}function c(t,e){var n,r=t.next();return">"==r||"/"==r&&t.eat(">")?(e.tokenize=f,l=">"==r?"endTag":"selfcloseTag","tag"):"="==r?(l="equals",null):/[\'\"]/.test(r)?(e.tokenize=(n=r,function(t,e){for(;!t.eol();)if(t.next()==n){e.tokenize=c;break}return"string"}),e.tokenize(t,e)):(t.eatWhile(/[^\s\u00a0=<>\"\'\/?]/),"word")}function h(n,r){return function(t,e){for(;!t.eol();){if(t.match(r)){e.tokenize=f;break}t.next()}return n}}function n(){for(var t=arguments.length-1;0<=t;t--)a.cc.push(arguments[t])}function d(){return n.apply(null,arguments),!0}function p(t){if("openTag"==t)return a.tagName=o,d(m,(i=a.startOfLine,function(t){return"selfcloseTag"==t||"endTag"==t&&s.autoSelfClosers.hasOwnProperty(a.tagName.toLowerCase())||"endTag"==t&&(e=a.tagName,n=i,r=s.doNotIndent.hasOwnProperty(e)||a.context&&a.context.noIndent,a.context={prev:a.context,tagName:e,indent:a.indented,startOfLine:n,noIndent:r}),d();var e,n,r}));if("closeTag"!=t)return d();var e,i,n=!1;return(n=!a.context||a.context.tagName!=o)&&(r="error"),d((e=n,function(t){return e&&(r="error"),"endTag"==t?(a.context&&(a.context=a.context.prev),d()):(r="error",d(arguments.callee))}))}function m(t){return"word"==t?(r="attribute",d(m)):"equals"==t?d(g,m):"string"==t?(r="error",d(m)):n()}function g(t){return"word"==t&&s.allowUnquoted?(r="string",d()):"string"==t?d(v):n()}function v(t){return"string"==t?d(v):n()}return{startState:function(){return{tokenize:f,cc:[],indented:0,startOfLine:!0,tagName:null,context:null}},token:function(t,e){if(t.sol()&&(e.startOfLine=!0,e.indented=t.indentation()),t.eatSpace())return null;r=l=o=null;var n=e.tokenize(t,e);if(e.type=l,(n||l)&&"comment"!=n)for(a=e;;){if((e.cc.pop()||p)(l||n))break}return e.startOfLine=!1,r||n},indent:function(t,e,n){var r=t.context;if(t.tokenize!=c&&t.tokenize!=f||r&&r.noIndent)return n?n.match(/^(\s*)/)[0].length:0;if(u&&/<!\[CDATA\[/.test(e))return 0;for(r&&/^<\//.test(e)&&(r=r.prev);r&&!r.startOfLine;)r=r.prev;return r?r.indent+i:0},compareStates:function(t,e){if(t.indented!=e.indented||t.tokenize!=e.tokenize)return!1;for(var n=t.context,r=e.context;;n=n.prev,r=r.prev){if(!n||!r)return n==r;if(n.tagName!=r.tagName)return!1}},electricChars:"/"}}),CodeMirror.defineMIME("application/xml","xml"),CodeMirror.defineMIME("text/html",{name:"xml",htmlMode:!0}),CodeMirror.defineMode("javascript",function(t,e){var n,r,i,o,l,a=t.indentUnit,s=e.json,u=(n=f("keyword a"),r=f("keyword b"),i=f("keyword c"),o=f("operator"),{if:n,while:n,with:n,else:r,do:r,try:r,finally:r,return:i,break:i,continue:i,new:i,delete:i,throw:i,var:f("var"),const:f("var"),let:f("var"),function:f("function"),catch:f("catch"),for:f("for"),switch:f("switch"),case:f("case"),default:f("default"),in:o,typeof:o,instanceof:o,true:l={type:"atom",style:"atom"},false:l,null:l,undefined:l,NaN:l,Infinity:l});function f(t){return{type:t,style:"keyword"}}var c,h,d=/[+\-*&%=<>!?|]/;function p(t,e,n){return(e.tokenize=n)(t,e)}function m(t,e){for(var n,r=!1;null!=(n=t.next());){if(n==e&&!r)return;r=!r&&"\\"==n}return r}function g(t,e,n){return c=t,h=n,e}function v(t,e){var n=t.next();if('"'==n||"'"==n)return p(t,e,(r=n,function(t,e){return m(t,r)||(e.tokenize=v),g("string","string")}));if(/[\[\]{}\(\),;\:\.]/.test(n))return g(n);if("0"==n&&t.eat(/x/i))return t.eatWhile(/[\da-f]/i),g("number","number");if(/\d/.test(n))return t.match(/^\d*(?:\.\d*)?(?:[eE][+\-]?\d+)?/),g("number","number");if("/"==n)return t.eat("*")?p(t,e,x):t.eat("/")?(t.skipToEnd(),g("comment","comment")):e.reAllowed?(m(t,"/"),t.eatWhile(/[gimy]/),g("regexp","string")):(t.eatWhile(d),g("operator",null,t.current()));if("#"==n)return t.skipToEnd(),g("error","error");if(d.test(n))return t.eatWhile(d),g("operator",null,t.current());t.eatWhile(/[\w\$_]/);var r,i=t.current(),o=u.propertyIsEnumerable(i)&&u[i];return o&&e.kwAllowed?g(o.type,o.style,i):g("variable","variable",i)}function x(t,e){for(var n,r=!1;n=t.next();){if("/"==n&&r){e.tokenize=v;break}r="*"==n}return g("comment","comment")}var y={atom:!0,number:!0,variable:!0,string:!0,regexp:!0};function k(t,e,n,r,i,o){this.indented=t,this.column=e,this.type=n,this.prev=i,this.info=o,null!=r&&(this.align=r)}function C(t,e){for(var n=t.localVars;n;n=n.next)if(n.name==e)return 1}var w={state:null,column:null,marked:null,cc:null};function M(){for(var t=arguments.length-1;0<=t;t--)w.cc.push(arguments[t])}function b(){return M.apply(null,arguments),!0}function S(t){var e=w.state;if(e.context){w.marked="def";for(var n=e.localVars;n;n=n.next)if(n.name==t)return;e.localVars={name:t,next:e.localVars}}}var L={name:"this",next:{name:"arguments"}};function A(){w.state.context||(w.state.localVars=L),w.state.context={prev:w.state.context,vars:w.state.localVars}}function z(){w.state.localVars=w.state.context.vars,w.state.context=w.state.context.prev}function T(e,n){function t(){var t=w.state;t.lexical=new k(t.indented,w.stream.column(),e,null,t.lexical,n)}return t.lex=!0,t}function E(){var t=w.state;t.lexical.prev&&(")"==t.lexical.type&&(t.indented=t.lexical.indented),t.lexical=t.lexical.prev)}function W(e){return function(t){return t==e?b():";"==e?M():b(arguments.callee)}}function N(t){return"var"==t?b(T("vardef"),V,W(";"),E):"keyword a"==t?b(T("form"),D,N,E):"keyword b"==t?b(T("form"),N,E):"{"==t?b(T("}"),B,E):";"==t?b():"function"==t?b(_):"for"==t?b(T("form"),W("("),T(")"),F,W(")"),E,N,E):"variable"==t?b(T("stat"),O):"switch"==t?b(T("form"),D,T("}","switch"),W("{"),B,E,E):"case"==t?b(D,W(":")):"default"==t?b(W(":")):"catch"==t?b(T("form"),A,W("("),q,W(")"),N,E,z):M(T("stat"),D,W(";"),E)}function D(t){return y.hasOwnProperty(t)?b(I):"function"==t?b(_):"keyword c"==t?b(H):"("==t?b(T(")"),D,W(")"),E,I):"operator"==t?b(D):"["==t?b(T("]"),U(D,"]"),E,I):"{"==t?b(T("}"),U(P,"}"),E,I):b()}function H(t){return t.match(/[;\}\)\],]/)?M():M(D)}function I(t,e){return"operator"==t&&/\+\+|--/.test(e)?b(I):"operator"==t?b(D):";"!=t?"("==t?b(T(")"),U(D,")"),E,I):"."==t?b(R,I):"["==t?b(T("]"),D,W("]"),E,I):void 0:void 0}function O(t){return":"==t?b(E,N):M(I,W(";"),E)}function R(t){if("variable"==t)return w.marked="property",b()}function P(t){if("variable"==t&&(w.marked="property"),y.hasOwnProperty(t))return b(W(":"),D)}function U(e,n){function r(t){return","==t?b(e,r):t==n?b():b(W(n))}return function(t){return t==n?b():M(e,r)}}function B(t){return"}"==t?b():M(N,B)}function V(t,e){return"variable"==t?(S(e),b(K)):b()}function K(t,e){return"="==e?b(D,K):","==t?b(V):void 0}function F(t){return"var"==t?b(V,G):";"!=t&&"variable"==t?b(j):M(G)}function j(t,e){return"in"==e?b(D):b(I,G)}function G(t,e){return";"==t?b(Y):"in"==e?b(D):b(D,W(";"),Y)}function Y(t){")"!=t&&b(D)}function _(t,e){return"variable"==t?(S(e),b(_)):"("==t?b(T(")"),A,U(q,")"),E,N,z):void 0}function q(t,e){if("variable"==t)return S(e),b()}return E.lex=!0,{startState:function(t){return{tokenize:v,reAllowed:!0,kwAllowed:!0,cc:[],lexical:new k((t||0)-a,0,"block",!1),localVars:null,context:null,indented:0}},token:function(t,e){if(t.sol()&&(e.lexical.hasOwnProperty("align")||(e.lexical.align=!1),e.indented=t.indentation()),t.eatSpace())return null;var n=e.tokenize(t,e);return"comment"==c?n:(e.reAllowed="operator"==c||"keyword c"==c||c.match(/^[\[{}\(,;:]$/),e.kwAllowed="."!=c,function(t,e,n,r,i){var o=t.cc;for(w.state=t,w.stream=i,w.marked=null,w.cc=o,t.lexical.hasOwnProperty("align")||(t.lexical.align=!0);;){if((o.length?o.pop():s?D:N)(n,r)){for(;o.length&&o[o.length-1].lex;)o.pop()();return w.marked?w.marked:"variable"==n&&C(t,r)?"variable-2":e}}}(e,n,c,h,t))},indent:function(t,e){if(t.tokenize!=v)return 0;var n=e&&e.charAt(0),r=t.lexical,i=r.type,o=n==i;return"vardef"==i?r.indented+4:"form"==i&&"{"==n?r.indented:"stat"==i||"form"==i?r.indented+a:"switch"!=r.info||o?r.align?r.column+(o?0:1):r.indented+(o?0:a):r.indented+(/^(?:case|default)\b/.test(e)?a:2*a)},electricChars:":{}"}}),CodeMirror.defineMIME("text/javascript","javascript"),CodeMirror.defineMIME("application/json",{name:"javascript",json:!0}),CodeMirror.defineMode("css",function(t){var i,r=t.indentUnit;function o(t,e){return i=e,t}function l(t,e){var i,n=t.next();return"@"==n?(t.eatWhile(/[\w\\\-]/),o("meta",t.current())):"/"==n&&t.eat("*")?(e.tokenize=a)(t,e):"<"==n&&t.eat("!")?(e.tokenize=s)(t,e):"="!=n?"~"!=n&&"|"!=n||!t.eat("=")?'"'==n||"'"==n?(e.tokenize=(i=n,function(t,e){for(var n,r=!1;null!=(n=t.next())&&(n!=i||r);)r=!r&&"\\"==n;return r||(e.tokenize=l),o("string","string")}),e.tokenize(t,e)):"#"==n?(t.eatWhile(/[\w\\\-]/),o("atom","hash")):"!"==n?(t.match(/^\s*\w*/),o("keyword","important")):/\d/.test(n)?(t.eatWhile(/[\w.%]/),o("number","unit")):/[,.+>*\/]/.test(n)?o(null,"select-op"):/[;{}:\[\]]/.test(n)?o(null,n):(t.eatWhile(/[\w\\\-]/),o("variable","variable")):o(null,"compare"):void o(null,"compare")}function a(t,e){for(var n,r=!1;null!=(n=t.next());){if(r&&"/"==n){e.tokenize=l;break}r="*"==n}return o("comment","comment")}function s(t,e){for(var n,r=0;null!=(n=t.next());){if(2<=r&&">"==n){e.tokenize=l;break}r="-"==n?r+1:0}return o("comment","comment")}return{startState:function(t){return{tokenize:l,baseIndent:t||0,stack:[]}},token:function(t,e){if(t.eatSpace())return null;var n=e.tokenize(t,e),r=e.stack[e.stack.length-1];return"hash"==i&&"rule"==r?n="atom":"variable"==n&&("rule"==r?n="number":r&&"@media{"!=r||(n="tag")),"rule"==r&&/^[\{\};]$/.test(i)&&e.stack.pop(),"{"==i?"@media"==r?e.stack[e.stack.length-1]="@media{":e.stack.push("{"):"}"==i?e.stack.pop():"@media"==i?e.stack.push("@media"):"{"==r&&"comment"!=i&&e.stack.push("rule"),n},indent:function(t,e){var n=t.stack.length;return/^\}/.test(e)&&(n-="rule"==t.stack[t.stack.length-1]?2:1),t.baseIndent+n*r},electricChars:"}"}}),CodeMirror.defineMIME("text/css","css"),CodeMirror.defineMode("htmlmixed",function(t,e){var r=CodeMirror.getMode(t,{name:"xml",htmlMode:!0}),i=CodeMirror.getMode(t,"javascript"),o=CodeMirror.getMode(t,"css");function n(t,e){var n=r.token(t,e.htmlState);return"tag"==n&&">"==t.current()&&e.htmlState.context&&(/^script$/i.test(e.htmlState.context.tagName)?(e.token=a,e.localState=i.startState(r.indent(e.htmlState,"")),e.mode="javascript"):/^style$/i.test(e.htmlState.context.tagName)&&(e.token=s,e.localState=o.startState(r.indent(e.htmlState,"")),e.mode="css")),n}function l(t,e,n){var r=t.current(),i=r.search(e);return-1<i&&t.backUp(r.length-i),n}function a(t,e){return t.match(/^<\/\s*script\s*>/i,!1)?(e.token=n,e.curState=null,e.mode="html",n(t,e)):l(t,/<\/\s*script\s*>/,i.token(t,e.localState))}function s(t,e){return t.match(/^<\/\s*style\s*>/i,!1)?(e.token=n,e.localState=null,e.mode="html",n(t,e)):l(t,/<\/\s*style\s*>/,o.token(t,e.localState))}return{startState:function(){return{token:n,localState:null,mode:"html",htmlState:r.startState()}},copyState:function(t){if(t.localState)var e=CodeMirror.copyState(t.token==s?o:i,t.localState);return{token:t.token,localState:e,mode:t.mode,htmlState:CodeMirror.copyState(r,t.htmlState)}},token:function(t,e){return e.token(t,e)},indent:function(t,e){return t.token==n||/^\s*<\//.test(e)?r.indent(t.htmlState,e):t.token==a?i.indent(t.localState,e):o.indent(t.localState,e)},compareStates:function(t,e){return r.compareStates(t.htmlState,e.htmlState)},electricChars:"/{}:"}}),CodeMirror.defineMIME("text/html","htmlmixed");