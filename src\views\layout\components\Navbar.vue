<template>
  <div>
    <el-menu class="navbar" mode="horizontal">
      <!-- <div class="jlbLogo" v-if="JlbInfo.customName && JlbInfo.logoEnable">
      {{ JlbInfo.customName }}的会员系统 !
    </div> -->
      <div class="left-menu">
        <img src="@/assets/pic_logo2.png" style="width: 145.14px; height: 36px; margin-left: 16px; margin-right: 50px" />
        <el-button
          v-for="btnItem in aggregationModuleList"
          :key="btnItem.path"
          class="aggregation-module-btn"
          :class="{ 'aggregation-module-btn-active': $aggregationModule.path === btnItem.path }"
          @click="handleAggregationModuleClick(btnItem)"
        >
          {{ btnItem.name }}
        </el-button>
      </div>
      <div class="right-menu" :style="this.screenUWidth < 460 ? 'fontSize:12px;' : 'fontSize:16px;'">
        <!-- <div style="display: inline-block; float: left; margin-right: 10px" v-if="checkPermission(['School', 'Operations', 'zxBrand'])">
        <span>剩余可用合同数：</span>
        <el-tag style="margin-right: 10px">{{ contractNum }}个</el-tag>
        <el-button type="primary" size="mini" @click="goBuy">去购买</el-button>
         </div> -->

        <!-- <div style="display: flex; justify-content: center; align-items: center; float: left; margin-right: 10px" v-if="checkPermission(['Operations'])">
        <span>剩余学习管理系统数:</span>
        <el-tag>{{ systemNumber }}个</el-tag>
        <div class="tooltip-box" v-if="this.roleTag !== 'Operations'">
          <el-tooltip content="剩余学习管理系统数-采购未发货预扣除数=实际剩余数" placement="bottom" effect="dark">
            <el-button type="text" class="show-box"></el-button>
          </el-tooltip>
          <img class="img" src="../../../assets/images/studyRoom/question.png" style="width: 30px" alt="" />
        </div>
        </div> -->

        <!-- <error-log v-if="this.arrears == true" class="errLog-container right-menu-item">您的账户余额不足</error-log>
        <div v-if="this.roleTag !== 'School' && this.roleTag !== 'Agent' && this.roleTag !== 'Operations'" style="display: inline-block; float: left">
          <span>账户余额：</span>
          <el-tag style="margin-right: 10px">￥{{ count }}</el-tag>
        </div> -->
        <!-- <div v-if="this.roleTag === 'Operations'" style="display: inline-block; float: left">
          <span>可开通合伙人名额：</span>
          <el-tag style="margin-right: 10px">{{ operationsFreeSchoolNum }}个</el-tag>
        </div> -->

        <!-- <div
          v-if="this.roleTag === 'School' || this.roleTag === 'Operations' || this.roleTag === 'admin'"
          style="display: inline-block; float: left; margin-right: 20px; position: relative"
          @click="update"
        >
          <span>消息</span>
          <i class="el-icon-chat-dot-square" style="font-size: 22px; margin-left: 5px"></i>
          <el-badge
            :value="newsNum"
            :max="99"
            class="item"
            style="margin-top: 10px; position: absolute; top: -20px; right: -10px"
            v-if="newsNum > 0 && this.roleTag !== 'admin'"
          ></el-badge>
        </div> -->

        <!-- <div v-if="this.roleTag === 'School' && this.currentAdmin.schoolType !== 3" style="display: inline-block; float: left">
          <span>剩余学时：</span>
          <el-tag style="margin-right: 0px">{{ count }}</el-tag>
        </div> -->
        <!-- <div v-if="this.roleTag === 'School' && this.currentAdmin.schoolType === 3" style="display: inline-block; float: left">
          <span>到期时间：</span>
          <el-tag style="margin-right: 0px">{{ this.currentAdmin.expiryTime }}</el-tag>
        </div> -->
        <!-- <el-tooltip content="Global Size" class="sizeSelect" v-if="this.screenUWidth > 362" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->
        <!-- <div style="display: inline-block; float: left">
          <span>帐号：</span>
          <el-tag style="margin-right: 10px">{{ name }}</el-tag> -->
        <!-- <span>角色：</span>
          <el-tag style="margin-right: 5px;" type="danger" v-if="roles.length==0" >游客（未配置任何角色）</el-tag>
          <el-tag style="margin-right: 5px;" type="success" v-else v-for="r in roles" :key="r.val">{{r.name}}</el-tag> -->
        <!-- </div> -->
        <el-dropdown class="avatar-container" style="margin: 26px 37px 0 8px" @command="mergeMethod" trigger="click" placement="bottom">
          <div class="avatar-wrapper" style="cursor: pointer">
            <div>{{ userName }}</div>
            <i class="el-icon-caret-bottom"></i>
          </div>
          <el-dropdown-menu slot="dropdown" :append-to-body="false" style="min-width: 179px">
            <el-dropdown-item command="goToRegister" style="text-align: center; color: #73bf8a; font-size: 14px; height: 35px; line-height: 35px">
              <span>{{ isZxBrand ? '俱乐部注册码' : isOperations ? '合伙人注册码' : '首页' }}</span>
            </el-dropdown-item>
            <el-dropdown-item command="userInfo" style="text-align: center; color: #73bf8a; font-size: 14px; height: 35px; line-height: 35px">
              <span style="display: block">用户信息</span>
            </el-dropdown-item>
            <el-dropdown-item command="handleUpdatePwd" style="text-align: center; color: #73bf8a; font-size: 14px; height: 35px; line-height: 35px">
              <span style="display: block">修改密码</span>
            </el-dropdown-item>
            <el-dropdown-item command="logout" style="text-align: center; color: #ea8a32; height: 35px; line-height: 35px">
              <span style="display: block">退出</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </el-menu>
    <!--弹出窗口：修改密码-->
    <el-dialog :append-to-body="true" :close-on-click-modal="false" :close-on-press-escape="false" title="修改密码" :visible.sync="dialogVisible" width="60%">
      <el-form :rules="rules" ref="dataForm" :model="temp" label-position="left" label-width="120px" class="updatePwd">
        <el-form-item label="手机号" prop="mobile" style="width: 80%">
          <el-input v-model="temp.mobile" placeholder="请输入手机号" disabled></el-input>
        </el-form-item>

        <el-form-item label="密码" prop="newPwd" style="width: 80%">
          <el-input :type="passwordNewType" v-model="temp.newPwd" placeholder="请输入密码">
            <template #suffix>
              <span class="svg-container-end" @click="showNewPwd">
                <svg-icon :icon-class="passwordNewType == 'password' ? 'eye' : 'eye-open'" />
              </span>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPwd" style="width: 80%">
          <el-input :type="passwordComType" v-model="temp.confirmPwd" placeholder="请再次输入密码">
            <template #suffix>
              <span class="svg-container-end" @click="showComPwd">
                <svg-icon :icon-class="passwordComType == 'password' ? 'eye' : 'eye-open'" />
              </span>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="验证码" prop="smsCode" style="width: 80%">
          <div style="display: flex; justify-content: center">
            <el-input placeholder="请输入验证码" v-model="temp.smsCode" style="flex: 1"></el-input>
            <el-button type="primary" @click.stop="getSmsClick()" :disabled="disabledSmsClick">{{ countdown }}</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-tip">温馨提示:</div>
      <div class="dialog-tip">1:新密码长度不少于8位,需包含字母、数字;</div>
      <div class="dialog-tip">2:如果您的登录号码收不到验证码，请联系渠道经理修改登录手机号;</div>
      <div slot="footer" class="dialog-pwd" style="text-align: center">
        <el-button @click="dialogVisible = false" size="medium">取消</el-button>
        <el-button type="primary" @click="updatePwd" size="medium" style="margin-left: 50px">确定</el-button>
      </div>
    </el-dialog>
    <!--弹出窗口：续费提醒-->
    <el-dialog title="提示" :visible.sync="renewDialogVisible" width="30%">
      <span slot="title">
        <i class="el-icon-info" style="color: #f56c6c; font-size: 26px; margin-right: 5px; vertical-align: middle"></i>
        提示
      </span>
      <div style="display: flex">
        <span style="line-height: 24px; padding: 0 30px">
          尊敬的超级合伙人，您的账户即将到期，未及时续费将失去推广权益与被动收益，但不会影响您已获得的收益和奖励。学员新开户和充值权限将被收回，合作期内已充值学员不受影响。
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="renewDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <Verify ref="verify" :captcha-type="'blockPuzzle'" :img-size="{ width: '375px', height: '200px' }" @success="sendSmg" style="z-index: 9999 !important" />
    <purchaseContract ref="spurchaseDialogVisible" style="z-index: 9999 !important"></purchaseContract>
    <!-- <el-dialog title="修改密码" :visible.sync="dialogVisible" width="70%">
      <el-form :rules="rules" ref="dataForm" :model="temp" label-position="left" label-width="120px">

        <el-form-item label="密码" prop="pwd">
          <el-input type="password" v-model="temp.pwd"></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="pwd2">
          <el-input type="password" v-model="temp.pwd2"></el-input>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updatePwd">确定</el-button>
      </div>
    </el-dialog> -->
    <registrationCode ref="registrationCode"></registrationCode>
  </div>
</template>

<script>
  import Verify from '@/components/verifition/Verify';
  import { mapGetters } from 'vuex';
  import userApi from '@/api/user';
  import SizeSelect from '@/components/SizeSelect';
  import authenticationApi from '@/api/authentication';
  import schoolList from '@/api/schoolList';
  import forgotApi from '@/api/forgot';
  import schoolApi from '@/api/schoolList';
  import newsApi from '@/api/news';
  import Middle from '@/api/newUtil';
  import areaSchoolApi from '@/api/areasSchoolList';
  import checkPermission from '@/utils/permission';
  import { dxSource } from '@/utils/constants';
  import store from '@/store';
  import purchaseContract from '@/components/purchaseContract/index.vue';
  import registrationCode from './registrationCode.vue';
  // import { v4 as uuidv4 } from 'uuid';
  export default {
    data() {
      let validatePass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入密码'));
        } else if (value.length < 6) {
          callback(new Error('密码长度不得小于6位'));
        } else {
          if (this.temp.confirmPwd !== '') {
            this.$refs.dataForm.validateField('confirmPwd');
          }
          callback();
        }
      };

      let validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value != this.temp.newPwd) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      return {
        showAggregationModuleList: ['/znyyManagement', '/trainingCenter', '/userRole', '/courseManagement'],
        newsNum: '', // 未读数量
        operationsFreeSchoolNum: 0,
        currentAdmin: '',
        //屏幕宽度
        screenUWidth: 0,
        count: 0,
        roleTag: '',
        dialogVisible: false,
        renewDialogVisible: false,
        arrears: false,
        temp: {
          mobile: '',
          newPwd: '',
          confirmPwd: '',
          smsCode: '',
          source: 'admin',
          sysType: 1
        },
        rules: {
          mobile: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            {
              required: true,
              pattern: /^1[3456789]\d{9}$/,
              message: '手机号格式不正确',
              trigger: 'blur'
            }
          ],
          newPwd: [
            { required: true, message: '请输入密码', trigger: 'blur' },
            { validator: validatePass, trigger: 'blur' }
          ],
          confirmPwd: [
            { required: true, message: '请输入确认密码', trigger: 'blur' },
            { validator: validatePass2, trigger: 'change' }
          ],
          smsCode: [
            { required: true, message: '请输入验证码', trigger: 'blur' },
            { message: '验证码不得小于6位', trigger: 'blur' }
          ]
        },
        countdown: '获取验证码',
        disabledSmsClick: false,
        token: store.getters.token,
        passwordNewType: 'password',
        passwordComType: 'password',
        isOperations: false, // 是否是俱乐部
        isZxBrand: false // 是否是超级品牌
      };
    },
    //ErrorLog,
    components: {
      SizeSelect,
      Verify,
      purchaseContract,
      registrationCode
    },
    computed: {
      ...mapGetters(['name', 'merchantName', 'avatar', 'roles', 'JlbInfo', 'contractNum', 'systemNumber', 'setpayUrl', 'permission_routers']),
      aggregationModuleList() {
        const list = [];
        this.permission_routers.forEach((each) => {
          if (this.showAggregationModuleList.includes(each.path)) {
            list.push({ name: each.meta.title, perm: each.meta.perm, path: each.path, redirect: each.meta.redirect });
          }
        });
        return list;
      },
      userName() {
        return this.merchantName ? this.merchantName : this.name === 'dxadmin' ? '总管理员' : this.name;
      }
    },
    mounted() {
      this.$store.dispatch('getContract');
      this.$store.dispatch('getSystem');
      this.getNewsnum();
      this.getRoleTag();
      this.selectData(); // 页面加载时自动触发权限检查
      const that = this;
      that.searchFormWidth();
      window.onresize = () => {
        if (!that.timer) {
          // 使用节流机制，降低函数被触发的频率
          that.timer = true;
          setTimeout(function () {
            that.$forceUpdate();
            that.searchFormWidth();
            that.timer = false;
          }, 400);
        }
      };

      Middle.$on('methodA', (data) => {
        that.getNewsnum();
      });

      authenticationApi.checkAccountBalance().then((res) => {
        this.count = res.data.data.money;
        this.roleTag = res.data.data.roleTag;
        this.operationsFreeSchoolNum = res.data.data.operationsFreeSchoolNum;
        localStorage.setItem('roleTag', this.roleTag);
        localStorage.setItem('operationsFreeSchoolNum', this.operationsFreeSchoolNum);
        if (this.roleTag == 'ExpertsFill') {
          if (Number(this.count) > 0) {
            this.arrears = false;
          } else {
            this.arrears = true;
          }
        }
      });
    },

    methods: {
      handleAggregationModuleClick(btnItem) {
        this.$router.push(btnItem.redirect);
      },
      mergeMethod(val) {
        switch (val) {
          case 'goToRegister':
            this.goToRegister(1);
            break;
          case 'userInfo':
            this.goToUserInfo();
            break;
          case 'handleUpdatePwd':
            this.handleUpdatePwd();
            break;
          default:
            this.logout();
            break;
        }
      },
      checkPermission,
      selectData() {
        this.isOperations = checkPermission(['Operations']); // 是否超级俱乐部 获取列表数据
        this.isZxBrand = checkPermission(['zxBrand']); // 超级品牌
      },
      // 拼图成功的事件
      onSuccess(left) {
        // left为滑块移动距离，**调用后端接口把移动距离传给后端，后端进行校验是否验证成功**
      },
      // 拼图失败的事件
      onFail() {},
      // 刷新拼图的事件
      onRefresh() {},
      goBuy() {
        this.$refs.spurchaseDialogVisible.open();
        // this.$store.commit('SET_CONTRACTNUM', 8);
      },
      update() {
        let that = this;
        if (that.roleTag === 'admin') {
          that.$router.push({
            path: '/news/sendNews'
          });
        } else {
          that.$router.push({
            path: '/news/list'
          });
        }
      },
      getNewsnum() {
        newsApi.NoticeNum().then((res) => {
          this.newsNum = res.data;
          this.$forceUpdate();
        });
      },

      getRoleTag() {
        schoolList.getCurrentAdmin().then((res) => {
          this.currentAdmin = res.data;
          let needRenew = localStorage.getItem('needRenew');
          if (localStorage.getItem('needRenew') != undefined && needRenew) {
            this.renewDialogVisible = false;
            return;
          }
          //是否开启续费弹窗
          if (this.currentAdmin.needRenew != null && this.currentAdmin.needRenew && this.currentAdmin.roleTag != 'admin' && this.currentAdmin.schoolType == 3) {
            this.renewDialogVisible = true;
            localStorage.setItem('needRenew', true);
          }
        });
      },
      searchFormWidth() {
        this.screenUWidth = window.innerWidth;
      },
      logout() {
        // 清空token
        this.$store.dispatch('LogOut').then(() => {
          localStorage.removeItem('needRenew');
          location.reload(); // In order to re-instantiate the vue-router object to avoid bugs
        });
      },
      handleUpdatePwd() {
        this.getPhoneNum();
        this.dialogVisible = true;
        this.dialogVisible = true;
        this.temp.newPwd = '';
        this.temp.confirmPwd = '';
        this.temp.smsCode = '';
        clearInterval(this.timer);
        this.timer = null;
        this.disabledSmsClick = false;
        this.countdown = '获取验证码';
        // this.$nextTick(() =>
        //   this.$refs['dataForm'].clearValidate()
        // )
      },

      updatePwd() {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) return;
          const tempData = Object.assign({}, this.temp); //copy obj
          forgotApi.pswReset(tempData).then((res) => {
            this.dialogVisible = false;
            this.$message.success('更新密码成功');
          });
        });
      },

      //完款  1 全额  2 成本  3免费
      allGone(id, goneType, renew) {
        if (goneType === 3) {
          this.$confirm('此操作将消耗您一个免费名额给该门店开通或续费一年, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              areaSchoolApi.allGone(id, goneType, renew).then((res) => {
                if (renew) {
                  this.$message.success('操作成功');
                  this.fetchData();
                } else {
                  this.$message.success('操作成功');
                  this.fetchData();
                }
                if (this.currentAdmin.needRenew) {
                  this.renewDialogVisible = false;
                }
              });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消操作'
              });
            });
          return;
        }
        areaSchoolApi.allGone(id, goneType, renew).then((res) => {
          if (checkPermission(['b:merchant:OperationsVersion'])) {
            const split = dxSource.split('##');
            res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
            let params = JSON.stringify(res.data);
            let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
            //需要编码两遍，避免出现+号等
            var encode = Base64.encode(Base64.encode(req));
            window.open(this.setpayUrl + 'product?' + encode, '_blank');
            // window.open("http://192.168.5.35:8000/product?" + encode, "_blank");
          } else {
            this.$message.success('完款成功');
            this.fetchData();
            if (this.currentAdmin.needRenew) {
              this.renewDialogVisible = false;
            }
          }
        });
      },

      // 获取当前登录用户手机号
      getPhoneNum() {
        userApi.getPhone().then((res) => {
          console.log(res);
          this.temp.mobile = res.data.phone;
        });
      },

      //发送验证码
      getSmsClick() {
        if (this.temp.newPwd == '' || this.temp.confirmPwd == '') {
          this.$message.error('请输入密码');
          return;
        }
        this.sendSmg();
      },
      sendSmg() {
        forgotApi.sendSmg(this.temp.mobile).then((res) => {
          console.log(res);
          this.$message.success('短信验证码发送成功，请注意查收');
          var num = 60;
          this.disabledSmsClick = true;
          if (!this.timer) {
            this.timer = setInterval(() => {
              if (num > 0) {
                num--;
                this.countdown = num + 's';
              } else {
                clearInterval(this.timer);
                this.timer = null;
                this.countdown = '重新获取验证码';
                this.disabledSmsClick = false;
              }
            }, 1000);
          }
        });
      },
      // 修改密码
      confirm() {
        // var submitData = {
        //   mobile : that.mobile,
        //   newPwd : that.newPwd,
        //   smsCode : that.smsCode,
        //   source : 'admin'
        // }
        forgotApi.pswReset(this.temp).then((res) => {
          if (res) {
            this.$message.success('密码修改成功');
            this.dialogVisible = false;
          }
        });
      },
      showNewPwd() {
        if (this.temp.newPwd == '') return;
        if (this.passwordNewType === 'password') {
          this.passwordNewType = '';
        } else {
          this.passwordNewType = 'password';
        }
      },
      showComPwd() {
        if (this.temp.confirmPwd == '') return;
        if (this.passwordComType === 'password') {
          this.passwordComType = '';
        } else {
          this.passwordComType = 'password';
        }
      },

      closeDialog() {
        this.temp.newPwd = '';
        this.temp.confirmPwd = '';
        this.temp.smsCode = '';
        this.countdown = '获取验证码';
      },
      // 注册码跳转
      goToRegister(type) {
        if (this.isZxBrand || this.isOperations) {
          const userInfo = {
            isZxBrand: this.isZxBrand,
            isOperations: this.isOperations
          };
          this.$refs.registrationCode.openDialogVisible(userInfo);
          this.$refs.registrationCode.getQR(userInfo, type, '');
        } else {
          this.$router.push({ path: '/dashboard/dashboard' });
        }
      },
      goToUserInfo() {
        this.$router.push({ path: '/role/system/userInfo' });
      }
    }
  };
</script>

<style rel="stylesheet/scss" lang="scss">
  .updatePwd {
    .el-input__inner {
      height: 100% !important;
    }
    .el-input {
      display: inline-block;
      height: 5vh;
      // width: 85%;
    }

    .el-form-item {
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: #454545;
      margin-bottom: 20px;
    }
  }
  input[type='password']::-ms-reveal,
  input[type='password']::-ms-clear {
    display: none;
  }
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
  .item {
    margin: 4px;
  }

  .tooltip-box {
    position: relative;
  }

  ::v-deep .show-box {
    width: 20px;
    position: absolute;
    /* 绝对定位使按钮相对于父容器定位 */
    top: 50%;
    /* 垂直居中 */
    left: 50%;
    /* 水平居中 */
    transform: translate(-50%, -50%);
    /* 微调位置确保完全居中 */
    background-color: rgba(255, 255, 255, 0);
    /* 半透明背景 */
    border: none;
    /* 无边框 */
    color: transparent;
    /* 文字颜色 */

    /* 内边距 */
    cursor: pointer;
    /* 鼠标悬停时显示手指图标 */
    z-index: 10;
    /* 确保按钮在图片上层 */
  }

  .show-box:hover .el-button__text {
    opacity: 0;
  }

  .img {
    position: relative;
    top: 10px;
  }

  .sizeSelect .svg-icon.size-icon {
    margin-bottom: 10px !important;
  }

  .el-menu.el-menu--horizontal {
    border: none;
  }

  .el-menu::before,
  .el-menu::after {
    display: none;
  }

  .navbar {
    height: 80px;
    line-height: 80px;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    border-radius: 0px !important;
    background: linear-gradient(90deg, #21312b 0%, #2f5144 100%);

    ::v-deep .el-dialog__body {
      padding: 0 20px;
    }

    .jlbLogo {
      position: absolute;
      top: 0;
      left: 50%;
      padding: 0 40px;
      transform: translateX(-100%);
      min-width: 250px;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      border-radius: 0 0 5px 5px;
      text-align: center;
      background: linear-gradient(to bottom, #dc9316, #f4ab68);
      margin: 0 auto;
      color: #fff;
    }

    .errLog-container {
      display: inline-block;
      vertical-align: top;
    }

    .left-menu {
      display: flex;
      padding-top: 26px;

      .aggregation-module-btn {
        height: 36px;
        padding: 6px 36px;
        color: rgba(255, 255, 255, 0.8);
        background: transparent;
        border: none;
        border-radius: 4px;
        font-size: 18px;

        &-active {
          color: #51cc92;
          background: rgba(81, 204, 146, 0.2);
        }
      }
    }

    .right-menu {
      display: flex;
      height: 100%;

      &:focus {
        outline: none;
      }

      .right-menu-item {
        // display: inline-block;
        margin: 0 8px;
        vertical-align: middle;
      }

      .screenfull {
        height: 20px;
      }

      .international {
        vertical-align: top;
      }

      .theme-switch {
        vertical-align: 15px;
      }

      .avatar-container {
        height: 36px;
        display: flex;
        align-items: center;
        // margin-right: 30px;

        .avatar-wrapper {
          width: fit-content;
          line-height: 36px;
          padding: 0 15px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 40px 40px 40px 40px;

          .user-avatar {
            // width: 40px;
            height: 40px;
            border-radius: 10px;
          }

          .el-icon-caret-bottom {
            margin-left: 16px;
            font-size: 12px;
          }
        }
      }
    }
  }
  .svg-container-end {
    position: absolute;
    color: #d7dee3;
    vertical-align: middle;
    display: inline-block;
    margin: 0 -30px;
    margin-top: 5px;
    font-size: 20px;
  }
  .dialog-tip {
    text-align: left;
    color: red;
    font-size: 12px;
    margin-top: 20px;
    margin-left: 50px;
    height: 20px;
  }
  .dialog-pwd {
    margin-top: 20px;
  }
</style>
