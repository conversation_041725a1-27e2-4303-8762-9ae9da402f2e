#app {
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    padding-left: $sideBarWidth;
    padding-top: $navbarWidth + $tagsViewWidth;
    position: relative;
    background-color: #f6f7f9;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: calc(100% - 80px);
    position: fixed;
    font-size: 0px;
    top: 80px;
    bottom: 0;
    left: 0;
    z-index: 2000;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      width: 1.715em;
      height: 1.715em;
      vertical-align: middle;
      margin-right: 12px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      margin: 0 8px;
      border-radius: 4px;
      &:hover {
        background-color: $menuHover !important;
      }
    }

    .is-active > .el-submenu__title {
      color: $menuActiveText !important;
      background-color: $menuActiveBg !important;
      &:hover {
        background-color: $subMenuHover !important;
      }
    }

    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      height: auto;
      line-height: 30px;
      font-size: 14px;
      min-width: auto !important;
      max-width: $sideBarWidth;
      border-radius: 4px;
      margin: 4px 8px;
      padding: 0 10px;
      background-color: $subMenuBg;
      white-space: normal;
    }

    & .el-submenu .el-menu-item:hover:not(.is-active) {
      background-color: $subMenuHover !important;
    }

    .el-menu-item.is-active {
      background-color: $subMenuActiveBg !important;
    }

    @for $i from 0 through 3 {
      .submenu-title-#{$i} {
        padding-left: $i * 20px + 8px !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }
    .tags-view-container {
      margin-left: 54px;
    }
    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 8px;
        }

        .sub-el-icon {
          margin-left: 7px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 8px;
        }

        .sub-el-icon {
          margin-left: 7px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .hideMenu {
    .sidebar-container {
      width: 0px !important;
    }
    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;
    border-radius: 4px;

    .nest-menu {
      .el-menu-item {
        height: auto;
        line-height: 30px;
        border-radius: 4px;
        margin: 4px 8px;
      }
      .is-active {
        background-color: $subMenuActiveBg !important;
      }
    }

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
