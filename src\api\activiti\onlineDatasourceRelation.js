import request from '@/utils/request'

export default {
  //分页
  list(data) {
    return request({
      url: '/activiti/onlineDatasourceRelation/list',
      method: 'GET',
      params: data
    })
  },
  add(data) {
    return request({
      url: '/activiti/onlineDatasourceRelation/add',
      method: 'POST',
      data
    })
  },
  update(data) {
    return request({
      url: '/activiti/onlineDatasourceRelation/update',
      method: 'POST',
      data
    })
  },
  view(data) {
    return request({
      url: '/activiti/onlineDatasourceRelation/view',
      method: 'GET',
      params: data
    })
  },
  delete(data){
    return request({
      url: '/activiti/onlineDatasourceRelation/delete',
      method: 'DELETE',
      params: data
    })
  }
}
