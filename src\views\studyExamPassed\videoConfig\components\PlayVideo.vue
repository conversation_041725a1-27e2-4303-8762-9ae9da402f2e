<template>
  <div class="video-container" id="player"></div>
</template>

<script>
  import { set } from 'nprogress';

  export default {
    props: {
      vid: {
        type: String,
        default: '88083abbf5bcf1356e05d39666be527a_8'
      },
      videoKeyframes: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        vodPlayerJs: 'https://player.polyv.net/resp/vod-player/latest/player.js'
        // videoKeyframes: [
        //   {
        //     // 打点出现时间
        //     keytime: 80,
        //     // 打点提示内容
        //     keycontent: 'test111',
        //     // 打点跳转按钮文案,可选
        //     btnDesc: 'text',
        //     // 打点跳转按钮跳转链接，可选
        //     btnHref: 'https://www.example.com/'
        //   }
        // ]
      };
    },

    mounted() {
      this.loadPlayerScript(this.loadPlayer);
    },

    updated() {
      this.loadPlayerScript(this.loadPlayer);
    },

    methods: {
      // 转换成时分秒
      formatSeconds(value) {
        // 转换 时分秒
        let h = Math.floor(value / 3600);
        let m = Math.floor((value - h * 3600) / 60);
        let s = value - h * 3600 - m * 60;
        h = h < 10 ? '0' + h : h;
        m = m < 10 ? '0' + m : m;
        s = s < 10 ? '0' + s : s;

        return `${h ? h : '00'}:${m ? m : '00'}:${s ? s : '00'}`;
      },
      // 获取视频总时长
      getDuration() {
        let duration = this.player?.j2s_getDuration() || 0;

        let durationText = this.formatSeconds(duration);
        this.$emit('update:duration', durationText);
      },
      // 获取当前播放时长
      getCurrentTime() {
        let keytime = this.player?.j2s_getCurrentTime() || 0;
        keytime = Math.floor(keytime);
        let nodeTime = this.formatSeconds(keytime);
        return {
          nodeTime,
          keytime
        };
      },
      loadPlayerScript(callback) {
        if (!window.polyvPlayer) {
          const myScript = document.createElement('script');
          myScript.setAttribute('src', this.vodPlayerJs);
          myScript.onload = callback;
          document.body.appendChild(myScript);
        } else {
          callback();
        }
      },

      loadPlayer() {
        const polyvPlayer = window.polyvPlayer;
        this.player = polyvPlayer({
          wrap: '#player',
          width: '100%',
          height: '100%',
          vid: this.vid,
          hideSwitchPlayer: true,
          videoKeyframes: this.videoKeyframes,
          history_video_duration: 0, //默认时长超过5分钟的视频才会开启续播功能，可通过此参数修改，单位：分钟。
          ban_history_time: 'off' //当值为’on‘时会禁用续播功能。
        });
        this.player.on('s2j_onPlayerInitOver', () => {
          this.getDuration();
        });
      }
    },
    destroyed() {
      if (this.player) {
        this.player.destroy();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .video-container {
    width: 100%;
    height: 100%;
  }
</style>
