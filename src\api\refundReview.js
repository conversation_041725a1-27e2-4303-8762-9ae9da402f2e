import request from '@/utils/request'

export const getRefundSummaryPage = (data) => {
    return request({
        url: '/znyy/merchant/refund/summary/page',
        method: 'GET',
        params: data
    })
}
export const getRefundQueryPage = (data) => {
    return request({
        url: '/znyy/merchant/refund/query/page',
        method: 'GET',
        params: data
    })
}
export const getRefundQueryDeduction = (data) => {
    return request({
        url: '/znyy/merchant/refund/deduction',
        method: 'POST',
        data
    })
}