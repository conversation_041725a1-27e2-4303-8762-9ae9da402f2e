<!-- 班型题目配置 -->
<template>
  <div class="app-container">
    <div class="redStar">
      <h4>课程大类：</h4>
      <el-form :model="dataQuery" ref="queryForm" :inline="true">
        <el-form-item>
          <el-select v-model="dataQuery.curriculumId" placeholder="请选择" @change="curriculumIdChange">
            <el-option v-for="item in kcdlList" :key="item.id" :value="item.id" :label="item.enName"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="btnBox">
      <el-radio-group v-model="tabPosition" size="medium" @input="tabPositionClick">
        <el-radio-button label="0">学前测配置</el-radio-button>
        <el-radio-button label="4">学后测配置</el-radio-button>
        <el-radio-button label="1">课后习题配置</el-radio-button>
        <el-radio-button label="2">错题带刷</el-radio-button>
        <el-radio-button label="3">学前测试卷配置</el-radio-button>
        <el-radio-button label="5">练一练配置</el-radio-button>
      </el-radio-group>
    </div>
    <div v-if="tabPosition === '5'" class="practice">
      <div style="margin-bottom: 20px">
        <span>单次推送题目数量：</span>
        <el-select v-model="dataQuery.pushQuestionNum" placeholder="请选择">
          <el-option v-for="item in questionNum" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
      <div style="display: flex">
        <p>推题频率：</p>
        <div>
          <p>正式课内的同步课第二天推题,随后</p>
          <p>隔天推送,一共推三次</p>
        </div>
      </div>
      <div style="display: flex">
        <p>推题规则：</p>
        <div>
          <p>根据学生学后测试题目对错情况推题,</p>
          <p>非完全掌握则推原题同难度变式题,完全掌握则推比原题高一级难度的</p>
          <p>变式题;</p>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="redStar" v-if="tabPosition !== '2'">
        <h4>出题方式：</h4>
        <el-radio-group v-model="dataQuery.topicType">
          <el-radio :label="1" v-if="tabPosition === '3'">统一出题</el-radio>
          <el-radio :label="0" v-else>根据分班情况出题</el-radio>
        </el-radio-group>
      </div>
      <div>
        <ErrerProxy ref="errerProxy" v-if="tabPosition === '2'" :topicConfigId="topicConfigId" :curriculumId="dataQuery.curriculumId" />
        <!-- <span>*</span>
        <h4>根据课堂所有学生最近</h4>
        &nbsp;
        <el-input v-model="dataQuery.month" placeholder="请输入" :max="99" :min="1" clearable style="width: 100px"></el-input>
        &nbsp;
        <h4>天题目为基础生成课上习题</h4> -->
      </div>
      <div v-if="tabPosition === '3'">
        <div class="btnClassBox">
          <el-radio-group v-model="tabBasic" size="medium">
            <el-radio-button label="0">单选</el-radio-button>
            <el-radio-button label="1">填空</el-radio-button>
            <el-radio-button label="2">计算</el-radio-button>
            <el-radio-button label="3">解方程</el-radio-button>
            <el-radio-button label="4">证明题</el-radio-button>
            <el-radio-button label="5">几何综合题</el-radio-button>
          </el-radio-group>
        </div>
        <el-form :model="testPaperData" ref="testPaperData" :inline="false" class="formBox">
          <el-form-item label="低难度题目数在总题目数中占比：">
            <el-select v-model="testPaperData.bottomValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="中难度题目数在总题目数中占比：">
            <el-select v-model="testPaperData.middleValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="高难度题目数在总题目数中占比：">
            <el-select v-model="testPaperData.topValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="tabPosition !== '3' && tabPosition !== '2'">
        <div class="redStar">
          <span>*</span>
          <h4>基础班：</h4>
        </div>
        <div class="btnClassBox">
          <el-radio-group v-model="tabBasic" size="medium">
            <el-radio-button label="0">单选</el-radio-button>
            <el-radio-button label="1">填空</el-radio-button>
            <el-radio-button label="2">计算</el-radio-button>
            <el-radio-button label="3">解方程</el-radio-button>
            <el-radio-button label="4">证明题</el-radio-button>
            <el-radio-button label="5">几何综合题</el-radio-button>
          </el-radio-group>
        </div>
        <!-- 学前测配置 -->
        <el-form :model="basicData" ref="basicData" :inline="false" class="formBox" v-if="tabPosition === '0'">
          <el-form-item label="低难度题目数在总题目数中占比：">
            <el-select v-model="basicData.bottomValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="中难度题目数在总题目数中占比：">
            <el-select v-model="basicData.middleValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="高难度题目数在总题目数中占比：">
            <el-select v-model="basicData.topValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <!-- 学后测配置 -->
        <el-form :model="studybasicData" ref="studybasicData" :inline="false" class="formBox" v-if="tabPosition === '4'">
          <el-form-item label="低难度题目数在总题目数中占比：">
            <el-select v-model="studybasicData.bottomValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="中难度题目数在总题目数中占比：">
            <el-select v-model="studybasicData.middleValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="高难度题目数在总题目数中占比：">
            <el-select v-model="studybasicData.topValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <!-- 课后习题配置 -->
        <el-form :model="basicDataAfter" ref="basicDataAfter" :inline="false" class="formBox" v-if="tabPosition === '1'">
          <el-form-item label="低难度题目数在总题目数中占比：">
            <el-select v-model="basicDataAfter.bottomValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="中难度题目数在总题目数中占比：">
            <el-select v-model="basicDataAfter.middleValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="高难度题目数在总题目数中占比：">
            <el-select v-model="basicDataAfter.topValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <!-- 基础班 错题带刷 -->
        <!-- <el-form :model="errorBasicData" ref="errorBasicData" :inline="false" class="formBox" v-if="tabPosition === '2'">
          <el-form-item label="单选题总数：">
            <el-select v-model="errorBasicData.countValue" placeholder="请选择">
              <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="低难度题目数：">
            <el-select v-model="errorBasicData.bottomValue" placeholder="请选择">
              <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="中难度题目数：">
            <el-select v-model="errorBasicData.middleValue" placeholder="请选择">
              <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="高难度题目数：">
            <el-select v-model="errorBasicData.topValue" placeholder="请选择">
              <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form> -->
      </div>
      <div v-if="tabPosition !== '3' && tabPosition !== '2'">
        <div class="redStar">
          <span>*</span>
          <h4>拔高班：</h4>
        </div>
        <div class="btnClassBox">
          <el-radio-group v-model="tabDifficult" size="medium">
            <el-radio-button label="0">单选</el-radio-button>
            <el-radio-button label="1">填空</el-radio-button>
            <el-radio-button label="2">计算</el-radio-button>
            <el-radio-button label="3">解方程</el-radio-button>
            <el-radio-button label="4">证明题</el-radio-button>
            <el-radio-button label="5">几何综合题</el-radio-button>
          </el-radio-group>
        </div>
        <!-- 学前测配置 -->
        <el-form :model="difficultData" ref="difficultData" :inline="false" class="formBox" v-if="tabPosition === '0'">
          <el-form-item label="低难度题目数在总题目数中占比：">
            <el-select v-model="difficultData.bottomValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="中难度题目数在总题目数中占比：">
            <el-select v-model="difficultData.middleValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="高难度题目数在总题目数中占比：">
            <el-select v-model="difficultData.topValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <!-- 学后测配置 -->
        <el-form :model="studyDifficultData" ref="studyDifficultData" :inline="false" class="formBox" v-if="tabPosition === '4'">
          <el-form-item label="低难度题目数在总题目数中占比：">
            <el-select v-model="studyDifficultData.bottomValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="中难度题目数在总题目数中占比：">
            <el-select v-model="studyDifficultData.middleValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="高难度题目数在总题目数中占比：">
            <el-select v-model="studyDifficultData.topValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <!-- 课后习题配置 -->
        <el-form :model="difficultDataAfter" ref="difficultDataAfter" :inline="false" class="formBox" v-if="tabPosition === '1'">
          <el-form-item label="低难度题目数在总题目数中占比：">
            <el-select v-model="difficultDataAfter.bottomValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="中难度题目数在总题目数中占比：">
            <el-select v-model="difficultDataAfter.middleValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="高难度题目数在总题目数中占比：">
            <el-select v-model="difficultDataAfter.topValue" placeholder="请选择">
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <!-- 拔高班 错题带刷 -->
        <!-- <el-form :model="errorDifficultData" ref="errorDifficultData" :inline="false" class="formBox" v-if="tabPosition === '2'">
          <el-form-item label="单选题总数：">
            <el-select v-model="errorDifficultData.countValue" placeholder="请选择">
              <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="低难度题目数：">
            <el-select v-model="errorDifficultData.bottomValue" placeholder="请选择">
              <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="中难度题目数：">
            <el-select v-model="errorDifficultData.middleValue" placeholder="请选择">
              <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="高难度题目数：">
            <el-select v-model="errorDifficultData.topValue" placeholder="请选择">
              <el-option v-for="item in errgradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form> -->
      </div>
    </div>
    <div class="footerBtnBox">
      <el-button v-if="tabPosition === '5'" type="info" size="medium" @click="search">取 消</el-button>

      <el-button type="primary" size="medium" @click="submit" :loading="butLoading">保 存</el-button>
    </div>
  </div>
</template>
<script>
  import ErrerProxy from '@/views/_aaa_demo/topicOfClassConfig/components/errerProxy.vue';
  import forStudent from '@/api/testPaper/management';
  export default {
    name: 'topicOfClassConfig',
    components: {
      ErrerProxy
    },
    data() {
      return {
        butLoading: false,
        topicConfigId: null,

        kcdlList: [], // 课程大类列表
        testPaperData: null,
        basicData: null,
        studybasicData: null,
        difficultData: null,
        studyDifficultData: null,
        basicDataAfter: null,
        difficultDataAfter: null,
        errorBasicData: null,
        errorDifficultData: null,
        dataQuery: {
          month: '',
          curriculumId: '',
          curriculumName: '',
          topicType: 0,
          month: 1,
          pushQuestionNum: 0
        },
        gradeList: [
          { id: 0, name: '0%' },
          { id: 10, name: '10%' },
          { id: 20, name: '20%' },
          { id: 30, name: '30%' },
          { id: 40, name: '40%' },
          { id: 50, name: '50%' },
          { id: 60, name: '60%' },
          { id: 70, name: '70%' },
          { id: 80, name: '80%' },
          { id: 90, name: '90%' },
          { id: 100, name: '100%' }
        ],
        errgradeList: Array.from({ length: 41 }, (_, index) => ({
          id: index,
          name: index.toString()
        })),
        tabPosition: '0',
        tabBasic: 0,
        tabDifficult: 0,
        // 存储所有题型的表单数据
        testPaperFormData: {
          0: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          1: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          2: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          3: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          4: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          5: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 }
        },
        basicFormData: {
          0: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          1: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          2: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          3: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          4: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          5: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 }
        },
        difficultFormData: {
          0: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          1: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          2: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          3: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          4: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          5: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 }
        },
        studybasicFormData: {
          0: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          1: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          2: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          3: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          4: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          5: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 }
        },
        studyDifficultFormData: {
          0: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          1: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          2: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          3: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          4: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          5: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 }
        },
        basicFormDataAfter: {
          0: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          1: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          2: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          3: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          4: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 },
          5: { id: null, bottomValue: 80, middleValue: 10, topValue: 10 }
        },
        difficultFormDataAfter: {
          0: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          1: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          2: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          3: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          4: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 },
          5: { id: null, bottomValue: 40, middleValue: 40, topValue: 20 }
        },
        errorBasicFormData: {
          0: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          1: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          2: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          3: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          4: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          5: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 }
        },
        errorDifficultFormData: {
          0: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          1: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          2: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          3: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          4: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 },
          5: { id: null, countValue: 5, bottomValue: 3, middleValue: 1, topValue: 1 }
        },
        questionNum: [
          { value: 3, lable: '3' },
          { value: 4, lable: '4' },
          { value: 5, lable: '5' },
          { value: 6, lable: '6' },
          { value: 7, lable: '7' },
          { value: 8, lable: '8' },
          { value: 9, lable: '9' },
          { value: 10, lable: '10' }
        ]
      };
    },
    watch: {
      tabBasic: {
        handler(newVal) {
          if (this.tabPosition === '0') {
            this.basicData = this.basicFormData[newVal];
          } else if (this.tabPosition === '1') {
            this.basicDataAfter = this.basicFormDataAfter[newVal];
          } else if (this.tabPosition === '2') {
            this.errorBasicData = this.errorBasicFormData[newVal];
          } else if (this.tabPosition === '3') {
            this.testPaperData = this.testPaperFormData[newVal];
          } else if (this.tabPosition === '4') {
            this.studybasicData = this.studybasicFormData[newVal];
          }
        },
        immediate: true
      },
      tabDifficult: {
        handler(newVal) {
          if (this.tabPosition === '0') {
            this.difficultData = this.difficultFormData[newVal];
          } else if (this.tabPosition === '1') {
            this.difficultDataAfter = this.difficultFormDataAfter[newVal];
          } else if (this.tabPosition === '2') {
            this.errorDifficultData = this.errorDifficultFormData[newVal];
          } else if (this.tabPosition === '4') {
            this.studyDifficultData = this.studyDifficultFormData[newVal];
          }
        },
        immediate: true
      }
    },
    created() {
      this.basicData = this.basicFormData[this.tabBasic];
      this.studybasicData = this.studybasicFormData[this.tabBasic];
      this.difficultData = this.difficultFormData[this.tabDifficult];
      this.studyDifficultData = this.studyDifficultFormData[this.tabDifficult];
      this.basicDataAfter = this.basicFormDataAfter[this.tabBasic];
      this.difficultDataAfter = this.difficultFormDataAfter[this.tabDifficult];
      this.errorBasicData = this.errorBasicFormData[this.tabBasic];
      this.errorDifficultData = this.errorDifficultFormData[this.tabDifficult];
      this.testPaperData = this.testPaperFormData[this.tabBasic];
      this.getKcdlList();
    },
    methods: {
      getListCount(l) {
        console.log(l, '469');

        l.forEach((list) => {
          console.log(list.classTypes, '--------');
          return Object.values(list.classTypes).reduce((acc, cur) => {
            return acc + cur.questionTypesTotal;
          }, 0);
        });
      },
      submit() {
        // if (this.tabPosition === '2') {
        //   const monthValue = this.dataQuery.month;
        //   if (!monthValue || isNaN(monthValue) || monthValue <= 0 || monthValue > 99 || !Number.isInteger(Number(monthValue))) {
        //     this.$message.warning('请输入1~99之间的整数');
        //     return;
        //   }
        // }
        this.butLoading = true;
        switch (this.tabPosition) {
          case '0':
            this.saveClassing();
            break;
          case '1':
            this.saveClassAfter();
            break;
          case '2':
            this.saveError();
            break;
          case '3':
            this.saveTestPaper();
            break;
          case '4':
            this.saveClassingAfter();
            break;
          case '5':
            this.savePushQuestion();
            break;
        }
      },
      saveClassing() {
        // 验证基础班数据
        const isBasicValid = this.validateProportion(this.basicFormData);
        if (!isBasicValid) {
          this.$message.error('基础班各题型难度占比总和需等于 100%，请核对后重新输入');
          return;
        }
        // 验证拔高班数据
        const isDifficultValid = this.validateProportion(this.difficultFormData);
        if (!isDifficultValid) {
          this.$message.error('拔高班各题型难度占比总和需等于 100%，请核对后重新输入');
          return;
        }
        // 整合所有数据
        const basicResult = Object.keys(this.basicFormData).map((key) => ({
          id: this.basicFormData[key].id || null,
          classType: '0',
          classTopicType: key,
          lowTopicProportion: this.basicFormData[key].bottomValue,
          middleTopicProportion: this.basicFormData[key].middleValue,
          highTopicProportion: this.basicFormData[key].topValue
        }));
        const difficultResult = Object.keys(this.difficultFormData).map((key) => ({
          id: this.difficultFormData[key].id || null,
          classType: '1',
          classTopicType: key,
          lowTopicProportion: this.difficultFormData[key].bottomValue,
          middleTopicProportion: this.difficultFormData[key].middleValue,
          highTopicProportion: this.difficultFormData[key].topValue
        }));
        const allData = [...basicResult, ...difficultResult];
        let data = {};
        data.curriculumId = this.dataQuery.curriculumId;
        data.curriculumName = this.dataQuery.curriculumName;
        data.configurationType = this.tabPosition;
        data.questionMethod = this.dataQuery.topicType;
        data.coList = JSON.parse(JSON.stringify(allData));
        let params = JSON.stringify(data);
        this.saveItem(params);
      },
      saveClassAfter() {
        // 验证基础班数据
        const isBasicValid = this.validateProportion(this.basicFormDataAfter);
        if (!isBasicValid) {
          this.$message.error('基础班各题型难度占比总和需等于 100%，请核对后重新输入');
          return;
        }
        // 验证拔高班数据
        const isDifficultValid = this.validateProportion(this.difficultFormDataAfter);
        if (!isDifficultValid) {
          this.$message.error('拔高班各题型难度占比总和需等于 100%，请核对后重新输入');
          return;
        }
        // 整合所有数据
        const basicResult = Object.keys(this.basicFormDataAfter).map((key) => ({
          id: this.basicFormDataAfter[key].id || null,
          classType: '0',
          classTopicType: key,
          lowTopicProportion: this.basicFormDataAfter[key].bottomValue,
          middleTopicProportion: this.basicFormDataAfter[key].middleValue,
          highTopicProportion: this.basicFormDataAfter[key].topValue
        }));
        const difficultResult = Object.keys(this.difficultFormDataAfter).map((key) => ({
          id: this.difficultFormDataAfter[key].id || null,
          classType: '1',
          classTopicType: key,
          lowTopicProportion: this.difficultFormDataAfter[key].bottomValue,
          middleTopicProportion: this.difficultFormDataAfter[key].middleValue,
          highTopicProportion: this.difficultFormDataAfter[key].topValue
        }));
        const allData = [...basicResult, ...difficultResult];
        let data = {};
        data.curriculumId = this.dataQuery.curriculumId;
        data.curriculumName = this.dataQuery.curriculumName;
        data.configurationType = this.tabPosition;
        data.questionMethod = this.dataQuery.topicType;
        data.coList = JSON.parse(JSON.stringify(allData));
        let params = JSON.stringify(data);
        this.saveItem(params);
      },
      saveError() {
        let errList = this.$refs.errerProxy.getList();
        this.saveItem({
          id: this.topicConfigId,
          questionMethod: 0,
          configurationType: 2,
          curriculumName: this.dataQuery.curriculumName,
          curriculumId: this.dataQuery.curriculumId,
          wrongRuleConfigList: errList,
          coList: []
        });
        return;
        // 验证基础班数据
        const isBasicValid = this.validateErrorProportion(this.errorBasicFormData);
        if (!isBasicValid) {
          this.$message.error('基础班各题型难度题目数总和需等于单选题总数，请核对后重新输入');
          return;
        }
        // 验证拔高班数据
        const isDifficultValid = this.validateErrorProportion(this.errorDifficultFormData);
        if (!isDifficultValid) {
          this.$message.error('拔高班各题型难度题目数总和需等于单选题总数，请核对后重新输入');
          return;
        }
        // 整合所有数据
        const basicResult = Object.keys(this.errorBasicFormData).map((key) => ({
          id: this.errorBasicFormData[key].id || null,
          classType: '0',
          classTopicType: key,
          radioTopicTotal: this.errorBasicFormData[key].countValue,
          lowDifficultyTopicNum: this.errorBasicFormData[key].bottomValue,
          middleDifficultyTopicNum: this.errorBasicFormData[key].middleValue,
          highDifficultyTopicNum: this.errorBasicFormData[key].topValue
        }));
        const difficultResult = Object.keys(this.errorDifficultFormData).map((key) => ({
          id: this.errorDifficultFormData[key].id || null,
          classType: '1',
          classTopicType: key,
          radioTopicTotal: this.errorDifficultFormData[key].countValue,
          lowDifficultyTopicNum: this.errorDifficultFormData[key].bottomValue,
          middleDifficultyTopicNum: this.errorDifficultFormData[key].middleValue,
          highDifficultyTopicNum: this.errorDifficultFormData[key].topValue
        }));
        const allData = [...basicResult, ...difficultResult];
        let data = {};
        data.curriculumId = this.dataQuery.curriculumId;
        data.curriculumName = this.dataQuery.curriculumName;
        data.configurationType = this.tabPosition;
        data.questionMethod = this.dataQuery.topicType;
        data.recentMonthNum = this.dataQuery.month;
        data.coList = JSON.parse(JSON.stringify(allData));
        let params = JSON.stringify(data);
        this.saveItem(params);
      },
      saveTestPaper() {
        const isBasicValid = this.validateProportion(this.testPaperFormData);
        if (!isBasicValid) {
          this.$message.error('各题型难度占比总和需等于 100%，请核对后重新输入');
          return;
        }
        // 整合所有数据
        const basicResult = Object.keys(this.testPaperFormData).map((key) => ({
          id: this.testPaperFormData[key].id || null,
          classType: '0',
          classTopicType: key,
          lowTopicProportion: this.testPaperFormData[key].bottomValue,
          middleTopicProportion: this.testPaperFormData[key].middleValue,
          highTopicProportion: this.testPaperFormData[key].topValue
        }));
        const allData = [...basicResult];
        let data = {};
        data.curriculumId = this.dataQuery.curriculumId;
        data.curriculumName = this.dataQuery.curriculumName;
        data.configurationType = this.tabPosition;
        data.questionMethod = this.dataQuery.topicType;
        data.coList = JSON.parse(JSON.stringify(allData));
        let params = JSON.stringify(data);
        this.saveItem(params);
      },
      saveClassingAfter() {
        // 验证基础班数据
        const isBasicValid = this.validateProportion(this.studybasicFormData);
        if (!isBasicValid) {
          this.$message.error('基础班各题型难度占比总和需等于 100%，请核对后重新输入');
          return;
        }
        // 验证拔高班数据
        const isDifficultValid = this.validateProportion(this.studyDifficultFormData);
        if (!isDifficultValid) {
          this.$message.error('拔高班各题型难度占比总和需等于 100%，请核对后重新输入');
          return;
        }
        // 整合所有数据
        const basicResult = Object.keys(this.studybasicFormData).map((key) => ({
          id: this.studybasicFormData[key].id || null,
          classType: '0',
          classTopicType: key,
          lowTopicProportion: this.studybasicFormData[key].bottomValue,
          middleTopicProportion: this.studybasicFormData[key].middleValue,
          highTopicProportion: this.studybasicFormData[key].topValue
        }));
        const difficultResult = Object.keys(this.studyDifficultFormData).map((key) => ({
          id: this.studyDifficultFormData[key].id || null,
          classType: '1',
          classTopicType: key,
          lowTopicProportion: this.studyDifficultFormData[key].bottomValue,
          middleTopicProportion: this.studyDifficultFormData[key].middleValue,
          highTopicProportion: this.studyDifficultFormData[key].topValue
        }));
        const allData = [...basicResult, ...difficultResult];
        let data = {};
        data.curriculumId = this.dataQuery.curriculumId;
        data.curriculumName = this.dataQuery.curriculumName;
        data.configurationType = this.tabPosition;
        data.questionMethod = this.dataQuery.topicType;
        data.coList = JSON.parse(JSON.stringify(allData));
        let params = JSON.stringify(data);
        this.saveItem(params);
      },
      savePushQuestion() {
        let data = {};
        let coList = [
          {
            id: 1,
            classType: 0,
            classTopicType: 0,
            lowTopicProportion: 0,
            middleTopicProportion: 0,
            highTopicProportion: 0
          }
        ];
        data.curriculumId = this.dataQuery.curriculumId;
        data.curriculumName = this.dataQuery.curriculumName;
        data.configurationType = this.tabPosition;
        data.questionMethod = this.dataQuery.topicType;
        data.recentMonthNum = this.dataQuery.pushQuestionNum;
        data.coList = JSON.parse(JSON.stringify(coList));
        let params = JSON.stringify(data);
        this.saveItem(params);
      },
      validateErrorProportion(formData) {
        for (const key in formData) {
          const { countValue, bottomValue, middleValue, topValue } = formData[key];
          const total = parseInt(bottomValue) + parseInt(middleValue) + parseInt(topValue);
          if (total !== parseInt(countValue)) {
            return false;
          }
        }
        return true;
      },
      saveItem(params) {
        console.log(params);

        forStudent
          .updateForClass(params)
          .then((res) => {
            if (res.success) {
              this.$message({ message: '保存成功', type: 'success' });
              if (this.tabPosition == '2') {
                this.$refs.errerProxy.assignmentData(res.data);
              }
            }
          })
          .finally(() => {
            this.butLoading = false;
          });
      },
      validateProportion(formData) {
        for (const key in formData) {
          const { bottomValue, middleValue, topValue } = formData[key];
          const total = parseInt(bottomValue) + parseInt(middleValue) + parseInt(topValue);
          if (total > 100) {
            return false;
          }
          if (total < 100) {
            return false;
          }
        }
        return true;
      },
      tabPositionClick(newVal) {
        if (newVal === '3' || newVal === '5') {
          this.dataQuery.topicType = 1;
        } else {
          this.dataQuery.topicType = 0;
        }
        this.tabBasic = 0;
        this.tabDifficult = 0;
        this.search();
      },
      search() {
        let data = {};
        data.curriculumId = this.dataQuery.curriculumId;
        data.configType = this.tabPosition;
        data.questionMethod = this.dataQuery.topicType;
        forStudent.searchForClass(data).then((res) => {
          console.log(res, '774');

          if (res.success) {
            if (this.tabPosition == '2') {
              console.log(res.data);
              this.topicConfigId = res.data.id;
              this.$refs.errerProxy.assignmentData(res.data);
              // this.totalize(res.data);
            }

            // console.log(res.data);
            for (const item of res.data.voList) {
              if (this.tabPosition === '0') {
                const formData = item.classType === 0 ? this.basicFormData : this.difficultFormData;
                formData[item.classTopicType].id = item.id;
                formData[item.classTopicType].bottomValue = item.lowTopicProportion;
                formData[item.classTopicType].middleValue = item.middleTopicProportion;
                formData[item.classTopicType].topValue = item.highTopicProportion;
              } else if (this.tabPosition === '1') {
                const formData = item.classType === 0 ? this.basicFormDataAfter : this.difficultFormDataAfter;
                formData[item.classTopicType].id = item.id;
                formData[item.classTopicType].bottomValue = item.lowTopicProportion;
                formData[item.classTopicType].middleValue = item.middleTopicProportion;
                formData[item.classTopicType].topValue = item.highTopicProportion;
              } else if (this.tabPosition === '2') {
                // const formData = item.classType === 0 ? this.errorBasicFormData : this.errorDifficultFormData;
                // formData[item.classTopicType].id = item.id;
                // formData[item.classTopicType].countValue = item.radioTopicTotal;
                // formData[item.classTopicType].bottomValue = item.lowDifficultyTopicNum;
                // formData[item.classTopicType].middleValue = item.middleDifficultyTopicNum;
                // formData[item.classTopicType].topValue = item.highDifficultyTopicNum;
              } else if (this.tabPosition === '3') {
                const formData = this.testPaperFormData;
                formData[item.classTopicType].id = item.id;
                formData[item.classTopicType].bottomValue = item.lowTopicProportion;
                formData[item.classTopicType].middleValue = item.middleTopicProportion;
                formData[item.classTopicType].topValue = item.highTopicProportion;
              } else if (this.tabPosition === '4') {
                const formData = item.classType === 0 ? this.studybasicFormData : this.studyDifficultFormData;
                formData[item.classTopicType].id = item.id;
                formData[item.classTopicType].bottomValue = item.lowTopicProportion;
                formData[item.classTopicType].middleValue = item.middleTopicProportion;
                formData[item.classTopicType].topValue = item.highTopicProportion;
              }
            }
            if (res.data.configurationType == 2) {
              this.dataQuery.month = res.data.recentMonthNum;
            }
            if (res.data.configurationType == 5) {
              this.dataQuery.pushQuestionNum = res.data.recentMonthNum;
            }
          }
        });
      },
      curriculumIdChange(e) {
        this.dataQuery.curriculumName = this.kcdlList.find((i) => i.id == e).enName;
        console.log(this.dataQuery.curriculumName);

        this.search();
      },
      getKcdlList() {
        forStudent.getKcdlForKnowledge().then((res) => {
          if (res.success) {
            this.kcdlList = res.data;
            this.dataQuery.curriculumId = res.data[0].id;
            this.dataQuery.curriculumName = res.data[0].enName;
            this.search();
          }
        });
      }
    }
  };
</script>
<style lang="less" scoped>
  .btnBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    margin-bottom: 10px;
  }
  .btnClassBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 5%;
  }
  .formBox {
    margin-top: 10px;
    margin-left: 5%;
  }
  .practice {
    margin-left: 20px;
  }
  .redStar {
    display: flex;
    align-items: center;
    span {
      color: red;
      font-size: 20px;
    }
    .el-form {
      margin-top: 20px;
    }
  }
  ::v-deep .el-form-item__label {
    font-weight: bolder;
    font-size: 16px;
    color: #000000;
  }
  ::v-deep .footerBtnBox {
    margin-left: 12%;
    button {
      width: 200px;
    }
  }
</style>
