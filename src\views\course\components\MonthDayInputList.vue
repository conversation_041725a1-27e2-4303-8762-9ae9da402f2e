<template>
  <div :class="['container', `grad${dialogWidth}`]">
    <!-- -->
    <div v-for="(item, index) in localList" :key="index" style="margin-bottom: 10px">
      <div style="display: flex; align-items: center">
        <el-input v-model.trim="item.value" placeholder="输入月.日" @input="validateDate(index)" style="width: 100px; margin-right: 10px" maxlength="5" />
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="addDate"
          size="mini"
          style="padding: 2px; border-radius: 50%"
          v-if="(index == 0 && localList.length == 1) || index == localList.length - 1"
        ></el-button>
        <el-button icon="el-icon-minus" type="danger" @click="removeDate(index)" size="mini" v-if="localList.length > 1" style="padding: 2px; border-radius: 50%"></el-button>
      </div>
      <div :style="{ color: item.valid ? 'green' : 'red', fontSize: '12px' }">
        {{ item.tip }}
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'MonthDayInputList',

    data() {
      return {
        localList: []
      };
    },
    props: {
      value: {
        type: Array,
        required: true
      },
      dialogWidth: {
        type: String,
        default: '4'
      }
    },
    watch: {
      value: {
        immediate: true,
        handler(newVal) {
          if (this.syncing) return;
          const safeArray = Array.isArray(newVal) ? newVal : [];
          this.localList = safeArray.map((item) => ({
            value: item,
            valid: false,
            tip: ''
          }));
          this.localList.forEach((_, i) => this.validateDate(i));
        }
      },
      localList: {
        deep: true,
        handler() {
          this.syncing = true;
          const validValues = this.localList.map((item) => item.value);
          this.$emit('input', validValues);
          this.$nextTick(() => {
            this.syncing = false;
          });
        }
      }
    },
    mounted() {},

    methods: {
      syncToParent() {
        this.$emit(
          'input',
          this.localList.map((item) => item.value)
        );
      },
      addDate() {
        this.localList.push({ value: '', valid: false, tip: '' });
        this.syncToParent();
      },
      removeDate(index) {
        this.localList.splice(index, 1);
        this.syncToParent();
      },
      // 新方法：检查日期是否重复，排除自身
      isDuplicateDate(value, currentIndex) {
        for (let i = 0; i < this.localList.length; i++) {
          // 跳过自身
          if (i === currentIndex) continue;

          // 检查是否与其他项重复
          if (this.localList[i].value === value) {
            return true;
          }
        }
        return false;
      },
      validateDate(index) {
        const val = this.localList[index].value.trim();
        if (!val) {
          this.localList[index].valid = false;
          this.localList[index].tip = '';
          return;
        }
        // const regex = /^(0?[1-9]|1[0-2]).(0?[1-9]|[12][0-9]|3[01])$/;
        const regex = /^\d{1,2}\.\d{1,2}$/;

        if (!regex.test(val)) {
          this.localList[index].valid = false;
          this.localList[index].tip = '❌ 格式应为“月.日”，如 3.15';
          return;
        }
        if (this.isDuplicateDate(val, index)) {
          this.localList[index].valid = false;
          this.localList[index].tip = '❌ 日期重复，请检查';
          return;
        }
        const [month, day] = val.split('.').map(Number);
        const date = new Date(2024, month - 1, day);
        const isValid = date.getMonth() + 1 === month && date.getDate() === day;

        this.localList[index].valid = isValid;
        this.localList[index].tip = isValid ? '✅ 格式正确' : '❌ 日期不存在，请检查';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container {
    display: grid;
    // grid-template-columns: repeat(4, 1fr);
    gap: 10px;
  }
  .grad1 {
    grid-template-columns: repeat(1, 1fr);
  }
  .grad2 {
    grid-template-columns: repeat(2, 1fr);
  }
  .grad3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .grad4 {
    grid-template-columns: repeat(4, 1fr);
  }
</style>
