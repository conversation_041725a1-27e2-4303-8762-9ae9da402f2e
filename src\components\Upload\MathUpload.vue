<template>
  <div>
    <el-upload
      :class="fileList.length == 1 && limit == 1 && 'upload'"
      action
      v-loading="loading"
      :http-request="uploadHttp"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-preview="handlePictureCardPreview"
      :on-error="handleError"
      :on-exceed="onExceed"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      list-type="picture-card"
      name="file"
    >
      <i class="el-icon-plus"></i>
      <div v-if="showTip" slot="tip" class="el-upload__tip">
        <div v-if="tipText">{{ tipText }}</div>
        <div v-else>只能上传JPG、JPEG、PNG文件，且不超过 {{ this.isKbOrMb }}</div>
      </div>
    </el-upload>
    <!-- 图片放大预览 -->
    <el-dialog :visible.sync="picDialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
  import { ossPrClient } from '@/api/alibaba';

  export default {
    name: 'MyUpload',
    props: {
      //点击错误提示信息
      errMsg: {
        type: String,
        default: undefined
      },
      // 图片上传数量
      limit: {
        type: [Number, String],
        default: 1
      },
      tipText: {
        type: String,
        default: ''
      },
      // 图片大小尺寸
      imgSize: {
        type: Number,
        default: 5 * 1024 * 1024 // 5M=>5*1024*1024 500KB=>500*1024
      },
      // 是否显示图片的tip
      showTip: {
        type: Boolean,
        default: true
      },
      // 展示的图片列表
      fileList: {
        type: Array,
        default() {
          return [];
        }
      },
      fullUrl: {
        type: Boolean,
        default: false
      },
      dialogVisible: false
    },
    data() {
      return {
        loading: false,
        dialogImageUrl: '',
        picDialogVisible: false
      };
    },
    computed: {
      // 动态显示MB或者KB
      isKbOrMb() {
        return this.imgSize / 1024 / 1024 >= 1 ? `${Number(this.imgSize / 1024 / 1024).toFixed(0)}MB` : `${Number(this.imgSize / 1024).toFixed(0)}KB`;
      }
    },
    created() {
      ossPrClient();
    },
    methods: {
      uploadHttp({ file }) {
        this.loading = true;
        let suf = file.name.substring(file.name.lastIndexOf('.'));
        const fileName = 'manage/' + Date.parse(new Date()) + suf;
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              this.fileList.push({ uid: file.uid, url: this.aliUrl + name });
              if (this.fullUrl) {
                this.handleSuccess(this.aliUrl + name);
              } else {
                this.handleSuccess(name);
              }
              this.loading = false;
            }
          })
          .catch((err) => {
            this.$message.error('上传图片失败请检查网络或者刷新页面');
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      },
      /* 上传图片开始 */
      // 图片上传之前的校验
      handleBeforeUpload(file) {
        if (this.errMsg) {
          this.$message.error(this.errMsg);
          return false;
        }
        const isJPG = file.type === 'image/jpeg';
        const isPNG = file.type === 'image/png';
        const isSize = file.size < this.imgSize; // 图片是否小于限定的尺寸
        if (this.tipText) {
          if (!isJPG && !isPNG) {
            this.$message.error('上传的图片只能是 JPG、PNG格式');
            return false;
          }
        } else {
          if (!isJPG && !isPNG) {
            this.$message.error('上传的图片只能是 JPG、JPEG、PNG 格式');
            return false;
          }
        }

        // 图片是否小于限定的尺寸
        if (!isSize) {
          this.$message.error(`上传的图片大小不能超过 ${this.isKbOrMb}`);
          return false;
        }
        return (isJPG || isPNG) && isSize;
      },
      // 图片上传失败
      handleError(err) {
        this.$message.error('图片上传失败');
        console.log(err);
      },
      // 文件超过上传个数
      onExceed() {
        this.$message.error(`最多只能上传 ${this.limit} 张图片`);
        return false;
      },
      // 图片删除
      handleRemove(file) {
        console.log('前===', this.fileList);
        var index = this.fileList.findIndex((item) => {
          if (item.uid === file.uid) {
            return true;
          }
        });
        if (index !== -1) {
          this.fileList.splice(index, 1);
          console.log('后===', this.fileList);
          this.$emit('handleRemove', file);
        }
      },
      // 图片上传成功
      handleSuccess(res) {
        this.$emit('handleSuccess', res || '');
      },
      /* 上传图片结束 */

      // 打开图片预览弹窗
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.picDialogVisible = true;
      }
    }
  };
</script>
<style>
  .upload .el-upload--picture-card {
    //display: none !important;
  }
</style>
