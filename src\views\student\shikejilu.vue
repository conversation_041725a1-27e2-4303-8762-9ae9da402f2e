<template>
  <div style="padding: 50px; background-color: #f6f7f9">
    <el-scrollbar>
      <div id="print" style="margin-top: 1vw" ref="print">
        <div style="width: 100%; display: flex; justify-content: end; margin-bottom: 3vw">
          <el-button type="primary" @click="printFn" class="no-print" style="width: 70px">打印</el-button>
        </div>
        <table border="1" cellspacing="0" cellpadding="10" align="center">
          <tr align="center">
            <th colspan="10" align="center" style="font-size: 20px">咨询记录表</th>
            <th colspan="10" align="center" style="font-size: 20px">咨询记录表</th>
          </tr>

          <tr class="th" align="center">
            <td colspan="1" rowspan="8">试课信息</td>
            <td colspan="1">
              <div style="width: 80px">试学日期</div>
            </td>
            <td colspan="1">
              <input type="text" style="width: 180px" v-model="changeClassList.studyDate" />
            </td>

            <td colspan="1">
              <div style="width: 80px">姓名</div>
            </td>
            <td colspan="1">
              <input type="text" style="width: 100px" v-model="changeClassList.studentName" />
            </td>

            <td colspan="1">
              <div style="width: 80px">年级</div>
            </td>
            <td colspan="1">
              <input type="text" style="width: 100px" v-model="changeClassList.grade" />
            </td>
          </tr>

          <tr align="center">
            <td colspan="1">
              <div style="width: 80px">首测词汇量</div>
            </td>
            <td colspan="1">
              <input type="text" style="width: 180px" v-model="changeClassList.firstVocabulary" />
            </td>
            <td colspan="1">
              <div style="width: 100px">识记词汇数量</div>
            </td>
            <td colspan="3">
              <input type="text" style="width: 80px" v-model="changeClassList.experienceWord" />
              个
            </td>
          </tr>

          <tr align="center">
            <td colspan="1">
              <div style="width: 180px">学习时间-结束时间</div>
            </td>
            <td colspan="1">
              <input type="text" style="width: 180px" v-model="changeClassList.learnAndReachTime" />
            </td>
            <td colspan="1">
              <div style="width: 100px">试学学时</div>
            </td>
            <td colspan="3">
              <input type="text" style="width: 80px" v-model="changeClassList.tryTime" />
              小时
            </td>
          </tr>

          <tr align="center">
            <td colspan="1">
              <div style="width: 180px">词汇量测试水平</div>
            </td>
            <td colspan="1">
              <input type="text" style="width: 160px" v-model="changeClassList.vocabularyLevel" />
            </td>
            <td colspan="1">
              <div style="width: 100px">遗忘数量</div>
            </td>
            <td colspan="3">
              <input type="text" style="width: 80px" v-model="changeClassList.forgetWord" />
              个
            </td>
          </tr>
          <tr align="center">
            <td colspan="1">
              <div style="width: 180px">体验词库</div>
            </td>
            <td colspan="1">
              <el-input type="textarea" class="inputWrap" style="width: 180px" :autosize="{ minRows: 2, maxRows: 6 }" v-model="changeClassList.experienceCourse" />
            </td>
            <td colspan="1">
              <div style="width: 100px">记忆率</div>
            </td>
            <td colspan="3">
              <input type="text" style="width: 80px" v-model="changeClassList.experienceRate" />
              %
            </td>
          </tr>
          <tr align="center">
            <td colspan="1">
              <div style="width: 180px">记忆情况</div>
            </td>
            <td colspan="5">
              <div style="display: flex; align-items: center">
                <input v-model="changeClassList.memoryTime" style="width: 5vw" type="number" maxlength="8" :min="0" />
                分钟记住
                <input v-model="changeClassList.memoryNum" style="width: 5vw" type="number" maxlength="8" :min="0" />
                个单词
              </div>
            </td>
          </tr>

          <tr align="center">
            <td colspan="1">
              <div style="width: 180px">体验后学习意愿</div>
            </td>
            <td colspan="5">
              <el-radio-group v-model="changeClassList.experienceInterest">
                <el-radio v-for="(item, index) in wishList" :label="item.label" :key="index">{{ item.label }}</el-radio>
              </el-radio-group>
            </td>
          </tr>

          <tr align="center">
            <td colspan="1">
              <div style="width: 180px">学员学习状况反馈</div>
            </td>
            <td colspan="5">
              <el-input v-model="changeClassList.stuLearnFeedBack" autosize style="width: 35vw" type="textarea" maxlength="500"></el-input>
            </td>
          </tr>

          <!--   学时规划方案     -->
          <tr class="th" align="center">
            <td colspan="1" rowspan="4">咨询情况</td>
            <td colspan="1">
              <div style="width: 180px; height: 80px; line-height: 80px">学时规划方案</div>
            </td>
            <td colspan="5">
              <div style="width: 550px; height: 100%" v-if="!dayin">{{ changeClassList.learnTimePlan }}</div>
              <el-input v-if="dayin" v-model="changeClassList.learnTimePlan" autosize style="width: 35vw" type="textarea" maxlength="500"></el-input>
            </td>
          </tr>

          <tr align="center">
            <td colspan="1">
              <div style="width: 180px; height: 80px; line-height: 80px">课程规划方案</div>
            </td>
            <td colspan="5">
              <div style="width: 550px; height: 100%" v-if="!dayin">{{ changeClassList.coursePlan }}</div>
              <el-input v-if="dayin" v-model="changeClassList.coursePlan" autosize style="width: 35vw" type="textarea" maxlength="500"></el-input>
            </td>
          </tr>

          <tr align="center" valign="middle">
            <td colspan="1">
              <div style="width: 180px; height: 80px; line-height: 80px">上学时间规划</div>
            </td>
            <td colspan="5">
              <div style="width: 550px; height: 100%" v-if="!dayin">{{ changeClassList.studyScheme }}</div>
              <el-input v-if="dayin" v-model="changeClassList.studyScheme" autosize style="width: 35vw" type="textarea" maxlength="500"></el-input>
            </td>
          </tr>

          <tr align="center">
            <td colspan="1">
              <div style="width: 180px; height: 80px; line-height: 80px">抗遗忘复习时间规划</div>
            </td>
            <td colspan="5">
              <div style="width: 550px; height: 100%" v-if="!dayin">{{ changeClassList.reviewScheme }}</div>
              <el-input v-if="dayin" v-model="changeClassList.reviewScheme" autosize style="width: 35vw" type="textarea" maxlength="500"></el-input>
            </td>
          </tr>
        </table>
      </div>
      <div style="margin-top: 2vw; margin-bottom: 5vw; display: flex; justify-content: space-around">
        <el-button type="primary" @click="querenFn" style="width: 70px">确认</el-button>
        <el-button type="primary" @click="delFn" style="width: 70px; border: 1px solid #51cc92; box-sizing: border-box; background-color: #fff; color: #51cc92">取消</el-button>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
  import { addTrialClassRecord, getTableInfo } from '@/api/studentjf';
  export default {
    name: 'changeClassList',
    data() {
      return {
        merchantCodeL: '',
        studentCodeL: '',
        memory: '',
        memoryList: [
          { label: '弱', value: '1' },
          { label: '正常', value: '2' },
          { label: '强', value: '3' }
        ],
        wishList: [
          { label: '愿意', value: '0' },
          { label: '不愿意', value: '1' }
        ],
        changeClassList: {
          studyDate: '',
          studentName: '',
          grade: '',
          firstVocabulary: '',
          experienceWord: '',
          learnAndReachTime: '',
          tryTime: '',
          vocabularyLevel: '',
          forgetWord: '',
          experienceCourse: '',
          experienceRate: '',
          experienceInterest: '',
          stuLearnFeedBack: '',
          learnTimePlan: '',
          coursePlan: '',
          studyScheme: '',
          reviewScheme: '',
          // 两个code
          merchantCode: '',
          studentCode: '',
          memoryTime: '',
          memoryNum: ''
        },
        tableType: 2,
        lookwatch: {
          tableType: 2
        },
        dayin: true,
        experience: {
          intelligence: '',
          teacherClass: '',
          oneon: '',
          smallClass: ''
        }
      };
    },
    created() {
      this.merchantCodeL = window.localStorage.getItem('merchantCode');
      this.studentCodeL = window.localStorage.getItem('studentCode');
      this.initData();
    },
    methods: {
      printFn() {
        this.dayin = false;

        setTimeout(() => {
          this.$print(this.$refs.print);
        }, 500);
        this.dayinStatus();
      },
      dayinStatus() {
        setTimeout(() => {
          this.dayin = true;
          this.memoryList = {};
          this.experience = {};
        }, 1500);
      },
      // 回显
      async initData() {
        this.lookwatch.merchantCode = this.merchantCodeL;
        this.lookwatch.studentCode = this.studentCodeL;
        let res = await getTableInfo(this.lookwatch);
        if (res.data == null) {
          this.changeClassList = {};
        } else {
          this.changeClassList = res.data;
          this.memory = res.data.memory;
        }
      },
      async querenFn() {
        this.changeClassList.merchantCode = this.merchantCodeL;
        this.changeClassList.studentCode = this.studentCodeL;
        this.changeClassList.memory = this.memory;
        let res = await addTrialClassRecord(this.changeClassList);
        this.$message.success('保存成功');
        // this.$router.go(-1);
      },
      delFn() {
        this.$router.go(-1);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .th td:first-child {
    width: 65px;
    margin: 0px auto;
    text-align: center;
    padding-left: 32px;
    word-wrap: break-word;
    letter-spacing: 28px;
  }

  .ch td:first-child {
    text-align: center;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #000;
    border-color: #000;
  }

  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #000;
  }

  .el-checkbox.is-bordered.is-checked {
    border-color: #000;
  }

  .el-checkbox__input.is-focus .el-checkbox__inner {
    border-color: #000;
  }

  ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #000;
  }

  ::v-deep .el-checkbox__label {
    font-size: 16px;
  }

  input {
    width: 4.8vw;
    border-style: none;
    outline: none;
    color: #5f6368;
    font-size: 14px;
    text-align: center;
  }

  .inputWrap {
    display: block;
    white-space: pre-wrap;
  }

  .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
    white-space: nowrap;
  }
</style>
