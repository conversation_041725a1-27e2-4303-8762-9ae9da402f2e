<template>
  <el-dialog class="parse-dialog" :visible.sync="visible" :title="dialogText" width="70%" @close="handleClose" destroy-on-close>
    <ParseLook :question="queryParams" />
  </el-dialog>
</template>

<script>
  import ParseLook from '../../videoConfig/components/ParseLook.vue';
  export default {
    name: 'ParseDialog',
    components: { ParseLook },
    data() {
      return {
        dialogText: '',
        visible: false,
        loading: false,
        queryParams: {}
      };
    },
    computed: {},
    methods: {
      // 初始化方法
      init(dialogText, record) {
        this.dialogText = dialogText;
        let answer = '';
        if (record.questionType == 1 || record.questionType == 2) {
          answer = record.xktOptionVoList
            .filter((a) => {
              return a.optionIsAnswer == 1;
            })
            .map((a) => a.choiceOption)
            .join(',');
        }
        this.queryParams =
          {
            ...record,
            caption: record.caption ? record.caption.split(',') : [],
            analysisImage: record.analysisImage ? record.analysisImage.split(',') : [],
            answer
          } || {};
        this.visible = true;
      },

      // 关闭对话框
      handleClose() {
        //   this.$refs['form'].resetFields(); //重置表单校验
        // this.loading = false
        this.visible = false;
      }
    }
  };
</script>

<style scoped lang="less">
  /* 加粗弹框标题字体 */
  /deep/ .el-dialog__title {
    font-weight: bold;
    font-size: 24px;
  }
</style>
