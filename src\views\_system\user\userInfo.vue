<template>
  <div class="app-container" style="width">
    <div class="main-card">
      <div class="title">账号信息</div>
      <div class="content">
        <div class="row">
          <div class="label">账号：</div>
          <div>{{ name }}</div>
        </div>
        <div class="row">
          <div class="label">到期时间：</div>
          <div>{{ expirationTime }}</div>
        </div>
        <div class="row">
          <div class="label">账户余额：</div>
          <div>￥{{ accountBalance }}</div>
        </div>
        <div class="row">
          <div class="label">剩余课时：</div>
          <div>{{ remainingHours }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import userApi from '@/api/user';
  export default {
    name: 'userInfo',
    data() {
      return {
        expirationTime: '',
        accountBalance: '',
        remainingHours: ''
      };
    },
    computed: {
      ...mapGetters(['name'])
    },
    created() {
      this.getUserInfoData();
    },
    methods: {
      getUserInfoData() {
        userApi.getUserInfoData().then((res) => {
          console.log('getUserInfoData', res);
          this.expirationTime = res.data.data.expireTime;
          this.accountBalance = res.data.data.money;
          this.remainingHours = res.data.data.remainingHours;
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 0 40px 21px 24px;
    display: flex;
    flex-direction: column;
    .main-card {
      flex-grow: 1;
      padding: 24px 20px 20px 18px;
      background: #fff;
      border-radius: 8px;

      .title {
        font-size: 20px;
      }

      .content {
        padding-left: 42px;
        padding-top: 24px;
        color: #777777;

        .row {
          padding-bottom: 16px;
          display: flex;
          align-items: center;

          .label {
            width: 103px;
          }
        }
      }
    }
  }
</style>
