<template>
  <div class="dx-table-example">
    <h2>DxTable 使用示例</h2>

    <!-- 基础表格 -->
    <div class="example-section">
      <h3>基础表格</h3>
      <custom-table :data="tableData" style="width: 100%">
        <custom-table-column prop="date" label="日期" width="180"></custom-table-column>
        <custom-table-column prop="name" label="姓名" width="180"></custom-table-column>
        <custom-table-column prop="address" label="地址"></custom-table-column>
      </custom-table>
    </div>

    <!-- 带斑马纹表格 -->
    <div class="example-section">
      <h3>带斑马纹表格</h3>
      <custom-table :data="tableData" stripe style="width: 100%">
        <custom-table-column prop="date" label="日期" width="180"></custom-table-column>
        <custom-table-column prop="name" label="姓名" width="180"></custom-table-column>
        <custom-table-column prop="address" label="地址"></custom-table-column>
      </custom-table>
    </div>

    <!-- 带边框表格 -->
    <div class="example-section">
      <h3>带边框表格</h3>
      <custom-table :data="tableData" border style="width: 100%">
        <custom-table-column prop="date" label="日期" width="180"></custom-table-column>
        <custom-table-column prop="name" label="姓名" width="180"></custom-table-column>
        <custom-table-column prop="address" label="地址"></custom-table-column>
      </custom-table>
    </div>

    <!-- 带状态表格 -->
    <div class="example-section">
      <h3>带状态表格</h3>
      <custom-table :data="tableData" style="width: 100%">
        <custom-table-column prop="date" label="日期" width="180"></custom-table-column>
        <custom-table-column prop="name" label="姓名" width="180"></custom-table-column>
        <custom-table-column prop="address" label="地址"></custom-table-column>
        <custom-table-column label="操作">
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>
            <el-button type="text" size="small">编辑</el-button>
          </template>
        </custom-table-column>
      </custom-table>
    </div>

    <!-- 固定表头 -->
    <div class="example-section">
      <h3>固定表头</h3>
      <custom-table :data="longTableData" height="250" style="width: 100%">
        <custom-table-column prop="date" label="日期" width="180"></custom-table-column>
        <custom-table-column prop="name" label="姓名" width="180"></custom-table-column>
        <custom-table-column prop="address" label="地址"></custom-table-column>
      </custom-table>
    </div>

    <!-- 多选表格 -->
    <div class="example-section">
      <h3>多选表格</h3>
      <custom-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
        <custom-table-column type="selection" width="55"></custom-table-column>
        <custom-table-column prop="date" label="日期" width="120"></custom-table-column>
        <custom-table-column prop="name" label="姓名" width="120"></custom-table-column>
        <custom-table-column prop="address" label="地址" show-overflow-tooltip></custom-table-column>
      </custom-table>
      <div style="margin-top: 20px">
        <el-button @click="toggleSelection([tableData[1], tableData[2]])">切换第二、第三行的选中状态</el-button>
        <el-button @click="toggleSelection()">取消选择</el-button>
      </div>
    </div>

    <!-- 排序表格 -->
    <div class="example-section">
      <h3>排序表格</h3>
      <custom-table :data="tableData" style="width: 100%" :default-sort="{ prop: 'date', order: 'descending' }">
        <custom-table-column prop="date" label="日期" sortable width="180"></custom-table-column>
        <custom-table-column prop="name" label="姓名" sortable width="180"></custom-table-column>
        <custom-table-column prop="address" label="地址" :formatter="formatter"></custom-table-column>
      </custom-table>
    </div>

    <!-- 筛选表格 -->
    <div class="example-section">
      <h3>筛选表格</h3>
      <custom-table :data="tableData" style="width: 100%">
        <custom-table-column prop="date" label="日期" sortable width="180"></custom-table-column>
        <custom-table-column
          prop="name"
          label="姓名"
          width="180"
          :filters="[
            { text: '王小虎', value: '王小虎' },
            { text: '张小刚', value: '张小刚' }
          ]"
          :filter-method="filterHandler"
          filter-placement="bottom-end"
        ></custom-table-column>
        <custom-table-column prop="address" label="地址" :formatter="formatter"></custom-table-column>
      </custom-table>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'CustomTableExample',
    data() {
      return {
        tableData: [
          {
            date: '2016-05-02',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1518 弄'
          },
          {
            date: '2016-05-04',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1517 弄'
          },
          {
            date: '2016-05-01',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1519 弄'
          },
          {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄'
          }
        ],
        multipleSelection: []
      };
    },
    computed: {
      longTableData() {
        // 生成更多数据用于演示固定表头
        const data = [];
        for (let i = 0; i < 20; i++) {
          data.push({
            date: `2016-05-${String(i + 1).padStart(2, '0')}`,
            name: `用户${i + 1}`,
            address: `地址${i + 1}`
          });
        }
        return data;
      }
    },
    methods: {
      handleClick(row) {
        console.log('点击了行:', row);
        this.$message.info(`点击了 ${row.name} 的查看按钮`);
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
        console.log('选择变化:', val);
      },
      toggleSelection(rows) {
        if (rows) {
          rows.forEach((row) => {
            this.$refs.multipleTable.toggleRowSelection(row);
          });
        } else {
          this.$refs.multipleTable.clearSelection();
        }
      },
      formatter(row, column) {
        return row.address;
      },
      filterHandler(value, row, column) {
        const property = column['property'];
        return row[property] === value;
      }
    }
  };
</script>

<style scoped>
  .custom-table-example {
    padding: 20px;
  }

  .example-section {
    margin-bottom: 40px;
  }

  .example-section h3 {
    margin-bottom: 20px;
    color: #303133;
    font-size: 18px;
  }
</style>
