// base color
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;

// sidebar
$menuText: #fff;

$menuActiveText: #51cc92;
$menuActiveBg: #223a30;

$subMenuActiveText: #fff; // https://github.com/ElemeFE/element/issues/12951
$subMenuActiveBg: #51cc9299;

$menuBg: #223a30;
$menuHover: #ffffff14;

$subMenuBg: #223a30;
$subMenuHover: #ffffff14;

$sideBarWidth: 210px;

$sideBarCloseWidth: 54px;

$navbarWidth: 80px;

$tagsViewWidth: 122px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
