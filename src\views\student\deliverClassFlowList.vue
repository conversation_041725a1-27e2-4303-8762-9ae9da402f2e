<template>
  <div class="app-container">
    <el-card style="margin-bottom: 15px">
      <el-form label-width="110px" label-position="left">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="姓名：">
              <el-input v-model="dataQuery.name" placeholder="请输入姓名" clearable style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="学员编号：">
              <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-form-item label="时间筛选:" clearable>
              <el-date-picker
                style="width: 100%"
                v-model="dateArr"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :lg="6" :md="8" :sm="12" :xs="24">
            <el-button type="primary" class="searchStyle" size="small" @click="fetchData01()">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card>
      <div v-if="tableData.length > 0">
        <el-table
          class="common-table"
          stripe
          v-loading="tableLoading"
          :data="tableData"
          style="width: 100%; margin-bottom: 20px"
          row-key="id"
          default-expand-all
          :tree-props="{ list: 'children', hasChildren: 'true' }"
        >
          <el-table-column prop="studentName" label="姓名"></el-table-column>
          <el-table-column prop="studentCode" label="学员编号"></el-table-column>
          <el-table-column prop="deliverMerchantName" label="所属交付中心"></el-table-column>
          <el-table-column prop="createTime" label="充值时间"></el-table-column>
          <el-table-column prop="amount" label="充值金额（元）"></el-table-column>
          <el-table-column prop="hours" label="充值交付学时（节）"></el-table-column>
          <el-table-column prop="haveHours" label="剩余交付学时（节）"></el-table-column>
        </el-table>
        <el-row>
          <!-- 分页 -->
          <el-col :span="24" style="overflow-x: auto; text-align: center" :xs="24">
            <el-pagination
              :current-page="tablePage.currentPage"
              :page-sizes="[10, 20, 30, 40, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tablePage.totalItems"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </el-col>
        </el-row>
      </div>
      <div v-else>
        <NoMore></NoMore>
      </div>
    </el-card>
  </div>
</template>

<script>
  import { getDeliverHours } from '@/api/studyroom/studentList';
  import Tinymce from '@/components/Tinymce';
  import { pageParamNames } from '@/utils/constants';
  import ls from '@/api/sessionStorage';
  import NoMore from '@/components/NoMore/index.vue';
  export default {
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          name: '',
          studentCode: '',
          loginName: '',
          merchantCode: '',
          startTime: '',
          endTime: ''
        },
        dateArr: []
      };
    },
    components: { NoMore },
    created() {
      this.fetchData();
    },
    methods: {
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      // 查询列表
      fetchData() {
        const that = this;
        if (this.dateArr && this.dateArr.length > 0) {
          this.dataQuery.startTime = this.dateArr[0];
          this.dataQuery.endTime = this.dateArr[1];
        } else {
          this.dataQuery.startTime = '';
          this.dataQuery.endTime = '';
        }
        that.tableLoading = true;
        let param = {};

        getDeliverHours(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      //查看进度
      enterChildrenList(studentCode) {
        ls.setItem('courseStudentCode', studentCode);
        const that = this;
        that.$router.push({
          path: '/student/studentCourseProgress',
          query: {
            studentCode: studentCode
          }
        });
      }
    }
  };
</script>

<style>
  .period-table td,
  .period-table th {
    text-align: center;
  }
  .mt20 {
    margin-top: 20px;
  }
  .red {
    color: red;
  }
  .green {
    color: green;
  }
</style>
