<template>
  <div class="app-container">
    <el-card v-loading="pageLoading">
      <el-form ref="form" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="所属分类:">
          <!-- <span>{{ videoDetail.curriculumName }}- {{ videoDetail.courseVersionNodeName }} - {{ videoForm.courseSubjectNodeName }} - {{ videoForm.coursePeriodNodeName }}</span> -->
          <span v-if="videoDetail && videoDetail.id">
            {{ videoDetail.curriculumName }} - {{
              videoDetail.courseVersionNodeName
            }} - {{ videoForm.coursePeriodNodeName }} - {{ videoForm.courseSubjectNodeName }}
          </span>
          <span v-else>{{ videoForm.curriculumName }} - {{
              videoForm.courseVersionNodeName
            }} - {{ videoForm.coursePeriodNodeName }} - {{ videoForm.courseSubjectNodeName }}</span>
        </el-form-item>
        <!-- <el-form-item label="序号:" prop="sortsNum">
          <el-input v-model.number="formData.sortsNum" placeholder="请输入正整数" @input="validateIntegerInput" style="width: 200px"></el-input>
        </el-form-item> -->
        <el-form-item label="视频:" prop="type">
          <el-radio-group v-model="formData.type" @change="changeVideoType">
            <el-radio :label="1">本地上传</el-radio>
            <el-radio :label="2">视频VID (视频服务商提供)</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="视频名称:" prop="videoName" style="width: 75%">
          <el-input v-model="formData.videoName" placeholder="请输入视频名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="关联知识点:">
          <div class="knowledge-item" @click="handleKnowledgeScopeChange">
            <span>
              已选择{{
                (courseKnowledgeCorrelationList && courseKnowledgeCorrelationList.length) || (formData.knowledgeIdList && formData.knowledgeIdList.length)
              }}个知识点
            </span>
            <i class="el-icon-arrow-down"></i>
          </div>
        </el-form-item>
        <el-form-item prop="videoList" v-if="formData.type == '1'">
          <BaseUploadVideo
            :video-list="videoList"
            :video-size="5 * 1024 * 1024 * 1024"
            :video-duration="120 * 60"
            @addVideo="handleAddVideo"
            @saveVideo="handleVideoSave"
            @videoSucceed="handleVideoSucceed"
          />
        </el-form-item>
        <!-- vid 链接 -->
        <el-form-item label="视频VID:" v-if="formData.type == '2'" style="width: 75%" prop="vid">
          <div class="vid-input-container">
            <el-input v-model="formData.vid" placeholder="请输入视频VID" show-word-limit class="vid-input"
                      @input="vidInput"
            />
            <div class="red-dot-container">
              <i v-if="videoLinkValid" class="el-icon-success" style="color: #67c23a"></i>
              <i v-else-if="formData.vid && !videoLinkValid" class="el-icon-error" style="color: #f56c6c"></i>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="formData.type == '2'">
          <div v-if="imgUrl">
            <img :src="imgUrl" alt="" style="width: 178px; height: 178px" />
          </div>
          <div v-else
               style="width: 178px; height: 178px; text-align: center; line-height: 178px; border: 1px dashed #d9d9d9; border-radius: 6px"
          >暂无封面
          </div>
        </el-form-item>

        <el-form-item style="text-align: center; margin-top: 30px">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="() => handleSave(true)">保存</el-button>
          <el-button type="primary" @click="() => handleSave(false)">保存并继续新增</el-button>
        </el-form-item>
      </el-form>
      <el-dialog title="选择知识点" :visible.sync="knowledgeDialogVisible" width="70%" :before-close="dialogBeforeClose"
                 :close-on-click-modal="false"
      >
        <div>
          <KnowledgePoint
            ref="knowledgePoint"
            v-if="knowledgeDialogVisible"
            :disciplineId="videoForm.courseSubjectNodeId"
            :curriculumId="curriculumId"
            :knowledgePointIds="knowledgeIds"
            :type="1"
          ></KnowledgePoint>
        </div>
        <div slot="footer">
          <el-button @click="dialogBeforeClose">取 消</el-button>
          <el-button type="primary" @click="confirmKonwLedge">确 定</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import videoManageMentAPI from '@/api/mathApi/videoManageMentAPI';
import BaseUploadVideo from './components/BaseUploadVideo.vue';
import { saveAPI } from '@/api/uploadVideoFile';
import { debounce } from '@/utils';
import { mapActions } from 'vuex';
import KnowledgePoint from './components/knowledgePoint.vue';
import { addEditVideo, getVideoDetail, videoVidCheck } from '@/api/superReaderAPI/videoManagement';

export default {
  name: 'addVideo',
  components: {
    BaseUploadVideo,
    KnowledgePoint
  },
  data() {
    return {
      pageLoading: false, // 添加页面loading状态
      formData: {
        // sortsNum: '', // 排序号
        type: 1, // 1本地上传 2视频VID
        videoName: '', // 视频名称
        vid: '', // 视频VID
        knowledgeIdList: [] // 知识点列表
      },
      rules: {
        videoName: [
          { required: true, message: '请输入视频名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        vid: [{ required: true, message: '请输入视频VID', trigger: 'blur' }]
      },

      knowledgeIds: [],
      knowledgeDialogVisible: false,

      uploadLoading: false,
      firstImage: '',
      isVideoUploaded: false, // 视频是否点击保存上传
      videoList: [], // 用于存储视频上传组件的视频列表
      videoLinkValid: false,
      imgUrl: '',

      id: '', // 确保在 data 中初始化 id
      // 回显的视频地址
      videoUrl: '',
      videoDetail: {}, // 详情数据
      curriculumId: '',
      courseKnowledgeCorrelationList: [],
      videoForm: {}
    };
  },
  created() {
    // 在 created 钩子中获取路由参数
    this.videoForm = JSON.parse(sessionStorage.getItem('videoData'));
    console.log('videoForm', this.videoForm);
    this.curriculumId = this.videoForm.curriculumId;
    if (this.videoForm && this.videoForm.id) {
      this.id = this.videoForm.id;
      this.getVideoDetail(this.videoForm.id);
    }
  },
  watch: {
    $route(to, from) {
      if (to.name == 'superReaderVideoManagement') {
        this.resetForm();
        sessionStorage.removeItem('videoData');
      } else {
        this.videoForm = JSON.parse(sessionStorage.getItem('videoData'));
      }
      this.curriculumId = this.videoForm && this.videoForm.curriculumId ? this.videoForm.curriculumId : '';
      if (this.videoForm && this.videoForm.id) {
        this.id = this.videoForm.id;
        this.getVideoDetail(this.videoForm.id);
        // this.confirmKonwLedge();
      }
    },
    $route: function(newVal, oldVal) {
      if (oldVal.path == '/_chinese_reader/superReaderAddVideo') {
        this.deleteView(oldVal.path, oldVal.name);
      }
    }
  },
  mounted() {
    //   // 获取路由参数
    //   if (this.videoForm.id) {
    //     this.getVideoDetail(this.videoForm.id);
    //   }
  },
  beforeDestroy(to, from, next) {
    this.id = '';
    sessionStorage.removeItem('videoData');
  },
  methods: {
    ...mapActions(['delVisitedViews']),
    deleteView(path, name) {
      // 定义要删除的视图对象，需包含 path 和 name 属性
      const viewToDelete = {
        path: path,
        name: name
      };
      this.delVisitedViews(viewToDelete).then((visitedViews) => {
        console.log('删除后剩余的 visitedViews:', visitedViews);
        this.resetForm();
      });
    },
    changeVideoType(e) {
      console.log(e);
      if (e == 2) {
        if (this.formData.vid) {
          this.checkVideovid(this.formData.vid);
        }
      }
    },
    confirmKonwLedge() {
      let data = this.$refs.knowledgePoint.multipleSelection;
      // console.log(data);
      this.courseKnowledgeCorrelationList = data;
      this.knowledgeIds = data;
      // 同步更新 formData 中的知识点列表
      this.formData.knowledgeIdList = data;
      this.knowledgeDialogVisible = false;
    },
    dialogBeforeClose() {
      this.knowledgeDialogVisible = false;
    },
    handleKnowledgeScopeChange() {
      this.knowledgeDialogVisible = true;
    },
    // 获取视频的地址
    getVideoUrl(vid) {
      videoManageMentAPI.getVideoImageAPI({ vid: vid }).then((res) => {
        if (res.success) {
          this.videoList = [
            {
              vid: vid,
              url: res.data.videoUrl,
              fileName: this.formData.videoName || '',
              progress: 100
            }
          ];
          this.videoUrl = res.data.videoUrl; // 保存视频地址用于回显
          // this.imgUrl = res.data.firstImage;
        }
      });
    },
    // 排序校验
    validateIntegerInput() {
      // 移除非数字字符
      if (this.formData.sortsNum !== '' && this.formData.sortsNum !== null) {
        let numStr = this.formData.sortsNum.toString().replace(/[^\d]/g, '');
        // 移除前导零，除非是单个0
        if (numStr.length > 1 && numStr.startsWith('0')) {
          numStr = numStr.substring(1);
        }
        if (numStr === '') {
          this.formData.sortsNum = null;
        } else {
          let num = parseInt(numStr, 10);
          // 确保不超过9999999
          if (num > 9999999) {
            num = 9999999;
          }
          this.formData.sortsNum = num;
        }
      }
    },
    // 获取视频详情
    getVideoDetail(id) {
      this.pageLoading = true; // 开始加载时显示loading
      getVideoDetail({ id })
        .then((res) => {
          if (res.success) {
            console.log(res, 'fdsfdsfdsfsdfsdfsdfsd');
            this.formData = res.data;
            // 确保保存完整的知识点对象
            this.knowledgeIds = JSON.parse(JSON.stringify(this.formData.knowledgeIdList));
            this.courseKnowledgeCorrelationList = this.formData.knowledgeIdList;
            if (this.formData.type == '2') {
              this.checkVideovid();
            } else {
              this.getVideoUrl(this.formData.vid);
            }
          }
          this.videoDetail = res.data; // 保存完整详情
          this.isVideoUploaded = true;
        })
        .finally(() => {
          this.pageLoading = false; // 无论成功失败都关闭loading
        });
    },
    handleCancel() {
      this.resetForm();
      this.$router.go(-1); // 返回上一页
    },
    handleAddVideo() {
      // 触发表单验证
      this.$refs.form.validateField('videoList');
    },
    handleVideoSave(videoData) {
      // 可以处理视频保存后的额外逻辑
      // this.firstImage = videoData.videoImg;
    },
    handleVideoSucceed(videoList) {
      console.log(videoList, 'videoList');
      this.videoList = videoList;
      this.isVideoUploaded = true;
    },

    handleSave(flag) {
      const debouncedFunction = debounce(()=>{
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.formData.type == 1) {
              if (!this.videoList || this.videoList.length === 0) return this.$message.warning('请上传视频');
            } else {
              if (!this.formData.vid) return this.$message.warning('请输入视频VID');
            }
            // 修复 Cannot read properties of null (reading 'map') 错误
            let knowledgeIdList = [];
            if (this.courseKnowledgeCorrelationList && this.courseKnowledgeCorrelationList.length > 0) {
              knowledgeIdList = this.courseKnowledgeCorrelationList.map((item) => item.id || item);
            } else if (this.formData.knowledgeIdList && this.formData.knowledgeIdList.length > 0) {
              knowledgeIdList = this.formData.knowledgeIdList.map((item) => item.id || item);
            }
            let submitData = {
              videoName: this.formData.videoName,
              type: this.formData.type,
              knowledgeIdList: knowledgeIdList,
              curriculumId: this.curriculumId,
              courseVersionNodeId: this.videoForm.courseVersionNodeId,
              coursePeriodNodeId: this.videoForm.coursePeriodNodeId,
              courseSubjectNodeId: this.videoForm.courseSubjectNodeId
            };
            if (this.videoForm.id) {
              submitData.id = this.videoForm.id;
            }
            if (this.formData.type == '1') {
              if (!this.isVideoUploaded) {
                this.$message.warning('请等待视频上传完之后才能保存');
                return;
              }
              // console.log(this.videoList || [], 'this.videoList');
              if (this.videoList.length > 0) {
                submitData.vid = this.videoList[0].vid;
              }
            } else {
              // 视频的vid
              submitData.vid = this.formData.vid;
            }
            // let vid = this.formData.type == '1' ? this.videoList[0].vid : this.formData[this.formData.type].vid;

            // 这里的视频本地视频vid获取不对
            // console.log(vid, '11111111111111');
            let vid = this.formData.type == 1 ? this.videoList[0].vid : this.formData.vid;
            console.log(this.formData.vid, submitData, 'submitData');
            // return;
            this.checkVideoLinkBeforeSave(vid, submitData, flag);
          } else {
            // console.log(123);
            return false;
          }
        });
      },1000)
      // 立即执行防抖函数
      debouncedFunction();
    },
    resetForm() {
      this.$refs.form.clearValidate();
      Object.assign(this._data.formData, this.$options.data().formData);
      this.courseKnowledgeCorrelationList = [];
      this.knowledgeIds = [];
      this.id = '';
      this.videoDetail = {};
      this.videoList = []; // 清空视频列表
      this.imgUrl = '';
      this.videoLinkValid = false; // 重置验证状态
    },
    vidInput: debounce(function() {
      if (this.formData.vid) {
        console.log(213);
        this.checkVideovid();
      }
    }, 500),
    // 校验vid是否正确
    checkVideovid() {
      console.log(this.formData.vid, 'this.formData.vid');
      if (!this.formData.vid) return false;
      videoVidCheck({
        vid: this.formData.vid
      })
        .then((res) => {
          console.log(res, '11111111111111');
          if (res.success && res.data.status == 1) {
            saveAPI({ vid: this.formData.vid }).then(() => {
              setTimeout(() => {
                videoManageMentAPI
                  .getVideoImageAPI({
                    vid: this.formData.vid
                  })
                  .then((img) => {
                    console.log(img, '=========');
                    this.imgUrl = img.data.firstImg;
                  })
                  .catch((err) => {
                    this.$message.error(err.message);
                  });
              }, 1000);
            });
            this.videoLinkValid = true;
          } else {
            this.$message.warning('请输入正确的视频VID');
            this.videoLinkValid = false;
          }
        })
        .catch((err) => {
          this.$message.warning(err.message);
        });
      // 如果为空，重置状态
      if (!this.formData.vid) {
        this.videoLinkValid = false;
        return;
      }
    },
    // 保存视频前校验视频vid--校验成功后调用保存接口
    checkVideoLinkBeforeSave(vid, submitData, flag) {
      console.log(vid, submitData, flag);
      this.pageLoading = true; // 使用全局页面loading
      videoVidCheck({
        vid: vid
      })
        .then((res) => {
          if (res.success) {
            // 只在编辑时才传 id 参数
            let apiData = submitData;
            addEditVideo(apiData).then((response) => {
              this.$message.success('保存成功');
              if (flag) {
                this.resetForm();
                this.$router.go(-1);
              } else {
                this.resetForm();
              }
            }).catch((error) => {
              this.$message.error('保存失败: ' + (error.message || '未知错误'));
            });
          } else {
            this.$message.error(res.message || '视频验证失败');
          }
        })
        .catch((err) => {
          this.$message.error('视频验证出错: ' + (err.message || '未知错误'));
        })
        .finally(() => {
          this.pageLoading = false; // 结束全局页面loading
        });
    }
  }
};
</script>

<style lang="less" scoped>
.app-container {
  padding: 20px;
}

.knowledge-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 240px;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  line-height: 32px;
  padding: 0 8px 0 15px;
  box-sizing: border-box;
  cursor: pointer;

  i {
    color: #c0c4cc;
    font-size: 14px;
  }
}

.video-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-uploader .el-upload:hover {
  border-color: #409eff;
}

.video-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.video-thumbnail {
  width: 178px;
  height: 178px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #8c939d;
  background-color: #f5f7fa;
  font-size: 14px;
}

.upload-tips {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
  line-height: 1.5;
}

.vid-input-container {
  position: relative;
  display: flex;
  align-items: flex-start;
}

.vid-input {
  flex: 1;
}

.red-dot-container {
  padding-top: 10px; /* 调整垂直位置 */
  margin-left: 10px;

  i {
    font-size: 20px;
    color: red;
  }
}
</style>
