// 商户审核-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/merchantReview',
  component: Layout,
  redirect: '/merchantReview',
  meta: {
    perm: 'm:merchantReview',
    title: '商户审核',
    icon: 'merchantReview'
  },
  children: [
    {
      path: 'storeAuditList',
      component: () => import('@/views/merchantReview/storeAuditList'),
      name: 'storeAuditList',
      meta: {
        perm: 'm:merchantReview:storeAuditList',
        title: '门店审核'
      }
    },
    {
      path: 'clubReviewList',
      component: () => import('@/views/merchantReview/clubReviewList'),
      name: 'clubReviewList',
      meta: {
        perm: 'm:merchantReview:clubReviewList',
        title: '俱乐部审核'
      }
    }
  ]
};
