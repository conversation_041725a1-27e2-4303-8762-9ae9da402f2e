/**
 * 渠道2.0首页/老门店合同弹窗
 */
import request from '@/utils/request';
import { param } from 'jquery';

export default {
  // 获取合同的模板类型及新渠道上线时间
  getContractTemplate(params) {
    return request({
      url: '/znyy/sign/contract/template-type',
      method: 'GET',
      params
    });
  },
  // 获取E签宝批量合同二维码链接
  getEsignBatchContractUrl(params) {
    return request({
      url: '/znyy/sign/contract/batch-e-qr-link',
      method: 'GET',
      params
    });
  },
  // 判断新老俱乐部/或者门店
  getClubType() {
    return request({
      url: '/znyy/operations/v2/judgeMerchant',
      method: 'GET'
    });
  },
  // 查询学习系统管理合同签署状态
  getContractStatus(params) {
    return request({
      url: '/znyy/operations/v2/operationsPaymentQuery',
      method: 'get',
      params
    });
  },
  // 查看二维码及其状态
  fetchContractQrLink(data) {
    return request({
      url: '/znyy/sign/contract/qr-link',
      method: 'GET',
      params: data
    });
  }
};
