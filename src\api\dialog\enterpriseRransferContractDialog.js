/**
 * 俱乐部个人签署合同后，企业转让合同签署弹窗相关接口
 */
import request from '@/utils/request';

/**
 * 获取合同签署弹窗显示开关状态
 */
export const getContractSigningDialogShowStatus = () => {
  return request({
    url: '/znyy/contractInvolve/v2/getContractSwitch',
    method: 'GET'
  });
};
/**
 * 获取俱乐部是否需要签署企业转让合同及剩余天数
 * @returns
 */
const getNeedSigningRransferContractStatusAndDays = () => {
  return request({
    url: '/znyy/contractInvolve/v2/checkContractStatus',
    method: 'GET'
  });
};
/**
 * 个人俱乐部信息修改接口
 *
*/
const updateClubPersonInfo = (data) => {
  return request({
    url: 'znyy/operations/v2/updateClubPersonInfo',
    method: 'POST',
    data
  });
}
/**
 * 获取修改俱乐部用户信息
*/
const getClubPersonContractInfo = () => {
  return request({
    url: '/znyy/operations/v2/getClubPersonContractInfo',
    method: 'GET'
  });
}


/**
 * 获取俱乐部用户信息
 * @returns
 */
const getContractUserInfo = () => {
  return request({
    url: '/znyy/contractInvolve/v2/getContractUserInfo',
    method: 'GET'
  });
};
const checkEnterpriseName = (keyword) => {
  return request({
    url: '/znyy/operations/v2/businessLicense',
    method: 'GET',
    params: { keyword, pageNo: 1, pageSize: 10 }
  });
};

const saveUserAndGenerateContract = (data) => {
  return request({
    url: '/znyy/operations/v2/clubBuildClubPersonContract',
    method: 'POST',
    data
  });
};
/**
 * 通过合同流程id获取合同二维码
 * @param {*} flowId 合同流程id
 * @param {*} participantFlag 签署方标识 甲方 乙方
 * @returns
 */
const getContractQRUrl = (flowId, participantFlag) => {
  return request({
    url: '/znyy/sign/contract/getTransferContractQrLink',
    method: 'GET',
    params: { flowId, participantFlag, signSource: 1 }
  });
};

/**
 * 获取合同二维码签署状态
 * @param {object} params
 * @param {string} params.flowId 流程id
 * @param {number} params.signSource 签署来源：0-e签宝 ，1-电子签
 * @param {string} params.participantFlag 签署方：甲方 ，乙方
 * @returns
 */
const getContractSigingStatus = (params) => {
  return request({
    url: '/znyy/sign/contract/getContractQrLinkStatus',
    method: 'GET',
    params: { ...params, signSource: 1 }
  });
};

export { getNeedSigningRransferContractStatusAndDays, getContractUserInfo, checkEnterpriseName, saveUserAndGenerateContract, getContractQRUrl, getContractSigingStatus, updateClubPersonInfo, getClubPersonContractInfo };
