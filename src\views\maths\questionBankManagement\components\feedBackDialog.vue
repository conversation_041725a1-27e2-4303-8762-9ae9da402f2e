<template>
  <el-dialog :show-close="false" :visible.sync="visible" title="题目有问题？" width="300px" @close="handleClose" center>
    <div style="color: #999; font-size: 13px; margin-bottom: 10px; top: 52px; left: 43%; position: absolute">可多选</div>
    <div class="feedback-options">
      <el-button
        v-for="option in options"
        :key="option.value"
        :type="selected.includes(option.value) ? 'primary' : 'default'"
        @click="toggleOption(option.value)"
        class="feedback-btn"
      >
        {{ option.label }}
      </el-button>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :disabled="selected.length === 0" @click="handleSubmit">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    name: 'FeedBackDialog',
    props: {
      visible: {
        type: Boolean,
        required: true
      }
    },
    data() {
      return {
        selected: [],
        options: [
          { label: '题目错误', value: '1' },
          { label: '答案错误', value: '2' },
          { label: '答案解析错误', value: '3' },
          { label: '以上都有', value: '4' }
        ]
      };
    },
    methods: {
      // 单选与多选逻辑
      toggleOption(value) {
        if (value === '4') {
          if (this.selected.includes('4')) {
            this.selected = [];
          } else {
            this.selected = ['4'];
          }
        } else {
          let newSelected = this.selected.filter((v) => v !== '4');
          if (newSelected.includes(value)) {
            newSelected = newSelected.filter((v) => v !== value);
          } else {
            newSelected.push(value);
          }
          this.selected = newSelected;
        }
      },
      handleSubmit() {
        const result = this.selected.join(',');
        console.log('submit', result);
        this.$emit('childsubmit', result);
        this.handleClose();
      },
      handleClose() {
        this.selected = [];
        this.$emit('feedclose');
      }
    }
  };
</script>

<style scoped>
  .feedback-options {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-bottom: 20px;
  }
  .feedback-btn {
    width: 46%;
    margin-bottom: 12px;
  }
</style>
