import DxTable from './DxTable.vue'
import DxTableColumn from './DxTableColumn.vue'

// 为组件提供 install 安装方法，供按需引入
DxTable.install = function (Vue) {
  Vue.component(DxTable.name, DxTable)
}

DxTableColumn.install = function (Vue) {
  Vue.component(DxTableColumn.name, DxTableColumn)
}

// 导出组件
export { DxTable, DxTableColumn }

// 默认导出组件
export default {
  DxTable,
  DxTableColumn,
  install(Vue) {
    Vue.component(DxTable.name, DxTable)
    Vue.component(DxTableColumn.name, DxTableColumn)
  }
}
