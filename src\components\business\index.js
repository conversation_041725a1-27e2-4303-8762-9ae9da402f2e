import Vue from 'vue';

const requireComponent = require.context(
  '@/components/business', // 组件目录
  true, // 是否遍历子目录
  /\.vue$/ // 匹配规则
);

requireComponent.keys().forEach((fileName) => {
  const componentConfig = requireComponent(fileName);
  const componentName = fileName
    .split('/')
    .pop()
    .replace(/\.\w+$/, '')
    .replace(/([a-z])([A-Z])/g, '$1-$2') // 驼峰转短横线
    .toLowerCase();

  Vue.component(componentName, componentConfig.default || componentConfig);
});
