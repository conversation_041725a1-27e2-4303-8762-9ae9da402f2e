import request from '@/utils/request';

// 获取课程大类的接口
export function getCurriculumAPI(query) {
  return request({
    url: '/znyy/curriculum/all',
    method: 'get',
    params: query
  });
}
// 获取学科学段数据
export function getSubjectAPI(query) {
  return request({
    url: '/dsx/math/web/coursePeriodConfig/selectTree',
    method: 'get',
    params: query
  });
}
// 获取试题难度接口
export function getDifficultyAPI(query) {
  return request({
    url: '/dsx/math/web/mathQuestionBank/questionDifficulty',
    method: 'get',
    params: query
  });
}
// 获取试题类型接口
export function getTypeAPI(query) {
  return request({
    url: '/dsx/math/web/mathQuestionBank/questiontype',
    method: 'get',
    params: query
  });
}
// 获取生成类型接口
export function getBuildTypeAPI(query) {
  return request({
    url: '/dsx/math/web/mathQuestionBank/buildType',
    method: 'get',
    params: query
  });
}
// 是否关联知识点
export function getRelationAPI(query) {
  return request({
    url: '/dsx/math/web/mathQuestionBank/linkKnowledgeEnum',
    method: 'get',
    params: query
  });
}
// 题目分页
export function listQuestionAPI(query) {
  return request({
    url: '/dsx/math/web/mathQuestionBank/question/page',
    method: 'get',
    params: query
  });
}
// 查看解析
export function listQuestionOneAPI(query) {
  return request({
    url: '/dsx/math/web/mathQuestionBank/info',
    method: 'get',
    params: query
  });
}

// 题目新增
export function addQuestionAPI(query) {
  return request({
    url: '/dsx/math/web/mathQuestionBank/insertOrUpdate',
    method: 'post',
    data: query
  });
}

// 题目反馈
export function FeedbackAPI(query) {
  return request({
    url: '/dsx/math/wap/mathQuestionFeedback/saveMathQuestionFeedback',
    method: 'post',
    data: query
  });
}

// 题目删除
export function deleteQuestionAPI(query) {
  return request({
    url: '/dsx/math/web/mathQuestionBank/delete',
    method: 'delete',
    params: query
  });
}
