// 商户管理-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/areas',
  component: Layout,
  meta: {
    perm: 'm:areas',
    title: '商户管理',
    icon: 'merchantManagement'
  },
  children: [
    {
      path: 'areasPromoterAgentList',
      component: () => import('@/views/areas/promoter/areasPromoterAgentList'),
      name: 'areasPromoterAgentList',
      meta: {
        perm: 'm:areas:areasPromoterAgentList',
        title: '市级服务商列表'
      }
    },
    {
      path: 'areasAgentAdd',
      hidden: true,
      component: () => import('@/views/areas/promoter/areasAgentAdd'),
      name: 'areasAgentAdd',
      meta: {
        perm: 'm:areas:areasAgentAdd',
        title: '市级服务商新增'
      }
    },
    {
      path: 'areasDealerLists',
      component: () => import('@/views/areas/dealer/areasDealerList'),
      name: 'areasDealerLists',
      meta: {
        perm: 'm:areas:areasDealerList',
        title: '托管中心列表'
      }
    },
    {
      path: 'areasAgentDealerList',
      component: () => import('@/views/areas/dealer/areasAgentDealerList'),
      name: 'areasAgentDealerList',
      meta: {
        perm: 'm:areas:areasAgentDealerList',
        title: '托管中心列表'
      }
    },
    {
      path: 'areasDealerAdd',
      hidden: true,
      component: () => import('@/views/areas/dealer/areasDealerAdd'),
      name: 'areasDealerAdd',
      meta: {
        perm: 'm:areas:areasDealerAdd',
        title: '托管中心新增'
      }
    },
    {
      path: 'areasAgentSchoolList',
      component: () => import('@/views/areas/dealer/areasAgentSchoolList'),
      name: 'areasAgentSchoolList',
      meta: {
        perm: 'm:areas:areasAgentSchoolList',
        title: '门店列表'
      }
    },
    {
      path: 'areasSchoolEdit',
      hidden: true,
      component: () => import('@/views/areas/dealer/areasSchoolEdit'),
      name: 'areasSchoolEdit',
      meta: {
        perm: 'm:areas:areasSchoolEdit',
        title: '门店编辑'
      }
    },
    {
      path: 'areasSchoolAdd',
      hidden: true,
      component: () => import('@/views/areas/dealer/areasSchoolAdd.vue'),
      name: 'areasSchoolAdd',
      meta: {
        perm: 'm:areas:areasSchoolEdit',
        title: '门店新增'
      }
    },
    {
      path: 'addMoreInfo',
      hidden: true,
      component: () => import('@/views/areas/dealer/addMoreInfo'),
      name: 'addMoreInfo',
      meta: {
        perm: 'm:merchant:addMoreInfo',
        title: '批量新增'
      }
    }
  ]
};
