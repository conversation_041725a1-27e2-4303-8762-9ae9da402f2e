<template>
  <div style="border: 1px solid #ccc">
    <Toolbar ref="toolbar" style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
    <Editor class="rt-editor" v-model="html" :defaultConfig="editorConfig" :mode="mode" @onMaxLength="onMaxLength" @onChange="onChange" @onCreated="onCreated" />
  </div>
</template>

<script>
  import '@wangeditor/editor/dist/css/style.css';
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
  import { DomEditor } from '@wangeditor/editor';

  export default {
    components: { Editor, Toolbar },
    props: {
      value: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        isMaxLimit: true, // 内容超出最大长度限制
        editor: null,
        html: '',
        // 工具栏配置
        toolbarConfig: {
          insertKeys: {
            index: 6,
            keys: [
              'through', // 下划线
              {
                // 更多按钮
                key: 'group-more-style2',
                title: '更多',
                iconSvg:
                  '<svg viewBox="0 0 1024 1024"><path d="M204.8 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M505.6 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M806.4 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path></svg>',
                menuKeys: ['code', 'sup', 'sub', 'clearStyle']
              }
            ]
          },
          excludeKeys: [
            // 禁用工具栏按钮
            'group-more-style', // 禁用原来的更多按钮
            'insertLink', // 禁用插入链接
            'todo', // 禁用待办事项
            'group-image', // 禁用图片上传
            'group-video', // 禁用视频上传
            'emotion', // 禁用表情
            'codeBlock', // 禁用代码块
            'insertTable', // 禁用表格
            'fullScreen' // 禁用全屏
          ]
        },
        // 编辑器配置
        editorConfig: {
          placeholder: '请输入内容...',
          maxLength: 5000
        },
        mode: 'default' // or 'simple'
      };
    },
    methods: {
      onCreated(editor) {
        this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
        this.$nextTick(() => {
          // 获取工具栏配置
          const toolbar = DomEditor.getToolbar(editor);
          console.log(toolbar.getConfig().toolbarKeys);
        });
        this.html = this.value; // 异步渲染编辑器内容
        setTimeout(() => {
          this.isMaxLimit = false;
        }, 1000);
      },
      onChange(html) {
        console.log('content', html.children);
        let isEmpty = this.editor.isEmpty();
        if (!isEmpty) {
          // console.log('🚀 ~ onChange ~ this.editor.getHtml():', this.editor.getHtml());
          this.$emit('update:value', this.editor.getHtml());
          return;
        }
        this.$emit('update:value', '');
      },
      onMaxLength() {
        // 加一个节流，防止频繁触发
        if (this.isMaxLimit) return;
        this.isMaxLimit = true;
        setTimeout(() => {
          this.isMaxLimit = false;
        }, 3000);
        this.$message.warning('内容达到最大长度限制');
      }
    },
    mounted() {
      // 模拟 ajax 请求，异步渲染编辑器
      // setTimeout(() => {
      //   this.html = '<p>模拟 Ajax 异步设置内容 HTML</p>';
      // }, 1500);
    },
    beforeDestroy() {
      const editor = this.editor;
      if (editor == null) return;
      editor.destroy(); // 组件销毁时，及时销毁编辑器
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .w-e-drop-panel {
      height: 250px;
      overflow-y: scroll;
    }
  }
  .rt-editor {
    height: 300px;
    overflow-y: hidden;
  }
</style>
