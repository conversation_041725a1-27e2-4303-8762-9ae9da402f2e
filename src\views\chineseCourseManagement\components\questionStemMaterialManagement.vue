<!--语文课程管理-题库管理-题干材料管理-->
<template>
  <div>
    <el-row class="main-container">
      <el-col :span="24">
        <el-col :span="6" class="left-sidebar">
          <div style="margin: 20px 0 0 20px; line-height: 32px">条件筛选</div>
          <el-form>
            <el-form-item label="课程大类" label-width="90px">
              <el-select style="width: 65%" v-model="form.curriculumId" placeholder="请选择课程大类">
                <el-option v-for="item in curriculumList" :key="item.id" :value="item.id" :label="item.enName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="题干材料ID" label-width="90px">
              <el-input placeholder="请输入题干材料ID" v-model="form.id" style="width: 65%" clearable></el-input>
            </el-form-item>
            <el-form-item label="材料标题" label-width="90px">
              <el-select style="width: 65%" v-model="form.questionMaterialTitle" placeholder="请选择材料标题" clearable>
                <el-option v-for="item in questionMaterialTitleList" :key="item.value" :value="item.label" :label="item.label"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学科学段" label-width="90px">
              <el-cascader style="width: 65%" :options="subjectList" v-model="form.gradeId" @change="handleChange"
                           clearable
              ></el-cascader>
            </el-form-item>
            <el-form-item label="关联题目" label-width="90px">
              <el-select style="width: 65%" v-model="form.isLinkQuestion" placeholder="请选择是否关联题目" clearable>
                <el-option v-for="item in relatedQuestions" :key="item.id" :value="item.id" :label="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div style="margin-top: 20px; text-align: center">
            <el-button type="primary" size="mini" @click="handleSearch()">搜索</el-button>
            <el-button type="primary" size="mini" @click="handleReset()">重置</el-button>
          </div>
        </el-col>
        <div class="right-content">
          <el-col :span="20" style="text-align: right; overflow: auto">
            <div class="filter-add">
              <span>共筛选出{{ tablePage.totalItems }}条数据</span>
              <div>
<!--                <el-button style="margin-right: 20px" type="success" size="mini"-->
<!--                           v-if="checkPermission(['b:aaademo:topicManagementAdd'])" @click="handleOutMaterials"-->
<!--                >导出题干材料-->
<!--                </el-button>-->
<!--                <el-button style="margin-right: 20px" type="success" size="mini"-->
<!--                           v-if="checkPermission(['b:aaademo:topicManagementAdd'])" @click="handleDownloadTemplate"-->
<!--                >下载导入模板-->
<!--                </el-button>-->
<!--                <el-button style="margin-right: 20px" type="success" size="mini"-->
<!--                           v-if="checkPermission(['b:aaademo:topicManagementAdd'])" @click="handleImportMaterials"-->
<!--                >导入题干材料-->
<!--                </el-button>-->
                <el-button style="margin-right: 20px" type="primary" size="mini"
                           v-if="checkPermission(['b:aaademo:topicManagementAdd'])" @click="handleAdd"
                >新增题干材料
                </el-button>
              </div>
            </div>
          </el-col>
          <el-col :span="20">
            <el-container>
              <el-col :span="24" v-if="questionStemList.length > 0">
                <el-card style="margin-bottom: 20px" v-for="(item, index) in questionStemList" :key="index">
                  <el-header style="font-size: 12px; margin-bottom: 20px;justify-content: space-between"
                             class="flex-around"
                  >
                    <span>材料ID：{{ item.id }}</span>
                    <span>学段：{{ item.gradeName }}</span>
                    <span>材料标题：{{ item.questionMaterialTitle }}</span>
                    <span>最近编辑时间：{{ item.updateTime }}</span>
                  </el-header>
                  <el-main>
                    <!-- 题干区域 -->
                    <div style="margin-bottom: 10px;display: flex;justify-content: end">
                      <el-button @click="handleEdit(item.id)">修改</el-button>
                      <el-button @click="handleDelect(item.id,item.linkQuestionList)">删除</el-button>
                    </div>
                    <el-card>
                      <div class="question-material-text">{{ item.questionMaterialText }}</div>
                    </el-card>
                    <div class="flex-around" style="margin-top: 10px">
                      <span>关联题目：</span>
                      <span v-if="!item.linkQuestionList">暂无</span>
                      <div v-else>
                        <span v-for="(item, index) in item.linkQuestionList" :key="index" style="margin-left: 10px"
                        >{{ item.questionTypeName }}*{{ item.questionCount }} </span>
                        <el-button @click="handlePreview(item.id)" style="margin-left: 20px">预览</el-button>
                      </div>
                    </div>
                  </el-main>
                </el-card>
              </el-col>
              <el-col :span="20" v-else style="height: 650px">
                <p style="text-align: center; margin-top: 300px">暂无数据</p>
              </el-col>
            </el-container>
          </el-col>
          <el-col :span="20" style="display: flex; justify-content: center">
            <el-pagination
              :page-size="tablePage.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tablePage.totalItems"
              :page-sizes="[10, 20, 30, 40, 50]"
              @size-change="handleSizeChange"
              :current-page.sync="tablePage.currentPage"
              @current-change="handleCurrentChange"
            />
          </el-col>
        </div>
      </el-col>
    </el-row>

    <!-- 预览弹窗 -->
    <el-dialog title="关联题目列表" :visible.sync="showRelevanceListDialog">
      <el-card>
        <div v-for="(item, index) in bindQuestionsList" :key="index" style="margin-bottom: 20px;">
          <div style="margin-bottom: 10px">{{item.questionTypeName}}:{{item.questionText}}（{{item.questionScore}}分）</div>
          <div style="display: flex">
            <div v-for="(item, index) in item.chineseQuestionBankOptionDtoList" :key="index" style="margin-right: 20px">
              <div>{{item.choiceOption}}:{{item.content}}</div>
            </div>
          </div>
        </div>
      </el-card>
    </el-dialog>

    <!-- 新增/编辑题干材料 -->
    <el-dialog center :title="dialogTitle" :visible.sync="showAddQuestionStemDialog" width="70%">
      <el-form :model="addForm" :rules="addFormRules" ref="addForm">
        <el-form-item label="材料标题" prop="questionMaterialTitle">
          <el-input placeholder="请输入材料标题" v-model="addForm.questionMaterialTitle" style="width: 90%" clearable></el-input>
        </el-form-item>
        <el-form-item label="学科学段" prop="gradeId">
          <el-cascader style="width: 90%" :options="subjectList" v-model="addForm.gradeId" @change="handleChange"
                       clearable
          ></el-cascader>
        </el-form-item>
        <el-form-item label="题干材料" prop="questionMaterialText">
          <el-input type="textarea" placeholder="请输入题干材料" v-model="addForm.questionMaterialText" style="width: 90%" clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="图片：" prop="imageCover">
          <SuperReaderUpload
            :imgSize="30 * 1024 * 1024"
            :showTip="false"
            :isKbOrMb="300000"
            @handleSuccess="handlePicSuccess"
            @handleRemove="handlePicRemove"
            :fullUrl="true"
            :file-list="fileList"
            :limit="5"
          />
          <div class="tipss" style="font-size: 12px; color: #909399">支持jpg, jpeg, png格式，小于30MB，最多可上传5张图片
          </div>
        </el-form-item>
        <el-form-item style="display: flex;justify-content: center">
          <el-button type="info" @click="close">取消</el-button>
          <el-button type="primary" @click="debouncedSubmit">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission';
import viewAnalysisDialog from '../../maths/questionBankManagement/components/viewAnalysisDialog.vue';
import mathematicalFormula from '../../maths/questionBankManagement/components/mathematicalFormula.vue';
import mathTest from '../../maths/questionBankManagement/components/mathTest.vue';
import {
  getAllQuestionMaterialList,
  deleteQuestionMaterial,
  addQuestionMaterial,
  getQuestionMaterialById,
  editQuestionMaterial,
  getQuestionMaterialRelationList,
  getAllCourseTypeList, getQuestionMaterialIdList, getAllSubjectList
} from '@/api/superReaderAPI/testBaseManagement';
import SuperReaderUpload from '@/components/Upload/MathUpload.vue';
import { debounce } from '@/utils';
import * as XLSX from 'xlsx';

export default {
  name: 'topicManagement',
  components: {
    SuperReaderUpload,
    viewAnalysisDialog,
    mathematicalFormula,
    mathTest
  },
  data() {
    return {
      viewId: '', // 查看解析id
      // 筛选数据
      form: {
        id: '',
        curriculumId: '',
        questionMaterialTitle: '',
        disciplineId: '',
        gradeId: '',
        isLinkQuestion: '',
      },
      questionMaterialTitleList: [],//材料标题列表

      addForm: {
        questionMaterialTitle: '',
        gradeId: '',
        curriculumId: '',
        questionMaterialText: '',
        questionMaterialImage: []
      },
      fileList: [], // 上传文件列表

      showItem: false,
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      showAddQuestionStemDialog: false,
      dialogTitle: '',
      bindQuestionsList: [], // 绑定题目列表
      showRelevanceListDialog: false,
      associationList: [],
      questionTypeList: [],
      dialogVisible: false,
      url: '',
      srcList: [],
      // 题库列表
      questionStemList: [],
      curriculumList: [], // 课程大类列表
      subjectList: [], // 学科列表
      subjectValue: [], // 学科列表选中值
      buildTypeList: [], // 生成类型列表
      // 是否关联题目List
      relatedQuestions: [
        {
          id: '1',
          name: '已关联题目'
        },
        {
          id: '0',
          name: '未关联题目'
        }
      ],

      addFormRules: {
        questionMaterialTitle: [
          { required: true, message: '请输入材料标题', trigger: 'blur' }
        ],
        gradeId: [
          { required: true, message: '请选择学段', trigger: 'change' }
        ],
        questionMaterialText: [
          { required: true, message: '请舒服题干材料', trigger: 'blur' }
        ]
      },

    };
  },
  created() {
    this.initData();
  },

  watch: {
    // 监听课程大类
    'form.curriculumId': function(newVal) {
      if (newVal) {
        this.getSubjectList();
      }
    },
    '$route.query.refresh': function(newVal) {
      if (newVal) {
        this.initData(); // 调用分页数据刷新方法
      }
    }
  },

  methods: {
    checkPermission,
    async initData() {
      await this.getKcdlList();
      await this.getSubjectList(); // 获取学科学段
      // await this.fetchData(); // 请求分页数据
      await this.getQuestionStemTitle() // 获取材料标题列表
      await this.getAllQuestionStem(); // 获取题干材料列表
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      // this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getAllQuestionStem();
    },
    // 获取所有的课程大类
    getKcdlList() {
      return new Promise((resolve) => {
        getAllCourseTypeList().then((res) => {
          if (res.success) {
            this.curriculumList = res.data;
            this.form.curriculumId = res.data[0].id;
          }
          resolve();
        });
      });
    },

    // 获取页面数据
    // fetchData() {
    //   listQuestionAPI({
    //     pageNum: this.tablePage.currentPage,
    //     pageSize: this.tablePage.size,
    //     curriculumId: this.form.curriculumId,
    //     disciplineId: this.form.disciplineId?.length ? this.form.disciplineId[0] : '',
    //     gradeId: this.form.disciplineId?.length ? this.form.disciplineId[1] : '',
    //     questionType: this.form.questionType,
    //     questionText: this.form.questionText,
    //     id: this.form.id,
    //     buildType: this.form.buildType,
    //     linkKnowledge: this.form.linkKnowledge
    //   })
    //     .then((res) => {
    //       if (res.success) {
    //         // this.topicList = res.data.data;
    //         this.$set(this, 'topicList', res.data.data);
    //         pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name] || 0)));
    //       }
    //     })
    //     .catch((err) => {
    //       this.$set(this, 'topicList', []);
    //     });
    // },
    // 转换数据为 el-cascader 支持的格式
    transformToCascaderData(data, level = 1, maxLevel = 2) {
      if (!data) return []; // 添加对 null 或 undefined 数据的处理
      return data
        .map((item) => {
          const node = {
            label: item.nodeName, // 使用 nodeName 作为显示的名称
            value: item.id // 使用 id 作为值
          };
          // 只在当前层级小于最大层级且存在子列表时才递归添加 children
          if (level < maxLevel && item.childList && item.childList.length > 0) {
            const children = this.transformToCascaderData(item.childList, level + 1, maxLevel);
            if (children.length > 0) {
              node.children = children; // 只有当 children 有数据时才保留
            }
          }
          return node;
        })
        .filter((node) => node.children || level === maxLevel); // 过滤掉没有 children 的节点，除非是最大层级
    },
    // 获取学科列表
    async getSubjectList() {
      const res = await getAllSubjectList({
        curriculumId: this.form.curriculumId,
        nodeLevel: 3 // 确保 API 请求获取到第三级数据
      });
      if (res.success && res.data) {
        // 直接使用返回的数据，因为数据已经具有正确的嵌套结构
        this.subjectList = this.transformToCascaderData(res.data, 1, 2);

        // 设置默认选中第一项
        if (this.subjectList.length > 0) {
          const firstSubject = this.subjectList[0];
          const firstGrade = firstSubject.children?.[0]?.value || null;

          this.form.disciplineId = [firstSubject.value, firstGrade]; // 默认选中第一项
        } else {
          this.form.disciplineId = []; // 如果没有数据，清空选中值
        }
      } else {
        this.subjectList = []; // 处理API调用失败或无数据的情况
        this.form.disciplineId = []; // 清空选中值
      }
    },

    handleChange(value) {
      console.log('🚀 ~ handleChange ~ value:', value);
      this.subjectValue = value; //
      this.form.disciplineId = value;
    },
    // 绑定题干材料题目列表
    async handlePreview(id) {
      const res = await getQuestionMaterialRelationList({ id: id });
      this.bindQuestionsList = res.data;
      this.showRelevanceListDialog = true;
    },

    handleOutMaterials() {
      if(this.questionStemList && this.questionStemList.length > 0) {
        this.exportToExcel(this.questionStemList);
      } else {
        this.$message.warning('请等待题干材料加载......');
      }
    },
    handleDownloadTemplate() {
      const url = 'https://document.dxznjy.com/dyw/语文阅读超人知识点导入模板.xlsx';
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', '语文阅读题干材料导入模板.xlsx');
      document.body.appendChild(link);
      try {
        link.click();
        this.$message.success('模板下载请求已发送，请查看浏览器下载列表');
      } catch (error) {
        this.$message.error('模板下载失败，请重试');
        console.error('文件下载失败:', error);
      }
      // 移除链接并释放内存
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
    handleImportMaterials() {
      this.$message('请联系对应产品')
    },

    // 导出题干材料数据为Excel
    exportToExcel(data) {
      console.log('准备转成表格的data:', data)
      // 准备导出的数据
      const exportData = data.map(item => {
        return {
          '材料编号': item.id,
          '学段': item.gradeName,
          '课程大类': item.curriculumName,
          '材料标题': item.questionMaterialTitle,
          '题干材料': item.questionMaterialText,
          '是否关联题目': item.isLinkQuestion === 1 ? '已关联' : '未关联',
          '关联题目详情': item.linkQuestionList ?
            item.linkQuestionList.map(q => `${q.questionTypeName}*${q.questionCount}`).join('; ') :
            '暂无',
          '图片数量': item.questionMaterialImage ?
            (Array.isArray(item.questionMaterialImage) ?
              item.questionMaterialImage.length :
              JSON.parse(item.questionMaterialImage)?.length || 0) : 0,
          '最近编辑时间': item.updateTime,
        };
      });

      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      ws['!cols'] = [
        { wch: 20 }, // 材料ID
        { wch: 15 }, // 学段
        { wch: 15 }, // 课程大类
        { wch: 20 }, // 材料标题
        { wch: 50 }, // 题干材料
        { wch: 12 }, // 是否关联题目
        { wch: 30 }, // 关联题目详情
        { wch: 10 }, // 图片数量
        { wch: 20 }, // 最近编辑时间
        { wch: 15 }  // 编辑人
      ];

      // 将工作表添加到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '题干材料');

      // 导出文件
      XLSX.writeFile(wb, '题干材料数据.xlsx');
    },
    // 新增题干材料
    handleAdd() {
      this.addForm.questionMaterialImage = [];
      this.addForm.questionMaterialText = '';
      this.addForm.curriculumId = '';
      this.addForm.questionMaterialTitle = '';
      this.addForm.gradeId = '';
      this.fileList = [];
      this.dialogTitle = '新增题干材料';
      this.showAddQuestionStemDialog = true;
    },
    // 修改题干材料
    handleEdit(id) {
      this.dialogTitle = '编辑题干材料';
      this.getQuestionStemDetail(id)
      this.showAddQuestionStemDialog = true;
    },
    // 删除题干材料
    handleDelect(id, linkQuestionList) {
      // 检查是否有关联题目
      if (linkQuestionList && linkQuestionList.length > 0) {
        // 如果有关联题目，提示用户先解除关联
        this.$message.warning('请先解除与该题干所关联的所有题目');
        return;
      }
      // 如果没有关联题目，执行删除操作
      console.log('删除', id);
      this.$confirm(`您确定删除这条数据吗？`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteQuestionMaterial({ id: id }).then(() => {
            this.$message.success(`删除题目成功`);
            this.getAllQuestionStem();
          });
        })
        .catch(() => {
          this.$message.info(`已取消删除`);
        });
    },

    // 点击取消按钮关闭弹窗
    close() {
      // 重置新增/编辑表单
      this.addForm.questionMaterialImage = [];
      this.addForm.questionMaterialText = '';
      this.addForm.curriculumId = '';
      this.addForm.questionMaterialTitle = '';
      this.addForm.gradeId = '';
      this.fileList = [];

      this.showAddQuestionStemDialog = false;
    },
    // 搜索
    handleSearch() {
      this.tablePage.currentPage = 1;
      this.getAllQuestionStem();
    },
    // 获取材料标题列表
    async getQuestionStemTitle() {
      const res = await getQuestionMaterialIdList({
        questionMaterialTitle: this.form.questionStemTitle,
      })
      console.log('获取材料标题', res);
      this.questionMaterialTitleList = res.data.map((item)=>{
        return {
          label: item.questionMaterialTitle,
          id: item.id
        }
      });
      console.log('获取材料标题列表', this.questionMaterialTitleList);
    },
    // 获取语文阅读所有题干材料
    async getAllQuestionStem() {
      console.log(this.form);
      const res = await getAllQuestionMaterialList({
        id: this.form.id,
        curriculumId: this.form.curriculumId,
        gradeId: this.form.gradeId[1],
        isLinkQuestion: this.form.isLinkQuestion,
        questionMaterialTitle: this.form.questionMaterialTitle,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.size
      });
      this.questionStemList = res.data.data;
      this.tablePage.currentPage = parseInt(res.data.currentPage);
      this.tablePage.size = parseInt(res.data.size);
      this.tablePage.totalItems = parseInt(res.data.totalItems);
      console.log('获取所有题干材料', res);
    },
    // 图片上传成功处理
    handlePicSuccess(res) {
      console.log(this.fileList);
      console.log('图片上传成功', res);

      // 确保addForm.questionMaterialImage是数组
      if (!Array.isArray(this.addForm.questionMaterialImage)) {
        this.addForm.questionMaterialImage = [];
      }

      // 检查是否已经存在该URL，避免重复添加
      if (res && !this.addForm.questionMaterialImage.includes(res)) {
        // 将上传成功的图片URL添加到questionMaterialImage数组中
        this.addForm.questionMaterialImage.push(res);
      }
    },

    // 图片移除处理
    handlePicRemove(file) {
      console.log('图片移除', file);

      // 确保addForm.questionMaterialImage是数组
      if (!Array.isArray(this.addForm.questionMaterialImage)) {
        this.addForm.questionMaterialImage = [];
      }

      // 获取要删除的文件URL
      const fileUrl = file.url || file;

      // 从addForm.questionMaterialImage中删除对应的值
      if (fileUrl) {
        this.addForm.questionMaterialImage = this.addForm.questionMaterialImage.filter(item => {
          return item !== fileUrl;
        });
      }

      // 同时更新fileList，移除对应的文件
      this.fileList = this.fileList.filter(item => {
        return item.url !== fileUrl;
      });
    },

    debouncedSubmit() {
      // 创建防抖函数，1秒内只能执行一次
      if (!this._debouncedSubmit) {
        this._debouncedSubmit = debounce(this.submit, 3000, true);
      }
      this._debouncedSubmit();
    },

    // 添加/编辑-题干材料
    async submit() {
      // 使用表单验证替代手动验证
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          // 创建一个临时对象用于提交，避免修改原始数据
          const submitForm = { ...this.addForm };

          if (this.dialogTitle === '新增题干材料') {
            submitForm.curriculumId = this.form.curriculumId;
            // 使用临时变量而不是直接修改 addForm.gradeId
            if (Array.isArray(this.addForm.gradeId) && this.addForm.gradeId.length > 1) {
              submitForm.gradeId = this.addForm.gradeId[1];
            } else {
              submitForm.gradeId = this.addForm.gradeId;
            }

            console.log('新增题干材料', submitForm);
            const res = await addQuestionMaterial(submitForm);
            if (res.success === true) {
              this.$message.success('添加成功');
              this.handleReset();
            } else {
              this.$message.error('添加失败');
            }
          } else {
            submitForm.curriculumId = this.form.curriculumId;
            // 同样使用临时变量处理
            if (Array.isArray(this.addForm.gradeId) && this.addForm.gradeId.length > 1) {
              submitForm.gradeId = this.addForm.gradeId[1];
            } else {
              submitForm.gradeId = this.addForm.gradeId;
            }

            const res = await editQuestionMaterial(submitForm);
            if (res.success) {
              this.$message.success('修改成功');
              this.handleReset();
            } else {
              this.$message.error('修改失败');
            }
          }
        } else {
          // 表单验证失败，element-ui会自动显示错误信息
          return false;
        }
      });
    },
    // 根据id 获取题干材料详情
    async getQuestionStemDetail(id) {
      const res = await getQuestionMaterialById({ id: id });
      console.log('获取题干材料详情', res.data);
      this.addForm.gradeId = res.data.gradeId;
      this.addForm.questionMaterialTitle = res.data.questionMaterialTitle;
      this.addForm.questionMaterialText = res.data.questionMaterialText;
      this.addForm.questionMaterialImage = res.data.questionMaterialImage;
      this.addForm.id = res.data.id;
      this.addForm.curriculumId = res.data.curriculumId;
      this.fileList = res.data.questionMaterialImage.map((url, index) => {
        return {
          name: `image_${index + 1}`,
          url: url
        };
      });
    },
    // 重置
    handleReset() {
      // 重置搜索表单
      this.form.id = '';
      this.form.curriculumId = this.curriculumList.length > 0 ? this.curriculumList[0].id : '';
      this.form.questionMaterialTitle = '';
      this.form.gradeId = '';
      this.form.isLinkQuestion = '';

      // 重置新增/编辑表单
      this.addForm.questionMaterialImage = [];
      this.addForm.questionMaterialText = '';
      this.addForm.curriculumId = '';
      this.addForm.questionMaterialTitle = '';
      this.addForm.gradeId = '';
      this.fileList = [];

      // 重新获取数据
      this.tablePage.currentPage = 1;
      this.getAllQuestionStem();
      this.close();
    }
  }
};
</script>

<style lang="scss" scoped>
.filter-add {
  display: flex;
  justify-content: space-between;
  line-height: 32px;
  margin: 20px 0;
}

.el-header {
  background-color: #b3c0d1;
  color: #333;
  line-height: 60px;
}

.flex-around {
  align-items: center;
  display: flex;
}

.option {
  padding: 5px 0;
  margin: 5px 0;
}

.main-container {
  height: 100vh; /* 撑满整个视口高度 */
  display: flex;
  overflow: hidden;
}

.left-sidebar {
  height: 100%;
  overflow: auto; /* 如果你希望左边也可以滚动就保留 */
  // background: #f9f9f9;
  // border-right: 1px solid #eee;
}

.right-content {
  flex: 1;
  height: 100%;
  overflow-y: auto; /* 只让右边滚动 */
  padding: 16px;
}
.question-material-text {
  white-space: pre-line;
  line-height: 50px;
  text-align: left;
}

::v-deep .el-textarea__inner {
  height: 300px;
}
</style>
