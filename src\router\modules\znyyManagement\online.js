// 在线表格-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/online',
  component: Layout,
  redirect: '/online',
  meta: {
    perm: 'm:online',
    title: '在线表格',
    icon: 'onlineTable'
  },
  children: [
    {
      path: 'onlineForm',
      component: () => import('@/views/activiti/onlineForm/index'),
      name: 'onlineForm',
      meta: {
        perm: 'm:online:onlineForm',
        title: '在线表单'
      }
    },
    {
      path: 'onlineDict',
      component: () => import('@/views/activiti/onlineForm/formOnlineDict/index'),
      name: 'onlineDict',
      meta: {
        perm: 'm:online:onlineDict',
        title: '字典管理'
      }
    },
    {
      path: 'page',
      component: () => import('@/views/activiti/onlineForm/onlinePage/index'),
      name: 'onlinePage',
      meta: {
        perm: 'm:online:page',
        title: '表单管理'
      }
    }
  ]
};
