<template>
  <div style="width: 900px; margin: 20px">
    <el-table :data="tableDataShow" border stripe>
      <!-- 类 列 -->
      <el-table-column label="课程大类" prop="courseName" width="300" />

      <!-- 值 分组列 -->
      <el-table-column label="退课视频配置(全部视频：学考通指的是反馈解锁的视频，小四门指的是全部解锁的视频)">
        <template slot-scope="scope">
          <el-radio-group v-model="scope.row.isRetain">
            <el-radio :label="1" style="width: 260px">保留全部视频</el-radio>
            <el-radio :label="2">移除全部视频</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right">
      <el-button @click="cancel" style="margin: 20px">取消</el-button>
      <el-button type="primary" @click="reset" style="margin: 20px">重置</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </div>
</template>

<script>
  import courseApi from '@/api/studyExamPassed/course';
  export default {
    name: 'widthdraCourseVideoConfig',

    data() {
      return {
        tableData: [],
        tableDataShow: []
      };
    },

    mounted() {
      this.getConfigList();
    },

    methods: {
      getConfigList() {
        const loading = this.$loading({
          lock: true,
          text: '课程大类加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255, 255, 0.7)'
        });
        courseApi
          .getRefundVideoConfig()
          .then((res) => {
            loading.close();
            if (!res.success) return this.$message.error(res.message);
            this.tableData = res.data;
            this.tableDataShow = JSON.parse(JSON.stringify(this.tableData));
          })
          .catch((err) => {
            loading.close();
          });
      },
      submit() {
        let data = this.tableDataShow.map((item) => {
          return {
            id: item.id,
            isRetain: item.isRetain
          };
        });
        courseApi.saveRefundVideoConfig(data).then((res) => {
          if (!res.success) return this.$message.error(res.message);
          this.$message({
            message: '保存成功',
            type: 'success'
          });
          this.getConfigList();
        });
      },
      reset() {
        courseApi.resetRefundVideoConfig().then((res) => {
          if (!res.success) return this.$message.error(res.message);
          this.tableData = res.data;
          this.tableDataShow = JSON.parse(JSON.stringify(this.tableData));
          this.$message({
            message: '重置成功',
            type: 'success'
          });
        });
      },
      cancel() {
        this.tableDataShow = JSON.parse(JSON.stringify(this.tableData));
        this.$message({
          message: '取消成功',
          type: 'success'
        });
      }
    }
  };
</script>

<style lang="less" scoped></style>
