<!-- 课程分类配置 -->
<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="课程大类：">
        <el-select v-model="dataQuery.curriculumId" placeholder="请选择" @change="exchangeKcdl">
          <el-option v-for="item in kcdlList" :key="item.id" :value="item.id" :label="item.enName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段名称：">
        <el-input v-model.trim="dataQuery.stepName" placeholder="请输入学段名称搜索" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="margin-left: 5px" @click="resetQuery">重置</el-button>
        <el-button type="primary" style="margin-right: 5px" @click="search">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="contentBox">
      <div style="display: flex; justify-content: space-between; align-items: center">
        <!-- 左边部分：课程分类 + 新增学科 -->
        <div style="display: flex; align-items: center; gap: 10px">
          <h3 style="margin: 0">课程分类</h3>
          <el-button type="primary" @click="newVersion">新增学科</el-button>
        </div>

        <!-- 右边部分：学段/关联版本 + 新增学段，左右对齐 -->
        <div style="display: flex; justify-content: space-between; align-items: center; width: 75%">
          <el-radio-group v-model="tabPosition" size="medium" @input="tabPositionClick">
            <el-radio-button label="0">学段</el-radio-button>
            <el-radio-button label="1">关联版本</el-radio-button>
          </el-radio-group>
          <el-button type="primary" @click="newStage">新增学段</el-button>
        </div>
      </div>
    </div>
    <div class="container">
      <div style="margin-right: 28px" class="courseType">
        <el-tree
          ref="categoryTree"
          v-loading="treeLoading"
          :data="categoryTreeData"
          node-key="id"
          default-expand-all
          highlight-current
          :current-node-key="currentNodeKey"
          @node-click="categoryClick"
          :props="defaultProps"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <i class="el-icon-document"></i>
            <span class="tree-label">{{ node.label }}</span>
            <span style="display: none" class="btns">
              <!-- <span class="el-icon-edit" @click="handleUpdate(node, data)"></span> -->
              <template>
                <el-button type="text" class="el-icon-top" :disabled="getActionState(node, data, 'up')" @click.stop="upClass(node, data)"></el-button>
                <el-button type="text" class="el-icon-bottom" :disabled="getActionState(node, data, 'down')" @click.stop="downClass(node, data)"></el-button>
              </template>
              &nbsp;
              <span class="el-icon-delete" @click.stop="handleTypeDel(node, data)"></span>
            </span>
          </span>
        </el-tree>
      </div>
      <div class="list-area">
        <el-table class="common-table" :data="tableData" v-loading.body="tableLoading" :default-sort="{ prop: 'sort', order: 'descending' }" height="62vh">
          <el-table-column type="index" width="50" label="序号"></el-table-column>
          <el-table-column :label="bbLabel">
            <template slot-scope="scope">
              {{ scope.row.nodeName || scope.row.basisVersionName }}
            </template>
          </el-table-column>
          <el-table-column label="排序">
            <template slot-scope="scope">
              {{ scope.row.sortsNum || scope.row.sort }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="新增时间" />
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="writeQuestion(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" @click="delQuestion(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-col :span="24" style="margin-bottom: 20px; margin-top: 20px">
          <el-pagination
            :current-page="tablePage.currentPage"
            :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tablePage.totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-col>
      </div>
    </div>
    <el-dialog :title="dialogTitle" :visible.sync="viewDiaOpen" width="500px" :close-on-click-modal="false">
      <el-form ref="importFrom" :model="importFrom" :rules="rules" label-position="right" label-width="120px" style="width: 100%">
        <el-row :gutter="20" v-if="dialogState == 0">
          <el-col :span="24">
            <el-form-item label="学科名称" label-width="180px" prop="xkmc">
              <el-select v-model="importFrom.xkmc" placeholder="请选择" @change="getBb">
                <el-option v-for="item in xkList" :key="item.id" :value="item.id" :label="item.subjectName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="版本名称" label-width="180px" prop="bbmc">
              <el-select v-model="importFrom.bbmc" placeholder="请选择" multiple collapse-tags>
                <el-option v-for="item in bbList" :key="item.id" :value="item.id" :label="item.versionName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="dialogState == 1 || dialogState == 2">
          <el-col :span="24" v-if="dialogState == 1">
            <el-form-item label="学段名称" label-width="180px" prop="xdmc">
              <el-input v-model.trim="importFrom.xdmc" style="width: 220px" maxlength="10" show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="排序号" label-width="180px" prop="pxh">
              <el-input v-model.trim="importFrom.pxh" style="width: 220px" @input="validateMinValue"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="viewDiaOpen = false">取消</el-button>
        <el-button size="mini" type="primary" @click="saveItem">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import courseApi from '@/api/training/course';
  import forStudent from '@/api/testPaper/management';
  import {
    addCourseSubject, addEditCourseSubject, deleteCourseRelation, deleteCourseSubject,
    getCourseCategories, getCourseCategoryList, getCourseRelationList, getCourseSubjectList,
    getCourseTreeDataNoVersion, getCourseVersionList, updateCourseRelationSort
  } from '@/api/superReaderAPI/courseClassificationConfiguration';
  export default {
    name: 'courseTypeConfig',
    data() {
      return {
        treeLoading: false,
        tabPosition: '0', // 学段/关联版本
        bbLabel: '学段名称', // 学段/版本名称label
        currentNodeKey: '', // 当前点击节点的id
        defaultProps: {
          children: 'children',
          label: 'nodeName'
        },
        categoryTreeData: [], // 课程分类树
        kcdlList: [], // 课程大类列表
        dataQuery: {
          curriculumId: null, // 课程大类
          curriculumName: '',
          stepName: ''
        },
        importFrom: {
          xkmc: '', // 学科名称
          bbmc: [], // 版本名称
          xdmc: '', // 学段名称
          pxh: '', // 排序号
          subjectName: '' // 学科名称label
        },
        rules: {
          bbmc: [{ required: true, message: '必填', trigger: 'blur' }],
          xdmc: [{ required: true, message: '必填', trigger: 'blur' }],
          pxh: [{ required: true, message: '必填', trigger: 'blur' }],
          xkmc: [{ required: true, message: '必填', trigger: 'blur' }]
        },
        tableLoading: false,
        tableData: [],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 0
        },
        viewDiaOpen: false,
        dialogTitle: '新增版本',
        dialogState: 0,
        xkList: [], // 学科列表
        bbList: [], // 版本列表
        currentNodeData: {}, // 当前点击节点的数据
        saveRow: {} // 保存的行数据
      };
    },
    created() {
      this.getKcdlList();
    },
    watch: {
      viewDiaOpen: {
        handler(newVal, oldVal) {
          if (newVal == false) {
            this.bbList = [];
          }
        }
      },
      $route(to, from) {
        console.log(to, from, 'courseTypeConfig路由变化了，重新加载数据');
        if (to.path == '/_aaa_demo/superReaderCourseTypeConfig') {
          this.dataQuery.stepName = '';
          this.getCategoryTree();
        }
      }
    },
    methods: {
      tabPositionClick(newVal) {
        if (newVal == '0') {
          this.bbLabel = '学段名称';
          this.submitForm();
        } else {
          this.bbLabel = '版本名称';
          this.querytable();
        }
      },
      categoryClick(data, node) {
        this.currentNodeData = data;
        this.currentNodeKey = node.data.id;
        if (this.tabPosition == '0') {
          this.submitForm();
        } else {
          this.querytable();
        }
      },
      getKcdlList() {
        getCourseCategories().then(async (res) => {
          if (res.success) {
            this.kcdlList = res.data;
            this.dataQuery.curriculumId = res.data[0].id;
            this.dataQuery.curriculumName = res.data[0].enName;
            await this.getCategoryTree();
          }
        });
      },
      search() {
        this.tablePage.currentPage = 1;
        this.tablePage.size = 10;
        if (this.tabPosition == '0') {
          this.submitForm();
        } else {
          this.querytable();
        }
      },
      submitForm() {
        this.tableLoading = true;
        let param = {};
        param.curriculumId = this.dataQuery.curriculumId;
        param.nodeName = this.dataQuery.stepName;
        param.nodeId = this.currentNodeKey;
        param.pageSize = this.tablePage.size;
        param.pageNum = this.tablePage.currentPage;
        getCourseCategoryList(param)
          .then((res) => {
            if (res.success) {
              this.tableData = res.data.data || [];
              this.tablePage.totalItems = Number(res.data.totalItems);
            }
            this.tableLoading = false;
          })
          .catch((err) => {
            this.tableLoading = false;
          });
      },
      querytable() {
        this.tableLoading = true;
        let param = {};
        param.curriculumId = this.dataQuery.curriculumId;
        param.subjectId = this.currentNodeKey;
        param.pageSize = this.tablePage.size;
        param.pageNum = this.tablePage.currentPage;
        getCourseRelationList(param)
          .then((res) => {
            if (res.success) {
              this.tableData = res.data.data || [];
              this.tablePage.totalItems = Number(res.data.totalItems);
              this.tableLoading = false;
            }
          })
          .catch((err) => {
            this.tableLoading = false;
          });
      },
      // 获取课程分类树
      getCategoryTree() {
        this.treeLoading = true;
        this.categoryTreeData = [];
        const that = this;
        let data = {
          curriculumId: this.dataQuery.curriculumId,
          nodeLevel: 1
        };
        getCourseTreeDataNoVersion(data)
          .then((res) => {
            if (res.success) {
              that.categoryTreeData = res.data;
              if (that.categoryTreeData instanceof Array && that.categoryTreeData.length > 0) {
                that.currentNodeKey = that.categoryTreeData[0].id;
                that.currentNodeData = that.categoryTreeData[0];
                // this.submitForm();
                this.search();
                that.$nextTick(function () {
                  that.$refs['categoryTree'].setCurrentKey(that.currentNodeKey);
                });
              }
              that.treeLoading = false;
            }
          })
          .catch((err) => {
            that.treeLoading = false;
          });
      },
      exchangeKcdl() {
        this.getCategoryTree();
        const selectedItem = this.kcdlList.find((item) => item.id == this.dataQuery.curriculumId);
        if (selectedItem) {
          this.dataQuery.curriculumName = selectedItem.enName;
        }
      },
      writeQuestion(row) {
        this.saveRow = row;
        if (this.tabPosition == '0') {
          this.dialogTitle = '编辑学段';
          this.dialogState = 1;
          this.importFrom = { xdmc: row.nodeName, pxh: row.sortsNum };
          this.viewDiaOpen = true;
        } else {
          this.dialogTitle = '编辑';
          this.dialogState = 2;
          this.importFrom = { pxh: row.sort };
          this.viewDiaOpen = true;
        }
      },
      delQuestion(row) {
        this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            if (this.tabPosition == '0') {
              deleteCourseSubject({ id: row.id }).then((res) => {
                if (res.success) {
                  this.$message({
                    type: 'success',
                    message: res.message
                  });
                  this.submitForm();
                }
              });
            } else {
              deleteCourseRelation({ versionId: row.id }).then((res) => {
                if (res.success) {
                  this.$message({
                    type: 'success',
                    message: res.message
                  });
                  this.querytable();
                }
              });
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },

      saveItem() {
        this.$refs['importFrom'].validate((valid) => {
          if (!valid) return;
          console.log('this.dialogState', this.dialogState);
          switch (this.dialogState) {
            case 0: // 新增学科
              this.addSubject();
              break;
            case 1: // 新增学段
              this.addLearnStep();
              break;
            case 2: // 编辑
              this.rewrite();
              break;
            default:
              break;
          }
        });
      },
      rewrite() {
        let data = {};
        data.id = this.saveRow.id; // 修改
        data.curriculumId = this.saveRow.curriculumId;
        data.subjectId = this.currentNodeKey;
        data.sort = this.importFrom.pxh;
        updateCourseRelationSort(data).then((res) => {
          if (res.success) {
            this.$message.success('修改成功！');
            this.viewDiaOpen = false;
            this.querytable();
          }
        });
      },
      addLearnStep() {
        let data = {};
        if (this.saveRow && this.saveRow.id) {
          data.id = this.saveRow.id; // 修改
          data.nodeName = this.importFrom.xdmc;
          data.nodeType = this.saveRow.nodeType;
          data.nodeLevel = this.saveRow.nodeLevel;
          data.curriculumId = this.saveRow.curriculumId;
          data.curriculumName = this.saveRow.curriculumName;
          data.parentNodeId = this.saveRow.parentNodeId;
          data.sortsNum = this.importFrom.pxh;
          data.isChild = this.saveRow.isChild;
        } else {
          data.id = null; // 新增
          data.nodeName = this.importFrom.xdmc;
          data.nodeType = this.currentNodeData.nodeType + 1;
          data.nodeLevel = this.currentNodeData.nodeLevel + 1;
          data.curriculumId = this.dataQuery.curriculumId;
          data.curriculumName = this.dataQuery.curriculumName;
          data.parentNodeId = this.currentNodeData.id;
          data.sortsNum = this.importFrom.pxh;
          data.isChild = this.currentNodeData.isChild;
        }
        addEditCourseSubject(data).then((res) => {
          if (res.success) {
            this.$message.success('新增成功！');
            this.viewDiaOpen = false;
            this.getCategoryTree();
          }
        });
      },
      addSubject() {
        let param = {
          curriculumId: this.dataQuery.curriculumId,
          curriculumName: this.dataQuery.curriculumName,
          subjectId: this.importFrom.xkmc,
          subjectName: this.importFrom.subjectName,
          versionIds: this.importFrom.bbmc.toString()
        };
        addCourseSubject(param).then((res) => {
          if (res.success) {
            this.$message.success('新增成功！');
            this.viewDiaOpen = false;
            this.getCategoryTree();
          }
        });
      },
      // 新增学科
      async newVersion() {
        this.dialogTitle = '新增学科';
        this.dialogState = 0;
        this.importFrom = { xkmc: '', bbmc: [] };
        await this.getSubject();
        this.viewDiaOpen = true;
      },
      // 获取学科列表
      getSubject() {
        let param = {
          curriculumId: this.dataQuery.curriculumId
        };
        getCourseSubjectList(param).then((res) => {
          if (res.success) {
            this.xkList = res.data;
          }
        });
      },
      // 获取版本列表
      getBb() {
        let param = {
          curriculumId: this.dataQuery.curriculumId,
          subjectId: this.importFrom.xkmc
        };
        getCourseVersionList(param).then((res) => {
          if (res.success) {
            this.bbList = res.data;
          }
        });
        const selectedItem = this.xkList.find((item) => item.id === this.importFrom.xkmc);
        if (selectedItem) {
          this.importFrom.subjectName = selectedItem.subjectName;
        }
      },
      // 新增学段
      newStage() {
        this.saveRow = {};
        this.dialogTitle = '新增学段';
        this.dialogState = 1;
        this.importFrom = { xdmc: '', pxh: '' };
        this.viewDiaOpen = true;
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.currentPage = 1;
        this.tablePage.size = val;
        if (this.tabPosition == '0') {
          this.submitForm();
        } else {
          this.querytable();
        }
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        if (this.tabPosition == '0') {
          this.submitForm();
        } else {
          this.querytable();
        }
      },
      // 重置
      resetQuery() {
        this.dataQuery = {};
        this.dataQuery.curriculumId = this.kcdlList[0].id;
        this.dataQuery.curriculumName = this.kcdlList[0].enName;
        this.search();
      },
      getActionState(node, data, action) {
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const index = children.findIndex((el) => el.id == data.id);
        const len = children.length;
        if (index == 0 && action == 'up') {
          return true;
        } else if (index == len - 1 && action == 'down') {
          return true;
        }
        return false;
      },
      upClass(node, data) {
        let param = [];
        param.push({
          id: data.id,
          sortsNum: data.sortsNum
        });
        const parentData = Array.isArray(node.parent.data) ? node.parent.data : [node.parent.data];
        let matchedIndex = parentData.findIndex((item) => item.id === data.id);
        // 获取前一项
        const prevItem = parentData[matchedIndex - 1];
        param.push({
          id: prevItem.id,
          sortsNum: prevItem.sortsNum
        });
        forStudent.updateSort(param).then((res) => {
          if (res.success) {
            this.$message.success('操作成功！');
            this.viewDiaOpen = false;
            this.getCategoryTree();
          }
        });
      },
      downClass(node, data) {
        let param = [];
        param.push({
          id: data.id,
          sortsNum: data.sortsNum
        });
        const parentData = Array.isArray(node.parent.data) ? node.parent.data : [node.parent.data];
        let matchedIndex = parentData.findIndex((item) => item.id === data.id);
        // 获取前一项
        const prevItem = parentData[matchedIndex + 1];
        param.push({
          id: prevItem.id,
          sortsNum: prevItem.sortsNum
        });
        forStudent.updateSort(param).then((res) => {
          if (res.success) {
            this.$message.success('操作成功！');
            this.viewDiaOpen = false;
            this.getCategoryTree();
          }
        });
      },
      handleTypeDel(node, data) {
        this.$confirm('确认删除课程分类？删除分类前系统会检查分类下是否有子分类和课程，若存在，则不允许删除。', '课程分类删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let param = { id: data.id };
            forStudent.deleteAboutTree(param).then((res) => {
              if (res.success) {
                this.$message.success('删除成功！');
                this.getCategoryTree();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      validateMinValue() {
        const inputValue = this.importFrom.pxh;
        // 若输入为空，直接返回
        if (inputValue === '') return;

        // 检查输入是否为有效数字
        if (isNaN(Number(inputValue))) {
          // 输入非有效数字，清空输入框
          this.importFrom.pxh = '';
          return;
        }
        const value = Number(inputValue);
        // 检查是否小于 1
        if (value < 1) {
          this.importFrom.pxh = '1';
        }
        // 检查是否大于 1000
        else if (value > 1000) {
          // 回退到上一个有效的值
          this.importFrom.pxh = inputValue.slice(0, -1);
        }
      }
    },
    computed: {}
  };
</script>

<style lang="less" scoped>
  /deep/.el-dialog__header {
    border-bottom: 1px solid #dfdfdf;
  }
  .contentBox {
    padding: 20px 0;
  }
</style>

<style scoped>
  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
  /* 树形控件样式 */
  .el-icon-document {
    display: inline-block;
    margin-right: 6px;
  }
  ::v-deep .el-tree {
    max-height: 75vh;
    overflow-y: auto;
  }
  ::v-deep .el-tree::-webkit-scrollbar {
    width: 4px !important;
  }
  /* 滑块样式 */
  ::v-deep .el-tree::-webkit-scrollbar-thumb {
    background-color: #aaa;
    border-radius: 10px;
  }
  /* 滚动条轨道样式 */
  ::v-deep .el-tree::-webkit-scrollbar-track {
    background-color: #fafafa;
    border-radius: 10px;
  }

  ::v-deep .el-tree .el-tree-node__content:hover {
    color: #1890ff;
  }
  ::v-deep .el-tree .el-tree-node__content:hover .btns {
    display: inline-block !important;
    margin-left: 20px;
  }
  ::v-deep .el-tree .el-tree-node__content:hover .btns span {
    display: inline-block;
    margin-right: 6px;
    font-weight: 500;
  }
  ::v-deep .el-tree .tree-label {
    font-size: 15px;
  }
  .container {
    width: 100%;
    display: flex;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    padding: 18px;
    box-sizing: border-box;
    border-radius: 6px;
  }
  .courseType {
    border: 1px solid #eee;
    flex: 2;
  }
  .tree-title {
    width: 100%;
    background-color: #f0f0f0;
    border-bottom: 1px solid #eee;
    padding: 5px 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }
  ::v-deep .courseType .el-tree {
    padding: 20px;
    box-sizing: border-box;
  }
  .list-area {
    flex: 7;
    margin-left: 10px;
    overflow: hidden;
  }
  .list-area .toast {
    color: #555;
    font-size: 22px;
    margin-top: 20%;
    transform: translateY(-50%);
    text-align: center;
  }
  .search-input {
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 20px;
    box-sizing: border-box;
  }
  .tree-title {
    width: 100%;
    background-color: #f0f0f0;
    border-bottom: 1px solid #eee;
    padding: 5px 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }
  ::v-deep .courseType .el-tree {
    padding: 20px;
    box-sizing: border-box;
  }
  /* 表格区域样式 */
  .add-course-btn {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  ::v-deep .common-table .el-table__header th {
    background-color: #f0f0f0;
  }
  ::v-deep .el-pagination {
    text-align: right;
  }
  .disabled {
    color: #aaa;
  }
</style>
