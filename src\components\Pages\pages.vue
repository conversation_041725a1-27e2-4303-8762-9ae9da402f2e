<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      background
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :layout="$store.getters.device !== 'mobile' ? 'total, sizes, prev, pager, next, jumper' : 'total, sizes, prev, next'"
      :page-sizes="pageSizes"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
  export default {
    name: 'Pagination',
    props: {
      total: {
        required: true,
        type: Number
      },
      page: {
        type: Number,
        default: 1
      },
      limit: {
        type: Number,
        default: 20
      },
      pageSizes: {
        type: Array,
        default() {
          return [10, 20, 30, 40, 50];
        }
      },
      hidden: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      currentPage: {
        get() {
          return this.page;
        },
        set(val) {
          this.$emit('update:page', val);
        }
      },
      pageSize: {
        get() {
          return this.limit;
        },
        set(val) {
          this.$emit('update:page', 1);
          this.$emit('update:limit', val);
        }
      }
    },
    methods: {
      handleSizeChange(val) {
        this.$emit('pagination');
      },
      handleCurrentChange(val) {
        this.$emit('pagination');
      }
    }
  };
</script>

<style scoped>
  .pagination-container {
    text-align: center;
    background: #fff;
    padding: 15px 0;
  }

  .el-pager li {
    min-width: 20px;
  }

  .pagination-container.hidden {
    display: none;
  }
</style>
