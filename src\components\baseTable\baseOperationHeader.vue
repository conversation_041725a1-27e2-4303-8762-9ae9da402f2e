<template>
  <div class="header-group">
    <span>操作</span>
    <!-- <i class="el-icon-setting" style=" font-size: 14px"></i> -->
    <img v-if="headerConfig" @click="headerList" style="width: 20px" src="../../assets/icons_images/operation.png" alt="" />
  </div>
</template>

<script>
  export default {
    name: 'baseOperationHeader',
    props: {
      headerConfig: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      headerList() {
        this.$emit('headerList');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .header-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 18px;
  }
</style>
