import request from '@/utils/request'

export default {
  //分页
  listOnlinePageDatasource(data){
    return request({
      url: '/activiti/onlineDatasource/listOnlinePageDatasource',
      method: 'GET',
      params: data
    })
  },
  add(data){
    return request({
      url: '/activiti/onlineDatasource/add',
      method: 'POST',
      data
    })
  },
  update(data){
    return request({
      url: '/activiti/onlineDatasource/update',
      method: 'POST',
      data
    })
  },
  view(data){
    return request({
      url: '/activiti/onlineDatasource/view',
      method: 'GET',
      params: data
    })
  },
  delete(data){
    return request({
      url: '/activiti/onlineDatasource/delete',
      method: 'DELETE',
      params: data
    })
  },
}
