import request from "@/utils/request";
// 知识点管理接口

// 获取课程大类
export function getCourseCategories() {
  return request({
    url: '/znyy/curriculum/chinese',
    method: 'GET'
  });
}

// 查询课程分类树及版本(不需要版本呢ID)
export function getCourseTreeDataNoVersion(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/selectTree',
    method: 'GET',
    params:data
  })
}

// 分页查询知识点
export function getKnowledgeList(data) {
  return request({
    url: '/dyw/web/chinese/courseKnowledge/list',
    method: 'GET',
    params:data
  })
}

// 新增/编辑 知识点
export function addEditKnowledge(data) {
  return request({
    url: '/dyw/web/chinese/courseKnowledge/saveOrUpdateKnowledge',
    method: 'POST',
    data
  })
}

// 新增/编辑 知识点小节
export function addEditKnowledgeSection(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/insertOrUpdate',
    method: 'POST',
    data
  })
}

// 删除知识点小节
export function deleteKnowledgeSection(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/delete',
    method: 'DELETE',
    params: {id: data}
  })
}

// 导入文件
export function importKnowledgeFile(data) {
  return request({
    url: '/dyw/web/chinese/courseKnowledge/importFile',
    method: 'POST',
    data
  })
}

// 删除知识点
export function deleteKnowledge(data) {
  return request({
    url: '/dyw/web/chinese/courseKnowledge/delete-knowledge',
    method: 'DELETE',
    params: data
  })
}

// 更新知识点排序
export function updateKnowledgeSort(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/updateSort',
    method: 'POST',
    data
  })
}
