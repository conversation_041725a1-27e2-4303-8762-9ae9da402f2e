<template>
  <div>
    <CustomDialog title="编辑渠道经理" v-if="dialogVisible" :value="dialogVisible" width="500px" @close="handleOuterClose">
      <el-form ref="channerManagerRef" :model="form" label-width="120px" :rules="rules">
        <el-form-item label="渠道经理姓名" prop="channelManagerName">
          <el-select
            v-model="form.channelManagerName"
            clearable
            filterable
            placeholder="请输入渠道经理姓名"
            remote
            :remote-method="remoteMethod"
            :ladding="loading"
            @change="changMobile"
            @input="handleInput"
          >
            <el-option v-for="item in managerList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="渠道经理手机号" prop="mobilePhone">
          <el-input disabled v-model="form.mobilePhone"></el-input>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button @click="handleOuterClose">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="handleConfirm">保 存</el-button>
      </template>
    </CustomDialog>
  </div>
</template>

<script>
  import brands from '@/api/brands';
  import CustomDialog from '@/components/customDialog/index.vue';
  export default {
    name: 'EditChannelManager',
    props: {
      isShowChannelManager: {
        type: Boolean,
        default: false
      }
    },
    components: {
      CustomDialog
    },
    data() {
      return {
        form: {
          channelManagerName: '',
          mobilePhone: '',
          merchantName: '',
          channelManagerUserCode: ''
        },
        merchantCode: '',
        merchantId: '',
        loading: false,
        managerList: [],
        rules: {
          channelManagerName: [{ required: true, message: '渠道经理姓名不为空', trigger: ['blur', 'change'] }],
          mobilePhone: [{ required: true, message: '渠道经理手机号不为空', trigger: ['blur', 'change'] }]
        }
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.isShowChannelManager;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },

    watch: {
      'form.channelManagerName': {
        handler(newVal) {
          // 当被清空时触发验证
          if (!newVal) {
            this.form.mobilePhone = '';
            return;
          }
          this.$refs.channerManagerRef.validateField('channelManagerName');
        },
        immediate: false
      }
    },
    methods: {
      changMobile(e) {
        this.form.mobilePhone = this.managerList.find((item) => e === item.value)?.channelManagerPhone || '';
        this.form.merchantCode = this.managerList.find((item) => e === item.value)?.merchantCode || '';
        this.form.channelManagerUserCode = this.managerList.find((item) => e === item.value)?.channelManagerUserCode || '';
      },
      handleInput(value) {
        if (value === undefined) {
          this.managerList = [];
          this.form.channelManagerPhone = undefined;
          // 在这里处理清除图标的点击事件
        } else if (value.length > 30) {
          this.$message.error('渠道经理名称不能大于30个字符');
        }
      },

      remoteMethod(name) {
        if (!name) return;
        if (name.length > 30) {
          this.$message.error('渠道经理名称不能大于30个字符');
          return;
        }
        brands
          .queryChannelManagerApi({ channelManagerName: name })
          .then((res) => {
            this.managerList =
              res.data?.map((item) => {
                return {
                  label: item.channelManagerName + item.channelManagerPhone,
                  value: item.channelManagerUserCode,
                  channelManagerUserCode: item.channelManagerUserCode,
                  merchantCode: item.merchantCode,
                  channelManagerPhone: item.channelManagerPhone
                };
              }) || [];
          })
          .catch((error) => {
            this.$message.error(error.message);
          });
      },

      handleOuterClose() {
        this.reset();
        this.$emit('handleOuterClose');
      },
      async handleConfirm() {
        if (this.loading) return;
        try {
          await this.$refs.channerManagerRef.validate((valid) => {
            if (!valid) return;
            this.loading = true;
            let params = {
              merchantCode: this.merchantCode,
              channelManagerUserCode: this.form.channelManagerUserCode,
              merchantId: this.merchantId
            };

            brands
              .editChannelManagerApi(params)
              .then((res) => {
                this.$message.success('操作成功');
                this.loading = false;
                this.reset();
                this.$emit('handleOuterClose', true);
              })
              .catch((error) => {
                this.loading = false;
                this.managerList = [];
                console.log('🚀🥶💩~ error', error);
              });
          });
        } catch (error) {
          console.log('🚀 ~ handleConfirm ~ error:', error);
          this.$message.error('操作失败');
          this.loading = false;
          this.managerList = [];
        }
      },

      setData(data) {
        this.form.mobilePhone = data.channelManagerPhone || '';
        this.form.merchantName = data.merchantName || '';
        this.merchantCode = data.merchantCode || '';
        this.merchantId = data.merchantId;
        this.form.channelManagerName = data.channelManagerName + this.form.mobilePhone || '';
        this.form.channelManagerUserCode = data.channelManagerUserCode || '';
      },

      reset() {
        this.form = {
          channelManagerName: '',
          mobilePhone: '',
          merchantName: '',
          channelManagerUserCode: ''
        };
        this.managerList = [];
        this.$refs.channerManagerRef.resetFields();
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    text-align: right;
  }
</style>
