<template>
  <div v-loading="pageLoading" element-loading-text="保存中">
    <div class="flex-title">{{ !this.editId ? '新增' : '编辑' }}题目</div>
    <el-card>
      <el-main class="flex-center">
        <el-form :model="form" label-width="180px">
          <el-form-item label="课程大类：" required>
            <el-select :disabled="editId != ''" style="width: 600px" v-model="form.curriculumId" placeholder="请选择课程大类">
              <el-option v-for="item in curriculumList" :key="item.id" :value="item.id" :label="item.enName"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="试题难度：" required>
            <el-select style="width: 600px" v-model="form.questionDifficulty" placeholder="请选择试题难度">
              <el-option v-for="item in questionDifficultyList" :key="item.code" :label="item.value" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学科学段：" required>
            <el-cascader :disabled="editId != ''" style="width: 600px" :options="subjectList" v-model="form.disciplineId" @change="handleChange" clearable></el-cascader>
          </el-form-item>
          <el-form-item label="题目类型：" required>
            <el-select :disabled="editId != ''" style="width: 600px" v-model="form.questionType" placeholder="请选择题目类型" @change="changeQuestion">
              <el-option v-for="item in questionTypeList" :key="item.code" :label="item.value" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择题类型：" required>
            <el-radio-group v-model="form.choiceQuestionType">
              <el-radio disabled :label="1">单选题</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选项类型：" required>
            <el-radio-group v-model="form.optionType">
              <el-radio disabled :label="1">文本类型</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="关联知识点：">
            <el-button style="width: 600px" @click="handleChangeKnowledgeId" clearable>
              {{ !this.knowledgeIds ? '请选择关联知识点' : `已选择${this.knowledgeIds.length}个知识点` }}
            </el-button>
          </el-form-item>
          <el-form-item label="推荐答题时间：" required>
            <el-input style="width: 600px" v-model="form.answerTime" @input="validateAnswerTime" placeholder="请输入推荐答题时间"><template slot="append">分钟</template></el-input>
          </el-form-item>
          <!-- start -->

          <el-form-item label="题干：" required>
            <el-input
              style="width: 600px; margin-right: 10px"
              :rows="20"
              type="textarea"
              v-model="form.questionText"
              placeholder="请输入题干内容"
              maxlength="1000"
              show-word-limit
            ></el-input>
            <!-- <div style="border: 1px solid #ccc; width: 600px; margin-bottom: 20px">
              <Editor
                style="height: 200px; overflow-y: hidden"
                v-model="form.questionText"
                :defaultConfig="editorConfig"
                @onChange="questionTextChange"
                :mode="mode"
                @onCreated="onCreated"
              />
            </div> -->
            <span v-if="questionShow">
              <el-button v-if="form.questionType == 1" style="color: #4c8eff; border-color: #4c8eff" size="small" icon="el-icon-plus" @click="addBlank()">添加填空位置</el-button>
              <el-button v-else-if="form.questionType !== 0" style="color: #4c8eff; border-color: #4c8eff" size="small" icon="el-icon-plus" @click="addQuestion()">
                添加问题小点
              </el-button>
            </span>
          </el-form-item>
          <!-- 小题循环区域 -->
          <div v-for="(itemMaths, index) in form.mathQuestionOptionCos" :key="index">
            <el-form-item :label="textLabel + (index + 1) + '：'" style="width: 830px" required>
              <div v-if="questionShow">
                <span v-if="form.questionType != 1">
                  <el-input
                    v-model="itemMaths.questionSmallProblem"
                    type="textarea"
                    placeholder="请输入问题："
                    style="margin-bottom: 10px; width: 600px; margin-right: 10px"
                  ></el-input>
                  <el-input
                    v-model="itemMaths.scoreProportion"
                    placeholder="请输入分数占比："
                    style="margin-bottom: 10px; width: 570px; margin-right: 10px"
                    @input="itemMaths.scoreProportion = validateInput($event)"
                  >
                    <template slot="append">%</template>
                  </el-input>
                  <i class="el-icon-delete" style="font-size: 26px; margin-top: 5px" @click="deleteQuestion(index)"></i>
                </span>
              </div>

              <el-radio-group :value="itemMaths.correctAnswerIndex" v-model="itemMaths.correctAnswerIndex" @change="groupChanged(index, $event)">
                <span v-if="form.questionType == 1">
                  <el-input
                    v-model="itemMaths.scoreProportion"
                    placeholder="请输入分数占比："
                    style="margin-bottom: 10px; width: 570px; margin-right: 10px"
                    @input="itemMaths.scoreProportion = validateInput($event)"
                  >
                    <template slot="append">%</template>
                  </el-input>
                  <i class="el-icon-delete" style="font-size: 26px; margin-top: 5px" @click="deleteQuestion(index)"></i>
                </span>
                <el-table
                  class="common-table"
                  style="margin-bottom: 30px; width: 600px"
                  :data="itemMaths.optionsData"
                  stripe
                  border
                  max-height="500"
                  :default-sort="{
                    prop: 'addTime',
                    order: 'descending'
                  }"
                >
                  <el-table-column prop="choiceOption" label="勾选正确答案">
                    <template slot-scope="scope">
                      <el-radio :key="scope.row.id" :label="scope.$index" :value="scope.row.optionIsAnswer">
                        {{ scope.row.choiceOption }}
                      </el-radio>
                    </template>
                  </el-table-column>
                  <el-table-column prop="content" label="选项内容">
                    <template slot-scope="scope">
                      <el-input
                        v-if="scope.row.tableEditing"
                        type="textarea"
                        :autosize="{ minRows: 1, maxRows: 5 }"
                        v-model="scope.row.content"
                        placeholder="请输入内容"
                        maxlength="100"
                        show-word-limit
                        @keydown.native="handleOptionInputKeydown"
                      ></el-input>
                      <span v-else-if="scope.row.isDeleted !== 1" v-html="scope.row.content"></span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="id" label="操作" fixed="right" width="200px">
                    <template slot-scope="scope" v-if="scope.row.isDeleted !== 1">
                      <el-button v-if="scope.row.tableEditing" type="text" size="medium" @click="confirmTable(scope.$index, scope.row)">确定</el-button>
                      <el-button v-if="!scope.row.tableEditing" type="text" size="medium" @click="confirmTableEdit(scope.$index, scope.row)">编辑</el-button>
                      <el-button type="text" size="medium" style="color: red" @click="deleteOption(index, scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button style="color: #4c8eff; border-color: #4c8eff; width: 600px; margin-top: -10px" size="medium" icon="el-icon-plus" @click="confirmOrigin(index)">
                  添加选项
                </el-button>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- end -->
          <el-form-item label="图片：">
            <math-upload
              :imgSize="10 * 1024 * 1024"
              :showTip="false"
              :isKbOrMb="100000"
              @handleSuccess="handlePicSuccess"
              @handleRemove="handlePicRemove"
              :fullUrl="true"
              :file-list="fileList"
              :limit="5"
            />
            <div class="tips" style="font-size: 12px; color: red">仅支持上传5张图，图片大小不超过10MB，支持jpg，jpeg，png格式图片</div>
          </el-form-item>
          <el-form-item label="解析：" required>
            <el-input style="width: 600px" :rows="20" type="textarea" v-model="form.analysis" placeholder="请输入解析内容" maxlength="2000" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="解析图片：">
            <math-upload
              :showTip="false"
              :imgSize="10 * 1024 * 1024"
              :isKbOrMb="100000"
              @handleSuccess="analysisImageSuccess"
              @handleRemove="analysisImageRemove"
              :fullUrl="true"
              :file-list="analysisImageList"
              :limit="5"
            />
            <div class="tips" style="font-size: 12px; color: red">图片大小不超过10MB，支持jpg，jpeg，png格式图片</div>
          </el-form-item>
        </el-form>
      </el-main>
    </el-card>
    <div class="bottom">
      <el-button size="medium" @click="onCancel">取消</el-button>
      <el-button size="medium" type="primary" @click="onSubmit">保存</el-button>
    </div>
    <el-dialog title="添加知识点" :visible.sync="isKnow" width="70%" @close="isKnow = false" :before-close="dialogBeforeClose">
      <span>
        <knowledge-point
          ref="knowledgePointRef"
          v-if="isKnow"
          :disciplineId="this.form.disciplineId[0]"
          :curriculumId="this.form.curriculumId"
          :knowledgePointIds="this.knowledgeIds"
        ></knowledge-point>
      </span>
      <span slot="footer">
        <el-button @click="isKnow = false">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import MathUpload from '@/components/Upload/MathUpload.vue';
import { Editor } from '@wangeditor/editor-for-vue';
import forStudent from '@/api/testPaper/management';
import { getCurriculumAPI, addQuestionAPI, getSubjectAPI, getDifficultyAPI, getTypeAPI, listQuestionOneAPI } from '@/api/mathApi/topicManagementAPI';
export default {
  name: 'addQuestion',
  components: { MathUpload, Editor },
  data() {
    return {
      pageLoading: false,
      isKnow: false,
      editor: null,
      editor1: null,
      editor2: null,
      html: '',
      toolbarConfig: {},
      knowledgeIds: [],
      editorConfig: { placeholder: '请输入内容...' },
      mode: 'default', // or 'simple'

      questionShow: false, // 是否显示选择题
      form: {
        optionType: 1, // 选项类型
        choiceQuestionType: 1, // 选择题类型
        curriculumId: '', // 课程大类
        questionType: '', // 题目类型
        answerTime: '', // 推荐答题时间
        questionText: '', // 题干
        analysis: '', // 解析
        correctAnswer: '', // 正确答案
        disciplineId: '', // 学科学段
        gradeId: '', // 年级
        questionDifficulty: '', // 试题难度
        questionImg: [], // 选项图片
        analysisImg: [], // 解析图片
        mathQuestionOptionCos: [
          // 选项1
          {
            questionId: '',
            questionSmallProblem: '', // 小问题
            questionScore: '', // 分数占比
            correctAnswerIndex: '', // 正确答案索引
            scoreProportion: '',
            optionsData: [
              {
                choiceOption: 'A',
                optionIsAnswer: '0', // 是否为正确答案
                content: '',
                tableEditing: true,
                questionLocation: 0,
                isDeleted: 0
              }
            ]
          }
        ]
      },
      editId: '',
      fileList: [], // 上传图片已有图片列表
      analysisImageList: [], // 解析图片已有图片列表
      uploadLoading: false, // 上传图片加载按钮
      textLabel: '填空',
      curriculumList: [], // 课程大类列表
      questionDifficultyList: [], // 试题难度列表
      subjectList: [], // 学科列表
      subjectValue: [], // 学科值
      questionTypeList: [], // 题目类型列表
      previousQuestionType: null // 新增字段用于保存上一次的题目类型
    };
  },
  created() {
    this.editId = this.$route.query.editId;
    this.getKcdlList();
    this.getDifficulty();
    this.getType();
    if (this.editId != '') {
      this.getQuestionDetail();
    }
  },
  watch: {
    '$route.query.editId': function (newVal) {
      console.log('bbbbbbbbbb', newVal);
      if (newVal) {
        // 编辑模式
        this.editId = newVal;
        this.getQuestionDetail();
      } else {
        // 新增模式
        this.editId = '';
        this.resetForm();
      }
    },
    'form.curriculumId': function (newVal) {
      if (newVal) {
        this.getSubjectList();
      }
    },
    'form.questionType': function (newVal) {
      if (newVal == 0) {
        console.log('🚀 ~ newVal:', newVal);
        this.questionShow = false; // 填空
        this.textLabel = '选项';
      } else if (newVal == 1) {
        this.questionShow = true; // 选择题
        this.textLabel = '填空';
      } else {
        this.questionShow = true; // 选择题
        this.textLabel = '问题';
      }
      this.form.mathQuestionOptionCos.forEach((question) => {
        // question.optionsData = []; // 清空选项
        // question.correctAnswerIndex = ''; // 清空正确答案索引
      });
    }
  },

  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  },

  methods: {
    handleOptionInputKeydown(e) {
      // 左箭头或右箭头（37 或 39）时阻止冒泡
      if ([37, 38, 39, 40].includes(e.keyCode)) {
        e.stopPropagation();
      }
    },
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    onCreated1(editor) {
      this.editor1 = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    questionTextChange(editor) {
      const maxLength = 1000; // 最大字数限制
      const plainText = editor.getText(); // 获取纯文本内容

      if (plainText.length > maxLength) {
        const truncatedText = plainText.substring(0, maxLength); // 截取前 1000 个字符
        editor.dangerouslyInsertHtml(truncatedText); // 更新编辑器内容
        this.form.questionText = truncatedText; // 同步更新 v-model 的值
        this.$message.warning(`输入内容已超出最大限制 ${maxLength} 字，已自动截取`);
      } else {
        this.form.questionText = editor.getHtml(); // 更新绑定的内容
      }
    },
    validateInput(value) {
      // 仅保留数字字符
      value = value.replace(/\D/g, '');
      // 若输入为空，直接返回空字符串
      if (value === '') {
        return '';
      }
      if (Number(value.slice(0, 2)) < 100 && Number(value.slice(0, 2)) != 10) {
        value = value.slice(0, 2);
      } else if (Number(value.slice(0, 3)) == 100) {
        return '100';
      }
      // 将输入值转换为数字
      const num = parseInt(value, 10);
      // 限制数值范围在 0 - 100 之间
      const clampedNum = Math.min(Math.max(num, 0), 100);
      return clampedNum.toString();
    },

    validateAnswerTime(value) {
      // 只允许输入正整数
      const num = parseInt(value, 10);
      if (!/^\d+$/.test(value) || num < 1 || num > 60) {
        this.$message.warning('推荐答题时间必须是 1 到 60 的正整数');
        this.form.answerTime = ''; // 清空不合法的输入
      } else {
        this.form.answerTime = num; // 确保值为整数
      }
    },
    analysisChange(editor) {
      try {
        const maxLength = 2000; // 最大字数限制
        const plainText = editor.getText(); // 获取纯文本内容

        if (plainText.length > maxLength) {
          const truncatedText = plainText.substring(0, maxLength);
          this.form.analysis = truncatedText; // 更新 v-model 的值
          editor.dangerouslyInsertHtml(truncatedText); // 使用安全的方式更新编辑器内容
          this.$message.warning(`输入内容已超出最大限制 ${maxLength} 字，已自动截取`);
        } else {
          this.form.analysis = editor.getHtml(); // 更新绑定的内容
        }
      } catch (error) {
        console.error('Error in analysisChange:', error);
      }
    },

    onCreated2(editor) {
      this.editor2 = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    dialogBeforeClose() {
      this.isKnow = false;
    },
    changeQuestion() {
      console.log('changeQuestion题目类型切换', this.form.questionType);
      // 判断当前类型是否与之前的不同
      if (this.form.questionType !== this.previousQuestionType) {
        this.clearQuestionData(); // 清空上一题型的数据
        this.previousQuestionType = this.form.questionType; // 更新记录的题型
      }
    },
    clearQuestionData() {
      this.knowledgeIds = [];
      this.form.answerTime = '';
      this.form.questionText = '';
      // 清空 mathQuestionOptionCos 数据
      this.form.mathQuestionOptionCos = [
        {
          questionId: '',
          questionSmallProblem: '',
          scoreProportion: '',
          correctAnswerIndex: '',
          optionsData: [
            {
              choiceOption: 'A',
              optionIsAnswer: '0',
              scoreProportion: '',
              content: '',
              tableEditing: true,
              questionLocation: 0,
              isDeleted: 0
            }
          ]
        }
      ];
      this.fileList = [];
      this.analysisImageList = [];
      this.form.questionImg = [];
      this.form.analysisImg = [];
      this.form.analysis = '';
    },
    handleChangeKnowledgeId() {
      if (this.form.curriculumId == '' || this.form.curriculumId == null) {
        this.$message.error('请先选择课程大类');
        return false;
      }
      if (this.form.disciplineId.length == 0) {
        console.log('🚀 ~ handleChangeKnowledgeId ~ this.form.disciplineId:', this.form.disciplineId);
        this.$message.error('请先选择学科学段');
        return;
      }
      this.isKnow = true;
    },
    confirmDialog() {
      const selectedKnowledge = this.$refs.knowledgePointRef.multipleSelection;
      console.log('🚀 ~ confirmDialog ~ selectedKnowledge:', selectedKnowledge);
      console.log('1111111:', this.form.disciplineId[0]);
      // 处理你的逻辑，比如赋值到表单
      this.knowledgeIds = selectedKnowledge;
      this.isKnow = false;
    },
    // 获取所有的课程大类
    getKcdlList() {
      forStudent.getKcdlForKnowledge().then((res) => {
        if (res.success) {
          this.curriculumList = res.data;
        }
      });
    },
    // 获取试题难度列表
    async getDifficulty() {
      const res = await getDifficultyAPI();
      if (res.success) {
        this.questionDifficultyList = res.data;
      }
    },
    // 获取题目类型列表
    async getType() {
      const res = await getTypeAPI();
      if (res.success) {
        this.questionTypeList = res.data;
      }
    },
    transformToForm(data) {
      if (!data) return null;

      const mathQuestionOptionCos = [];

      // 按 questionLocation 分组选项
      const optionsGroupedByLocation = {};
      data.mathQuestionOptionVos?.forEach((option, index) => {
        console.log('11111111111111111111111111111111111QuestionOptionVos?.forEach ~ option:', data.mathQuestionOptionVos);
        console.log('222222222222222222222222222222222222222?.forEach ~ option:', data.mathQuestionOptionVos[index].scoreProportion);
        const location = option.questionLocation || 0; // 默认归到第0小题
        if (!optionsGroupedByLocation[location]) {
          optionsGroupedByLocation[location] = {
            questionSmallProblem: option.questionSmallProblem,
            scoreProportion: data.mathQuestionOptionVos[index].scoreProportion,
            optionsData: []
          };
        }
        optionsGroupedByLocation[location].optionsData.push({
          choiceOption: option.choiceOption, // A/B/C/D
          content: option.content, // 选项文本
          questionId: option.questionId, // 题目ID
          id: option.id,
          optionIsAnswer: option.optionIsAnswer, // 是否正确答案
          tableEditing: false, // 初始状态非编辑
          isDeleted: option.isDeleted, // 是否删除
          scoreProportion: data.mathQuestionOptionVos[index].scoreProportion // 分数占比
        });
      });

      // 构建 mathQuestionOptionCos 结构
      Object.keys(optionsGroupedByLocation).forEach((location) => {
        const locationData = optionsGroupedByLocation[location];
        const options = locationData.optionsData;

        // 找到正确答案的索引
        const correctAnswerIndex = options.findIndex((opt) => opt.optionIsAnswer === 1);
        console.log('🚀 ~ Object.keys ~ correctAnswerIndex:', correctAnswerIndex);

        mathQuestionOptionCos.push({
          questionId: data.id,
          questionSmallProblem: locationData.questionSmallProblem || '', // 小题题干
          scoreProportion: locationData.scoreProportion, // 分数占比
          correctAnswerIndex: correctAnswerIndex >= 0 ? correctAnswerIndex : '', // 确保索引有效
          optionsData: options // 关联的选项列表
        });
      });
      console.log('🚀 ~ Object.keys ~ mathQuestionOptionCos:', mathQuestionOptionCos);

      // 如果没有选项，初始化一个空小题（防止空数据）
      if (mathQuestionOptionCos.length === 0) {
        mathQuestionOptionCos.push({
          questionId: data.id,
          questionSmallProblem: '',
          scoreProportion: '',
          correctAnswerIndex: -1,
          optionsData: []
        });
      }
      return {
        optionType: 1,
        choiceQuestionType: data.choiceQuestionType || 1,
        curriculumId: data.curriculumId,
        knowledgeIds: data.courseKnowledgeCos?.map((item) => item.id).join(','),
        questionType: data.questionType,
        answerTime: data.answerTime,
        questionText: data.questionText,
        analysis: data.analysis,
        correctAnswer: data.correctAnswer,
        disciplineId: data.disciplineId ? [data.disciplineId, data.gradeId] : [], // 级联选择器格式
        gradeId: data.gradeId,
        questionDifficulty: data.questionDifficulty,
        questionImg: data.questionImg || [], // 图片列表
        analysisImg: data.analysisImg || [],
        mathQuestionOptionCos: mathQuestionOptionCos // 嵌套结构的选项
      };
    },
    groupChanged(questionIndex, selectedIndex) {
      const optionsData = this.form.mathQuestionOptionCos[questionIndex].optionsData;
      optionsData.forEach((option, index) => {
        option.optionIsAnswer = index === selectedIndex ? '1' : '0';
      });
      this.form.mathQuestionOptionCos[questionIndex].correctAnswerIndex = selectedIndex;
    },
    // 获取试题详情
    async getQuestionDetail() {
      const res = await listQuestionOneAPI({ id: this.editId });
      console.log('form:', res);
      if (res.success) {
        // 确保数据正确转换
        const transformedData = this.transformToForm(res.data);
        console.log('Transformed data:', transformedData);
        this.form = transformedData;
        this.fileList = res.data.questionImg?.map((url) => ({ url })) || [];
        this.analysisImageList = res.data.analysisImg?.map((url) => ({ url })) || [];
        this.knowledgeIds = res.data.courseKnowledgeDtoList;

        // 确保选项数据正确显示
        this.form.mathQuestionOptionCos.forEach((question, index) => {
          console.log(`Question ${index}:`, question);
        });
      }
    },

    transformToCascaderData(data, level = 1, maxLevel = 2) {
      if (!data) return []; // 添加对 null 或 undefined 数据的处理
      return data
        .map((item) => {
          const node = {
            label: item.nodeName, // 使用 nodeName 作为显示的名称
            value: item.id // 使用 id 作为值
          };
          // 只在当前层级小于最大层级且存在子列表时才递归添加 children
          if (level < maxLevel && item.childList && item.childList.length > 0) {
            const children = this.transformToCascaderData(item.childList, level + 1, maxLevel);
            if (children.length > 0) {
              node.children = children; // 只有当 children 有数据时才保留
            }
          }
          return node;
        })
        .filter((node) => node.children || level === maxLevel); // 过滤掉没有 children 的节点，除非是最大层级
    },

    // 获取学科列表
    async getSubjectList() {
      const res = await getSubjectAPI({
        curriculumId: this.form.curriculumId,
        nodeLevel: 3 // 确保 API 请求获取到第三级数据
      });
      if (res.success && res.data) {
        this.subjectList = this.transformToCascaderData(res.data, 1, 2);
        // console.log('🚀 ~ transformed subjectList:', this.subjectList); // 确认转换后的数据结构
      } else {
        this.subjectList = [];
      }
    },
    handleChange(value) {
      console.log('🚀 ~ cascader selected value:', value);
      this.subjectValue = value;
      console.log('🚀 ~ handleChange ~ this.subjectValue:', this.subjectValue);
    },

    addBlank() {
      const newBlank = {
        questionLocation: '',
        questionSmallProblem: '',
        questionScore: '',
        correctAnswerIndex: '',
        scoreProportion: '',
        questionImg: '',
        optionsData: [],
        isDeleted: 0 // 是否删除
      };
      this.form.mathQuestionOptionCos.push(newBlank);
    },
    getChoiceOption(index) {
      return String.fromCharCode(65 + index); // 65 是字母 'A' 的 ASCII 码
    },
    confirmOrigin(index) {
      const question = this.form.mathQuestionOptionCos[index];
      // 过滤掉已删除的选项
      const validOptions = question.optionsData.filter((option) => option.isDeleted === 0);
      if (validOptions.length >= 26) {
        this.$message.warning('每个小题最多只能添加 26 个选项（A - Z）');
        return;
      }
      // 标记已使用的字母
      const usedLetters = new Array(26).fill(false);
      validOptions.forEach((option) => {
        const charCode = option.choiceOption.charCodeAt(0) - 65;
        if (charCode >= 0 && charCode < 26) {
          usedLetters[charCode] = true;
        }
      });
      // 找到第一个未使用的字母
      let nextCharCode = 65;
      for (let i = 0; i < 26; i++) {
        if (!usedLetters[i]) {
          nextCharCode = 65 + i;
          break;
        }
      }
      const newOption = {
        choiceOption: String.fromCharCode(nextCharCode), // 自动生成下一个选项字母
        content: '', // 选项内容
        optionIsAnswer: '0', // 是否为正确答案
        tableEditing: true, // 初始状态为编辑
        isDeleted: 0 // 是否删除
      };
      question.optionsData.push(newOption);
      // 对选项按字母顺序排序
      question.optionsData.sort((a, b) => {
        return a.choiceOption.localeCompare(b.choiceOption);
      });
    },
    addQuestion(index) {
      const newBlank = {
        questionSmallProblem: '',
        questionScore: '',
        correctAnswerIndex: '',
        questionImg: '',
        optionsData: [],
        isDeleted: 0 // 是否删除
      };
      this.form.mathQuestionOptionCos.push(newBlank);
    },
    deleteQuestion(index) {
      console.log('deleteQuestion', index);
      this.$confirm('您确定要删除这条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 删除指定索引的数据
          this.form.mathQuestionOptionCos.splice(index, 1);
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    confirmTableEdit(index, row) {
      console.log(index, row, '1111111111');
      this.$set(row, 'tableEditing', true);
    },
    confirmTable(index, row) {
      console.log('🚀 ~ confirmTable ~ row:', row);
      // 处理确定按钮点击逻辑
      this.$set(row, 'tableEditing', false);
    },
    deleteOption(index, questionIndex) {
      // this.form.mathQuestionOptionCos[index].optionsData.splice(questionIndex, 1);
      const options = this.form.mathQuestionOptionCos[index].optionsData;

      // 删除指定选项
      options.splice(questionIndex, 1);

      // 重新分配 choiceOption (A, B, C...)
      options.forEach((option, i) => {
        option.choiceOption = String.fromCharCode(65 + i);
      });
    },
    handlePicSuccess(url) {
      this.form.questionImg.push(url);
    },
    handlePicRemove() {
      this.form.questionImg = [];
    },
    analysisImageSuccess(url) {
      this.form.analysisImg.push(url);
    },
    analysisImageRemove() {
      this.form.analysisImg = [];
    },
    onCancel() {
      this.$router.go(-1);
      this.resetForm();
      this.knowledgeIds = [];
    },
    validateForm() {
      if (!this.form.curriculumId) {
        this.$message.error('请选择课程大类');
        return false;
      }
      if (this.form.questionDifficulty === '') {
        this.$message.error('请选择试题难度');
        return false;
      }
      if (!this.form.disciplineId || this.form.disciplineId.length === 0) {
        this.$message.error('请选择学科学段');
        return false;
      }
      if (this.form.questionType === '' || this.form.questionType === null || this.form.questionType === undefined) {
        console.log('🚀 ~ validateForm ~ this.form.questionType:', this.form.questionType);
        this.$message.error('请选择题目类型');
        return false;
      }
      // if (!this.knowledgeIds) {
      //   this.$message.error('请选择关联知识点');
      //   return false;
      // }
      if (!this.form.answerTime) {
        this.$message.error('请输入推荐答题时间');
        return false;
      }
      if (!this.form.questionText) {
        this.$message.error('请输入题干内容');
        return false;
      }
      if (!this.form.analysis) {
        this.$message.error('请输入解析内容');
        return false;
      }

      // 校验选项数据
      for (let i = 0; i < this.form.mathQuestionOptionCos.length; i++) {
        const question = this.form.mathQuestionOptionCos[i];
        const options = question.optionsData;

        // 校验选项内容是否为空或处于编辑状态
        for (let j = 0; j < options.length; j++) {
          const option = options[j];
          // console.log('🚀 ~ validateForm ~ option:', option);
          if (!option.content || option.tableEditing) {
            this.$message.error(`第 ${i + 1} 个问题的选项 ${String.fromCharCode(65 + j)} 内容为空或未完成编辑`);
            return false;
          }
        }

        // 校验是否有正确答案
        const hasCorrectAnswer = options.some((option) => option.optionIsAnswer == '1');
        console.log('🚀 ~ validateForm ~ hasCorrectAnswer:', hasCorrectAnswer);
        console.log('🚀 ~ validateForm ~ options:', options);
        if (!hasCorrectAnswer) {
          this.$message.error(`第 ${i + 1} 个问题未选择正确答案`);
          return false;
        }
      }

      return true;
    },
    resetForm() {
      this.form = {
        optionType: 1,
        choiceQuestionType: 1,
        curriculumId: '',
        knowledgeIds: '',
        questionType: '',
        answerTime: '',
        questionText: '',
        analysis: '',
        correctAnswer: '',
        disciplineId: '',
        gradeId: '',
        questionDifficulty: '',
        questionImg: [],
        analysisImg: [],
        mathQuestionOptionCos: [
          {
            questionId: '',
            questionSmallProblem: '',
            questionScore: '',
            correctAnswerIndex: '',
            scoreProportion: '',
            optionsData: [
              {
                choiceOption: 'A',
                optionIsAnswer: '0',
                scoreProportion: '',
                content: '',
                tableEditing: true,
                questionLocation: 0,
                isDeleted: 0
              }
            ]
          }
        ]
      };
      this.fileList = [];
      this.analysisImageList = [];
      this.subjectValue = [];
    },
    onSubmit() {
      console.log('form', this.form);
      if (!this.validateForm()) {
        return;
      }
      console.log('form', this.form);
      // 转换 mathQuestionOptionCos 数据结构
      const transformedMathQuestionOptionCos = this.form.mathQuestionOptionCos.flatMap((item, index) =>
        item.optionsData.map((option) => ({
          ...option, // 展开选项的字段
          questionId: item.questionId,
          questionLocation: index,
          scoreProportion: item.scoreProportion,
          questionSmallProblem: item.questionSmallProblem,
          questionScore: item.questionScore,
          correctAnswerIndex: item.correctAnswerIndex
        }))
      );
      const params = {
        id: this.editId,
        buildType: 0,
        choiceQuestionType: this.form.choiceQuestionType,
        optionType: this.form.optionType,
        courseKnowledgeDtoList: this.knowledgeIds,
        curriculumId: this.form.curriculumId,
        courseId: this.form.courseId,
        questionType: this.form.questionType,
        answerTime: this.form.answerTime,
        questionText: this.form.questionText,
        analysis: this.form.analysis,
        correctAnswer: this.form.correctAnswer,
        disciplineId: this.editId == '' ? this.subjectValue[0] : this.form.disciplineId[0],
        gradeId: this.editId == '' ? this.subjectValue[1] : this.form.disciplineId[1],
        questionDifficulty: this.form.questionDifficulty,
        questionImg: this.form.questionImg,
        analysisImg: this.form.analysisImg,
        mathQuestionOptionCos: transformedMathQuestionOptionCos
      };
      this.pageLoading = true;
      console.log('🚀 ~ onSubmit ~ params:', params);
      addQuestionAPI(params)
        .then((res) => {
          console.log('🚀 ~ addQuestionAPI ~ res:', res);
          if (res.success) {
            this.$message({
              message: this.editId != '' ? '编辑成功' : '添加成功',
              type: 'success'
            });
            // this.$router.replace({ query: { refresh: true } });
            this.$store.dispatch('delVisitedViews', this.$route);
            this.$router.push({ path: './topicManagement' });
            // this.$router.go(-1);
            this.resetForm();
            this.knowledgeIds = [];
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.pageLoading = false;
        });
    }
  }
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style lang="scss" scoped>
.flex-title {
  display: flex;
  justify-content: center;
  font-size: 26px;
  margin: 15px;
}
.flex-center {
  display: flex;
  justify-content: center;
}
.bottom {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}
</style>
