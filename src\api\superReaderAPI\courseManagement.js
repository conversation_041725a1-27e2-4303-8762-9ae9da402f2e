import request from "@/utils/request";
// 课程管理接口

// 获取课程大类
export function getCourseCategories() {
  return request({
    url: '/znyy/curriculum/chinese',
    method: 'GET'
  });
}

// 分页获取课程列表
export function getCourseList(data) {
  return request({
    url: '/dyw/web/chinese/courseManagement/list',
    method: 'GET',
    params:data
  })
}

// 获取课程版本列表
export function getCourseVersionList(data) {
  return request({
    url: '/dyw/web/chinese/basisConfig/selectVersionInfo',
    method: 'GET',
    params:data
  })
}

// 查询课程分类树及版本(需要版本ID)
export function getCourseTreeData(data) {
  return request({
    url: '/dyw/web/chinese/coursePeriodConfig/selectTreeVersion',
    method: 'GET',
    params:data
  })
}

// 新增课程
export function addCourse(data) {
  return request({
    url: '/dyw/web/chinese/courseManagement/saveOrUpdateCourse',
    method: 'POST',
    data
  })
}

// 查询课程详情
export function getCourseDetail(data) {
  return request({
    url: '/dyw/web/chinese/courseManagement/get-course-detail',
    method: 'GET',
    params:data
  })
}

// 保存话术
export function saveCourseSpeech(data) {
  return request({
    url: '/dyw/web/chinese/chineseCourseSpeech/insertOrUpdate',
    method: 'POST',
    data
  })
}

// 获取话术详情
export function getSpeechDetail(data) {
  return request({
    url: "/dyw/web/chinese/chineseCourseSpeech/queryList",
    method: "GET",
    params: data
  })
}

// 视频分页查询
export function getVideoListAPI(data) {
  return request({
    url: '/dyw/web/chinese/video/page',
    method: 'GET',
    params:data
  })
}

// 删除课程
export function delCourseAPI(data) {
  return request({
    url: '/dyw/web/chinese/courseManagement/delete-course',
    method: 'DELETE',
    params: data
  })
}

// 保存草稿
export function saveDraftAPI(data) {
  return request({
    url: '/dyw/web/chinese/courseManagement/saveDraft',
    method: 'POST',
    data
  })
}


