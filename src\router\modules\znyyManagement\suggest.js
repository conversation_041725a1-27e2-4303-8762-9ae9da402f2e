// 用户意见反馈-路由
import Layout from '@/views/layout/Layout';
const _import = require('../../_import_' + process.env.NODE_ENV);
export default {
  path: '/suggest',
  component: Layout,
  meta: {
    perm: 'm:suggest:suggest',
    redirect: '/suggest/suggest',
    title: '用户意见反馈',
    icon: 'suggest'
  },
  children: [
    {
      path: 'suggest',
      component: () => import('@/views/suggest/suggest'),
      name: 'suggest',
      meta: {
        perm: 'm:suggest:suggest',
        title: '用户意见反馈',
        icon: 'suggest'
      }
    }
  ]
};
