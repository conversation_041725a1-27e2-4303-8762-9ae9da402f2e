# DxSelect 自定义选择器组件

这是基于 Element UI 的 el-select、el-option 和 el-option-group 组件完全复制封装的自定义选择器组件。使用方式与原生 Element UI 选择器组件完全一致，但可以自由修改和定制。

## 组件说明

- `DxSelect`: 自定义选择器组件，对应 `el-select`，使用 `<dx-select>` 标签
- `DxOption`: 自定义选项组件，对应 `el-option`，使用 `<dx-option>` 标签
- `DxOptionGroup`: 自定义选项组组件，对应 `el-option-group`，使用 `<dx-option-group>` 标签

## 使用方式

### 基础用法

```vue
<template>
  <div>
    <dx-select v-model="value" placeholder="请选择">
      <dx-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </dx-option>
    </dx-select>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: '',
      options: [{
        value: '选项1',
        label: '黄金糕'
      }, {
        value: '选项2',
        label: '双皮奶'
      }, {
        value: '选项3',
        label: '蚵仔煎'
      }]
    }
  }
}
</script>
```

### 多选

```vue
<template>
  <dx-select v-model="value" multiple placeholder="请选择">
    <dx-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value">
    </dx-option>
  </dx-select>
</template>

<script>
export default {
  data() {
    return {
      value: [],
      options: [
        // ... 选项数据
      ]
    }
  }
}
</script>
```

### 分组

```vue
<template>
  <dx-select v-model="value" placeholder="请选择">
    <dx-option-group
      v-for="group in options"
      :key="group.label"
      :label="group.label">
      <dx-option
        v-for="item in group.options"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </dx-option>
    </dx-option-group>
  </dx-select>
</template>
```

## 支持的功能

- ✅ 所有 Element UI 选择器的原生功能
- ✅ 单选和多选
- ✅ 可清空选项
- ✅ 禁用状态
- ✅ 选项分组
- ✅ 自定义选项模板
- ✅ 可搜索选项
- ✅ 远程搜索
- ✅ 创建条目
- ✅ 所有事件和方法
- ✅ 不同尺寸

## 属性

### DxSelect Attributes

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| value / v-model | 绑定值 | boolean / string / number | — | — |
| multiple | 是否多选 | boolean | — | false |
| disabled | 是否禁用 | boolean | — | false |
| value-key | 作为 value 唯一标识的键名，绑定值为对象类型时必填 | string | — | value |
| size | 输入框尺寸 | string | medium/small/mini | — |
| clearable | 是否可以清空选项 | boolean | — | false |
| collapse-tags | 多选时是否将选中值按文字的形式展示 | boolean | — | false |
| multiple-limit | 多选时用户最多可以选择的项目数，为 0 则不限制 | number | — | 0 |
| name | select input 的 name 属性 | string | — | — |
| autocomplete | select input 的 autocomplete 属性 | string | — | off |
| placeholder | 占位符 | string | — | 请选择 |
| filterable | 是否可搜索 | boolean | — | false |
| allow-create | 是否允许用户创建新条目，需配合 filterable 使用 | boolean | — | false |
| filter-method | 自定义搜索方法 | function | — | — |
| remote | 是否为远程搜索 | boolean | — | false |
| remote-method | 远程搜索方法 | function | — | — |
| loading | 是否正在从远程获取数据 | boolean | — | false |
| loading-text | 远程加载时显示的文字 | string | — | 加载中 |
| no-match-text | 搜索条件无匹配时显示的文字，也可以使用slot="empty"设置 | string | — | 无匹配数据 |
| no-data-text | 选项为空时显示的文字，也可以使用slot="empty"设置 | string | — | 无数据 |
| popper-class | Select 下拉框的类名 | string | — | — |
| reserve-keyword | 多选且可搜索时，是否在选中一个选项后保留当前的搜索关键词 | boolean | — | false |
| default-first-option | 在输入框按下回车，选择第一个匹配项。需配合 filterable 或 remote 使用 | boolean | - | false |
| popper-append-to-body | 是否将弹出框插入至 body 元素。在弹出框的定位出现问题时，可将该属性设置为 false | boolean | - | true |
| automatic-dropdown | 对于不可搜索的 Select，是否在输入框获得焦点后自动弹出选项菜单 | boolean | - | false |

### DxSelect Events

| 事件名称 | 说明 | 回调参数 |
|---------|------|----------|
| change | 选中值发生变化时触发 | 目前的选中值 |
| visible-change | 下拉框出现/隐藏时触发 | 出现则为 true，隐藏则为 false |
| remove-tag | 多选模式下移除tag时触发 | 移除的tag值 |
| clear | 可清空的单选模式下用户点击清空按钮时触发 | — |
| blur | 当 input 失去焦点时触发 | (event: Event) |
| focus | 当 input 获得焦点时触发 | (event: Event) |

### DxOption Attributes

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| value | 选项的值 | string/number/object | — | — |
| label | 选项的标签，若不设置则默认与 value 相同 | string/number | — | — |
| disabled | 是否禁用该选项 | boolean | — | false |

### DxOptionGroup Attributes

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| label | 分组的组名 | string | — | — |
| disabled | 是否将该分组下所有选项置为禁用 | boolean | — | false |

## 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| focus | 使 input 获取焦点 | - |
| blur | 使 input 失去焦点，并隐藏下拉框 | - |

## 后续维护

当需要修改选择器功能时，可以直接编辑 `src/components/DxSelect/` 目录下的文件，修改后会影响整个项目中使用该组件的地方。

现在您可以在项目中使用 `<dx-select>`、`<dx-option>` 和 `<dx-option-group>` 组件了，它们的功能与原生 Element UI 选择器组件完全一致！
