<template>
  <el-dialog :visible.sync="visible" center title="关联知识点" width="60%" @opened="handleOpened" @close="handleClose" destroy-on-close>
    <div class="title" v-if="!isEmpty">
      <span>已选择{{ selectedCount }}个知识点</span>
    </div>
    <div class="min-content">
      <div v-if="isEmpty" class="empty-box">
        <div class="empty-text">此课程大类下暂无知识点，请创建后选择</div>
      </div>
      <js-mind v-else class="js-mind" :options="options" :values="mind" ref="jsMind" height="500px"></js-mind>
    </div>
    <span slot="footer" v-show="!isEmpty" class="dialog-footer footer-center">
      <el-button @click="onCancel">取 消</el-button>
      <el-button type="primary" @click="onConfirm" style="margin-left: 40px">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import '../../../../../public/jsmind/jsmind.menu';
  import knowledgeApi from '@/api/studyExamPassed/questionKnowledgeManage';

  export default {
    name: 'PointDialog',
    props: {
      // 文本截断显示的最大字符数（不含省略号）
      maxNodeTextLength: { type: Number, default: 40 },
      // 每个节点可视最大宽度（px），用于控制溢出与省略
      nodeTextMaxWidth: { type: Number, default: 230 }
    },
    data() {
      return {
        visible: false,
        jm: null,
        pendingRecord: null,
        // 事件委托引用
        _checkboxListener: null,
        _blockNodeClick: null,
        // 索引结构
        parentMap: new Map(),
        childrenMap: new Map(),
        rawNodeMap: new Map(),
        allNodeIds: [],
        // 选中集合 & 用户显式集合
        selectedIdSet: new Set(),
        explicitSelected: new Set(),
        // 统计 & 导出
        selectedCount: 0,
        selectedNodes: [],
        // 原始树
        rawTree: null,
        // 空态
        isEmpty: false,
        emptyText: '此课程大类下暂无知识点，请创建后选择',
        // 默认选中（外部可覆盖）
        defaultSelectedIds: [],
        // jsMind 数据（打开弹窗时由后端返回数据动态生成）
        mind: {
          meta: { name: '课程知识点', author: 'system', version: '1.0' },
          format: 'node_tree',
          data: { id: 'root', topic: '知识点', children: [] }
        },
        options: {
          container: 'jsmind_container',
          editable: false,
          theme: 'clouds',
          support_html: true,
          view: { engine: 'canvas', hmargin: 100, vmargin: 50, line_width: 1, line_color: '#C18AFB' },
          menuOpts: { showMenu: false, injectionList: [] },
          events: { node_click: null }
        }
      };
    },
    mounted() {
      document.oncontextmenu = () => false;
    },
    beforeDestroy() {
      this.removeDelegates();
      this.jm = null;
    },
    methods: {
      async init(record) {
        console.log('PointDialog init', record);
        // 若外部传入 selectedIds（例如再次打开时需要回显），写入 defaultSelectedIds
        if (record && Array.isArray(record.selectedIds)) {
          this.defaultSelectedIds = record.selectedIds.map((id) => String(id));
        } else {
          this.defaultSelectedIds = [];
        }
        this.pendingRecord = record;
        // --- 预判是否空数据，提前设置 isEmpty 避免弹框先渲染“已选择0个”再切换空态产生闪烁 ---
        const baseData = record && record.data ? record.data : (this.mind && this.mind.data) || {};
        const hasChildren = Array.isArray(baseData.children) && baseData.children.length > 0;
        this.isEmpty = !hasChildren;
        if (this.isEmpty) {
          this.selectedIdSet.clear();
          this.selectedNodes = [];
          this.selectedCount = 0;
        }
        this.visible = true;
      },

      onCancel() {
        this.visible = false;
      },
      onConfirm() {
        this.recalcSelectedCount();
        this.recalcSelectedNodesArray();
        // 导出选中 id（去掉 root）
        const ids = Array.from(this.selectedIdSet).filter((i) => i !== 'root');
        this.$emit('confirm', { count: this.selectedCount, nodes: this.selectedNodes, ids });
        this.visible = false;
      },
      handleOpened() {
        this.$nextTick(() => {
          const record = this.pendingRecord;
          const baseData = record && record.data ? record.data : this.mind.data;
          // 若 init 已经判空，这里直接跳过后续 jsMind 构建
          if (this.isEmpty) {
            this.pendingRecord = null;
            return;
          }
          // 构建索引
          this.rawTree = baseData;
          this.buildIndex(baseData);
          // 根据后端 isMark=1 标记初始选中节点
          const collectMarked = (n, acc) => {
            if (n.id !== 'root' && Number(n.isMark) === 1) acc.push(n.id);
            if (Array.isArray(n.children)) n.children.forEach((c) => collectMarked(c, acc));
          };
          const marked = [];
          collectMarked(baseData, marked);
          // 初始选中集合 = 后端标记 + （可选）defaultSelectedIds 去重合并
          const initial = new Set([...(this.defaultSelectedIds || []), ...marked]);
          this.selectedIdSet = initial;
          this.explicitSelected = new Set(this.selectedIdSet);
          // 向上补齐祖先
          Array.from(this.selectedIdSet).forEach((id) => this.addAncestors(id));
          // 生成渲染数据
          const built = this.buildMindWithCheckboxes(baseData);
          this.mind = { ...this.mind, data: built };
          this.jm = (this.$refs.jsMind && (this.$refs.jsMind.jm || this.$refs.jsMind.$jm)) || this.jm;
          if (this.jm && this.jm.show) {
            this.jm.show(this.mind);
            if (this.jm.expand_all) this.jm.expand_all();
          }
          // 绑定事件委托
          this.bindDelegates();
          // 初始视觉 & 统计
          this.updateAllNodeVisual();
          this.recalcSelectedCount();
          this.recalcSelectedNodesArray();
          this.pendingRecord = null;
        });
      },
      handleClose() {
        this.visible = false;
        this.removeDelegates();
        this.jm = null;
      },
      // ====== 构建 & 渲染 ======
      buildMindWithCheckboxes(node) {
        const escapeHtml = (str) => String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;');
        const maxLen = this.maxNodeTextLength;
        const clone = (n) => {
          const isRoot = n.id === 'root' || n.isroot;
          const raw = (n.topic || '').toString();
          const textFull = raw.replace(/<[^>]*>/g, '') || (isRoot ? '根节点' : '未命名节点');
          const truncated = textFull.length > maxLen ? textFull.slice(0, maxLen) + '…' : textFull;
          const safeFull = escapeHtml(textFull);
          const safeDisplay = escapeHtml(truncated);
          const checked = this.selectedIdSet.has(n.id);
          const html = isRoot
            ? safeDisplay
            : `<span class="jm-topic-with-check" title="${safeFull}"><span class="node-text" style="max-width:${
                this.nodeTextMaxWidth
              }px">${safeDisplay}</span><input type="checkbox" class="node-checkbox" data-node-id="${n.id}" ${checked ? 'checked' : ''} /></span>`;
          const out = { id: n.id, topic: html };
          if (Array.isArray(n.children) && n.children.length) out.children = n.children.map((c) => clone(c));
          return out;
        };
        return clone(node);
      },
      buildIndex(root) {
        this.parentMap = new Map();
        this.childrenMap = new Map();
        this.rawNodeMap = new Map();
        this.allNodeIds = [];
        const dfs = (n, parentId) => {
          this.allNodeIds.push(n.id);
          this.rawNodeMap.set(n.id, n);
          if (parentId) this.parentMap.set(n.id, parentId);
          if (Array.isArray(n.children) && n.children.length) {
            const ids = n.children.map((c) => c.id);
            this.childrenMap.set(n.id, ids);
            n.children.forEach((c) => dfs(c, n.id));
          } else {
            this.childrenMap.set(n.id, []);
          }
        };
        dfs(root, null);
      },
      // ====== 选择逻辑 ======
      addAncestors(id) {
        let p = this.parentMap.get(id);
        while (p && p !== 'root') {
          this.selectedIdSet.add(p);
          p = this.parentMap.get(p);
        }
      },
      collectSubtreeIds(id) {
        const result = [];
        const stack = [id];
        while (stack.length) {
          const cur = stack.pop();
          result.push(cur);
          (this.childrenMap.get(cur) || []).forEach((c) => stack.push(c));
        }
        return result;
      },
      hasSelectedDescendant(id) {
        const children = this.childrenMap.get(id) || [];
        for (const c of children) {
          if (this.selectedIdSet.has(c) || this.hasSelectedDescendant(c)) return true;
        }
        return false;
      },
      // 获取层级：root 下第一层为 1
      getNodeLevel(id) {
        let level = 0;
        let cur = id;
        while (cur && cur !== 'root') {
          level++;
          cur = this.parentMap.get(cur);
        }
        return level; // 未找到返回已累加层级
      },
      setChecked(id, checked, explicit = false) {
        if (checked) {
          this.selectedIdSet.add(id);
          if (explicit) this.explicitSelected.add(id);
          this.addAncestors(id);
        } else {
          const level = this.getNodeLevel(id);
          if (level === 1) {
            // 取消一级：整棵（自身+全部后代）
            this.collectSubtreeIds(id).forEach((nid) => {
              this.selectedIdSet.delete(nid);
              this.explicitSelected.delete(nid);
            });
          } else if (level === 2) {
            // 取消二级：自身 + 其下所有三级
            this.collectSubtreeIds(id).forEach((nid) => {
              this.selectedIdSet.delete(nid);
              this.explicitSelected.delete(nid);
            });
            // 不影响一级父节点
          } else {
            // 三级（或更深）仅取消自己
            this.selectedIdSet.delete(id);
            this.explicitSelected.delete(id);
          }
        }
        this.updateNodeAndAncestorsVisual(id);
        this.recalcSelectedCount();
        this.recalcSelectedNodesArray();
      },
      toggleNode(id) {
        this.setChecked(id, !this.selectedIdSet.has(id), true);
      },
      // ====== 视觉同步 ======
      updateNodeAndAncestorsVisual(id) {
        const rootEl = this.$refs.jsMind && this.$refs.jsMind.$el;
        if (!rootEl) return;
        const refresh = (nid) => {
          if (nid === 'root') return;
          const el = rootEl.querySelector(`jmnode[nodeid="${CSS.escape(nid)}"]`);
          if (!el) return;
          const cb = el.querySelector('input.node-checkbox');
          if (cb) {
            const checked = this.selectedIdSet.has(nid);
            cb.checked = checked;
            cb.indeterminate = false;
            cb.setAttribute('aria-checked', String(checked));
          }
        };
        refresh(id);
        if (!this.selectedIdSet.has(id)) this.collectSubtreeIds(id).forEach((nid) => refresh(nid));
        let p = this.parentMap.get(id);
        while (p) {
          refresh(p);
          p = this.parentMap.get(p);
        }
      },
      updateAllNodeVisual() {
        const rootEl = this.$refs.jsMind && this.$refs.jsMind.$el;
        if (!rootEl) return;
        rootEl.querySelectorAll('jmnode').forEach((el) => {
          const nid = el.getAttribute('nodeid');
          if (!nid || nid === 'root') return;
          const cb = el.querySelector('input.node-checkbox');
          if (!cb) return;
          const checked = this.selectedIdSet.has(nid);
          cb.checked = checked;
          cb.indeterminate = false;
          cb.setAttribute('aria-checked', String(checked));
        });
      },
      // ====== 统计 & 导出 ======
      getMinimalSelectedRootIds() {
        const result = [];
        for (const id of this.selectedIdSet) {
          if (id === 'root') continue;
          let p = this.parentMap.get(id);
          let ancestorSelected = false;
          while (p) {
            if (this.selectedIdSet.has(p)) {
              ancestorSelected = true;
              break;
            }
            p = this.parentMap.get(p);
          }
          if (!ancestorSelected) result.push(id);
        }
        return result;
      },
      cloneRawSubtreeById(id) {
        const src = this.rawNodeMap.get(id);
        if (!src) return null;
        const clone = (n) => {
          const node = { id: n.id, topic: n.topic };
          if (Array.isArray(n.children) && n.children.length) node.children = n.children.map((c) => clone(c));
          return node;
        };
        return clone(src);
      },
      recalcSelectedCount() {
        let total = this.selectedIdSet.size;
        if (this.selectedIdSet.has('root')) total -= 1;
        this.selectedCount = total;
      },
      recalcSelectedNodesArray() {
        const pruneClone = (id) => {
          if (!this.selectedIdSet.has(id)) return null;
          const original = this.rawNodeMap.get(id);
          if (!original) return null;
          const node = { id: original.id, topic: original.topic };
          const children = this.childrenMap.get(id) || [];
          const kept = children.map((cid) => pruneClone(cid)).filter(Boolean);
          if (kept.length) node.children = kept;
          return node;
        };
        const roots = this.getMinimalSelectedRootIds();
        this.selectedNodes = roots.map((r) => pruneClone(r)).filter(Boolean);
      },
      // ====== 事件委托 ======
      bindDelegates() {
        const rootEl = this.$refs.jsMind && this.$refs.jsMind.$el;
        if (!rootEl) return;
        this.removeDelegates();
        this._blockNodeClick = (e) => {
          if (e.target && e.target.closest && e.target.closest('jmnode')) {
            e.stopPropagation();
          }
        };
        rootEl.addEventListener('click', this._blockNodeClick, true);
        // 复选框点击
        this._checkboxListener = (e) => {
          const cb = e.target.closest && e.target.closest('input.node-checkbox');
          if (!cb) return;
          e.stopPropagation();
          const id = cb.getAttribute('data-node-id');
          if (!id) return;
          const willCheck = cb.checked;
          this.setChecked(id, willCheck, true);
        };
        rootEl.addEventListener('click', this._checkboxListener, true);
      },
      removeDelegates() {
        const rootEl = this.$refs.jsMind && this.$refs.jsMind.$el;
        if (!rootEl) return;
        if (this._checkboxListener) rootEl.removeEventListener('click', this._checkboxListener, true);
        if (this._blockNodeClick) rootEl.removeEventListener('click', this._blockNodeClick, true);
        this._checkboxListener = null;
        this._blockNodeClick = null;
      }
    }
  };
</script>

<style lang="less" scoped>
  @normal-text: PingFangSC-regular;
  @normal-color: rgba(16, 16, 16, 1);
  .title {
    text-align: center;
    line-height: 23px;
    color: @normal-color;
    font-size: 16px;
    font-family: @normal-text;
  }
  .min-content {
    height: 100%;
    padding: 10px;
    display: flex;
    flex-direction: column;
    .empty-box {
      width: 100%;
      height: 500px;
      border-radius: 6px;
      position: relative;
      overflow: hidden;
    }
    .empty-box::before,
    .empty-box::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, rgba(0, 0, 0, 0.15) 0, rgba(0, 0, 0, 0.15) 1px, transparent 1px),
        linear-gradient(-45deg, rgba(0, 0, 0, 0.15) 0, rgba(0, 0, 0, 0.15) 1px, transparent 1px);
      background-size: 100% 100%, 100% 100%;
      pointer-events: none;
    }
    .empty-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: @normal-color;
      font-size: 22px;
      text-align: center;
      padding: 0 20px;
      font-family: @normal-text;
    }
    .js-mind {
      width: 100%;
      height: 500px;
      background-color: rgba(239, 239, 239, 1);
      border-radius: 6px;
      overflow: hidden;
      position: relative;
    }
  }

  /deep/ .theme-clouds .selected {
    background-color: #daebfe !important;
    color: #333 !important;
  }
  /deep/ jmnode {
    box-shadow: none !important;
    background-color: #daebfe !important;
  }
  /deep/ jmnode[nodeid='root'] {
    background-color: #1685fc !important;
    color: #ffffff !important;
  }
  /deep/ jmnode[nodeid='root'].selected {
    background-color: #1685fc !important;
    color: #ffffff !important;
  }
  /deep/ .jm-topic-with-check {
    display: inline-flex;
    align-items: center;
    max-width: 100%;
  }
  /deep/.node-checkbox {
    position: static;
    margin-left: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  /deep/ .jm-topic-with-check .node-text {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
  }
  /* 加粗弹框标题字体 */
  /deep/ .el-dialog__title {
    font-weight: bold;
    font-size: 24px;
  }
  .footer-center {
    text-align: center;
  }
</style>
