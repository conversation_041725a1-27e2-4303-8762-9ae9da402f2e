<template>
  <div>
    <div class="app-container">
      <el-card>
        <div class="title">采购商品价格配置</div>
        <el-table :data="list" :height="500">
          <el-table-column prop="commodityType" label="商品名称" align="center" width="width"></el-table-column>
          <el-table-column prop="createTime" label="更新时间" align="center" width="width"></el-table-column>
          <el-table-column label="操作" fixed="right" align="center" width="width">
            <template slot-scope="scope">
              <div>
                <el-button type="primary" @click="viewDetails(scope.row)">查看</el-button>
                <el-button type="primary" @click="edit(scope.row)">编辑</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <CustomDialog v-if="goodsDialogVisible1" title="编辑" :value.sync="goodsDialogVisible1" :closeOnClickModal="false" width="35%" @close="closeDialog(1)">
        <div>
          <el-form ref="priceForm" :rules="rules" label-position="left" :model="purchaseAmountForm" label-width="120px">
            <el-form-item label="商品名称">合伙人智能学习管理系数</el-form-item>
            <el-form-item label="品牌采购价" prop="clubPurchasePrice">
              <el-input v-model="purchaseAmountForm.brandPurchasePrice" placeholder="请输入采购价" style="width: 70%; margin-right: 10px"></el-input>
              元/个
            </el-form-item>
            <el-form-item label="品牌批发价" prop="brandPurchasePrice">
              <el-input v-model="purchaseAmountForm.brandWholesalePrice" placeholder="请输入批发价" style="width: 70%; margin-right: 10px"></el-input>
              元/个
            </el-form-item>
            <el-form-item label="俱乐部采购价" prop="brandWholesalePrice">
              <el-input v-model="purchaseAmountForm.clubPurchasePrice" placeholder="请输入采购价" style="width: 70%; margin-right: 10px"></el-input>
              元/个
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer-center">
          <el-button @click="closeDialog(1)">取消</el-button>
          <el-button type="primary" @click="handleSubmit(1)">确定</el-button>
        </div>
      </CustomDialog>

      <CustomDialog v-if="goodsDialogVisible2" title="编辑" :value.sync="goodsDialogVisible2" :closeOnClickModal="false" width="35%" @close="closeDialog(2)">
        <div>
          <el-form ref="priceForm" :rules="rules" label-position="left" :model="purchaseAmountForm" label-width="120px">
            <el-form-item label="商品名称">全款俱乐部智能学习管理系数</el-form-item>
            <el-form-item label="品牌采购价" prop="brandPurchasePrice">
              <el-input v-model="purchaseAmountForm.brandPurchasePrice" placeholder="请输入采购价" style="width: 70%; margin-right: 10px"></el-input>
              元/个
            </el-form-item>
            <el-form-item label="包含合伙人智能学习管理系数" prop="systemNumber" label-width="200px">
              <el-input :disabled="true" v-model="purchaseAmountForm.systemNumber" placeholder="请输入管理系数" style="width: 70%; margin-right: 10px"></el-input>
              套
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer-center">
          <el-button @click="closeDialog(2)">取消</el-button>
          <el-button type="primary" @click="handleSubmit(2)">确定</el-button>
        </div>
      </CustomDialog>

      <CustomDialog
        v-if="watchgoodsDialogVisible"
        title="查看"
        :value.sync="watchgoodsDialogVisible"
        :closeOnClickModal="false"
        width="35%"
        @close="handleClose"
        :before-close="handleClose"
      >
        <div class="f-s-c font">
          <div>
            <span class="m-r">商品名称</span>
            <span>{{ purchaseAmountForm.commodityType }}</span>
          </div>
          <div>
            <span class="m-r">品牌采购价</span>
            <span>{{ purchaseAmountForm.brandPurchasePrice }}元/个</span>
          </div>
          <div v-if="studyType2">
            <span class="m-r">包含合伙人智能学习管理系数</span>
            <span>{{ purchaseAmountForm.systemNumber }}套</span>
          </div>
          <div v-if="studyType1">
            <span class="m-r">品牌批发价</span>
            <span>{{ purchaseAmountForm.brandWholesalePrice }}元/个</span>
          </div>
          <div v-if="studyType1">
            <span class="m-r">俱乐部采购价</span>
            <span>{{ purchaseAmountForm.clubPurchasePrice }}元/个</span>
          </div>
        </div>
      </CustomDialog>
    </div>
  </div>
</template>
<script>
  import { queryPriceInfoList, queryCountInfo, updateCount, updatePrice } from '@/api/purchase/procurementAllocation';
  import CustomDialog from '@/components/customDialog/index.vue';

  export default {
    data() {
      return {
        studyType1: false,
        studyType2: false,
        list: [],
        purchaseAmountForm: {
          brandPurchasePrice: '',
          clubPurchasePrice: ''
        },
        rules: {
          clubPurchasePrice: [
            { required: true, message: '请输入俱乐部采购价', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (!/^\d+(\.\d{1,2})?$/.test(value)) {
                  callback(new Error('请输入合法金额格式（最多两位小数）'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          brandPurchasePrice: [
            { required: true, message: '请输入品牌采购价', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (!/^\d+(\.\d{1,2})?$/.test(value)) {
                  callback(new Error('请输入合法金额格式（最多两位小数）'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ]
        },
        changeStatus: true,
        goodsDialogVisible1: false,
        goodsDialogVisible2: false,
        watchgoodsDialogVisible: false
      };
    },
    components: {
      CustomDialog
    },
    mounted() {
      this.index();
    },
    methods: {
      index() {
        queryPriceInfoList().then((res) => {
          this.list = res.data;
        });
      },

      edit(info) {
        this.purchaseAmountForm = info;
        if (info.commodityType == '合伙人智能学习管理系统') {
          this.goodsDialogVisible1 = true;
        } else {
          this.goodsDialogVisible2 = true;
        }
      },
      viewDetails(info) {
        this.purchaseAmountForm = info;
        if (info.commodityType == '合伙人智能学习管理系统') {
          this.studyType1 = true;
        } else {
          this.studyType2 = true;
        }
        this.watchgoodsDialogVisible = true;
      },

      handleSubmit() {
        this.$refs.priceForm.validate((valid) => {
          if (valid) {
            // 校验通过，执行提交逻辑
            // let data = {
            //   clubPurchasePrice: this.purchaseAmountForm.clubPurchasePrice,
            //   brandPurchasePrice: this.purchaseAmountForm.brandPurchasePrice,
            //   brandWholesalePrice:this.purchaseAmountForm.brandWholesalePrice,
            //   systemNumber:this.purchaseAmountForm.systemNumber,
            // };

            updatePrice(this.purchaseAmountForm).then((res) => {
              this.submitForm();
              window.location.reload();
            });
          } else {
            // 校验失败，给出提示（Element 默认已有提示）
            return false;
          }
        });
      },
      closeDialog(id) {
        // 关闭弹窗并重置表单
        this.$refs.priceForm.resetFields();
        // 其他关闭逻辑...
        if (id == 1) {
          this.goodsDialogVisible1 = false;
        } else {
          this.goodsDialogVisible2 = false;
        }
      },
      handleClose() {
        this.studyType1 = false;
        this.studyType2 = false;
        this.watchgoodsDialogVisible = false;
      },
      submitForm() {
        // 实际的提交逻辑
        console.log('提交数据:', this.purchaseAmountForm);
      }
    }
  };
</script>
<style lang="scss">
  .title {
    margin-bottom: 10px;
  }

  .study {
    margin-top: 10px;
  }

  .f-c {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .input-box {
    width: 232px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .dialog-footer-center {
    display: flex;
    justify-content: right;
    gap: 10px;
    /* 按钮间距 */
  }
  .f-c {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .f-s-c {
    height: 100px;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
  }
  .font {
    font-size: 16px;
    font-weight: 400;
    color: #000;
  }
  .m-r {
    margin-right: 30px;
  }
  .m-b {
    margin-bottom: 10px;
  }
</style>
